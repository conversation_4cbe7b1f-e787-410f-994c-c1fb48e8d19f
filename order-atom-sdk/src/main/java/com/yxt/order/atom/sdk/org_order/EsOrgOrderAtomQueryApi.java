package com.yxt.order.atom.sdk.org_order;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.org_order.req.EsOrderBaseReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderAmountStaticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderCountStatisticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderPageQueryReqDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgOrderAmountStaticResDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgOrderCountStatisticResDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgOrderResDTO;
import com.yxt.order.common.es.EsPageDTO;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface EsOrgOrderAtomQueryApi {

  String ORDER_ENDPOINT = "/1.0";

  @PostMapping(ORDER_ENDPOINT + "/es/org-order/amount/static")
  ResponseBase<EsOrgOrderAmountStaticResDTO> orgOrderAmountStatic(@RequestBody @Valid EsOrgOrderAmountStaticReqDTO reqDto);

  @PostMapping(ORDER_ENDPOINT + "/es/org-order/count/statistic")
  ResponseBase<EsOrgOrderCountStatisticResDTO> orgOrderCountStatistic(@RequestBody @Valid EsOrgOrderCountStatisticReqDTO reqDto);

  @PostMapping(ORDER_ENDPOINT + "/es/org-order/page/query")
  ResponseBase<EsPageDTO<EsOrgOrderResDTO>> orgOrderPageQuery(@RequestBody @Valid EsOrgOrderPageQueryReqDTO reqDto);

  @PostMapping(ORDER_ENDPOINT + "/es/org-order/detail")
  ResponseBase<EsOrgOrderResDTO> orgOrderDetailQuery(@RequestBody @Valid EsOrderBaseReqDTO reqDto);
}
