package com.yxt.order.atom.sdk.order_info.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.lang.dto.PageBase;
import java.util.Date;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2024/12/10 16:34
 */
@Data
public class MemberRefundOrderListReqDto extends PageBase {

  /**
   * 会员ID
   */
  @NotEmpty(message = "userId can not null")
  private String userId;

  private String orderNo;
  private String refundNo;

  /**
   * 下单开始时间
   */
  @NotNull(message = "createdStart can not null")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdStart;

  /**
   * 下单结束时间
   */
  @NotNull(message = "createdEnd can not null")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdEnd;

  private String storeCode;

  private String erpName;

  private String erpCode;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  private String orderSource;


  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 三方退单号
   */
  private String thirdRefundNo;

}
