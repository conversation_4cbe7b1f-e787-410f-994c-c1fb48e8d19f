package com.yxt.order.atom.sdk.order_world;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldOrderQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldOrderBatchQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.res.OrderRelatedInfoRes;
import java.util.List;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface OrderWorldOrderAtomQueryApi {

  @PostMapping(ORDER_ENDPOINT + "/order-world/order/query/by-scale")
  ResponseBase<OrderRelatedInfoRes> getOrderInfoByScale(@RequestBody @Valid OrderWorldOrderQueryByScaleReq request);

  @PostMapping(ORDER_ENDPOINT + "/order-world/order/query/by-scale/batch")
  ResponseBase<List<OrderRelatedInfoRes>> getOrderInfoBatchByScale(@RequestBody @Valid OrderWorldOrderBatchQueryByScaleReq request);
}
