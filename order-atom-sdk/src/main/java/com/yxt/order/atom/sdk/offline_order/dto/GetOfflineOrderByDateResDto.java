package com.yxt.order.atom.sdk.offline_order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月22日 19:19
 * @email: <EMAIL>
 */
@Data
public class GetOfflineOrderByDateResDto {

  private List<OrderData> orderDataList;

  @Data
  public static class OrderData {

    private Long id; // 不使用分页,使用id来做
    private String orderNo;
    private String userId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
  }

}
