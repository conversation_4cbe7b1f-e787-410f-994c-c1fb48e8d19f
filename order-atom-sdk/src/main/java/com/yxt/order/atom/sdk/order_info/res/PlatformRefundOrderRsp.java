package com.yxt.order.atom.sdk.order_info.res;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 平台退款单列表实体。
 *
 * <AUTHOR>
 * @version 2021/4/25、10:58
 **/
@Data
public class PlatformRefundOrderRsp implements Serializable {

    @ApiModelProperty("三方平台退款单号")
    private String thirdRefundNo;

    @ApiModelProperty("三方平台退款单号")
    private String refundNo;

    @ApiModelProperty("第三方平台订单号")
    private String thirdOrderNo;

    @ApiModelProperty(value = "平台名称")
    private String platformName;

    @ApiModelProperty(value = "平台code")
    private String thirdPlatformCode;

    @ApiModelProperty(value = "机构名称")
    private String organizationName;

    @ApiModelProperty(value = "线上门店编码")
    private String onlineStoreCode;

    @ApiModelProperty(value = "线上门店名称")
    private String onlineStoreName;

    @ApiModelProperty("退货类型  0、仅退款，1、退货退款 2、取消订单")
    private String refundType;

    @ApiModelProperty("商家退款总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("第三方退款单状态 10.等待卖家同意 20.等待买家退货 30.待卖家确认收货 100.退款成功 102.卖家拒绝退款 103.退款取消 105.其他")
    private String state;

    @ApiModelProperty("第三方退款单状态 10.等待卖家同意 13.等待买家退货 16.待卖家确认收货 20.退款成功 30.卖家拒绝退款 50.退款取消 0.其他")
    private String thirdStatus;

    @ApiModelProperty("退款申请时间")
    private Date createTime;

    @ApiModelProperty("退款完成时间")
    private Date completeTime;

    @ApiModelProperty("是否重复退款： true 重复 ,其余 未重复 ")
    private Boolean repeatable;

    @ApiModelProperty("退款单处理状态: 0 未处理， 1 已处理")
    private Integer handleStatus;

    @ApiModelProperty("退款版本号")
    private String refundVersion;

    @ApiModelProperty("平台订单状态（目前只有美团的这个状态会处理逻辑）")
    private String olStatus;

    @ApiModelProperty(value = "大于0，表示存在已发货的系统订单")
    private Integer shipCount;

    @ApiModelProperty("订单归属类型 0-为商户，默认；1-为供应商订单")
    private Integer orderOwnerType;

    @ApiModelProperty("商户号")
    private String merCode;

    @ApiModelProperty("商户号名称")
    private String merName;

    @ApiModelProperty("履约方id")
    private String supplierCode;

    @ApiModelProperty("履约方名称")
    private String supplierName;

    @ApiModelProperty(value = "推送erp状态 0-未推送 1-推送成功 2-推送失败")
    private Integer erpPushStatus;

}
