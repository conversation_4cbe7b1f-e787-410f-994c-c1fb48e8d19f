package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.atom.sdk.common.data.CommodityStockChangeRecordDTO;
import com.yxt.order.atom.sdk.common.data.CommodityStockDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPayDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
@ApiModel("订单新模型-更新订单支付状态请求")
public class UpdateOrderPayStatusOptionalReq {

  @ApiModelProperty(value = "订单号")
  @NotBlank(message = "订单号不能为空")
  private String orderNo;

  @ApiModelProperty(value = "订单信息")
  private OrderInfoDTO orderInfo;

  @ApiModelProperty(value = "订单明细信息")
  private List<OrderDetailDTO> orderDetailList;

  @ApiModelProperty(value = "订单支付方式信息")
  private List<OrderPayDTO> orderPayTypeList;

  @ApiModelProperty(value = "库存占用记录")
  private List<CommodityStockChangeRecordDTO> commodityStockChangeRecordList;
}
