package com.yxt.order.atom.sdk.org_order.res;

import io.swagger.annotations.ApiModel;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("正单数量统计")
@AllArgsConstructor
@NoArgsConstructor
public class EsOrgOrderCountStatisticResDTO {

  private List<EsOrgOrderCountResult> countList;

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class EsOrgOrderCountResult {

    private String statisticKey;

    private String statisticValue;
  }
}
