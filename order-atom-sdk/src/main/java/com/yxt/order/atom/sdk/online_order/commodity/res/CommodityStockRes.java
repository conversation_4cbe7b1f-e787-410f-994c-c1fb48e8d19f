package com.yxt.order.atom.sdk.online_order.commodity.res;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @author: xin.tu
 * @date: 2023/1/6 10:06
 * @menu:
 */
@Data
public class CommodityStockRes implements Serializable {


  private Long id;

  private String merCode;

  private String orderNo;

  private String erpCode;

  private String onlineStoreCode;

  private Long orderDetailId;

  private Integer stockQty;

  private Integer type;

  private Date createTime;

  private String organizationCode;

  /**
   * 流水号
   */
  private Long serialNumber;

  private String storeId;
}
