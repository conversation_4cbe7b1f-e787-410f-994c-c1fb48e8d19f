package com.yxt.order.atom.sdk.common.order_world;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 订单支付信息
 */
@Data
public class OrderAmountDTO {

  private Long id;

  /**
   * 内部订单号，自己生成
   */
  private String orderNo;

  /**
   * 实付金额
   */
  private BigDecimal actualPayAmount;

  /**
   * 实收金额
   */
  private BigDecimal actualCollectAmount;

  /**
   * 交易佣金
   */
  private BigDecimal brokerageAmount;

  /**
   * 商品总金额
   */
  private BigDecimal totalAmount;

  /**
   * 应收配送费
   */
  private BigDecimal deliveryAmount;

  /**
   * 应收包装费
   */
  private BigDecimal packAmount;

  /**
   * 商家包装费
   */
  private BigDecimal merchantPackAmount;

  /**
   * 商家配送费
   */
  private BigDecimal merchantDeliveryAmount;

  /**
   * 商家订单级总优惠
   */
  private BigDecimal merchantOrderDiscountAmount;

  /**
   * 商家配送费优惠
   */
  private BigDecimal merchantDeliveryDiscountAmount;

  /**
   * 商家商品总优惠
   */
  private BigDecimal merchantCommodityDiscountAmount;

  /**
   * 平台包装费
   */
  private BigDecimal platformPackAmount;

  /**
   * 平台配送费
   */
  private BigDecimal platformDeliveryAmount;

  /**
   * 平台订单级优惠汇总
   */
  private BigDecimal platformOrderDiscountAmount;

  /**
   * 平台配送费优惠
   */
  private BigDecimal platformDeliveryDiscountAmount;

  /**
   * 平台商品优惠金额
   */
  private BigDecimal platformCommodityDiscountAmount;

  /**
   * 医保金额
   */
  private BigDecimal medicareAmount;

  /**
   * 剩余交易佣金（实时）
   */
  private BigDecimal remainBrokerageAmount;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

}
