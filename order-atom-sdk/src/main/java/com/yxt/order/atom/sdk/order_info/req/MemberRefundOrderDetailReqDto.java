package com.yxt.order.atom.sdk.order_info.req;

import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2024/12/10 16:34
 */
@Data
public class MemberRefundOrderDetailReqDto {

  /**
   * 系统退单号
   */
  @NotEmpty(message = "refundNo can not null")
  private String refundNo;

  @NotEmpty(message = "userId can not null")
  private String userId;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  @NotEmpty(message = "orderSource can not null")
  private String orderSource;


}
