package com.yxt.order.atom.sdk.account_info;


import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.account_info.req.AccountInfoQueryReq;
import com.yxt.order.atom.sdk.account_info.req.RefundAccountInfoQueryReq;
import com.yxt.order.atom.sdk.account_info.res.AccountInfoQueryDto;
import com.yxt.order.atom.sdk.account_info.res.RefundAccountInfoQueryDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> junfeng
 * @date 2024年11月12日 14:11
 */
public interface AccountAtomEsApi {

  String ACCOUNT_ENDPOINT = "/1.0";

  /**
   * 手动创建正向单索引
   *
   * @return ResponseBase<Boolean>
   */
  @PostMapping(ACCOUNT_ENDPOINT + "/es/account/createOrderIndex")
  ResponseBase<Boolean> accountCreateOrderIndex();

  /**
   * 手动创建逆向单索引
   *
   * @return ResponseBase<Boolean>
   */
  @PostMapping(ACCOUNT_ENDPOINT + "/es/account/createRefundIndex")
  ResponseBase<Boolean> accountCreateRefundIndex();

  /**
   * 有则更新,无责新增
   *
   * @param orderInfo
   * @return
   */
  @PostMapping(ACCOUNT_ENDPOINT + "/es/account/query")
  ResponseBase<PageDTO<AccountInfoQueryDto>> queryAccountInfo(@RequestBody AccountInfoQueryReq orderInfo);

  @PostMapping(ACCOUNT_ENDPOINT + "/es/refund/account/query")
  public ResponseBase<PageDTO<RefundAccountInfoQueryDto>> queryRefundAccountInfo(
      @RequestBody  RefundAccountInfoQueryReq req);

}
