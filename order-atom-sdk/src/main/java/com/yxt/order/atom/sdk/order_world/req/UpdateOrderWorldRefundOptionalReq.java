package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.atom.sdk.common.order_world.RefundOrderAmountDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderPayDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
@ApiModel("订单新模型-更新退款单请求")
public class UpdateOrderWorldRefundOptionalReq {

  @ApiModelProperty(value = "退款单号")
  @NotBlank(message = "退款单号不能为空")
  private String refundNo;

  @ApiModelProperty(value = "退款信息")
  private RefundOrderDTO refundOrder;

  @ApiModelProperty(value = "退款明细信息")
  private List<RefundOrderDetailDTO> refundOrderDetailList;

  @ApiModelProperty(value = "退款金额信息")
  private RefundOrderAmountDTO refundOrderAmount;

  @ApiModelProperty(value = "退款支付方式信息")
  private List<RefundOrderPayDTO> refundOrderPayList;

}
