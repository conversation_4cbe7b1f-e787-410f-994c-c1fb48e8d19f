package com.yxt.order.atom.sdk.common.order_world;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单支付方式信息
 */
@Data
public class OrderPayDTO {

  private Long id;

  /**
   * 内部订单号，自己生成
   */
  private String orderNo;

  /**
   * 支付唯一号
   */
  private String orderPayNo;

  /**
   * 支付方式
   */
  private String payType;

  /**
   * 支付方式名称
   */
  private String payName;

  /**
   * 支付金额
   */
  private BigDecimal payAmount;


  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;
}
