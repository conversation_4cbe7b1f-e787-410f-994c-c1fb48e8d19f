package com.yxt.order.atom.sdk.order_info.res;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 会员消费记录详细信息
 *
 * @author: moatkon
 * @time: 2024/12/10 16:36
 */
@Data
public class MemberRefundOrderResDto {

  /**
   * 系统单号
   */
  private String refundNo;

  /**
   * 系统单号
   */
  private String orderNo;
  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 退单三方单号
   */
  private String thirdRefundNo;

  /**
   * 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消
   */
  private Integer refundStatus;

  /**
   * 退单时间
   */
  private Date created;


  /**
   * 退款金额
   */
  private BigDecimal refundAmount;

  /**
   * 退款运费金额
   */
  private BigDecimal refundDeliveryFeeAmount;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  private String orderSource;

  /**
   * 退款原因
   */
  private String reason;

  /**
   * 订单明细
   */
  private List<MemberRefundOrderDetailDto> refundDetailDtoList;

  /**
   * 下账金额
   */
  private BigDecimal billAmount;

  private String storeCode;

  private String cashier; // 收银员
  private String cashierName;// 收银员姓名

}
