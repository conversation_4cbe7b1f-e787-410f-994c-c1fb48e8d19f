package com.yxt.order.atom.sdk.org_order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;


@Data
@ApiModel(value = "订单详情")
public class EsOrderBaseReqDTO {

  @ApiModelProperty(value = "订单号")
  @NotBlank(message = "订单号不能为空")
  private String orderNo;

  @ApiModelProperty(value = "机构编码")
  private String orgCode;

}
