package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.atom.sdk.common.order_world.PlatformOrderAmountDTO;
import com.yxt.order.atom.sdk.common.order_world.PlatformOrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.PlatformOrderInfoDTO;
import com.yxt.order.atom.sdk.common.order_world.PlatformOrderPayDTO;
import com.yxt.order.atom.sdk.common.order_world.PlatformOrderUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("订单新模型-新建平台订单请求")
public class SavePlatformOrderOptionalReq {

  @ApiModelProperty(value = "平台订单信息")
  private PlatformOrderInfoDTO platformOrderInfo;

  @ApiModelProperty(value = "平台订单明细信息")
  private List<PlatformOrderDetailDTO> platformOrderDetailList;

  @ApiModelProperty(value = "平台订单金额信息")
  private PlatformOrderAmountDTO platformOrderAmount;

  @ApiModelProperty(value = "平台订单用户信息")
  private PlatformOrderUserDTO platformOrderUser;

  @ApiModelProperty(value = "平台订单支付信息")
  private List<PlatformOrderPayDTO> platformOrderPayList;
}
