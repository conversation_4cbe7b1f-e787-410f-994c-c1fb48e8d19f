//package com.yxt.order.atom.sdk.inner;
//
//import io.swagger.annotations.ApiModelProperty;
//import java.util.List;
//import lombok.Data;
//
//@Data
//public class DetailInfoResDto {
//    @ApiModelProperty(value = "商品编码")
//    private String erpCode;
//    @ApiModelProperty("药品类型 OTC甲类(0)/处方(1)/OTC乙类(2)/非药品(3)/OTC(4)")
//    private Integer drugType;
//    @ApiModelProperty("供应商编码")
//    private String supplierCode;
//    @ApiModelProperty("三级分类Id")
//    private String typeId;
//    @ApiModelProperty("二级分类Id")
//    private String secondTypeId;
//    @ApiModelProperty("一级分类Id")
//    private String firstTypeId;
//    @ApiModelProperty("规格对应标签集合")
//    private List<CommodityLabelResDto> labelDTOList;
//    @ApiModelProperty("供应商商品规格id")
//    private Long goodsSpecId;
//}
