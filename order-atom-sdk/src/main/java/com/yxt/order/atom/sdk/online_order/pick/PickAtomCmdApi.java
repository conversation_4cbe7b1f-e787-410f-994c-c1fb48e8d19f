package com.yxt.order.atom.sdk.online_order.pick;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.online_order.pick.dto.req.InsertPickReqDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年02月23日 14:11
 * @email: <EMAIL>
 */
public interface PickAtomCmdApi {

  @PostMapping(ORDER_ENDPOINT + "/pick/insertPick")
  ResponseBase<Boolean> insertPick(@RequestBody InsertPickReqDto req);


}
