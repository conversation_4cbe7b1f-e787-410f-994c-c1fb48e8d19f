package com.yxt.order.atom.sdk.offline_order.req;

import static com.yxt.order.atom.sdk.BaseUtils.getNumStr;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月07日 13:47
 * @email: <EMAIL>
 */
@Data
public class OfflineRefundOrderExistsReqDto {

  private String storeCode;
  private String thirdRefundNo;
  private String thirdPlatformCode;
  private Date thirdCreated;

  // 用于分表,如果已确定分表则不用传,controller单独调用传
  private String defineNo;


  public String existsKey() {
    return String.format("offline_refund_order_%s_%s_%s_%s", storeCode, thirdRefundNo,
        thirdPlatformCode, getNumStr(thirdCreated));
  }
}
