package com.yxt.order.atom.sdk.common.data;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月11日 15:38
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class OriThirdOrderDetailDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;


  @ApiModelProperty(value = "第三方平台商品编码")
  //字段名和三方接口保持统一
  private String numIid;

  @ApiModelProperty(value = "erpCode")
  //字段名和三方接口保持统一
  private String outerIid;

  @ApiModelProperty(value = "条形码")
  //字段名和三方接口保持统一
  private String upc;

  @ApiModelProperty(value = "商品名称")
  //字段名和三方接口保持统一
  private String title;

  @ApiModelProperty(value = "原始订单数量")
  //字段名和三方接口保持统一
  private Integer num;

  @Deprecated//退货数量通过退款单来计算
  @ApiModelProperty(value = "平台已退货数量")
  private Integer refundCount = 0;

  @ApiModelProperty(value = "子订单编号-京东到家需要")
  private String oid;

  @ApiModelProperty(value = "第三方详情ID")
  private String thirdDetailId;

  @ApiModelProperty(value = "京东健康商家自配送用 配送方式有：1：商家配送，2：门店自提，3：订单支持商家自配，不自动呼叫运力")
  //字段名和三方接口保持统一
  private String deliveryType;

  @ApiModelProperty(value = "商品图片")
  private String mainPic;

  @ApiModelProperty(value = "商品原单价")
  private BigDecimal originalPrice;

  @ApiModelProperty(value = "商品售价")
  private BigDecimal price;

  @ApiModelProperty(value = "实付价")
  private BigDecimal payment;

  @ApiModelProperty(value = "订单号")
  private Long orderNo;
  @ApiModelProperty(value = "创建时间")
  private Date createTime;
  @ApiModelProperty(value = "更新时间")
  private Date modifyTime;

}