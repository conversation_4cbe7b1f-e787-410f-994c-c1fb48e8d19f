package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.types.order_world.RefundOrderSearchConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "退款单查询条件")
public class EsOrderWorldRefundOrderSearchCondition {

  @ApiModelProperty("查询条件类型")
  private RefundOrderSearchConditionEnum searchType;

  @ApiModelProperty("条件值")
  private String searchData;

}
