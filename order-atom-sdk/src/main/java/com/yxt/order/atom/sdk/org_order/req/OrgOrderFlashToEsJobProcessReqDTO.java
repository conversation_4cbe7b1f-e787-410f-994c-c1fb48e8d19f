package com.yxt.order.atom.sdk.org_order.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

@Data
public class OrgOrderFlashToEsJobProcessReqDTO {

  /**
   * 开始刷数时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date flashStartTime;

  /**
   * 结束刷数时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date flashEndTime;

  /**
   * 线上单：ONLINE 线下单：OFFLINE
   */
  private String orderSource;

  /**
   * 正单：ORDER 退款单：REFUND
   */
  private String orderType;

  /**
   * job状态
   */
  private Integer jobStatus;

  /**
   * 每批次处理数量
   */
  private Integer processSize = 10;

  /**
   * 一次运行处理几批
   */
  private Integer processBatchSize = 5;

  /**
   * 处理一批之后的休眠时间
   */
  private String sleepTime;

}
