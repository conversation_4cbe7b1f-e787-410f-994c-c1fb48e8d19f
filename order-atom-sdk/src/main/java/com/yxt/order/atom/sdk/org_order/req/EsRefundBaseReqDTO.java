package com.yxt.order.atom.sdk.org_order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;


@Data
@ApiModel(value = "退单详情")
public class EsRefundBaseReqDTO {

  @ApiModelProperty(value = "退单号")
  @NotBlank(message = "退单号不能为空")
  private String refundNo;

  @ApiModelProperty(value = "机构编码")
  private String orgCode;

}
