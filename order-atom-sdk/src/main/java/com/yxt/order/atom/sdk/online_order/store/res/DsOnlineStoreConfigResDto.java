package com.yxt.order.atom.sdk.online_order.store.res;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 13:46
 * @email: <EMAIL>
 */
@Data
public class DsOnlineStoreConfigResDto implements Serializable {


  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 平台编码
   */
  private String platformCode;

  /**
   * 网店编码
   */
  private String clientCode;

  /**
   * 线上门店id
   */
  private Long onlineStoreId;

  /**
   * 服务模式，1o2o B2C
   */
  private Integer serviceType;

  /**
   * 配送模式，员工配送、到店自提、蜂鸟配送、达达配送、美团配送
   */
  private String selfDeliveryType;

  /**
   * 配送费收取方, 1平台，2商家 3，平台向商家收取
   */
  private Integer deliveryFeeTo;

  /**
   * 包装费收取方, 1平台，2，商家
   */
  private Integer packageFeeTo;

  /**
   * 平台优惠支付方, 1平台，2商家
   */
  private Integer platformBenefitOwner;

  /**
   * 佣金比例
   */
  private BigDecimal brokerageRate;

  /**
   * 保底佣金
   */
  private BigDecimal brokerageMinimum;

  /**
   * 是否同步库存,0不同步，1同步
   */
  private Integer syncStock;

  /**
   * 是否同步价格,0不同步，1同步
   */
  private Integer syncPrice;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;

  /**
   * 零售下账时机（1.拣货完成后 2.配送出库后）
   */
  private Integer retailTime;

  /**
   * 商家优惠是否分摊（0.否 1.是）
   */
  private Integer whetherDiscountShare;

  /**
   * 佣金是否分摊(0.否 1.是)
   */
  private Integer whetherCommissionShare;

  /**
   * 接单是否锁库存（0.否 1.是）
   */
  private Integer whetherInventoryLocked;
  /**
   * 是否需要审方（0. 不需要审方  1.审方后作业  2. 拣货复核校验）
   */
  private Integer whetherNeedPrescription;
  /**
   * 是否需要拆单（0不拆单 1拆单）
   */
  private Integer whetherSpliteOrder;
  /**
   * '物流回传接口 (0.打印面单 1.商品出库)'
   */
  private Integer logisticsReturnNode;
  /**
   * 服务模式
   */
  private String serviceMode;
  /**
   * 是否开票 1开票0不开票
   */
  private Integer whetherOpenInvoice;
  /**
   * 店铺分类
   */
  private String storeType;

  /**
   * 收款模式
   */
  private Integer settleMode;

  /**
   * 店铺发货加急标识0-正常 1-加急 ，字段为空时默认为正常
   */
  private Integer isUrgent;

  /**
   * 审核后，是否自动生成面单, 0-否，1-是
   */
  private Integer whetherGenerateSheet;

  /**
   * 扫描发货后，自动打印面单，0-否，1-是
   */
  private Integer whetherAutoPrintSheet;

  /**
   * 是否开启药房网隐私面单 , 0-否，1-是，默认否
   */
  private Integer yfwSheet;

  /**
   * 审方方式： 推送至审方平台审方 2, 在OMS手工审方 1
   */
  private Integer checkingType;

  @ApiModelProperty(value = "拣货方式：1-oms拣货，0-erp拣货")
  private Integer omsPickType;

  /**
   * 是否同步价格,0不同步，1同步(平台使用) 2023/2/6
   */
  private Integer platformSyncPrice;
  /**
   * 同步价格门店编码
   */
  private String syncPriceStoreCode;

  /**
   * 系统是否默认批号 0:否 1:是
   */
  private Integer defaultBatchNo;

  /**
   * 下账支付编码
   */
  private String accountPayCode;

  /**
   * B2C店铺配置扩展信息 存放定制配置
   *
   */
  private String b2cExtendConf;

  /**
   * 拣货方式 1-人工拣货 2-机器拣货
   */
  private Integer pickType;


}