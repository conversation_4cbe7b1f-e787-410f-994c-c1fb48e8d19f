package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.atom.sdk.common.order_world.OrderAmountDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderCouponDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDeliveryAddressDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailCouponDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailPromotionDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPayDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPrescriptionDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPromotionDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderSetDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderUserInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@ApiModel("订单新模型-更新订单请求")
public class UpdateOrderWorldOrderOptionalReq {

  @ApiModelProperty(value = "内部订单号")
  private String orderNo;

  @ApiModelProperty(value = "订单信息")
  private OrderInfoDTO orderInfo;

  @ApiModelProperty(value = "订单明细信息")
  private List<OrderDetailDTO> orderDetailList;

  @ApiModelProperty(value = "订单收件人信息")
  private OrderDeliveryAddressDTO orderDeliveryAddress;

  @ApiModelProperty(value = "订单金额信息")
  private OrderAmountDTO orderAmount;

  @ApiModelProperty(value = "订单支付方式信息")
  private List<OrderPayDTO> orderPayList;

  @ApiModelProperty(value = "订单处方信息")
  private List<OrderPrescriptionDTO> orderPrescriptionList;

  @ApiModelProperty(value = "订单用户信息")
  private OrderUserInfoDTO orderUserInfo;

  @ApiModelProperty(value = "订单优惠券信息")
  private List<OrderCouponDTO> orderCouponList;

  @ApiModelProperty(value = "订单促销信息")
  private List<OrderPromotionDTO> orderPromotionList;

  @ApiModelProperty(value = "订单明细优惠券信息")
  private List<OrderDetailCouponDTO> orderDetailCouponList;

  @ApiModelProperty(value = "订单明细促销信息")
  private List<OrderDetailPromotionDTO> orderDetailPromotionList;

  @ApiModelProperty(value = "订单组合明细信息")
  private List<OrderSetDetailDTO> orderSetDetailList;

  @ApiModelProperty(value = "需要删除的订单关联信息")
  private DeletedOrderRelated deletedOrderRelated;

  @Getter
  @Setter
  public static class DeletedOrderRelated {

    private List<Long> orderDetailIdList;

    private List<Long> orderCouponIdList;

    private List<Long> orderDetailCouponIdList;

    private List<Long> orderDetailPromotionIdList;

    private List<Long> orderPayIdList;

    private List<Long> orderPrescriptionIdList;

    private List<Long> orderPromotionIdList;

    private List<Long> orderSetDetailIdList;
  }

}
