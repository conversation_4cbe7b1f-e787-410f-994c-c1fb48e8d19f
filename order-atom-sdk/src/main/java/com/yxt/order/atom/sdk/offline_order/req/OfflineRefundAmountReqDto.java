package com.yxt.order.atom.sdk.offline_order.req;

import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月08日 16:34
 * @email: <EMAIL>
 */
@Data
public class OfflineRefundAmountReqDto {

  /**
   * 三方订单号
   */
  @NotEmpty(message = "thirdOrderNo can not empty")
  private String thirdOrderNo;

  /**
   * 三方平台编码
   */
  @NotEmpty(message = "thirdPlatformCode can not empty")
  private String thirdPlatformCode;

  @NotEmpty(message = "storeCode can not empty")
  private String storeCode;


  /**
   * 辅助分表字段-单号
   */
  @NotEmpty(message = "numberHelper can not empty")
  private String numberHelper;


}
