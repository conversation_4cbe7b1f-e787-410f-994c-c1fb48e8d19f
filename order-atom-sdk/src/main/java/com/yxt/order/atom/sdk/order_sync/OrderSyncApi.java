package com.yxt.order.atom.sdk.order_sync;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.order_world.OrderSyncInitErrorLogDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderSyncMappingDTO;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncInitErrorLogRemoveReq;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncInitErrorLogSearchReq;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncMappingSearchReq;
import java.util.List;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface OrderSyncApi {

  String ORDER_ENDPOINT = "/1.0";

  @PostMapping(ORDER_ENDPOINT + "/order-sync/mapping/search")
  ResponseBase<List<OrderSyncMappingDTO>> orderSyncMappingSearch(@RequestBody @Valid OrderSyncMappingSearchReq request);

  @PostMapping(ORDER_ENDPOINT + "/order-sync/mapping/saveOrUpdate")
  ResponseBase<Void> orderSyncMappingSaveOrUpdate(@RequestBody @Valid OrderSyncMappingDTO request);

  @PostMapping(ORDER_ENDPOINT + "/order-sync/init-error-log/search")
  ResponseBase<List<OrderSyncInitErrorLogDTO>> orderSyncInitErrorLogSearch(@RequestBody @Valid OrderSyncInitErrorLogSearchReq request);

  @PostMapping(ORDER_ENDPOINT + "/order-sync/init-error-log/saveOrUpdate")
  ResponseBase<Void> orderSyncInitErrorLogSaveOrUpdate(@RequestBody @Valid OrderSyncInitErrorLogDTO request);

  @PostMapping(ORDER_ENDPOINT + "/order-sync/init-error-log/remove")
  ResponseBase<Void> orderSyncInitErrorLogRemove(@RequestBody @Valid OrderSyncInitErrorLogRemoveReq request);
}
