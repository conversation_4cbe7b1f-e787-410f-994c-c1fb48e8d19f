package com.yxt.order.atom.sdk.common.data;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月11日 15:37
 * @email: <EMAIL>
 */
@Data
public class OrderDeliveryAddressDTO implements Serializable {

  private static final long serialVersionUID = 1L;


  private Long id;

  /**
   * 订单号
   */
  private Long orderNo;

  /**
   * 收货人
   */
  @ApiModelProperty(value = "收货人名")
  private String receiverName;

  /**
   * 收货人电话
   */

  @ApiModelProperty(value = "收货人电话")
  private String receiverTelephone;

  /**
   * 收货人手机
   */

  @ApiModelProperty(value = "收货人手机号码")
  private String receiverMobile;

  /**
   * 省份
   */
  @ApiModelProperty(value = "收货人省份")
  private String province;

  /**
   * 城市
   */
  @ApiModelProperty(value = "收货人城市")
  private String city;

  /**
   * 区域
   */
  @ApiModelProperty(value = "收货人所在城市区域")
  private String district;

  /**
   * 城镇
   */
  @ApiModelProperty(value = "收货人所在城镇")
  private String town;

  /**
   * 详细地址
   */
  @ApiModelProperty(value = "收货人详细地址")
  private String address;

  /**
   * 邮编
   */
  @ApiModelProperty(value = "邮编")
  private String zipCode;

  /**
   * 完整详细地址
   */
  @ApiModelProperty(value = "完整详细地址")
  private String fullAddress;

  @ApiModelProperty(value = "oms订单号-b2c")
  private Long omsOrderNo;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;

  @ApiModelProperty(value = "脱敏手机号")
  private String privacyPhone;

  @ApiModelProperty(value = "收货人隐私姓名")
  private String receiverNamePrivacy;

  @ApiModelProperty(value = "收货人隐私手机号")
  private String receiverPhonePrivacy;

  @ApiModelProperty(value = "收货人隐私地址")
  private String receiverAddressPrivacy;
}
