package com.yxt.order.atom.sdk.online_order.account;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.data.AccountOrderDTO;
import com.yxt.order.types.order.OrderNo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/8/1
 */
public interface AccountOrderAtomQryApi {

  @PostMapping(ORDER_ENDPOINT + "/accountOrder/getAccountOrder")
  ResponseBase<AccountOrderDTO> getAccountOrder(
      @RequestBody OrderNo orderNo);
}
