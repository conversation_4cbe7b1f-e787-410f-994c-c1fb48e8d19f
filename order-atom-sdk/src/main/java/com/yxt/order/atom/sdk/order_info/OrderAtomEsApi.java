package com.yxt.order.atom.sdk.order_info;


import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_info.req.FlashB2cDataToEsReq;
import com.yxt.order.atom.sdk.order_info.req.FlashDataToEsReq;
import com.yxt.order.atom.sdk.order_info.req.MemberOrderDetailReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberOrderListReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberOrderSimpleReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberRefundOrderDetailReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberRefundOrderListReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberRefundOrderSimpleReqDto;
import com.yxt.order.atom.sdk.order_info.req.OrderListQueryDto;
import com.yxt.order.atom.sdk.order_info.req.OrderPageOtherReqDto;
import com.yxt.order.atom.sdk.order_info.req.OrderPageReqDto;
import com.yxt.order.atom.sdk.order_info.res.EsOmsOrderData;
import com.yxt.order.atom.sdk.order_info.res.MemberOrderResDto;
import com.yxt.order.atom.sdk.order_info.res.MemberOrderSimpleResDto;
import com.yxt.order.atom.sdk.order_info.res.MemberRefundOrderResDto;
import com.yxt.order.atom.sdk.order_info.res.MemberRefundOrderSimpleResDto;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年02月23日 14:11
 * @email: <EMAIL>
 */
public interface OrderAtomEsApi {

  String ORDER_ENDPOINT = "/1.0";


  @PostMapping(ORDER_ENDPOINT + "/es-order/flashDataToEs")
  ResponseBase<Boolean> flashDataToEs(@RequestBody @Valid FlashDataToEsReq flashDataToEsReq);

  /**
   * omsOrderInfo刷数到ES
   *
   * @param req
   * @return ResponseBase<Boolean>
   */
  @PostMapping(ORDER_ENDPOINT + "/es-oms-order/flashDataToEs")
  ResponseBase<Boolean> omsOrderFlashDataToEs(@RequestBody @Valid FlashB2cDataToEsReq req);

  /**
   * 创建OmsOrderInfo索引
   *
   * @return ResponseBase<Boolean>
   */
  @PostMapping(ORDER_ENDPOINT + "/es/order/createIndex")
  ResponseBase<Boolean> esOrderCreateIndex();

  /**
   * 会员消费记录刷到Es
   * @param flashDataToEsReq
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es-member-order/flashDataToEs")
  ResponseBase<Boolean> memberOrderFlashDataToEs(@RequestBody @Valid FlashDataToEsReq flashDataToEsReq);


  /**
   * 刷缺失的五级分类
   * @param flashDataToEsReq
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/flashFiveClassMiss")
  ResponseBase<Boolean> flashFiveClassMiss(@RequestBody @Valid FlashDataToEsReq flashDataToEsReq);

  @PostMapping(ORDER_ENDPOINT + "/es/order/page/normal")
  ResponseBase<PageDTO<EsOmsOrderData>> orderPageNormal(@RequestBody @Valid OrderPageReqDto reqDto);

  @PostMapping(ORDER_ENDPOINT + "/es/order/erp/audit/count")
  ResponseBase<Integer> orderErpAuditCount(@RequestBody @Valid OrderPageReqDto reqDto);
  @PostMapping(ORDER_ENDPOINT + "/es/order/page/exception")
  ResponseBase<PageDTO<EsOmsOrderData>> orderPageException(@RequestBody @Valid OrderPageOtherReqDto reqDto);

  @PostMapping(ORDER_ENDPOINT + "/es/order/list")
  ResponseBase<PageDTO<EsOmsOrderData>> orderList(@RequestBody @Valid OrderListQueryDto reqDto);

  /*****会员消费记录接口Start**********/

  /**
   * 创建会员消费记录正单索引
   *
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es/order/member-transaction/createIndex")
  ResponseBase<Boolean> createMemberOrderIndex();

  /**
   * 创建会员消费记录退单索引
   *
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es/refund-order/member-transaction/createIndex")
  ResponseBase<Boolean> createMemberRefundOrderIndex();


  /**
   * 会员消费记录
   * @param reqDto
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es/order/member-transaction")
  ResponseBase<PageDTO<MemberOrderResDto>> memberOrderTransaction(@RequestBody @Valid MemberOrderListReqDto reqDto);

  /**
   * 订单交易记录
   * @apiNote 不含明细
   * @param reqDto
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es/order/member-transaction/simple")
  ResponseBase<PageDTO<MemberOrderSimpleResDto>> orderTransactionSimple(@RequestBody @Valid MemberOrderSimpleReqDto reqDto);

  /**
   * 订单交易记录
   * @apiNote 不含明细
   * @param reqDto
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es/order/member-transaction/detail")
  ResponseBase<MemberOrderResDto> orderTransactionDetail(@RequestBody @Valid MemberOrderDetailReqDto reqDto);


  /**
   * 会员退单记录
   * @param reqDto
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es/refund-order/member-transaction")
  ResponseBase<PageDTO<MemberRefundOrderResDto>> memberRefundOrderTransaction(@RequestBody @Valid MemberRefundOrderListReqDto reqDto);

  /**
   * 退单交易记录
   * @apiNote 不含明细
   * @param reqDto
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es/refund-order/member-transaction/simple")
  ResponseBase<PageDTO<MemberRefundOrderSimpleResDto>> refundOrderTransactionSimple(@RequestBody @Valid MemberRefundOrderSimpleReqDto reqDto);

  /**
   * 退单交易记录
   * @apiNote 不含明细
   * @param reqDto
   * @return
   */
  @PostMapping(ORDER_ENDPOINT + "/es/refund-order/member-transaction/detail")
  ResponseBase<MemberRefundOrderResDto> refundOrderTransactionDetail(@RequestBody @Valid MemberRefundOrderDetailReqDto reqDto);


  /*****会员消费记录接口Start**********/


}
