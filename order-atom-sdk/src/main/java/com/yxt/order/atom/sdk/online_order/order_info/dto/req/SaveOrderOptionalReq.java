package com.yxt.order.atom.sdk.online_order.order_info.dto.req;

import com.yxt.order.atom.sdk.common.data.*;

import java.util.List;

import com.yxt.order.common.base_order_dto.OrderPickInfo;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:23
 * @email: <EMAIL>
 */
@Data
public class SaveOrderOptionalReq {

  // 保存前删除
  private DeleteBeforeSaveDTO deleteBeforeSaveDTO;

  /**
   * 原始的平台订单明细
   */
  List<OriThirdOrderDetailDTO> oriThirdOrderDetailDtoList;
  private OrderInfoDTO orderInfoDto;
  private OrderPayInfoDTO orderPayInfoDto;
  private List<OrderMultiPayInfoDTO> orderMultiPayInfoDtoList;
  private List<OrderDetailDTO> orderDetailDtoList;
  private OrderDeliveryAddressDTO orderDeliveryAddressDto;
  private OrderDeliveryRecordDTO orderDeliveryRecordDto;
  private OrderDeliveryLogDTO orderDeliveryLogDto;
  private List<OrderPrescriptionDTO> orderPrescriptionDtoList;
  private List<OrderGiftInfoDTO> orderGiftInfoDtoList;
  private List<OrderCouponInfoDTO> orderCouponInfoDtoList;
  private ErpBillInfoDTO erpBillInfoDto;
  private List<OrderDetailCommodityCostPriceDTO> orderDetailCommodityCostPriceDtoList;
  private List<OrderCommodityDetailCostPriceDTO> orderCommodityDetailCostPriceDtoList;
  private List<CommodityStockDTO> commodityStockDtoList;
  private List<CommodityExceptionOrderDTO> commodityExceptionOrderDtoList;
  private OrderBusinessConsumerMessageDTO orderBusinessConsumerMessageDto;
  private List<OrderAssembleCommodityRelationDTO> orderAssembleCommodityRelationDTOList;
  private OrderInfoDTO saveDeliveryInfo;
  private List<OrderPickInfoDTO> orderPickInfoDTOList;
}
