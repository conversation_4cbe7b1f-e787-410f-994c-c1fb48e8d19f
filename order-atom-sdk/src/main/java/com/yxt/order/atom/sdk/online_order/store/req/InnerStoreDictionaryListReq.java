package com.yxt.order.atom.sdk.online_order.store.req;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 13:51
 * @email: <EMAIL>
 */
@Data
public class InnerStoreDictionaryListReq {

  /**
   * inner_store_dictionary  `pos_mode` tinyint(1) DEFAULT NULL COMMENT 'pos机模式（1：海典H1  , 2 :海典H2
   * ，3：老模式科传）'
   */
  @NotNull
  private Integer posMode;


}
