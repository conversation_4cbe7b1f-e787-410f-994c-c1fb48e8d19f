package com.yxt.order.atom.sdk.online_order.refund_info.dto.req;

import com.yxt.order.atom.sdk.common.data.ErpRefundInfoDTO;
import com.yxt.order.atom.sdk.common.data.RefundDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import com.yxt.order.common.base_order_dto.ErpRefundInfo;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsertRefundAuditOptionalReq {

  private RefundOrderDTO freightRefundOrder;

  private List<RefundDetailDTO> freightRefundDetailList;

  private ErpRefundInfoDTO freightErpRefundInfo;

  private ErpRefundInfoDTO erpRefundInfo;

}
