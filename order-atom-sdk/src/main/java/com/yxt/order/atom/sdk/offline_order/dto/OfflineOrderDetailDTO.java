package com.yxt.order.atom.sdk.offline_order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class OfflineOrderDetailDTO {

  private String orderNo;

  private String orderDetailNo;

  private String rowNo;

  private String platformSkuId;

  private String erpCode;

  private String erpName;

  private BigDecimal commodityCount;

  private String status;

  private String giftType;

  private BigDecimal originalPrice;

  private BigDecimal price;

  private BigDecimal totalAmount;

  private BigDecimal discountShare;

  private BigDecimal discountAmount;

  private BigDecimal billPrice;

  private BigDecimal billAmount;

  private BigDecimal commodityCostPrice;

  private String createdBy;

  private String updatedBy;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdTime;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updatedTime;

  private Long version;

  // 是否参加促销的标识, true,false
  private String isOnPromotion;

  private String detachable;

  private String salerId;

  private String salerName;

  private List<OfflineOrderDetailPickDTO> offlineOrderDetailPickDTOList;
  private List<OfflineOrderDetailTraceDTO> offlineOrderDetailTraceDTOList;


  /**
   * 商品五级分类编码
   */
  private String fiveClass;

  /**
   * 商品五级分类Name
   */
  private String fiveClassName;

  /**
   * 生产商
   */
  private String manufacture;
  /**
   * 商品规格
   */
  private String commoditySpec;

  private String mainPic;

  // 过账含税成本价
  private BigDecimal postedCostWithTaxPrice;

  // 过账成本价
  private BigDecimal postedCostPrice;

  // 过账税率
  private BigDecimal postedCostTax;

}