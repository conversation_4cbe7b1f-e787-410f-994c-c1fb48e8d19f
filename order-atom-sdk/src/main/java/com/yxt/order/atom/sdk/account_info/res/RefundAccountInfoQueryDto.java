package com.yxt.order.atom.sdk.account_info.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import lombok.Data;


@Data
public class RefundAccountInfoQueryDto {
  /**
   *
   */

  private Long id;

  /**
   * 三方平台订单号
   */
  @ApiModelProperty(value = "三方平台订单号")
  private String thirdOrderNo;

  /**
   * 系统订单号
   */
  @JsonSerialize(using = ToStringSerializer.class)
  @ApiModelProperty(value = "系统订单号")
  private Long orderNo;

  /**
   * 系统退款单号
   */
  @JsonSerialize(using = ToStringSerializer.class)
  @ApiModelProperty(value = "系统退款单号")
  private Long refundNo;


  /**
   * 平台退款单号
   */
  @ApiModelProperty(value = "平台退款单号")
  private String thirdRefundNo;

  /**
   * 三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康
   */
  @ApiModelProperty(value = "三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康")
  private String thirdPlatCode;


  /**
   * O2O / B2C
   */
  @ApiModelProperty(value = "O2O / B2C")
  private String serviceMode;

  /**
   * HD_H1-海典H1  HD_H2-海典H2  KC-科传
   */
  @ApiModelProperty(value = "HD_H1-海典H1  HD_H2-海典H2  KC-科传")
  private String posMode;

  /**
   * OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS
   */
  @ApiModelProperty(value = "OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS")
  private String pickType;

  /**
   * 子公司编码
   * */
  private String subCompanyCode;

  /**
   * 所属机构编码
   */
  @ApiModelProperty(value = "所属机构编码")
  private String organizationCode;



  /**
   * 组织机构父路径id链路 1000-1100-1110-
   */
  @ApiModelProperty(value = "组织机构父路径id链路 1000-1100-1110-")
  private String orgParentPath;

  /**
   * 下账机构编码 传入到pos下账的机构编码
   */
  @ApiModelProperty(value = "下账机构编码 传入到pos下账的机构编码")
  private String accOrganizationCode;


  /**
   * 下账机构父路径链路
   */
  @ApiModelProperty(value = "下账机构父路径链路")
  private String accOrgParentPath;

  /**
   * 下账机构店铺id
   * */
  @ApiModelProperty(value = "下账机构店铺id")
  private Long  accOnlineStoreId;

  /**
   * 退货类型 ：仅退款-ONLY_REFUND 退货退款- ALL_REFUND
   */
  @ApiModelProperty(value = "退货类型 ：仅退款-ONLY_REFUND 退货退款- ALL_REFUND")
  private String refundType;

  /**
   * 退款类型 PART-部分退款 ALL-全额退款
   */
  @ApiModelProperty(value = "退款类型 PART-部分退款 ALL-全额退款")
  private String type;

  /**
   * 退款总金额 = 退款商品明细的退款金额汇总+商家配送费退款金额+平台配送费退款金额+商家包装费退款金额+平台包装费退款金额+佣金退款金额+商家优惠退款金额+平台优惠退款金额+商品明细优惠退款金额
   */
  @ApiModelProperty(value = "退款总金额 = 退款商品明细的退款金额汇总+商家配送费退款金额+平台配送费退款金额+商家包装费退款金额+平台包装费退款金额+佣金退款金额+商家优惠退款金额+平台优惠退款金额+商品明细优惠退款金额")
  private BigDecimal refundAmount;

  /**
   * 下账退款商品总金额
   */
  @ApiModelProperty(value = "下账退款商品总金额")
  private BigDecimal refundGoodsTotal;

  /**
   * 商家配送费，金额大于等于0
   */
  @ApiModelProperty(value = "商家配送费，金额大于等于0")
  private BigDecimal refundPostFee;

  @ApiModelProperty(value = "下账总金额")
  private BigDecimal refundMerchantTotal;



  /**
   * 商品明细优惠
   */
  @ApiModelProperty(value = "商品明细优惠")
  private BigDecimal discountAmount;

  /**
   * 包装费
   */
  @ApiModelProperty(value = "包装费")
  private BigDecimal packageFee;

  /**
   * 退款单接收时间
   * */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "退款单接收时间")
  private LocalDateTime refundAcceptTime;

  /**
   * 退款理由
   * */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "退款理由")
  private String refundReason;


  /**
   * 下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
   */
  @ApiModelProperty(value = "下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败")
  private String state;

  /**
   * erp零售流水号 下账成功返回
   */
  @ApiModelProperty(value = "erp零售流水号 下账成功返回")
  private String saleNo;

  /**
   * 下账时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "下账时间")
  private LocalDateTime accountTime;

  /**
   * 下账失败原因 下账失败返回
   */
  @ApiModelProperty(value = "下账失败原因 下账失败返回")
  private String accountErrMsg;

  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  private LocalDateTime createTime;

  /**
   * 更新时间
   */
  @ApiModelProperty(value = "更新时间")
  private LocalDateTime updateTime;
  @ApiModelProperty(value = "商品信息")
  private List<AccountItemDto> items;
}
