package com.yxt.order.atom.sdk.offline_order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class OfflineOrderDetailPickDTO {

  private String orderDetailPickNo;

  private String orderNo;

  private String orderDetailNo;


  private String erpCode;

  private String makeNo;

  private BigDecimal count;

  private String createdBy;

  private String updatedBy;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdTime;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updatedTime;

  private Long version;

}