package com.yxt.order.atom.sdk.online_order.delivery;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.online_order.delivery.dto.req.UpdateOrderDeliveryRecordReqDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/8/1
 */
public interface DeliveryAtomCmdApi {


  @PostMapping(ORDER_ENDPOINT + "/delivery/updateOrderDeliveryRecordByOrderNo")
  ResponseBase<Boolean> updateOrderDeliveryRecordByOrderNo(
      @RequestBody UpdateOrderDeliveryRecordReqDto updateDto);

}
