package com.yxt.order.atom.sdk.online_order.refund_info.dto.res;

import com.yxt.order.atom.sdk.common.data.ErpRefundInfoDTO;
import com.yxt.order.atom.sdk.common.data.RefundDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundInfoQryResDto {

  /**
   * 退款单主信息
   */
  private RefundOrderDTO refundMainInfo;

  /**
   * 退款单明细
   */
  private List<RefundDetailDTO> refundDetailList;

  /**
   * 退款单erp信息
   */
  private ErpRefundInfoDTO erpRefundInfoDTO;

  /**
   * 同订单下其他的退款单
   */
  private List<RefundOrderDTO> otherRefundOrderList;

  /**
   * 同订单下其他的退款单明细
   */
  private List<RefundDetailDTO> otherRefundDetailList;

}
