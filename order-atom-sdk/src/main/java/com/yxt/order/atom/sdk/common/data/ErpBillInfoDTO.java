package com.yxt.order.atom.sdk.common.data;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月11日 15:38
 * @email: <EMAIL>
 */
@Data
public class ErpBillInfoDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;

  @ApiModelProperty(value = "系统单号")
  private Long orderNo;

  /**
   * 下账配置ID
   */
  @ApiModelProperty(value = "下账配置ID")
  private Long clientConfId;

  /**
   * 下账总金额
   */
  @ApiModelProperty(value = "下账总金额")
  private BigDecimal billTotalAmount;

  /**
   * 下账商品金额
   */
  @ApiModelProperty(value = "下账商品金额")
  private BigDecimal billCommodityAmount;

  /**
   * 平台优惠金额
   */
  @ApiModelProperty(value = "平台优惠金额")
  private BigDecimal platformDiscount;

  /**
   * 商家优惠金额
   */
  @ApiModelProperty(value = "商家优惠金额")
  private BigDecimal merchantDiscount;

  /**
   * 商家配送费
   */
  @ApiModelProperty(value = "商家配送费")
  private BigDecimal merchantDeliveryFee;

  /**
   * 平台配送费
   */
  @ApiModelProperty(value = "平台配送费")
  private BigDecimal platformDeliveryFee;

  /**
   * 商家包装费
   */
  @ApiModelProperty(value = "商家包装费")
  private BigDecimal merchantPackFee;

  /**
   * 平台包装费
   */
  @ApiModelProperty(value = "平台包装费")
  private BigDecimal platformPackFee;

  /**
   * 商品明细优惠金额
   */
  @ApiModelProperty(value = "商品明细优惠金额")
  private BigDecimal detailDiscountAmount;

  @ApiModelProperty(value = "平台收取佣金")
  private BigDecimal platBrokerageAmount;

  @ApiModelProperty(value = "订单总额")
  private BigDecimal orderTotalAmount;

  @ApiModelProperty(value = "商家实收金额")
  private BigDecimal merchantActualAmount;

  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  /**
   * 修改时间
   */
  @ApiModelProperty(value = "修改时间")
  private Date modifyTime;

  @ApiModelProperty(value = "商品的加权成本金额")
  private BigDecimal commodityAveragePrice;

  @ApiModelProperty(value = "参与计算综合毛利的下账金额")
  private BigDecimal billAmountComprehensive;

}
