package com.yxt.order.atom.sdk.org_order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@ApiModel("退单金额查询")
@Data
public class EsOrgRefundAmountStaticReqDTO {

  @ApiModelProperty(value = "筛选条件")
  @NotEmpty(message = "筛选条件不能为空")
  private List<EsOrgRefundSearchConditionDTO> searchConditionList;
}
