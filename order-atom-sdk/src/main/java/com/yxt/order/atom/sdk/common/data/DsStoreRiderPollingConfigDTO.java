package com.yxt.order.atom.sdk.common.data;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 线上门店骑手循环呼叫配置
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DsStoreRiderPollingConfigDTO implements Serializable {


  private Long id;
  /**
   * 线上门店id
   */
  private Long onlineStoreId;
  /**
   * 配送平台编码
   */
  private String platformCode;
  /**
   * 配送平台名称
   */
  private String platformName;
  /**
   * 是否开启循环 0-关闭，1-开启
   */
  private Integer status;
  /**
   * 优先级
   */
  private Integer priority;
  /**
   * 等待时间，单位：分
   */
  private Integer waitingTime;

  private Date createTime;

  private Date modifyTime;


}
