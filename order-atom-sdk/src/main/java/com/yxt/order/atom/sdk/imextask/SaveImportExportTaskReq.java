package com.yxt.order.atom.sdk.imextask;

import java.util.Date;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class SaveImportExportTaskReq {

  @NotEmpty
  private String taskNo;
  private String merCode = "500001";
  private String taskType;
  private String dataMappingClazz;
  private String paramJson;
  private String paramMappingClazz;
  private String state;
  private String note;
  private String downloadUrl;
  private String createdBy;
  private String updatedBy;
  private Date createdTime;
  private Date updatedTime;
  private Long version;
  private String fileName;
}