package com.yxt.order.atom.sdk.offline_order.req;

import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderMedInsSettleDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderUserDTO;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * 保存线下退款单
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月01日 14:32
 * @email: <EMAIL>
 */
@Data
public class SaveOfflineRefundOrderReqDto {

  private OfflineRefundOrderDTO offlineRefundOrderDTO;

  // 不用存,正单已有。https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24613728 上的内容只是订单模型,把数据推送给下游
  // 20241127恢复,开发完成
  private OfflineRefundOrderOrganizationDTO offlineRefundOrderOrganizationDTO;
  private OfflineRefundOrderCashierDeskDTO offlineRefundOrderCashierDeskDTO;

  private OfflineRefundOrderUserDTO offlineRefundOrderUserDTO;

  private List<OfflineRefundOrderDetailDTO> offlineRefundOrderDetailDTOList;

  private List<OfflineRefundOrderPayDTO> offlineRefundOrderPayDTOList;

  private OfflineRefundOrderMedInsSettleDto offlineRefundOrderMedInsSettleDto;


  public void absAmount() {

    if (Objects.nonNull(offlineRefundOrderDTO)) {
      offlineRefundOrderDTO.absAmount();
    }

    if (!CollectionUtils.isEmpty(offlineRefundOrderDetailDTOList)) {
      offlineRefundOrderDetailDTOList.forEach(OfflineRefundOrderDetailDTO::absAmount);
    }

    if (!CollectionUtils.isEmpty(offlineRefundOrderPayDTOList)) {
      offlineRefundOrderPayDTOList.forEach(OfflineRefundOrderPayDTO::absAmount);
    }
    if (Objects.nonNull(offlineRefundOrderMedInsSettleDto)) {
      offlineRefundOrderMedInsSettleDto.absAmount();
    }


  }
}
