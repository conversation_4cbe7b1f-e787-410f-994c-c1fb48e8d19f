package com.yxt.order.atom.sdk.online_order.account_check.req;

import com.yxt.order.atom.sdk.common.data.AccountCheckChannelDTO;
import com.yxt.order.types.order.MerCode;
import com.yxt.order.types.order.ThirdPlatformCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class AccountCheckSaveReqDto {

  @ApiModelProperty(value = "对账单数据")
  private List<AccountCheckChannelDTO> dataList;

}
