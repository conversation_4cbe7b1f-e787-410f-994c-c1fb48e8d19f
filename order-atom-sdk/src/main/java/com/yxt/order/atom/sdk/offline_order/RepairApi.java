package com.yxt.order.atom.sdk.offline_order;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.OFFLINE_ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.offline_order.dto.repair.RepairOfflineOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.dto.repair.RepairOfflineRefundOrderReqDto;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moat<PERSON>)
 * @date 2024年04月01日 14:31
 * @email: <EMAIL>
 */
public interface RepairApi {

  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/repair/haidian/onlineOrderAsOffline")
  ResponseBase<Boolean> haiDianOnlineOrderAsOffline(
      @RequestBody @Valid RepairOfflineOrderReqDto req);

  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/repair/haidian/onlineRefundOrderAsOffline")
  ResponseBase<Boolean> haiDianOnlineRefundOrderAsOffline(
      @RequestBody @Valid RepairOfflineRefundOrderReqDto req);


  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/repair/haidian/chailin/order")
  ResponseBase<Boolean> haiDianChaiLingOrder(
      @RequestBody @Valid RepairOfflineOrderReqDto req);


  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/repair/haidian/chailin/refund-order")
  ResponseBase<Boolean> haiDianChaiLingRefundOrder(
      @RequestBody @Valid RepairOfflineRefundOrderReqDto req);



}
