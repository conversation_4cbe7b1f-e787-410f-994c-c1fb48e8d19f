package com.yxt.order.atom.sdk.order_world;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderBaseReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderCountStatisticReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderCountWithConditionReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderPageQueryReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldRefundOrderBaseReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldRefundOrderPageQueryReq;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldCountStatisticRes;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldOrderInfoRes;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldRefundOrderInfoRes;
import com.yxt.order.common.es.EsPageDTO;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface EsOrderWorldAtomRefundOrderQueryApi {

  String ORDER_ENDPOINT = "/1.0";

  @PostMapping(ORDER_ENDPOINT + "/es/order-world/refund/page/query")
  ResponseBase<EsPageDTO<EsOrderWorldRefundOrderInfoRes>> refundOrderPageQuery(@RequestBody @Valid EsOrderWorldRefundOrderPageQueryReq request);

  @PostMapping(ORDER_ENDPOINT + "/es/order-world/refund/detail")
  ResponseBase<EsOrderWorldRefundOrderInfoRes> refundOrderDetailQuery(@RequestBody @Valid EsOrderWorldRefundOrderBaseReq request);

}
