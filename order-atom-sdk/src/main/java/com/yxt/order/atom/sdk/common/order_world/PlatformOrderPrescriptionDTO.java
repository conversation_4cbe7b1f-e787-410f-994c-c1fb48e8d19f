package com.yxt.order.atom.sdk.common.order_world;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 平台订单处方信息
 */
@Data
public class PlatformOrderPrescriptionDTO {

  /**
   * 主键
   */
  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 平台处方单号
   */
  private String thirdPrescriptionNo;

  /**
   * 心云处方单号(来源药事云)
   */
  private String prescriptionNo;

  /**
   * 订单处方单类型 WESTERN-西药 EAST中药
   */
  private String prescriptionType;

  /**
   * 用药人
   */
  private String drugUserName;

  /**
   * 性别
   */
  private String drugUserSex;

  /**
   * 生日
   */
  private String drugUserBirthday;

  /**
   * 年龄
   */
  private String drugUserAge;

  /**
   * 手机号码
   */
  private String drugUserPhone;

  /**
   * 身份证号
   */
  private String drugUserIdCardNo;

  /**
   * 处方原始图片地址
   */
  private String prescriptionThirdUrl;

  /**
   * 处方图片地址
   */
  private String prescriptionUrl;

  /**
   * 处方开方时间
   */
  private LocalDateTime prescriptionWritingTime;

  /**
   * 处方审方时间
   */
  private LocalDateTime prescriptionCheckTime;

  /**
   * 处方审方人
   */
  private String prescriptionChecker;

  /**
   * 处方审方人名字
   */
  private String prescriptionCheckerName;

  /**
   * 处方审核结果
   */
  private String prescriptionCheckStatus;

  /**
   * 处方备注
   */
  private String prescriptionRemark;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
