package com.yxt.order.atom.sdk.common.order_world;

import java.time.LocalDateTime;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PlatformOrderPayDTO {

  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 支付唯一号
   */
  private String orderPayNo;

  /**
   * 支付类型
   */
  private String payType;

  /**
   * 支付名称
   */
  private String payName;

  /**
   * 支付金额
   */
  private BigDecimal payAmount;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

}
