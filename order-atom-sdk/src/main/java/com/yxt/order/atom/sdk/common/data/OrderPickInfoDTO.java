package com.yxt.order.atom.sdk.common.data;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/8/8
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class OrderPickInfoDTO {

  /**
   * 记录ID
   */
//  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 订单详情id
   */
  @ApiModelProperty(value = "订单详情id")
  private Long orderDetailId;

  @ApiModelProperty(value = "erp编码")
  private String erpCode;
  /**
   * 商品批号
   */
  @ApiModelProperty(value = "商品批号")
  private String commodityBatchNo;

  /**
   * 批号对应的数量
   */
  @ApiModelProperty(value = "批号对应的数量")
  private Integer count;

  /**
   * 商品进价
   */
  @ApiModelProperty(value = "商品进价")
  private BigDecimal purchasePrice;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;

  /**
   * 拣货信息是否有效 1-有效 0-无效
   */
  private Integer isValid;
}
