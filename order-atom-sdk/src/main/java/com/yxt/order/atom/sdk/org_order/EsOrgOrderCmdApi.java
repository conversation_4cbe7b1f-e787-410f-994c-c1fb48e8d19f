package com.yxt.order.atom.sdk.org_order;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_info.req.FlashDataToEsReq;
import com.yxt.order.atom.sdk.org_order.req.OrgOrderFlashToEsJobProcessReqDTO;
import com.yxt.order.atom.sdk.org_order.req.OrgOrderFlashToEsWithJobReqDTO;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface EsOrgOrderCmdApi {

  String ORDER_ENDPOINT = "/1.0";

  /**
   * 从job获取待处理数据进行处理
   */
  @PostMapping(ORDER_ENDPOINT + "/es/org-order/flash/with-job/process")
  ResponseBase<Void> orgOrderFlashDataToEsWithJobProcess(@RequestBody @Valid OrgOrderFlashToEsJobProcessReqDTO request);

  /**
   * 加入job表，等job触发执行
   */
  @PostMapping(ORDER_ENDPOINT + "/es/org-order/flash/with-job")
  ResponseBase<Void> orgOrderFlashDataToEsWithJob(@RequestBody @Valid OrgOrderFlashToEsWithJobReqDTO request);

  /**
   * 创建门店正单索引
   */
  @PostMapping(ORDER_ENDPOINT + "/es/org-order/createIndex")
  ResponseBase<Boolean> createOrgOrderIndex();

  /**
   * 创建门店退单索引
   */
  @PostMapping(ORDER_ENDPOINT + "/es/org-refund/createIndex")
  ResponseBase<Boolean> createOrgRefundIndex();

  /**
   * 通过订单号刷数
   */
  @PostMapping(ORDER_ENDPOINT + "/es/org-order/flashDataToEs")
  ResponseBase<Void> orgOrderFlashDataToEs(@RequestBody @Valid FlashDataToEsReq flashDataToEsReq);
}
