package com.yxt.order.atom.sdk.common.order_world;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 心云订单套装明细
 */
@Data
public class OrderSetDetailDTO {

  /**
   * 主键
   */
  private Long id;

  /**
   * 内部订单号,自己生成
   */
  private String orderNo;

  /**
   * 订单套装明细唯一编码
   */
  private String orderSetDetailNo;

  /**
   * 平台订单明细编号
   */
  private String thirdOrderDetailNo;

  /**
   * 商品行号
   */
  private String rowNo;

  /**
   * 商品三方平台编码
   */
  private String platformSkuId;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 商品原单价
   */
  private BigDecimal originalPrice;

  /**
   * 商品售价
   */
  private BigDecimal price;

  /**
   * 商品成本价
   */
  private BigDecimal commodityCostPrice;

  /**
   * 商品总额=price*数量
   */
  private BigDecimal totalAmount;

  /**
   * 实付金额 (已乘数量)
   */
  private BigDecimal actualPayAmount;

  /**
   * 订单级优惠分摊
   */
  private BigDecimal discountShare;

  /**
   * 商品级折扣金额
   */
  private BigDecimal discountAmount;

  /**
   * 商家订单级优惠分摊(已乘数量)
   */
  private BigDecimal merchantOrderDiscountShare;

  /**
   * 平台订单级优惠分摊 (已乘数量)
   */
  private BigDecimal platformOrderDiscountShare;

  /**
   * 商家商品级折扣金额 (已乘数量)
   */
  private BigDecimal merchantGoodsDiscountAmount;

  /**
   * 平台商品级折扣金额 (已乘数量)
   */
  private BigDecimal platformGoodsDiscountAmount;

  /**
   * 商家订单级优惠分摊(单个数量)
   */
  private BigDecimal merchantOrderDiscountSharePrice;

  /**
   * 平台订单级优惠分摊 (单个数量)
   */
  private BigDecimal platformOrderDiscountSharePrice;

  /**
   * 商家商品级折扣金额 (单个数量)
   */
  private BigDecimal merchantGoodsDiscountAmountPrice;

  /**
   * 平台商品级折扣金额 (单个数量)
   */
  private BigDecimal platformGoodsDiscountAmountPrice;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
