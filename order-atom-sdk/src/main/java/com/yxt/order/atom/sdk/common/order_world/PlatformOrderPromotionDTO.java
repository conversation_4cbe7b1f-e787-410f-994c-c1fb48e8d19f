package com.yxt.order.atom.sdk.common.order_world;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 平台订单促销活动信息
 */
@Data
public class PlatformOrderPromotionDTO {

  /**
   * 主键
   */
  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 促销活动编码
   */
  private String promotionNo;

  /**
   * 子促销编码
   */
  private String subPromotionNo;

  /**
   * 促销类型
   */
  private String promotionType;

  /**
   * 促销金额
   */
  private BigDecimal promotionAmount;

  /**
   * 拓展字段
   */
  private String extendJson;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
