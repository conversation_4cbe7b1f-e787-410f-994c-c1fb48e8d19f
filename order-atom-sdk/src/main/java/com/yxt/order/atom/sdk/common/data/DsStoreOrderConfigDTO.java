package com.yxt.order.atom.sdk.common.data;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线上门店订单配置
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DsStoreOrderConfigDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 线上门店id
   */
  private Long onlineStoreId;

  /**
   * 是否自动接单，0否，1是
   */
  private Integer autoAcceptFlag;

  /**
   * 接单后自动打印拣货单，0否，1是
   */
  private Integer autoPrintPick;

  /**
   * 拣货后自动打印小票，0否，1是
   */
  private Integer autoPrintReceipt;

  /**
   * 自动呼叫骑手，0接单后呼叫骑手，1拣货后呼叫骑手
   */
  private Integer autoCallRider;

  /**
   * 订单是否转发到HEMS, 0 不转HEMS, 1 转HEMS
   */
  private Integer autoToHems;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;

  /**
   * 打印模板id
   */
  private Long printTemplateId;

  /**
   * 第二个打印模板id，目前用于拣货后自动打印
   */
  private Long secondPrintTemplateId;
  /**
   * 是否开启b2c
   */
  private Integer openB2c;
  /**
   * b2c关联id
   */
  private String outB2cClientId;
  /**
   * 骑手循环呼叫 0-否，1-是
   */
  private Integer riderPolling;
  /**
   * 骑手比价 0-否，1-是
   */
  private Integer riderCompare;
  /**
   * 骑手比价延时配置 单位：分钟，默认5分钟
   */
  private Integer riderCompareDelay;

}
