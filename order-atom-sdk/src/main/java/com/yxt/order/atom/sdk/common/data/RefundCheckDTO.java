package com.yxt.order.atom.sdk.common.data;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RefundCheckDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;

  /**
   * 退款单号
   */
  @ApiModelProperty(value = "退款单号")
  private Long refundNo;

  /**
   * 审核人id
   */
  @ApiModelProperty(value = "审核人id")
  private String checkerId;

  /**
   * 审核人名称
   */
  @ApiModelProperty(value = "审核人名称")
  private String checkerName;

  /**
   * 审核类型
   */
  @ApiModelProperty(value = "审核类型：待退款 待退货")
  private String checkType;


  /**
   * 审核结果
   */
  @ApiModelProperty(value = "审核结果：待退款（同意仅退款，同意退款退货，拒绝），待退货（商品不入库，商品入库)")
  private String checkResult;

  /**
   * 审核时间
   */
  @ApiModelProperty(value = "审核时间")
  private Date checkTime;

  /**
   * 审核备注
   */
  @ApiModelProperty(value = "审核备注")
  private String checkMark;


}
