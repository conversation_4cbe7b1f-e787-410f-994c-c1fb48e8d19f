package com.yxt.order.atom.sdk.org_order;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.org_order.req.EsOrgRefundAmountStaticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgRefundPageQueryReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsRefundBaseReqDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgRefundAmountStaticResDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgRefundResDTO;
import com.yxt.order.common.es.EsPageDTO;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface EsOrgRefundAtomQueryApi {

  String ORDER_ENDPOINT = "/1.0";

  @PostMapping(ORDER_ENDPOINT + "/es/org-refund/amount/static")
  ResponseBase<EsOrgRefundAmountStaticResDTO> orgRefundAmountStatic(@RequestBody @Valid EsOrgRefundAmountStaticReqDTO reqDto);

  @PostMapping(ORDER_ENDPOINT + "/es/org-refund/page/query")
  ResponseBase<EsPageDTO<EsOrgRefundResDTO>> orgRefundPageQuery(@RequestBody @Valid EsOrgRefundPageQueryReqDTO reqDto);

  @PostMapping(ORDER_ENDPOINT + "/es/org-refund/detail")
  ResponseBase<EsOrgRefundResDTO> orgRefundDetailQuery(@RequestBody @Valid EsRefundBaseReqDTO reqDto);
}
