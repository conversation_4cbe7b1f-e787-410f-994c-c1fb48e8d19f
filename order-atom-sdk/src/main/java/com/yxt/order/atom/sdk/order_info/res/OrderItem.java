/*
 * Copyright (c) 2011-2020, baomidou (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * https://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package com.baomidou.mybatisplus.core.metadata;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 排序元素载体
 *
 * <AUTHOR>
 * Create at 2019/5/27
 */
@Data
@Accessors(chain = true)
@ToString
public class OrderItem {
  /**
   * 需要进行排序的字段
   */
  private String column;

  /**
   * 是否正序排列，默认 true
   */
  private boolean asc = true;

  public static OrderItem asc(String column) {
    return build(column, true);
  }

  public static OrderItem desc(String column) {
    return build(column, false);
  }

  private static OrderItem build(String column, boolean asc) {
    OrderItem item = new OrderItem();
    item.setColumn(column);
    item.setAsc(asc);
    return item;
  }
}
