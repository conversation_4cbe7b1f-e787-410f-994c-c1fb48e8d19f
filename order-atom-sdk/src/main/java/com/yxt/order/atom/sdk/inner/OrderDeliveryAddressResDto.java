//package com.yxt.order.atom.sdk.inner;
//
//import io.swagger.annotations.ApiModelProperty;
//import java.io.Serializable;
//import java.util.Date;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.experimental.Accessors;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * <p>
// * 订单收货地址表
// * </p>
// *
// * <AUTHOR>
// * @since 2019-12-16
// */
//@Slf4j
//@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
//public class OrderDeliveryAddressResDto implements Serializable {
//
//    private static final long serialVersionUID=1L;
//
//    /**
//     * 记录ID
//     */
//
//    private Long id;
//
//    /**
//     * 订单号
//     */
//    private Long orderNo;
//
//
//    private Long omsOrderNo;
//    /**
//     * 收货人
//     */
//    @ApiModelProperty(value = "收货人名")
//    private String receiverName;
//
//    /**
//     * 收货人电话
//     */
//    @ApiModelProperty(value = "收货人电话")
//    private String receiverTelephone;
//
//    /**
//     * 收货人手机
//     */
//    @ApiModelProperty(value = "收货人手机号码")
//    private String receiverMobile;
//
//    /**
//     * 省份
//     */
//    @ApiModelProperty(value = "收货人省份")
//    private String province;
//
//    /**
//     * 城市
//     */
//    @ApiModelProperty(value = "收货人城市")
//    private String city;
//
//    /**
//     * 区域
//     */
//    @ApiModelProperty(value = "收货人所在城市区域")
//    private String district;
//
//    /**
//     * 城镇
//     */
//    @ApiModelProperty(value = "收货人所在城镇")
//    private String town;
//
//    /**
//     * 详细地址
//     */
//    @ApiModelProperty(value = "收货人详细地址")
//    private String address;
//
//    /**
//     * 邮编
//     */
//    @ApiModelProperty(value = "邮编")
//    private String zipCode;
//
//    /**
//     * 完整详细地址
//     */
//    @ApiModelProperty(value = "完整详细地址")
//    private String fullAddress;
//
//    @ApiModelProperty(value = "原始地址")
//    private String originalFullAddress;
//
//    @ApiModelProperty(value = "收货电话密文")
//    private String receiverMobileDesen;
//
//    @ApiModelProperty(value = "脱敏手机号")
//    private String privacyPhone;
//
//    @ApiModelProperty(value = "收货人隐私姓名")
//    private String receiverNamePrivacy;
//
//    @ApiModelProperty(value = "收货人隐私手机号")
//    private String receiverPhonePrivacy;
//
//    @ApiModelProperty(value = "收货人隐私地址")
//    private String receiverAddressPrivacy;
//
//    @ApiModelProperty(value = "收货人隐私地址详情- 省市区字段")
//    private String  privacyDetail;
//
//    /**
//     * 淘宝 收货人密文
//     */
//    private String oaid;
//
//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 修改时间
//     */
//
//    private Date modifyTime;
//
////    /**
////     * 检查解密后信息是否存在
////     * @return true:存在 false:不存在
////     * */
////    public boolean checkReceiveDecryptExist(){
////        return !(StringUtils.isEmpty(receiverName) || StringUtils.isEmpty(receiverTelephone) || StringUtils.isEmpty(fullAddress)
////        || StringUtils.isEmpty(receiverMobile) || StringUtils.isEmpty(province) || StringUtils.isEmpty(city) || StringUtils.isEmpty(address));
////    }
////
////    public void privacyReceiverInfo(){
////        try {
////            Optional.ofNullable(receiverName).ifPresent((name)->{
////                receiverName=name.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
////            });
////            Optional.ofNullable(receiverMobile).ifPresent((mobile)->{
////                receiverMobile=mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
////            });
////            Optional.ofNullable(fullAddress).ifPresent((address)->{
////                fullAddress = address.substring(0, 3) + "********";
////            });
////            Optional.ofNullable(address).ifPresent((s)->{
////                address = s.substring(0, 3) + "********";
////            });
////            Optional.ofNullable(originalFullAddress).ifPresent((address)->{
////                originalFullAddress = address.substring(0, 3) + "********";
////            });
////        }catch (Exception e) {
////            log.info("隐私收货人信息异常 oms_order_no {}", omsOrderNo);
////        }
////    }
////
////    /**
////     * 解密后的信息赋值
////     * */
////    public void setDecryptInfo(List<ReceiverDecryptInfoDto> receiverDecryptInfoDtos){
////        Map<String, String> decryptMap = receiverDecryptInfoDtos.stream()
////            .collect(Collectors.toMap(ReceiverDecryptInfoDto::getCipherText, ReceiverDecryptInfoDto::getPlainText, (a,b)->a));
////        receiverName = decryptMap.get(this.receiverNamePrivacy);
////        receiverTelephone = decryptMap.get(this.receiverPhonePrivacy);
////        receiverMobile = decryptMap.get(this.receiverPhonePrivacy);
////        address = decryptMap.get(this.receiverAddressPrivacy);
////        fullAddress = decryptMap.get(this.receiverAddressPrivacy);
////        originalFullAddress = decryptMap.get(this.receiverAddressPrivacy);
////        receiverMobileDesen = decryptMap.get(this.receiverPhonePrivacy);
////
////        ReceiverPrivacyDetailInfo receiverPrivacyDetailInfo = ReceiverPrivacyDetailInfo.jsonToObject(
////            this.privacyDetail);
////        if(receiverPrivacyDetailInfo != null){
////            province = decryptMap.get(receiverPrivacyDetailInfo.getReceiverStatePrivacy());
////            city = decryptMap.get(receiverPrivacyDetailInfo.getReceiverCityPrivacy());
////            district = decryptMap.get(receiverPrivacyDetailInfo.getReceiverDistrictPrivacy());
////            town = decryptMap.get(receiverPrivacyDetailInfo.getReceiverTownPrivacy());
////            address = decryptMap.get(receiverPrivacyDetailInfo.getReceiverDetailAddressPrivacy());
////          }
////
////    }
////
////    public List<String> obtainPrivacyValueList(){
////        List<String> list = new ArrayList<>();
////        list.add(receiverNamePrivacy);
////        list.add(receiverPhonePrivacy);
////        list.add(receiverAddressPrivacy);
////        ReceiverPrivacyDetailInfo receiverPrivacyDetailInfo = ReceiverPrivacyDetailInfo.jsonToObject(privacyDetail);
////        if (receiverPrivacyDetailInfo != null) {
////            list.addAll(receiverPrivacyDetailInfo.obtainValueList());
////        }
////        return list;
////    }
//}
