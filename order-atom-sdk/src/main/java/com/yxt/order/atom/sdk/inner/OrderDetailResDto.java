//package com.yxt.order.atom.sdk.inner;
//
//import io.swagger.annotations.ApiModelProperty;
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.Date;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.experimental.Accessors;
//
///**
// * <p>
// * 订单明细表
// * </p>
// *
// * <AUTHOR>
// * @since 2019-12-17
// */
//@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
//
//public class OrderDetailResDto implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//
//    private Long id;
//
//    /**
//     * 订单号
//     */
//    private Long orderNo;
//
//    @ApiModelProperty(value = "系统订单")
//    private Long omsOrderNo;
//
//    /**
//     * 商品三方平台编码
//     */
//    @ApiModelProperty(value = "商品三方平台编码")
//    private String platformSkuId;
//
//    /**
//     * 商品erp编码
//     *
//     */
//    @ApiModelProperty(value = "商品erp编码")
//    private String erpCode;
//
//    /**
//     * 商品条形编码
//     */
//    @ApiModelProperty(value = "商品条形编码")
//    private String barCode;
//
//    /**
//     * 商品名称
//     */
//    @ApiModelProperty(value = "商品名称")
//    private String commodityName;
//
//
//    @ApiModelProperty(value = "商品主图")
//    private String mainPic;
//
//    /**
//     * 商品规格
//     */
//    @ApiModelProperty(value = "商品规格")
//    private String commoditySpec;
//
//    /**
//     * 商品数量
//     */
//    @ApiModelProperty(value = "商品数量")
//    private Integer commodityCount;
//
//    /**
//     * 商品原单价
//     */
//    @ApiModelProperty(value = "商品原单价（商品单价）")
//    private BigDecimal originalPrice;
//
//    /**
//     * 商品售价
//     */
//    @ApiModelProperty(value = "商品售价")
//    private BigDecimal price;
//
//    /**
//     * 小计金额售价*数量
//     */
//    @ApiModelProperty(value = "小计金额售价*数量(商品总额)")
//    private BigDecimal totalAmount;
//
//    /**
//     * 促销优惠金额
//     */
//    @ApiModelProperty(value = "明细优惠")
//    private BigDecimal discountAmount;
//
//    /**
//     * 成交总额小计-优惠
//     */
//    @ApiModelProperty(value = "成交总额小计-优惠（实付金额）")
//    private BigDecimal actualAmount;
//
//
//    @ApiModelProperty(value = "调整金额")
//    private BigDecimal adjustAmount;
//
//    /**
//     * 优惠分摊
//     */
//    @ApiModelProperty(value = "订单级别优惠分摊")
//    private BigDecimal discountShare;
//
//    /**
//     * 下账金额
//     */
//    @ApiModelProperty(value = "下账金额")
//    private BigDecimal actualNetAmount;
//
//
//    /**
//     * 差异分摊
//     */
//    @ApiModelProperty(value = "差异分摊")
//    private BigDecimal differentShare;
//
//    /**
//     * 明细状态
//     */
//    @ApiModelProperty(value = "明细状态，0正常显示商品，1已退款，2被换货的商品")
//    private Integer status;
//
//    /**
//     * 生产商
//     */
//    @ApiModelProperty(value = "生产商")
//    private String manufacture;
//
//
//    /**
//     * 生产商
//     */
//    @ApiModelProperty(value = "替换该商品的detailId")
//    private Long swapId;
//
//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 修改时间
//     */
//    private Date modifyTime;
//
//    /**
//     * 下账单价
//     */
//    private BigDecimal billPrice;
//
//    /**
//     * 第三方详情ID
//     */
//    private String thirdDetailId;
//
//    /**
//     * 是否是赠品（0，不是赠品1，是赠品）
//     */
//    private Integer isGift;
//
//    /**
//     * 如果改商品是赠品，该值才有作用；赠品是否下账，0不下账，1下账（默认）
//     */
//    private Integer erpGift;
//
//    /**
//     * 0为非组合商品，1为组合商品明细，2为组合商品父商品
//     */
//    private Integer isJoint = 0;
//
//    /**
//     * 商品类型，1普通商品，3换货后的商品，4换货的源商品
//     */
//    private Integer goodsType;
//
//    /**
//     * 是否拆零，1-不拆零，2-拆零（默认按不拆零处理）
//     */
//    private Integer chailing;
//
//    /**
//     * 已退货数量
//     */
//    @ApiModelProperty(value = "已退款/退货数量")
//    private Integer refundCount = 0;
//
//    @ApiModelProperty(value = "微商城OB预约单 供应商来源 1-云货架，2-DC仓")
//    private Integer originType;
//    @ApiModelProperty(value = "微商城OB预约单 组织机构编码 云货架商家编码或DC仓编码")
//    private String stCode;
//    @ApiModelProperty(value = "微商城OB预约单 预计送达时间 ")
//    private Date expectDeliveryTime;
//
//    @ApiModelProperty(value = "供应商代发标识，0-非供应商代发，1-供应商代发")
//    private Integer directDeliveryType;
//
//    @ApiModelProperty(value = "换货商品关联码，换货的源商品和现商品此值都一致")
//    private String oldErpCode;
//
//
//    private String weight;
//
//    @ApiModelProperty("组合商品关联码，非组合商品此值为空")
//    private String relationCode;
//
//    @ApiModelProperty("是否减少了库存，1是，0否")
//    private Integer isReduceStock;
//
//    /**
//     * 商品成本。
//     */
//    @ApiModelProperty(value = "商品加权成本")
//    private BigDecimal averagePrice;
//
//    @ApiModelProperty(value = "拆零商品的拆零系数")
//    private Integer chaiLingNum;
//
//    /**
//     * 获取未退货的数量
//     *
//     * @return 获取未退货的数量
//     */
////    public Integer getNotRefundCount() {
////        return NumberUtil.sub(this.commodityCount, this.refundCount).intValue();
////    }
//
//    @ApiModelProperty(value = "平台优惠金额")
//    private BigDecimal platformDiscountFee;
//
//    @ApiModelProperty(value = "商家优惠金额")
//    private BigDecimal merchantDiscountFee;
//
//    @ApiModelProperty(value = "交易佣金")
//    private BigDecimal brokerageAmount;
//
//    @ApiModelProperty("药品类型 OTC甲类(0)/处方(1)/OTC乙类(2)/非药品(3)/OTC(4)")
//    private Integer drugType;
//
//    @ApiModelProperty("拆零商品原始数量")
//    private Integer chaiLingOriginalNum;
//
//    @ApiModelProperty(value = "会员优惠金额")
//    private BigDecimal vipDifferentAmt = new BigDecimal(0);
//
//    @ApiModelProperty(value = "结算价格金额-统计时使用")
//    private BigDecimal settlePrice = new BigDecimal(0);
//
//    /**
//     * 生产日期/有效期至（yyyy-MM-dd)/批次 药房网专用
//     */
//    @ApiModelProperty(value = "批次")
//    private String produceNo;
//
//    /**
//     * 扩展字段
//     */
//
//    private DetailInfoResDto extend;
//
//    @ApiModelProperty(value = "原系统订单号")
//    private Long originalOmsOrderNo;
//
//    /** 记录拆单后的原明细id --  便于处理退款单 不用入表 */
//
//    private Long originalDetailId;
//
//    @ApiModelProperty(value = "第三方子订单号")
//    private String thirdOrderNo;
//
//
//    /**
//     * 商品结算状态：0-待结算，1-已结算，9-已失效。
//     */
//    private Integer detailSettlementStatus;
//
//    /**
//     * 兼容数据json字段解析
//     * @param extend Object接收(@Data注解中的set方法入参为DetailInfo类型,sql查询返回JSONObject,进入set这个方法)
////     */
////    public void setExtend(Object extend) {
////        if (Objects.nonNull(extend)){
////            if (extend instanceof String){
////                this.extend = JSON.parseObject((String) extend, DetailInfo.class);
////            } else {
////                this.extend = JSON.parseObject(JSON.toJSONString(extend), DetailInfo.class);
////            }
////        }
////    }
////
////    /**
////     * 获取拣货数量
////     *
////     * @return 当前拣货数量
////     */
////    public Integer getPickCount() {
////        if (Objects.isNull(this.commodityCount) || Objects.isNull(this.refundCount)) {
////            return 0;
////        }
////        return this.commodityCount - this.refundCount;
////    }
//}
