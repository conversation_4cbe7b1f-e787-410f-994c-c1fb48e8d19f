package com.yxt.order.atom.sdk.order_world;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_world.req.SaveOrderWorldOrderOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderMainStatusOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderPayStatusOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderWorldOrderOptionalReq;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface OrderWorldOrderAtomCmdApi {

  @PostMapping(ORDER_ENDPOINT + "/order-world/order/save/optional")
  ResponseBase<Void> saveOptional(@RequestBody SaveOrderWorldOrderOptionalReq req);

  @PostMapping(ORDER_ENDPOINT + "/order-world/order/main-status/update/optional")
  ResponseBase<Void> updateMainStatusOptional(@RequestBody @Valid UpdateOrderMainStatusOptionalReq req);

  @PostMapping(ORDER_ENDPOINT + "/order-world/order/pay-status/update/optional")
  ResponseBase<Void> updatePayStatusOptional(@RequestBody @Valid UpdateOrderPayStatusOptionalReq req);

  @PostMapping(ORDER_ENDPOINT + "/order-world/order/update/optional")
  ResponseBase<Void> updateOptional(@RequestBody @Valid UpdateOrderWorldOrderOptionalReq req);
}
