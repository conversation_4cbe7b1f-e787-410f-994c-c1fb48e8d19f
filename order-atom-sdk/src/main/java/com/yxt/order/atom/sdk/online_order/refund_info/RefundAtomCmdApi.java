package com.yxt.order.atom.sdk.online_order.refund_info;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.data.RefundDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundAuditOptionalReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundDetailInsertReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundLogOptionalReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundWithOrderQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.res.RefundInfoQryResDto;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface RefundAtomCmdApi {

  /**
   * 退款审核结果保存
   */
  @PostMapping(ORDER_ENDPOINT + "/refund/audit/save")
  ResponseBase<Void> saveRefundAuditResult(@RequestBody RefundAuditOptionalReq request);


  /**
   * 退款日志保存
   */
  @PostMapping(ORDER_ENDPOINT + "/refund/log/save")
  ResponseBase<Void> saveRefundLog(@RequestBody RefundLogOptionalReq request);

  /**
   * 新增退单详情
   */
  @PostMapping(ORDER_ENDPOINT + "/refund/detail/creat")
  ResponseBase<List<RefundDetailDTO>> createRefundDetail(@RequestBody RefundDetailInsertReq request);
}
