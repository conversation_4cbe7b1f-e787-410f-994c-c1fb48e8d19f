package com.yxt.order.atom.sdk.order_world;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_world.req.SaveOrderWorldOrderOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.SavePlatformOrderOptionalReq;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface PlatformOrderAtomCmdApi {

  @PostMapping(ORDER_ENDPOINT + "/order-world/platform-order/save/optional")
  ResponseBase<Void> saveOptional(@RequestBody SavePlatformOrderOptionalReq req);

}
