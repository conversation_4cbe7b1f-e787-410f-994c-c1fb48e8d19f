package com.yxt.order.atom.sdk.offline_order.req.manage;

import com.yxt.lang.dto.PageBase;
import com.yxt.order.types.offline.enums.AfterSaleTypeEnum;
import com.yxt.order.types.offline.enums.RefundTypeEnum;
import com.yxt.order.types.offline.enums.StoreDirectJoinTypeEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2025/3/27 11:55
 */
@Data
public class OfflineRefundOrderListReq extends PageBase {

  @ApiModelProperty("系统退款单号")
  private String refundNo;

  @ApiModelProperty("系统订单号")
  private String orderNo;

  /**
   * @see ThirdPlatformCodeEnum
   */
  @ApiModelProperty("POS")
  private String thirdPlatformCode;

  @ApiModelProperty("平台订单号")
  private String thirdOrderNo;

  @ApiModelProperty("平台退款单号")
  private String thirdRefundNo;

  /**
   * @see RefundTypeEnum
   */
  @ApiModelProperty("退款类型")
  private String refundType;

  /**
   * @see AfterSaleTypeEnum
   */
  @ApiModelProperty("售后类型")
  private String afterSaleType;

  /**
   * @see StoreDirectJoinTypeEnum
   */
  @ApiModelProperty("门店直营加盟类型")
  private String storeDirectJoinType;

  @ApiModelProperty("门店")
  private List<String> storeCodeList;

  @ApiModelProperty("退款开始时间")
  private Date createdStart;

  @ApiModelProperty("退款结束时间")
  private Date createdEnd;

  @ApiModelProperty("商品编码")
  private String erpCode;

  @ApiModelProperty("公司编码")
  private List<String> companyCodeList;

}
