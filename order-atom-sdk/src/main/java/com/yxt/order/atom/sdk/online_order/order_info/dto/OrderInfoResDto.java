package com.yxt.order.atom.sdk.online_order.order_info.dto;

import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import com.yxt.order.common.base_order_dto.OrderPickInfo;
import com.yxt.order.types.order.DataVersion;
import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.ThirdOrderNo;
import com.yxt.order.types.order.enums.DeliveryTimeTypeEnum;
import com.yxt.order.types.order.enums.OrderDetailStatusEnum;
import com.yxt.order.types.order.enums.OrderDetailStorageTypeEnum;
import com.yxt.order.types.order.enums.OrderErpStatusEnum;
import com.yxt.order.types.order.enums.OrderLockFlagEnum;
import com.yxt.order.types.order.enums.OrderRemindFlag;
import com.yxt.order.types.order.enums.OrderServiceModeEnum;
import com.yxt.order.types.order.enums.OrderStatusEnum;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/7/4
 */
@Data
public class OrderInfoResDto {

  /**
   * 订单主信息
   */
  private OrderMainInfo orderMainInfo;
  /**
   * 订单明细信息
   */
  private OrderDetailInfo orderDetailInfo;
  /**
   * 订单其他信息
   */
  private OrderOtherInfo orderOtherInfo;


  @Data
  public static class OrderOtherInfo {

  }


  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderDetailInfo {

    /**
     * 订单明细
     */
    private List<OrderDetail> orderDetailList;

  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderDetail {

    /**
     * 订单唯一id
     */
    private Long id;

    /**
     * 订单号
     */
    private OrderNo orderNo;

    /**
     * 平台明细ID
     */
    private String thirdDetailId;
    /**
     * 平台商品编码
     */
    private String platformSkuId;
    /**
     * 心云商品编码
     */
    private String erpCode;

    /**
     * 订单明细状态
     */
    private OrderDetailStatusEnum orderDetailStatus;


    /**
     * 商品数量
     */
    private Integer commodityCount;

    /**
     * 已退货数量
     */
    private Integer refundCount;


    /**
     * 替换该商品的detailId
     */
    private Long swapId;


    /**
     * 换货关系码（换货商品前后此致都相同）
     */
    private String oldErpCode;

    /**
     * 订单明细金额信息
     */
    private OrderDetailAmount orderDetailAmount;

    /**
     * 订单明细组合信息
     */
    private OrderDetailJoint orderDetailJoint;


    /**
     * 订单明细拆零信息
     */
    private OrderDetailChaiLing orderDetailChaiLing;

    /**
     * 订单明细描述信息
     */
    private OrderDetailDescription orderDetailDescription;

    /**
     * 订单明细拣货信息
     */
    private List<OrderPickInfoDTO> orderPickInfoList;


  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderDetailJoint {

    /**
     * 是否是组合商品 1:是 0:否
     */
    private Integer isJoint;
    /**
     * 仅组合商品有值 组合商品erpCode，
     */
    private String originalErpCode;
    /**
     * 仅组合商品有值 组合商品数量
     */
    private Integer originalErpCodeNum;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderDetailChaiLing {


    /**
     * 是否拆零，1-不拆零，2-拆零（默认按不拆零处理）
     */
    private Integer chaiLing;

    /**
     * 仅拆零有值 拆零商品erpCode
     */
    private String chaiLingOriginalErpCode;
    /**
     * 仅拆零有值 拆零系数
     */
    private Integer chaiLingNum;

  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderDetailDescription {

    /**
     * 商品名称
     */
    private String commodityName;


    /**
     * 是否是赠品 1:是 0:否
     */
    private Integer isGift;
    /**
     * erp商品类型，1:普通商品 2:erp赠品
     */
    private Integer goodsType;

    /**
     * 商品条形编码
     */
    private String barCode;

    /**
     * 商品主图
     */
    private String mainPic;

    /**
     * 商品规格
     */
    private String commoditySpec;

    /**
     * 生产商
     */
    private String manufacture;


    /**
     * 商品储存方式
     */
    private OrderDetailStorageTypeEnum storageType;

    /**
     * 商品一级分类
     */
    private String firstTypeName;

    /**
     * 商品二级分类
     */
    private String secondTypeName;

    /**
     * 商品三级分类
     */
    private String typeName;
    /**
     * 药品类型  0:OTC甲类 1:处方 2:OTC乙类 3:非药品 4:OTC
     */
    private Integer drugType;


    /**
     * 是否医保商品 true:是 false:否
     */
    private Boolean isMedicareItem;

    /**
     * 流水号
     */
    private Long serialNumber;

    /**
     * 微商城OB预约单 供应商来源 1-云货架，2-DC仓
     */
    private Integer originType;

    /**
     * 微商城OB预约单 组织机构编码 云货架商家编码或DC仓编码
     */
    private String stCode;
    /**
     * 微商城OB预约单 预计送达时间
     */
    private Date expectDeliveryTime;
    /**
     * 供应商代发标识，1:是 0:否
     */
    private Integer directDeliveryType;


  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderDetailAmount {


    /**
     * 商品原单价
     */
    private BigDecimal originalPrice;

    /**
     * 商品售价
     */
    private BigDecimal price;


    /**
     * 小计金额 售价*数量
     */
    private BigDecimal totalAmount;

    /**
     * 明细优惠
     */
    private BigDecimal discountAmount;


    /**
     * 成交总额 = 小计金额 - 促销优惠金额
     */
    private BigDecimal actualAmount;


    /**
     * 调整金额
     */
    private BigDecimal adjustAmount;
    /**
     * 订单级别优惠分摊
     */
    private BigDecimal discountShare;


    /**
     * 下账单价
     */
    private BigDecimal billPrice;
    /**
     * 下账总金额
     */
    private BigDecimal actualNetAmount;

    /**
     * 差异分摊
     */
    private BigDecimal differentShare;

    /**
     * 换货价差
     */
    private BigDecimal modifyPriceDiff;

    /**
     * 心币换算金额
     */
    private BigDecimal healthValue;


    /**
     * 商品实付金额
     */
    private BigDecimal payment;


    /**
     * 平台分摊优惠明细
     */
    private String detailDiscount;
  }


  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderMainInfo {

    /**
     * 订单唯一id
     */
    private Long id;

    /**
     * 订单号
     */
    private OrderNo orderNo;

    /**
     * 三方平台订单号
     */
    private ThirdOrderNo thirdOrderNo;
    /**
     * 单店每日序号
     */
    private String dayNum;

    /**
     * 订单状态
     */
    private OrderStatusEnum orderStatus;

    /**
     * 订单平台创建时间
     */
    private Date created;
    /**
     * 完成时间
     */
    private Date completeTime;


    /**
     * 订单类型 O2O  B2C
     */
    private OrderServiceModeEnum serviceMode;

    /**
     * 订单版本号
     */
    private DataVersion dataVersion;

    /**
     * 订单扩展字段
     */
    private String orderExtendInfoStr;
    /**
     * 运费单号
     */
    private OrderNo freightOrderNo;

    /**
     * 订单组织信息
     */
    private OrderOrganization orderOrganization;
    /**
     * 订单下账信息
     */
    private AccountInfo accountInfo;
    /**
     * 订单锁定信息
     */
    private OrderLock orderLock;
    /**
     * 简单de订单送达信息
     */
    private OrderSimpleDelivery orderSimpleDelivery;


    /**
     * 订单接单员信息
     */
    private Acceptor acceptor;

    /**
     * 订单拣货员信息
     */
    private Picker picker;

    /**
     * 订单取消操作信息
     */
    private Canceler canceler;

    /**
     * 订单异常操作信息
     */
    private ExOperator expOperator;

    /**
     * 订单类型标识集
     */
    private OrderTypeFlags orderTypeFlags;

    /**
     * 订单开票信息
     */
    private InvoiceInfo invoiceInfo;


  }


  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class InvoiceInfo {

    /**
     * 是否开票，1:是  2:否
     */
    private String needInvoiceFlag;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票抬头类型 0:个人  1:企业普票  2:企业专票
     */
    private String invoiceType;

    /**
     * 发票内容
     */
    private String invoiceContent;

    /**
     * 纳税人识别码
     */
    private String taxerId;


  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderTypeFlags {


    /**
     * 是否转仓发货 1：是  0：否
     */
    private Integer transferDeliveryFlag;


    /**
     * 是否预约订单 1：是  0：否
     */
    private Integer appointmentFlag;

    /**
     * 预约单是否处理  1:已处理 0: 否
     */
    private Integer appointmentBusinessFlag;


    /**
     * 预约单处理类型 0:修改门店 1:确认到货 2:供应商代发
     */
    private Integer appointmentBusinessTypeFlag;


    /**
     * 供应商代发处理结果 1:发货 2:拒单
     */
    private Integer requestDeliverGoodsResultFlag;

    /**
     * 供应商代发拒单原因
     */
    private String deliverGoodsRefuseReason;

    /**
     * 是否处方单 1：是  0：否
     */
    private Integer prescriptionFlag;
    /**
     * 是否审方单 1：是  0：否
     */
    private Integer isPrescriptionFlag;
    /**
     * 处方状态 0:待审  1:通过  2:不通过  3:取消
     */
    private Integer prescriptionStatus;
    /**
     * 是否推送审方平台 1：是  0：否
     */
    private Integer isPushCheckFlag;

    /**
     * 是否新客， 1：是  0：否
     */
    private String newCustomerFlag;

    /**
     * 是否积分订单 1：是  0：否
     */
    private String integralFlag;


    /**
     * 复杂换货标识，1:是  0:否
     */
    private Integer complexModifyFlag;


    /**
     * 是否医保订单 1：是  0：否
     */
    private Integer medicalInsuranceFlag;

  }


  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class AccountInfo {

    /**
     * 订单下账状态
     */
    private OrderErpStatusEnum orderErpStatus;

    /**
     * 下账流水号
     */
    private String erpSaleNo;

    /**
     * 下账时间
     */
    private Date billTime;


    /**
     * 下账操作人工号
     */
    private String billOperator;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ExOperator {

    /**
     * 异常操作人工号
     */
    private String exOperatorId;

    /**
     * 异常操作人名称
     */
    private String exOperatorName;

    /**
     * 异常操作人时间
     */
    private Date exOperatorTime;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Canceler {

    /**
     * 取消者工号
     */
    private String cancellerId;


    /**
     * 取消者名称
     */
    private String cancellerName;

    /**
     * 取消者原因
     */
    private String cancelReason;

    /**
     * 取消时间
     */
    private Date cancelTime;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Picker {


    /**
     * 拣货操作人账户
     */
    private String pickerId;

    /**
     * 拣货操作人名称
     */
    private String pickerName;

    /**
     * 录入拣货员工号
     */
    private String pickOperatorId;

    /**
     * 录入拣货员名字
     */
    private String pickOperatorName;

    /**
     * 拣货时间
     */
    private Date pickTime;

  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Acceptor {


    /**
     * 接单员工号
     */
    private String acceptorId;

    /**
     * 接单员名称
     */
    private String acceptorName;
    /**
     * 接单时间
     */
    private Date acceptTime;

  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderLock {

    /**
     * 订单锁定标识
     */
    private OrderLockFlagEnum orderLockFlag;

    /**
     * 锁定信息
     */
    private String lockMsg;

    /**
     * 锁定者工号
     */
    private String lockerId;
  }


  @Getter
  @Setter
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderSimpleDelivery {

    /**
     * 送达方式，
     */
    private DeliveryTimeTypeEnum deliveryTimeType;

    /**
     * 送达时间描述
     */
    private String deliveryTimeDesc;


    /**
     * 买家昵称
     */
    private String buyerName;
    /**
     * 买家备注
     */
    private String buyerRemark;

    /**
     * 买家留言
     */
    private String buyerMessage;

    /**
     * 卖家备注
     */
    private String sellerRemark;


    /**
     * 催单标志,0未催，1已催促
     */
    private OrderRemindFlag remindFlag;


    /**
     * 收货地址纬度
     */
    private String receiverLat;


    /**
     * 收货地址经度
     */
    private String receiverLng;


    /**
     * 自提码
     */
    private String selfVerifyCode;


  }


  @Getter
  @Setter
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OrderOrganization {

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 平台编码
     */
    private String thirdPlatformCode;

    /**
     * 网店编码
     */
    private String onlineClientCode;

    /**
     * 网店配置ID
     */
    private Long onlineClientConfigId;

    /**
     * 线上门店编码 发货
     */
    private String deliverOnlineStoreCode;

    /**
     * 线上门店名称 发货门店
     */
    private String deliverOnlineStoreName;

    /**
     * 线下门店编码 发货门店
     */
    private String deliverStoreCode;

    /**
     * 线下门店名称 发货门店
     */
    private String deliverStoreName;


    /**
     * 下单线上门店编码
     */
    private String sourceOnlineStoreCode;

    /**
     * 下单线上门店名称
     */
    private String sourceOnlineStoreName;

    /**
     * 下单线下门店
     */
    private String sourceStoreCode;

    /**
     * 下单线下门店名称
     */
    private String sourceStoreName;


  }


}
