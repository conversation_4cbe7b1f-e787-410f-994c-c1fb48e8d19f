package com.yxt.order.delivery.atom.sdk.return_order.req;

import com.yxt.order.types.order_world.ReturnQueryScaleEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Service;

@Getter
@Setter
public class ReturnOrderQueryDBByAfterSaleNoReq {

  @ApiModelProperty("售后单号")
  @NotBlank(message = "售后单号不能为空")
  private String afterSaleNo;

  @ApiModelProperty("分表位置")
  @NotBlank(message = "分表位置不能为空")
  private String tableIndex;

  @ApiModelProperty("查询规模")
  private List<ReturnQueryScaleEnum> qryScaleList;
}
