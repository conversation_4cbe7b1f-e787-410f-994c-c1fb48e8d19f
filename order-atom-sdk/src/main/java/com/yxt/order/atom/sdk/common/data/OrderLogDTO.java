package com.yxt.order.atom.sdk.common.data;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderLogDTO implements Serializable {

    private static final long serialVersionUID=1L;

    private Long id;

    /**
     * 订单号
     */
    private Long orderNo;

    /**
     * 操作员id
     */
    private String operatorId;

    /**
     * 操作描述
     */
    private String operateDesc;

    /**
     * 操作前状态
     */
    private Integer stateBefore;

    /**
     * 操作后状态
     */
    private Integer state;

    /**
     * 其他信息
     */
    private String extraInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

    /**
     * erp状态
     */
    private Integer erpState;

    /**
     * 操作人姓名
     */
    private String operatorName;
}
