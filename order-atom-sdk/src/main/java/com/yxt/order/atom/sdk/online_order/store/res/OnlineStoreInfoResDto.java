package com.yxt.order.atom.sdk.online_order.store.res;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/17 18:57
 */
@Data
public class OnlineStoreInfoResDto {

  /**
   * 线上门店id
   */
  private Long id;

  // 线上门店对应的线下门店信息
  /**
   * 线上门店对应的平台编码
   */
  private String platformCode;
  /**
   * 线上门店对应的网店编码
   */
  private String onlineClientCode;
  private String onlineStoreCode;
  /**
   * 线上门店对应的门店名称
   */
  private String onlineStoreName;
  /**
   * 对应的线下门店编码
   */
  private String organizationCode;
  /**
   * 对应的线下门店名称
   */
  private String organizationName;
  /**
   * 线上门店地址
   */
  private String onlineStoreAddress;
  /**
   * 线上门店电话
   */
  private String onlineStorePhone;
  /**
   * 线上门店对应的merCode的sessionKey
   */
  private String sessionKey;
  /**
   * 外部门店ID
   **/
  private String outShopId;
  /**
   * 授权类型 1.商家应用授权。2-服务商应用授权
   */
  private Integer accessType;

  // 订单配置
  /**
   * 是否自动接单，0否，1是
   */
  private Integer autoAcceptFlag;
  /**
   * 拣货后自动打印小票，0否，1是
   */
  private Integer autoPrintPick;
  /**
   * 接单后自动打印拣货单，0否，1是
   **/
  private Integer autoPrintReceipt;
  /**
   * 自动呼叫骑手，0接单后呼叫骑手，1拣货后呼叫骑手"
   */
  private Integer autoCallRider;
  /**
   * 订单是否转发到HEMS, 0 不转HEMS, 1 转HEMS
   */
  private Integer autoToHems;
  /**
   * 循环呼叫骑手，0否，1是
   */
  private Integer riderPolling;
  /**
   * 循环呼叫骑手配置
   */
  private List<RiderPollingConfigResDto> riderPollingConfigs;
  /**
   * 拣货方式：1-人工拣货，2-机器自动拣货
   */
  private Integer pickType;
  /**
   * 是否骑手比价，0-否，1-是
   */
  private Integer riderCompare;
  /**
   * 比价延时配置，单位-分钟，默认5分钟
   */
  private Integer riderCompareDelay;

  //线上门店配置
  /**
   * 配送模式，员工配送、到店自提、蜂鸟配送、达达配送、美团配送
   **/
  private String selfDeliveryType;
  /**
   * 配送费收取方, 1平台，2商家 3平台向商家收取
   **/
  private Integer deliveryFeeTo;
  /**
   * 包装费收取方, 1平台，2商家
   **/
  private Integer packageFeeTo;
  /**
   * 平台优惠支付方, 1平台，2商家
   **/
  private Integer platformBenefitOwner;
  /**
   * 零售下账时机（1.拣货完成后 2.配送出库后）
   **/
  private Integer retailTime;
  /**
   * 商家优惠是否分摊（0.否 1.是）
   **/
  private Integer whetherDiscountShare;
  /**
   * 佣金是否分摊(0.否 1.是)
   **/
  private Integer whetherCommissionShare;
  /**
   * 接单是否锁库存（0.否 1.是）
   **/
  private Integer whetherInventoryLocked;
  /**
   * 是否需要审方（0.否 1.是）
   **/
  private Integer whetherNeedPrescription;
  /**
   * 审方类型（1.在OMS手工审方，2.推送至审方平台审方）
   **/
  private Integer checkingType;

  //线上门店声音配置
  /**
   * 新订单：0不提示，1提示一次，3提示三次，5循环提醒
   **/
  private Integer newOrder;
  /**
   * 预约单：0不提示，1提示一次，3提示三次，5循环提醒
   **/
  private Integer bookingOrder;
  /**
   * 退款单：0不提示，1提示一次，3提示三次，5循环提醒
   **/
  private Integer refundOrder;
  /**
   * 取消单：0不提示，1提示一次，3提示三次
   **/
  private Integer cancelOrder;
  /**
   * 催单：0不提示，1提示一次，3提示三次，5循环提醒
   **/
  private Integer urgeOrder;
  /**
   * 配送异常：0不提示，1提示一次，3提示三次，5循环提醒
   **/
  private Integer deliveryException;
  /**
   * 打印机断开：0不提示，1提示一次，3提示三次
   **/
  private Integer printerDisconnect;
  /**
   * 网络断开：0不提示，1提示一次
   **/
  private Integer netDisconnect;
  /**
   * 骑手取消订单：0不提示，1提示一次，3提示三次
   **/
  private Integer riderCancelOrder;
  /**
   * 骑手异常：1提示一次 3提示三次 0不提
   **/
  private Integer riderAbnormal;
  /**
   * 待审方单：1提示一次 3提示3次 0不提示
   **/
  private Integer waitTrialParty;
  /**
   * 拣货提醒：1提示一次 3提示3次 0不提示
   **/
  private Integer pickNotify;
  /**
   * 发货提醒：1提示一次 3提示3次 0不提示
   **/
  private Integer deliveryNotify;
  /**
   * 拣货时长：分钟
   */
  private Integer pickNotifyMins;
  /**
   * 发货时长：分钟
   **/
  private Integer deliveryNotifyMins;

  /**
   * 销售单待下账，0-不提示 其他-N次
   */
  private Integer needBillOrder;

  /**
   * 退款单待下账，0-不提示 其他-N次
   */
  private Integer needBillRefund;

  /**
   * 销售单待下账，0-不提示 其他-N次
   */
  private Integer needBillOrderFail;

}
