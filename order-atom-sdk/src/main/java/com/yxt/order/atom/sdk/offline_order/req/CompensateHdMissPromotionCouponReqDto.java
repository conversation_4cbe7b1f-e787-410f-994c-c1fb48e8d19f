package com.yxt.order.atom.sdk.offline_order.req;

import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCouponDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPromotionDTO;
import java.util.List;
import lombok.Data;

/**
 * 补充退款数据
 * @author: moatkon
 * @time: 2024/11/28 15:52
 */
@Data
public class CompensateHdMissPromotionCouponReqDto {
  private Long compensateId; // 补偿Id
  private OfflineOrderDTO offlineOrderDTO;
  private List<OfflineOrderPromotionDTO> offlineOrderPromotionDTOList;
  private List<OfflineOrderCouponDTO> offlineOrderCouponDTOList;
}
