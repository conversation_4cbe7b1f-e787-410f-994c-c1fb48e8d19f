package com.yxt.order.atom.sdk.order_world.res;

import com.yxt.order.types.order_world.RefundFlag;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class EsOrderWorldRefundOrderInfoRes {

  /**
   * 退单对应的内部订单号(自己生成)
   */
  private String orderNo;

  /**
   * 内部退款单号,自己生成
   */
  private String refundNo;

  /**
   * 会员id
   */
  private String userId;

  /**
   * 会员编码(唯一值)
   */
  private String userCardNo;

  /**
   * 平台编码,HAIDIAN,内部定义
   */
  private String thirdPlatformCode;

  /**
   * 第三方平台退款单号
   */
  private String thirdRefundNo;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 退款类型 PART-部分退款 ALL-全额退款
   */
  private String afterSaleScope;

  /**
   * 售后单类型 AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后
   */
  private String afterSaleType;

  /**
   * 退单状态 REFUNDED 已退款
   */
  private String refundStatus;

  /**
   * 退单创单时间
   */
  private LocalDateTime created;

  /**
   * 退单申请时间
   */
  private LocalDateTime applyTime;

  /**
   * 退款金额
   */
  private BigDecimal refundAmount;

  /**
   * 订单标记，通过空格分割，使用match匹配
   *
   * @see RefundFlag
   */
  private List<String> refundFlags;

  /**
   * 售后单号
   */
  private String afterSaleNo;

  /**
   * 交易场景 online:代表线上交易 ,offline:代表线下交易
   */
  private String transactionChannel;

  /**
   * 业务类型 O2O、B2C、B2B
   */
  private String businessType;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  private String launchOrganizationCode;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 是否起效 1-起效 -1-未起效
   */
  private Long valid;

  /**
   * 平台创建日
   */
  private String createdDay;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 退单明细
   */
  private List<EsOrderWorldRefundOrderDetailResDTO> detailList;

}
