package com.yxt.order.atom.sdk.mongo;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.MONGO_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.mongo.req.MqDataReqDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月01日 14:31
 * @email: <EMAIL>
 */
public interface MongoApi {

  @PostMapping(MONGO_ENDPOINT + "/insert/mqData")
  ResponseBase<Boolean> insert(
      @RequestBody MqDataReqDto mqDataReqDto);

}
