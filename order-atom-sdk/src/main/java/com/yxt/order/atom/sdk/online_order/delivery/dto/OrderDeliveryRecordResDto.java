package com.yxt.order.atom.sdk.online_order.delivery.dto;

import com.yxt.order.types.order.DataVersionTime;
import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.RiderOrderNo;
import com.yxt.order.types.order.enums.DeliveryPlatformCodeEnum;
import com.yxt.order.types.order.enums.DeliveryStateEnum;
import com.yxt.order.types.order.enums.DeliveryTypeEnum;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/8/1
 */
@Data
public class OrderDeliveryRecordResDto {


  /**
   * 主键
   */
  private Long id;

  /**
   * 订单号
   */
  private OrderNo orderNo;

  /**
   * 快递单当前状态，包括0待呼叫 1待接单，2待取货，3配送中，4已签收, 5已取消，6已过期，7异常
   */
  private DeliveryStateEnum deliveryStateEnum;


  /**
   * 第三方配送有配送平台
   */
  private DeliveryPlatformCodeEnum deliveryPlatformCodeEnum;

  /**
   * 骑手订单号
   */
  private RiderOrderNo riderOrderNo;


  /**
   * 配送方式 1平台配送 2平台合作方配送 3自配送 4到店自取 0未知
   */
  private DeliveryTypeEnum deliveryTypeEnum;


  /**
   * 骑手信息
   */
  private Rider rider;
  /**
   * 骑手金额信息
   */
  private RiderAmountInfo riderAmountInfo;
  /**
   * 配送目标信息
   */
  private TargetInfo targetInfo;
  /**
   * 取消信息
   */
  private CancelInfo cancelInfo;
  /**
   * 其他信息
   */
  private OtherInfo otherInfo;
  /**
   * 数据版本
   */
  private DataVersionTime dataVersion;

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Rider {

    /**
     * 骑手名称
     */
    private String riderName;

    /**
     * 配送员手机
     */
    private String riderPhone;

    /**
     * 员工自配送时，保存员工编号
     */
    private String riderStaffCode;


    /**
     * 呼叫时间
     */
    private Date callTime;


    /**
     * 接单时间
     */
    private Date acceptTime;

    /**
     * 取货时间
     */
    private Date pickTime;


    /**
     * 物流编码
     */
    private String logisticsCompany;


    /**
     * 物流名称
     */
    private String logisticsName;

    /**
     * 物流单号
     */
    private String logisticsNo;


  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class RiderAmountInfo {

    /**
     * 配送小费
     */
    private BigDecimal deliveryTip;

    /**
     * 实际运费
     */
    private BigDecimal actualDeliveryFee;


    /**
     * 总运费
     */
    private BigDecimal deliveryFeeTotal;

  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class TargetInfo {

    /**
     * 配送地址
     */
    private String riderAddress;


    /**
     * 纬度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class CancelInfo {

    /**
     * 取消标志, 0未取消，1已取消
     */
    private Integer cancelFlag;

    /**
     * 取消来源
     */
    private String cancelFrom;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 取消描述
     */
    private String cancelDetail;


  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class OtherInfo {


    /**
     * 第三方配送有配送网店
     */
    private String deliveryClientCode;

    /**
     * 第三方配送有配送门店
     */
    private String deliveryStoreCode;


    /**
     * 异常原因等
     */
    private String exceptionReason;

    /**
     * 额外信息
     */
    private String extraInfo;


    /**
     * 延迟呼叫标识 0-不需延迟呼叫 1-等待延迟呼叫
     */
    private Integer delayState;

    /**
     * 是否已预呼叫: 1-是
     */
    private Integer preCallFlag;

    /**
     * 是否上传坐标信息
     */
    private String uploadLocationFlag;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
  }

}
