package com.yxt.order.atom.sdk.online_order.oms_order_info.dto.req;

import com.yxt.order.types.order.OmsOrderNo;
import com.yxt.order.types.order.OrderNo;
import lombok.Data;
import lombok.Getter;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/11/1
 */
@Data
public class OmsOrderInfoQryReqDto {

  /**
   * 查询规模
   */
  private OmsOrderInfoQryReqDto.OrderQryScaleEnum qryScale;
  /**
   * 内部订单号
   */
  private OmsOrderNo omsOrderNo;


  @Getter
  public enum OrderQryScaleEnum {
    MAIN(50, "主信息"),
    ALL(100, "所有信息");
    private final Integer code;
    private final String msg;

    OrderQryScaleEnum(Integer code, String msg) {
      this.code = code;
      this.msg = msg;
    }
  }

}
