package com.yxt.order.atom.sdk.offline_order.res;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 订单退款信息
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月09日 11:42
 * @email: <EMAIL>
 */
@Data
public class OfflineRefundAmountResDTO {

  /**
   * 正单 实收金额
   */
  private BigDecimal actualCollectAmount;

  /**
   * 退款金额
   */
  private List<RefundOrderAmount> refundOrderAmountList;

  /**
   * 退款单的退款金额
   */
  @Data
  public static
  class RefundOrderAmount {

    private BigDecimal refundAmount; // 订单维度的退款金额
  }

}
