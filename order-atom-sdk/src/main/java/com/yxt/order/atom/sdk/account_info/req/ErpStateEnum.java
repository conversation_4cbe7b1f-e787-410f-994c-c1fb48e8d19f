package com.yxt.order.atom.sdk.account_info.req;

import lombok.Getter;

@Getter
public enum ErpStateEnum {
  //ERP状态
  WAIT_PICK(20, "待锁定"),
  WAIT_SALE(30, "待下账"),
  HAS_SALE_FAIL(99, "下账失败"),
  HAS_SALE(100, "下账成功"),
  CANCELED(110, "已取消"),
  HAS_REFUND(120,"已退款");

  private Integer code;
  private String msg;

  ErpStateEnum(Integer code, String msg) {
    this.code = code;
    this.msg = msg;
  }

  public static ErpStateEnum getErpState(Integer code) {
    for (ErpStateEnum erpStateEnum : ErpStateEnum.values()) {
      if(erpStateEnum.getCode().equals(code)) {
        return erpStateEnum;
      }
    }
    return WAIT_PICK;
  }

  public static ErpStateEnum getByErpState(Integer code) {
    for (ErpStateEnum erpStateEnum : ErpStateEnum.values()) {
      if(erpStateEnum.getCode().equals(code)) {
        return erpStateEnum;
      }
    }
    return null;
  }

}
