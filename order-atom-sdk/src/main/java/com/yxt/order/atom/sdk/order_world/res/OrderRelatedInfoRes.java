package com.yxt.order.atom.sdk.order_world.res;

import com.yxt.order.atom.sdk.common.order_world.BizLogInfoDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderAmountDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderCouponDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDeliveryAddressDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailCouponDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailPromotionDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPayDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPrescriptionDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPromotionDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderSetDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderUserInfoDTO;
import com.yxt.order.common.order_world_dto.db.OrderPrescription;
import com.yxt.order.common.order_world_dto.db.OrderSetDetail;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class OrderRelatedInfoRes {

  @ApiModelProperty(value = "订单信息")
  private OrderInfoDTO orderInfo;

  @ApiModelProperty(value = "订单明细")
  private List<OrderDetailDTO> orderDetailList;

  @ApiModelProperty(value = "订单套装明细")
  private List<OrderSetDetailDTO> orderSetDetailList;

  @ApiModelProperty(value = "订单支付方式信息")
  private List<OrderPayDTO> orderPayList;

  @ApiModelProperty(value = "订单金额信息")
  private OrderAmountDTO orderAmount;

  @ApiModelProperty(value = "订单用户信息")
  private OrderUserInfoDTO orderUserInfo;

  @ApiModelProperty(value = "订单收货人信息")
  private OrderDeliveryAddressDTO receiverInfo;

  @ApiModelProperty(value = "订单业务日志信息")
  private List<BizLogInfoDTO> bizLogList;

  @ApiModelProperty(value = "处方信息")
  private List<OrderPrescriptionDTO> orderPrescriptionList;

  @ApiModelProperty(value = "订单优惠券信息")
  private List<OrderCouponDTO> orderCouponList;

  @ApiModelProperty(value = "订单促销活动信息")
  private List<OrderPromotionDTO> orderPromotionList;

  @ApiModelProperty(value = "明细优惠券信息")
  private List<OrderDetailCouponDTO> orderDetailCouponList;

  @ApiModelProperty(value = "明细促销活动信息")
  private List<OrderDetailPromotionDTO> orderDetailPromotionList;
}
