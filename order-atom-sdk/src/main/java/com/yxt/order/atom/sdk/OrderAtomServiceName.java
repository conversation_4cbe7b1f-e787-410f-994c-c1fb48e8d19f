package com.yxt.order.atom.sdk;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月24日 9:52
 * @email: <EMAIL>
 */
public interface OrderAtomServiceName {

  String value = "order-atom-service";
  String MONGO_ENDPOINT = "/1.0/mongo";


  String ORDER_ENDPOINT = "/1.0";

  String OFFLINE_ORDER_ENDPOINT = "/1.0";
  String OFFLINE_ORDER_MANAGE_ENDPOINT = "/1.0/manage";


  String MQ_MESSAGE_ENDPOINT = "/1.0/mq-message";
  String IMPORT_EXPORT_ENDPOINT = "/1.0/im-ex";
  String RECONCILIATION_ENDPOINT = "/1.0/reconciliation";
}
