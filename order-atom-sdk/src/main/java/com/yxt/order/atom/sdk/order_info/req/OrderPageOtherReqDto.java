package com.yxt.order.atom.sdk.order_info.req;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/19 9:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderPageOtherReqDto extends Authority {

  private String merCode;

  @ApiModelProperty(value = "平台编码")
  private String platformCode;
  @ApiModelProperty(value = "订单号")
  private String orderNo;
  @ApiModelProperty("发货单号")
  private String omsShipNo;
  @ApiModelProperty(value = "三方订单号")
  private String thirdOrderNo;
  @ApiModelProperty(value = "买家昵称")
  private String buyerName;
  @ApiModelProperty(value = "收货人名称")
  private String receiverName;

//  private String serviceMode;

  private String warehouseId;
  @ApiModelProperty(value = "订单状态")
  private Integer orderState;
  @ApiModelProperty(value = "收货人电话")
  private String receiverPhone;
  @ApiModelProperty(value = "ERP商品编码")
  private String commodityCode;
  @ApiModelProperty(value = "商品名称，支持模糊查询")
  private String commodityName;
  private String remark;
  private String expressNo;
  private Integer orderType;
  @ApiModelProperty(value = "订单拆分状态：0 未拆单，1 拆分订单的源头订单，2 非源头的拆分订单")
  @Range(max = 2, message = "订单拆分状态不正确，请检查")
  private Integer splitStatus;
  private Integer timeType;

  @ApiModelProperty(value = "下单开始时间")
  private String beginTime;
  @ApiModelProperty(value = "下单结束时间")
  private String endTime;
  private String areas;

  @ApiModelProperty(value = "异常类型")
  private Integer exceptionType;

  @ApiModelProperty(value = "物流方式")
  private Long expressId;

  @ApiModelProperty(value = "支付方式")
  private String payType;

  @ApiModelProperty(value = "金额下限")
  private BigDecimal minAmount;
  @ApiModelProperty(value = "金额上限")
  private BigDecimal maxAmount;

  @ApiModelProperty(value = "店铺分类id")
  private Integer classifyId;

  @ApiModelProperty("1 有备注 2 无备注 0 查全部")
  private Integer remarkType = 0;

  @ApiModelProperty("备注信息")
  private String remarkText;


  private OrderTag tag;



}
