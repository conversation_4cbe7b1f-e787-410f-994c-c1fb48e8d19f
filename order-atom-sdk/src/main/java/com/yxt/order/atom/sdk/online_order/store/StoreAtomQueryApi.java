package com.yxt.order.atom.sdk.online_order.store;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.data.StoreBillConfigDTO;
import com.yxt.order.atom.sdk.online_order.store.req.GetOnlineStoreByPlatformShopIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.InnerStoreDictionaryListReq;
import com.yxt.order.atom.sdk.online_order.store.req.InnerStoreDictionaryReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigByIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryClientReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStoreAccessReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStorePageReq;
import com.yxt.order.atom.sdk.online_order.store.req.QuerySysStoreInfoReq;
import com.yxt.order.atom.sdk.online_order.store.req.StoreQueryByScaleReq;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreConfigResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreResDto;
import com.yxt.order.atom.sdk.online_order.store.res.InnerStoreDictionaryResDto;
import com.yxt.order.atom.sdk.online_order.store.res.OnlineStoreInfoResDto;
import com.yxt.order.atom.sdk.online_order.store.res.StoreQueryByScaleResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsStoreResDto;
import java.util.List;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年02月23日 14:11
 * @email: <EMAIL>
 */
public interface StoreAtomQueryApi {

  @PostMapping(ORDER_ENDPOINT + "/store/queryClient")
  ResponseBase<DsOnlineClientResDto> queryClient(@RequestBody QueryClientReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/querySysStoreInfo")
  ResponseBase<OnlineStoreInfoResDto> querySysStoreInfo(@RequestBody QuerySysStoreInfoReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/getStoreAccess")
  ResponseBase<The3DsStoreResDto> getStoreAccess(@RequestBody QueryStoreAccessReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/queryDsOnlineStore")
  ResponseBase<DsOnlineStoreResDto> queryDsOnlineStore(@RequestBody QueryDsOnlineStoreReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/queryDsOnlineStoreConfig")
  ResponseBase<DsOnlineStoreConfigResDto> queryDsOnlineStoreConfig(@RequestBody QueryDsOnlineStoreConfigReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/queryInnerStoreDictionary")
  ResponseBase<InnerStoreDictionaryResDto> queryInnerStoreDictionary(@RequestBody InnerStoreDictionaryReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/listInnerStoreDictionary")
  ResponseBase<List<InnerStoreDictionaryResDto>> listInnerStoreDictionary(@RequestBody @Valid InnerStoreDictionaryListReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/getOnlineStoreByPlatformShopId")
  ResponseBase<The3DsOnlineClientResDto> getOnlineStoreByPlatformShopId(@RequestBody GetOnlineStoreByPlatformShopIdReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/getBillConfigById")
  ResponseBase<StoreBillConfigDTO> getBillConfigById(@RequestBody QueryBillConfigByIdReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/getBillConfig")
  ResponseBase<StoreBillConfigDTO> getBillConfig(@RequestBody QueryBillConfigReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/query/by-scale")
  ResponseBase<StoreQueryByScaleResDto> queryStoreInfoByScale(@RequestBody StoreQueryByScaleReq req);

  @PostMapping(ORDER_ENDPOINT + "/store/page/query")
  ResponseBase<PageDTO<DsOnlineStoreResDto>> queryStorePage(@RequestBody QueryStorePageReq req);

}
