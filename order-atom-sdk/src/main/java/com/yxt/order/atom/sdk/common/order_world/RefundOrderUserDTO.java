package com.yxt.order.atom.sdk.common.order_world;

import lombok.Data;

/**
 * 订单用户信息
 */
@Data
public class RefundOrderUserDTO {

  private Long id;

  /**
   * 内部退款单号,自己生成
   */
  private String refundNo;

  /**
   * 会员id
   */
  private String userId;

  /**
   * 发起人id，目前仅B2B有值
   */
  private String launchUserId;

  /**
   * 会员名称
   */
  private String userName;

  /**
   * 会员标记
   */
  private String userTag;

  /**
   * 会员卡号
   */
  private String userCardNo;

  /**
   * 会员手机号
   */
  private String userMobile;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

}
