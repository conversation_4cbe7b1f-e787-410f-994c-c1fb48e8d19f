package com.yxt.order.atom.sdk.online_order.oms_order_info.dto;

import com.yxt.order.common.base_order_dto.ErpBillInfo;
import com.yxt.order.common.base_order_dto.OrderPayInfo;
import com.yxt.order.common.base_order_dto.OrderPickInfo;
import java.util.List;
import lombok.Data;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/11/1
 */
@Data
public class OmsOrderInfoResDto {

  private SimpleOmsOrderInfoDTO omsOrderMainInfo;


  private OmsOrderOtherInfo omsOrderOtherInfo;


  @Data
  public static class OmsOrderOtherInfo {
    private ErpBillInfo erpBillInfo;
    private OrderPayInfo orderPayInfo ;


    public OmsOrderOtherInfo(ErpBillInfo erpBillInfo,OrderPayInfo orderPayInfo) {
      this.erpBillInfo = erpBillInfo;
      this.orderPayInfo = orderPayInfo;
    }
  }


  public OmsOrderInfoResDto(SimpleOmsOrderInfoDTO omsOrderMainInfo) {
    this.omsOrderMainInfo = omsOrderMainInfo;
  }
}
