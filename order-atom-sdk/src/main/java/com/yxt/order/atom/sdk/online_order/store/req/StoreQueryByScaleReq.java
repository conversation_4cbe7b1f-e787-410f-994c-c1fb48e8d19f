package com.yxt.order.atom.sdk.online_order.store.req;

import com.yxt.order.types.order.MerCode;
import com.yxt.order.types.order.ThirdPlatformCode;
import com.yxt.order.types.order.enums.StoreQryScaleEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class StoreQueryByScaleReq {

  @ApiModelProperty(value = "store表主键id")
  private Long id;

  @ApiModelProperty(value = "门店编码")
  private String onlineStoreCode;

  @ApiModelProperty(value = "平台编码")
  private ThirdPlatformCode platformCode;

  @ApiModelProperty(value = "商户编码")
  private MerCode merCode;

  @ApiModelProperty(value = "网店编码")
  private String onlineClientCode;

  @ApiModelProperty(value = "查询范围")
  private List<StoreQryScaleEnum> scaleList;

}
