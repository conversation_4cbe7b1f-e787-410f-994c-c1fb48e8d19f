package com.yxt.order.atom.sdk.online_order.order_info.dto.req;

import com.yxt.order.atom.sdk.common.data.ErpBillInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderBusinessConsumerMessageDTO;
import com.yxt.order.atom.sdk.common.data.OrderCommodityDetailCostPriceDTO;
import com.yxt.order.atom.sdk.common.data.OrderDeliveryAddressDTO;
import com.yxt.order.atom.sdk.common.data.OrderDeliveryRecordDTO;
import com.yxt.order.atom.sdk.common.data.OrderDetailCommodityCostPriceDTO;
import com.yxt.order.atom.sdk.common.data.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPayInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPrescriptionDTO;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:23
 * @email: <EMAIL>
 */
@Data
public class UpdateOrderOptionalReq {

  private OrderInfoDTO orderInfoDto;
  private OrderPayInfoDTO orderPayInfoDto;
  private OrderDeliveryRecordDTO orderDeliveryRecordDto;
  private OrderDeliveryAddressDTO orderDeliveryAddressDto;
  private List<OrderDetailDTO> orderDetailDtoList;
  private List<OrderPrescriptionDTO> orderPrescriptionDtoList;
  private ErpBillInfoDTO billInfoDTO;

  private Boolean needUpdateCostFlag;
  private List<OrderDetailCommodityCostPriceDTO> orderDetailCommodityCostPriceList;
  private List<OrderCommodityDetailCostPriceDTO> orderCommodityDetailCostPriceList;

  private OrderBusinessConsumerMessageDTO orderBusinessConsumerMessageDto;

  private List<OrderPickInfoDTO> orderPickDtoList;
}
