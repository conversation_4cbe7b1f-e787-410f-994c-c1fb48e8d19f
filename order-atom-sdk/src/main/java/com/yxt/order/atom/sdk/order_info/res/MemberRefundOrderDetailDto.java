package com.yxt.order.atom.sdk.order_info.res;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2024/12/10 16:39
 */
@Data
public class MemberRefundOrderDetailDto {

  private String erpCode;// 商品编码
  private String erpName;// 商品名称
  private String commoditySpec;// 规格
  private String manufacture;// 生产厂商
  private BigDecimal commodityCount;//商品数量(线下单中药场景会出现小数)
  private BigDecimal actualRefundAmount;//商品实退金额,目前只有一心助手使用
  private BigDecimal billAmount;//下账金额

  private String salerId; // 售货员Id
  private String salerName; // 售货员名称

  private List<PickInfo> pickInfoList;


}
