package com.yxt.order.atom.sdk.offline_order.req;

import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.enums.OrderQryScaleEnum;
import java.util.List;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OfflineOrderInfoQryByScaleReqDto {

  /**
   * 查询规模
   */
  private List<OrderQryScaleEnum> qryScaleList;

  /**
   * 内部订单号
   */
  @NotNull(message = "订单号不能为空")
  private OrderNo orderNo;

}
