package com.yxt.order.atom.sdk.order_world;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldRefundBatchQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldRefundQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.res.RefundRelatedInfoRes;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface OrderWorldRefundAtomQueryApi {

  @PostMapping(ORDER_ENDPOINT + "/order-world/refund/query/by-scale")
  ResponseBase<RefundRelatedInfoRes> getRefundInfoByScale(@RequestBody OrderWorldRefundQueryByScaleReq req);

  @PostMapping(ORDER_ENDPOINT + "/order-world/refund/query/by-scale/batch")
  ResponseBase<List<RefundRelatedInfoRes>> getRefundInfoBatchByScale(@RequestBody OrderWorldRefundBatchQueryByScaleReq req);

  @PostMapping(ORDER_ENDPOINT + "/order-world/refund/query/by-afterSaleNo")
  ResponseBase<List<RefundRelatedInfoRes>> getRefundInfoBatchByScale(@RequestBody OrderWorldRefundBatchQueryByScaleReq req);
}
