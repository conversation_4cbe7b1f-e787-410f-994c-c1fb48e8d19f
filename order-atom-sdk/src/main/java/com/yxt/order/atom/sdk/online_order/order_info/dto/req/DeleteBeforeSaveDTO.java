package com.yxt.order.atom.sdk.online_order.order_info.dto.req;

import lombok.Data;

@Data
public class DeleteBeforeSaveDTO {

  private Long orderNo;
  private String platformCode;
  private String thirdOrderNo;
  private Boolean orderInfo = false;
  private Boolean orderPayInfo = false;
  private Boolean orderDetail = false;
  private Boolean orderDeliveryAddress = false;
  private Boolean orderDeliveryRecord = false;
  private Boolean orderPrescription = false;
  private Boolean orderGiftInfo = false;
  private Boolean erpBillInfo = false;
  private Boolean refundOrder = false;
  private Boolean refundOrderDetail = false;
  private Boolean commodityExceptionOrder = false;
  private Boolean orderAssembleCommodityRelation = false;
}