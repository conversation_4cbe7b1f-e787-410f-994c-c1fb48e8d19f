package com.yxt.order.atom.sdk.common.data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 退款下账信息
 */
@Data
public class ErpRefundInfoDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 下账配置id
     */
    private Long clientConfId;

    /**
     * 下账商家退款总金额
     */
    private BigDecimal refundMerchantTotal;

    /**
     * 待分摊金额
     */
    private BigDecimal apportionAmount;

    /**
     * 下账退款商品总金额
     */
    private BigDecimal refundGoodsTotal;

    /**
     * 退佣金
     */
    private BigDecimal brokerageAmount;

    /**
     * 退平台优惠
     */
    private BigDecimal platformDiscount;

    /**
     * 退商家优惠
     */
    private BigDecimal merchantDiscount;

    /**
     * 退平台配送费
     */
    private BigDecimal platformRefundDeliveryFee;

    /**
     * 退商家配送费
     */
    private BigDecimal refundPostFee;

    /**
     * 退平台包装费
     */
    private BigDecimal platformRefundPackFee;

    /**
     * 退商家包装费
     */
    private BigDecimal packageFee;

    /**
     * 退商品明细优惠
     */
    private BigDecimal discountAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

    /**
     * 订单号
     */
    private Long orderNo;

    /**
     * 退款单号
     */
    private Long refundNo;

}