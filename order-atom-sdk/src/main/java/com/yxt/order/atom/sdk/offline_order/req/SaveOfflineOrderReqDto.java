package com.yxt.order.atom.sdk.offline_order.req;

import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCouponDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderMedInsSettleDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPrescriptionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPromotionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderUserDTO;
import java.util.List;
import lombok.Data;

/**
 * 保存线下单
 *
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月01日 14:32
 * @email: <EMAIL>
 */
@Data
public class SaveOfflineOrderReqDto {

  private OfflineOrderDTO offlineOrderDTO;
  private OfflineOrderOrganizationDTO offlineOrderOrganizationDTO;
  private OfflineOrderCashierDeskDTO offlineOrderCashierDeskDTO;
  private OfflineOrderUserDTO offlineOrderUserDTO;
  private OfflineOrderPrescriptionDTO offlineOrderPrescriptionDTO;
  private List<OfflineOrderPayDTO> offlineOrderPayDTOList;
  private List<OfflineOrderDetailDTO> offlineOrderDetailDTOList;
  private OfflineOrderMedInsSettleDto offlineOrderMedInsSettleDto;
  private List<OfflineOrderPromotionDTO> offlineOrderPromotionDTOList;
  private List<OfflineOrderCouponDTO> offlineOrderCouponDTOList;

}
