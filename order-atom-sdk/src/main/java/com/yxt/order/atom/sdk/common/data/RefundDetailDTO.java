package com.yxt.order.atom.sdk.common.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RefundDetailDTO {

  @JsonFormat(shape = JsonFormat.Shape.STRING)
  private Long id;

  /**
   * 退款单号
   */
  @ApiModelProperty(value = "退款单号")
  @JsonFormat(shape = JsonFormat.Shape.STRING)
  private Long refundNo;

  /**
   * 商品erp编码
   */
  @ApiModelProperty(value = "商品erp编码")
  private String erpCode;

  /**
   * 商品条形编码
   */
  @ApiModelProperty(value = "商品条形编码")
  private String barCode;

  /**
   * 订单商品三方skuid
   */
  @ApiModelProperty(value = "订单商品三方skuid")
  private String thirdSkuId;

  /**
   * 商品名称
   */
  @ApiModelProperty(value = "商品名称")
  private String commodityName;

  @ApiModelProperty(value = "商品主图")
  private String mainPic;
  /**
   * 退款数量
   */
  @ApiModelProperty(value = "退款数量")
  private Integer refundCount;

  @ApiModelProperty(value = "平台退款数量")
  private Integer platformRefundCount;


  /**
   * 下账金额
   */
  @ApiModelProperty(value = "下账金额")
  private BigDecimal actualNetAmount;

  /**
   * 下账价格（单价）
   */
  @ApiModelProperty(value = "下账价格")
  private BigDecimal billPrice;

  /**
   * 退款商品退用户金额
   */
  @ApiModelProperty(value = "退款商品金额")
  private BigDecimal buyerAmount;

  /**
   * 商家退还给平台补贴的金额
   */
  @ApiModelProperty(value = "商家退还给平台补贴的金额")
  private BigDecimal merchantAmount;

  /**
   * 退款单价
   */
  @ApiModelProperty(value = "退款单价")
  private BigDecimal unitRefundPrice;

  /**
   * 订单原价
   */
  private BigDecimal originDetailPrice;

  /**
   * 退款明细优惠
   */
  private BigDecimal refundDiscountAmount = BigDecimal.ZERO;

  @ApiModelProperty(value = "状态")
  private Integer status;
  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  /**
   * 修改时间
   */
  @ApiModelProperty(value = "修改时间")
  private Date modifyTime;

  /**
   * 第三方详情ID
   */
  private String thirdDetailId;

  /**
   * 优惠券金额,单位:元
   */
  private BigDecimal couponAmount;

  /**
   * 活动优惠金额,单位:元
   */
  private BigDecimal activityDiscountAmont;

  /**
   * 退健康贝换算金额
   */
  private BigDecimal healthValue;

  /**
   * 退平摊分摊优惠明细
   */
  private String detailDiscount;

  /**
   * 分摊金额
   */
  private BigDecimal shareAmount;

  /**
   * 订单明细ID
   */
  @JsonFormat(shape = JsonFormat.Shape.STRING)
  private Long orderDetailId;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 三方平台退款ID
   */
  private String thirdRefundNo;
}
