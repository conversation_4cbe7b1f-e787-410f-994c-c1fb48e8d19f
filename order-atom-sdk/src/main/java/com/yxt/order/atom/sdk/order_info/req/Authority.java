package com.yxt.order.atom.sdk.order_info.req;


import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 权限控制类
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Authority extends PageBase {

    @ApiModelProperty(value = "平台编码")
    private String platformCode;

    @ApiModelProperty(value = "门店编码")
    private String onlineStoreCode;

    @ApiModelProperty(value = "仓库id")
    private String warehouseId;

    @ApiModelProperty("门店集合")
    private Set<String> storeCodeSet;

    /**
     * 仓库权限
     */
    private Set<String> warehouseIdSet;

    /**
     * 平台权限
     */
    private Set<String> platformCodeSet;

    /**
     * 平台权限-用作权限校验用
     */
//    private Set<String> permissionPlatformCodeSet;

    /**
     * 店铺权限
     */
    private Set<String> onlineStoreCodeSet;

    /**
     * 店铺权限-用作权限校验用
     */
//    private Set<String> permissionOnlineStoreCodeSet;

    /**
     * 线下店铺权限
     */
//    private Set<String> organizationStoreCodeSet;

}
