package com.yxt.order.atom.sdk.common.data;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月11日 15:38
 * @email: <EMAIL>
 */
@Data
public class OrderDetailDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;

  /**
   * 订单号
   */
  @ApiModelProperty(value = "订单号")
  private Long orderNo;

  /**
   * 商品三方平台编码
   */
  @ApiModelProperty(value = "商品三方平台编码")
  private String platformSkuId;

  /**
   * 商品erp编码
   */
  @ApiModelProperty(value = "商品erp编码")
  private String erpCode;

  /**
   * 商品条形编码
   */
  @ApiModelProperty(value = "商品条形编码")
  private String barCode;

  /**
   * 商品名称
   */
  @ApiModelProperty(value = "商品名称")
  private String commodityName;


  @ApiModelProperty(value = "商品主图")
  private String mainPic;

  /**
   * 商品规格
   */
  @ApiModelProperty(value = "商品规格")
  private String commoditySpec;

  /**
   * 商品数量
   */
  @ApiModelProperty(value = "商品数量")
  private Integer commodityCount;

  /**
   * 商品原单价
   */
  @ApiModelProperty(value = "商品原单价")
  private BigDecimal originalPrice;

  /**
   * 商品售价
   */
  @ApiModelProperty(value = "商品售价")
  private BigDecimal price;

  /**
   * 小计金额售价*数量
   */
  @ApiModelProperty(value = "小计金额售价*数量(商品总额)")
  private BigDecimal totalAmount;

  /**
   * 促销优惠金额
   */
  @ApiModelProperty(value = "明细优惠")
  private BigDecimal discountAmount;

  /**
   * 成交总额小计-优惠
   */
  @ApiModelProperty(value = "成交总额小计-优惠（实付金额）")
  private BigDecimal actualAmount;


  @ApiModelProperty(value = "调整金额")
  private BigDecimal adjustAmount;

  /**
   * 优惠分摊
   */
  @ApiModelProperty(value = "订单级别优惠分摊")
  private BigDecimal discountShare;

  /**
   * 下账金额
   */
  @ApiModelProperty(value = "下账金额")
  private BigDecimal actualNetAmount;


  /**
   * 差异分摊
   */
  @ApiModelProperty(value = "差异分摊")
  private BigDecimal differentShare;

  /**
   * 明细状态 NORMAL(0, "正常"), OUT_OF_STOCK(1, "库存不足异常"), NOT_EXIST(2, "商品不存在"),
   * <p>
   * REPLACE(10, "已换货"), REFUND(11, "已退款");
   */
  @ApiModelProperty(value = "明细状态，0正常，1已换货，2已退货")
  private Integer status;

  /**
   * 生产商
   */
  @ApiModelProperty(value = "生产商")
  private String manufacture;


  /**
   * 生产商
   */
  @ApiModelProperty(value = "替换该商品的detailId")
  private Long swapId;

  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  /**
   * 修改时间
   */
  @ApiModelProperty(value = "更新时间")
  private Date modifyTime;

  /**
   * 下账价格
   */
  @ApiModelProperty(value = "下账单价")
  private BigDecimal billPrice;

  /**
   * 第三方详情ID
   */
  @ApiModelProperty(value = "第三方详情ID")
  private String thirdDetailId;

  /**
   * 是否是赠品（0，不是赠品,1是赠品）
   */
  @ApiModelProperty(value = "是否是赠品（0，不是赠品,1是赠品）")
  private Integer isGift;

  /**
   * erp商品类型，1普通商品，2erp赠品
   */
  @ApiModelProperty(value = "erp商品类型，1普通商品，2erp赠品")
  private Integer goodsType;

  /**
   * 已退货数量
   */
  @ApiModelProperty(value = "已退货数量")
  private Integer refundCount = 0;

  @ApiModelProperty(value = "微商城OB预约单 供应商来源 1-云货架，2-DC仓")
  private Integer originType;
  @ApiModelProperty(value = "微商城OB预约单 组织机构编码 云货架商家编码或DC仓编码")
  private String stCode;
  @ApiModelProperty(value = "微商城OB预约单 预计送达时间 ")
  private Date expectDeliveryTime;
  @ApiModelProperty(value = "供应商代发标识，0-非供应商代发，1-供应商代发")
  private Integer directDeliveryType;

  /**
   * 组合商品erpCode，只有为组合商品拆单的订单详情才有值
   */
  @ApiModelProperty(value = "组合商品erpCode，只有为组合商品拆单的订单详情才有值")
  private String originalErpCode;
  /**
   * 组合商品原始数量，只有为组合商品拆单的订单详情才有值
   */
  @ApiModelProperty(value = "组合商品原始数量，只有为组合商品拆单的订单详情才有值")
  private Integer originalErpCodeNum;

  /**
   * 是否拆零，1-不拆零，2-拆零（默认按不拆零处理）
   */
  @ApiModelProperty(value = "是否拆零，1-不拆零，2-拆零（默认按不拆零处理）")
  private Integer chailing;

  /**
   * 拆零商品erpCode，只有为拆零商品的订单详情才有值
   */
  @ApiModelProperty(value = "拆零商品erpCode，只有为拆零商品的订单详情才有值")
  private String chaiLingOriginalErpCode;

  /**
   * 商品一级分类
   */
  @ApiModelProperty(value = "商品一级分类")
  private String firstTypeName;
  /**
   * 商品二级分类
   */
  @ApiModelProperty(value = "商品二级分类")
  private String secondTypeName;
  /**
   * 商品三级分类
   */
  @ApiModelProperty(value = "商品三级分类")
  private String typeName;

  /**
   * 换货价差
   */
  @ApiModelProperty(value = "换货价差")
  private BigDecimal modifyPriceDiff;

  /**
   * 健康贝换算金额
   */
  @ApiModelProperty(value = "健康贝换算金额")
  private BigDecimal healthValue;

  /**
   * 商品实付金额(汇总)
   */
  @ApiModelProperty(value = "商品实付金额(汇总)")
  private BigDecimal payment;

  /**
   * 拆零系数
   */
  @ApiModelProperty(value = "拆零系数")
  private Integer chaiLingNum;

  /**
   * 平台分摊优惠明细
   */
  @ApiModelProperty(value = "平台分摊优惠明细")
  private String detailDiscount;


  @ApiModelProperty(value = "商品储存方式枚举")
  private Integer storageType;

  /**
   * 是否是组合商品 0否1是
   */
  @ApiModelProperty(value = "是否是组合商品 0否1是")
  private Integer isJoint;

  /**
   * 药品类型 OTC甲类(0)/处方(1)/OTC乙类(2)/非药品(3)/OTC(4)
   */
  @ApiModelProperty(value = "药品类型 OTC甲类(0)/处方(1)/OTC乙类(2)/非药品(3)/OTC(4)")
  private Integer drugType;

  /**
   * 换货关系码（换货商品前后此致都相同） 迁移数据放的是换货前的ERPCODE
   */
  @ApiModelProperty(value = "换货关系码")
  private String oldErpCode;

  /**
   * 是否是医保商品
   */
  private Boolean isMedicareItem;


  /**
   * 流水号
   */
  private Long serialNumber;

  @ApiModelProperty(value = "商品加权成本")
  private BigDecimal averagePrice;


  private String fiveClass;
  private String fiveClassName;

}
