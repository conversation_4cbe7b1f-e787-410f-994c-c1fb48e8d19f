package com.yxt.order.atom.sdk.common.data;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 线上门店配置
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DsOnlineStoreConfigDTO implements Serializable {

    private static final long serialVersionUID=1L;

    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 线上门店id
     */
    private Long onlineStoreId;

    /**
     * 服务模式，1o2o
     */
    private Integer serviceType;

    /**
     * 配送模式，员工配送、到店自提、蜂鸟配送、达达配送、美团配送
     */
    private String selfDeliveryType;

    /**
     * 配送费收取方, 1平台，2商家 3，平台向商家收取
     */
    private Integer deliveryFeeTo;

    /**
     * 包装费收取方, 1平台，2，商家
     */
    private Integer packageFeeTo;

    /**
     * 平台优惠支付方, 1平台，2商家
     */
    private Integer platformBenefitOwner;

    /**
     * 佣金比例
     */
    private BigDecimal brokerageRate;

    /**
     * 保底佣金
     */
    private BigDecimal brokerageMinimum;

    /**
     * 是否同步库存,0不同步，1同步
     */
    private Integer syncStock;

    /**
     * 库存同步比例
     */
    private Integer syncStockRatio;

    /**
     * 是否同步价格,0不同步，1同步
     */
    private Integer syncPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

    /**
     * 零售下账时机（1.拣货完成后 2.配送出库后）
     */
    private Integer retailTime;

    /**
     * 商家优惠是否分摊（0.否 1.是）
     */
    private Integer whetherDiscountShare;

    /**
     * 佣金是否分摊(0.否 1.是)
     */
    private Integer whetherCommissionShare;

    /**
            * 接单是否锁库存（0.否 1.是）
            */
    private Integer whetherInventoryLocked;
    /**
    * 是否需要审方（0. 不需要审方  1.审方后作业  2. 拣货复核校验）
    */
    private Integer whetherNeedPrescription;
    /**
     * 审方类型（1.在OMS手工审方，2.推送至审方平台审方）
     */
    private Integer checkingType;
    /**
     * 是否需要拆单（0不拆单 1拆单）
     */
    private Integer whetherSpliteOrder;
    /**
     * '物流回传接口 (0.打印面单 1.商品出库)'
     */
    private Integer logisticsReturnNode;
    /**
     * 服务模式 B2C O2O
     */
    private String serviceMode;

    /**
     * 是否开发票 0.不开，1.开
     */
    private Integer whetherOpenInvoice;

    /**
     * 店铺分类
     */
    private String storeType;

    /**
     * 店铺分类
     */
    private Integer settleMode;

    /**
     * 换货是否校验下账总金额（0.否 1.是）
     */
    private Integer whetherModifyDetailCheckAmount;

    /**
     * 店铺发货加急标识0-正常 1-加急 ，字段为空时默认为正常
     */
    private Integer isUrgent;

    /**
     * 打印模板id
     */
    private Long printTemplateId;

    /**
     * 是否开启拣货复核确认
     */
    private Integer pickConfirm;

    /**
     * 拣货方式：1-人工拣货，2-机器自动拣货
     */
    private Integer pickType;

    /**
     * oms拣货类型：1-oms拣货，0-erp拣货
     */
    private Integer omsPickType;

    /**
     * 同步价格门店编码
     */
    private String syncPriceStoreCode;

    /**
     * 是否同步价格 0不同步 1同步
     */
    private Integer platformSyncPrice;

    /**
     * 配送方式 1平台配送  3自配送
     */
    private Integer deliveryType;

    /**
     * 审核后，是否自动生成面单, 0-否，1-是
     */
    private Integer whetherGenerateSheet;

    /**
     * 扫描发货后，自动打印面单，0-否，1-是
     */
    private Integer whetherAutoPrintSheet;

    /**
     * 系统是否默认批号
     */
    private Integer defaultBatchNo;

    /**
     * 下账支付编码
     */
    private String accountPayCode;

    /**
     * B2C店铺配置扩展信息 存放定制配置
     * @see B2CExtendConf
     * */
    private String b2cExtendConf;

    /**
     * 转化扩展信息对象
     * @return OnlineStoreExtendConf
     * */
    public B2CExtendConf extendConfTransferObject(){
        if (StringUtils.isEmpty(this.b2cExtendConf)){
            return new B2CExtendConf();
        }
        return JSON.parseObject(this.b2cExtendConf, B2CExtendConf.class);
    }

    /**
     * 扩展信息转化为json字符串
     * */
    public void objectToExtendConfJson(B2CExtendConf conf) {
        if (Objects.isNull(conf)) {
            throw new NullPointerException("OnlineStoreExtendConf is null");
        }
        this.b2cExtendConf = JSON.toJSONString(conf);
    }

    @Data
    public static class B2CExtendConf {

        //erp拣货配置
        @ApiModelProperty(value = "erp拣货配置")
        private ErpPickConf erpConf;

        //oms拣货配置
        @ApiModelProperty(value = "oms拣货配置")
        private OmsPickConf omsConf;


    }
    /**
     * erp拣货配置
     * */
    @Data
    public static class ErpPickConf{

        @ApiModelProperty(value = "店铺序号")
        private String storeNo;

        @ApiModelProperty(value = "订单类型")
        private String orderType;

        @ApiModelProperty(value = "支付编码")
        private String paymentMethod;
    }

    /**
     * Oms拣货配置
     * */
    @Data
    public static class OmsPickConf{

        @ApiModelProperty(value = "成本中心编码")
        private String costCenterCode;

        @ApiModelProperty(value = "订单审核后自动打印发货单 0:否 1:是")
        private Integer auditAutoPrintInvoice;

        @ApiModelProperty(value = "拣货完成后自动发货 0:否 1:是")
        private Integer pickAutoSend;

        @ApiModelProperty(value = "发货后自动打印发货单 0:否 1:是")
        private Integer sendAutoPrintInvoice;
    }
}
