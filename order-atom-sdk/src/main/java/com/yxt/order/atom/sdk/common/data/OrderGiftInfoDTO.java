package com.yxt.order.atom.sdk.common.data;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月11日 15:38
 * @email: <EMAIL>
 */
@Data
public class OrderGiftInfoDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;

  /**
   * 订单号
   */
  private Long orderNo;

  /**
   * 赠品的ERP商品编码
   */
  private String giftErpCode;

  /**
   * 赠品关联的主商品的ERP商品编码
   */
  private String mainErpCode;

  /**
   * 赠品数量
   */
  private Integer giftNum;

  /**
   * 赠品名称
   */
  private String giftName;

  /**
   * 主商品 skuid
   */
  private String mainSkuId;

  /**
   * 赠品 skuid
   */
  private String giftSkuId;

  /**
   * 商品条形编码
   */
  private String barCode;

  /**
   * 生产商
   */
  private String manufacture;

  /**
   * 商品图片
   */
  private String mainPic;

  /**
   * 商品规格
   */
  private String commoditySpec;


  private Integer storageType;

  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;

  /**
   * 数据库有该值
   */
  private Integer orderChildNo = 0;


}
