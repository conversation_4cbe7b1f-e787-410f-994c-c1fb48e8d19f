//package com.yxt.order.atom.sdk.inner;
//
//
//import io.swagger.annotations.ApiModelProperty;
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.NoArgsConstructor;
//import lombok.experimental.Accessors;
//
///**
// * <p>
// * ERP下账金额信息表
// * </p>
// *
// * <AUTHOR>
// * @since 2020-07-27
// */
//@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//public class ErpBillInfoResDto implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    private Long id;
//
//    @ApiModelProperty(value = "系统单号")
//    private Long orderNo;
//
//    /**
//     * 下账配置ID
//     */
//    @ApiModelProperty(value = "下账配置ID")
//    private Long clientConfId;
//
//    /**
//     * 下账总金额
//     */
//    @ApiModelProperty(value = "下账总金额")
//    private BigDecimal billTotalAmount;
//
//    /**
//     * 下账商品金额
//     */
//    @ApiModelProperty(value = "下账商品金额、商品优惠总金额")
//    private BigDecimal billCommodityAmount;
//
//    /**
//     * 平台优惠金额
//     */
//    @ApiModelProperty(value = "平台优惠金额")
//    private BigDecimal platformDiscount;
//
//    /**
//     * 商家优惠金额
//     */
//    @ApiModelProperty(value = "商家优惠金额")
//    private BigDecimal merchantDiscount;
//
//    /**
//     * 商家配送费
//     */
//    @ApiModelProperty(value = "商家配送费")
//    private BigDecimal merchantDeliveryFee;
//
//    /**
//     * 平台配送费
//     */
//    @ApiModelProperty(value = "平台配送费")
//    private BigDecimal platformDeliveryFee;
//
//    /**
//     * 商家包装费
//     */
//    @ApiModelProperty(value = "商家包装费")
//    private BigDecimal merchantPackFee;
//
//    /**
//     * 平台包装费
//     */
//    @ApiModelProperty(value = "平台包装费")
//    private BigDecimal platformPackFee;
//
//    /**
//     * 商品明细优惠金额
//     */
//    @ApiModelProperty(value = "商品明细优惠金额")
//    private BigDecimal detailDiscountAmount;
//
//    @ApiModelProperty(value = "平台收取佣金")
//    private BigDecimal platBrokerageAmount;
//
//    @ApiModelProperty(value = "订单总额")
//    private BigDecimal orderTotalAmount;
//
//    @ApiModelProperty(value = "商家实收金额")
//    private BigDecimal merchantActualAmount;
//
//    /**
//     * 创建时间
//     */
//    @ApiModelProperty(value = "创建时间")
//    private Date createTime;
//
//    /**
//     * 修改时间
//     */
//    @ApiModelProperty(value = "修改时间")
//
//    private Date modifyTime;
//
//    /**
//     * B2C系统订单-发货单
//     */
//    private Long omsOrderNo;
//
//    @ApiModelProperty("手工调整金额")
//    private BigDecimal adjustAmount;
//
//    /**
//     * 获取初始化实例
//     *
//     * @return
//     */
//    public static ErpBillInfoResDto getInstanceInitial() {
//        ErpBillInfoResDto erpBillInfo = new ErpBillInfoResDto();
//        BigDecimal zero = BigDecimal.ZERO;
//        //平台配送费退款金额
//        erpBillInfo.setPlatformDeliveryFee(zero);
//        //平台配送费退款金额
//        erpBillInfo.setMerchantDeliveryFee(zero);
//        //平台包装费退款金额
//        erpBillInfo.setPlatformPackFee(zero);
//        //商家包装费退款金额
//        erpBillInfo.setMerchantPackFee(zero);
//        //商家优惠退款金额
//        erpBillInfo.setMerchantDiscount(zero);
//        // 平台优惠退款金额
//        erpBillInfo.setPlatformDiscount(zero);
//        //佣金退款金额
//        erpBillInfo.setPlatBrokerageAmount(zero);
//        //商品明细优惠金额
//        erpBillInfo.setDetailDiscountAmount(zero);
//        //手工调整金额
//        erpBillInfo.setAdjustAmount(zero);
//        return erpBillInfo;
//    }
//}
