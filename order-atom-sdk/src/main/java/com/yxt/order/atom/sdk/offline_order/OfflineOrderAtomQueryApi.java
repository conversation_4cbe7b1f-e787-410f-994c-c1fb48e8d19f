package com.yxt.order.atom.sdk.offline_order;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.OFFLINE_ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineOrderByDateResDto;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineRefundOrderByDateResDto;
import com.yxt.order.atom.sdk.offline_order.req.CommonOfflineRefundQueryReqDto;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineOrderByDateReqDto;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineRefundOrderByDateReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoQryByScaleBatchReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundAmountReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundInfoQryBatchReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundInfoQryReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.ShardingReqDto;
import com.yxt.order.atom.sdk.offline_order.req.UnionOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.res.CommonOfflineRefundResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderInfoResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundAmountResDTO;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.ShardingResDto;
import com.yxt.order.atom.sdk.offline_order.res.UnionOrderResDto;
import java.util.List;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月01日 14:31
 * @email: <EMAIL>
 */
public interface OfflineOrderAtomQueryApi {

  /**
   * 获取线下单正单的信息
   *
   * @param offlineOrderInfoReqDto
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/get")
  ResponseBase<OfflineOrderInfoResDto> getOfflineOrderInfo(
      @RequestBody @Valid OfflineOrderInfoReqDto offlineOrderInfoReqDto);

  /**
   * 获取线下单正单实付金额和退单的所有金额
   *
   * @param offlineRefundAmountReqDto
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/amount")
  ResponseBase<OfflineRefundAmountResDTO> getOrderRefundAmount(
      @RequestBody @Valid OfflineRefundAmountReqDto offlineRefundAmountReqDto);


  /**
   * 正单详情
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/detail")
  ResponseBase<OfflineOrderDetailResDto> detail(
      @RequestBody @Valid OfflineOrderDetailReqDto detailReqDto);

  /**
   * 父子订单相关信息
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/is-union-order")
  ResponseBase<UnionOrderResDto> unionOrder(
      @RequestBody @Valid UnionOrderReqDto unionOrderReqDto);

  /**
   * 退单详情
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/detail")
  ResponseBase<OfflineRefundOrderDetailResDto> refundDetail(
      @RequestBody @Valid OfflineRefundOrderDetailReqDto refundDetailReqDto);

  /**
   * 根据orderNo查退单详情
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/common-info")
  ResponseBase<CommonOfflineRefundResDto> commonRefundInfo(
      @RequestBody @Valid CommonOfflineRefundQueryReqDto commonOfflineRefundQuery);

  /**
   * 给会员推线下单(只推会员单)
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/data-2-mq")
  ResponseBase<GetOfflineOrderByDateResDto> orderData2MqQuery(
      @RequestBody @Valid GetOfflineOrderByDateReqDto reqDto);

  /**
   * 给会员推线下单-退单(只推会员单)
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/data-2-mq")
  ResponseBase<GetOfflineRefundOrderByDateResDto> refundData2MqQuery(
      @RequestBody @Valid GetOfflineRefundOrderByDateReqDto reqDto);


  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/sharding-value")
  ResponseBase<ShardingResDto> shardingValue(
      @RequestBody @Valid ShardingReqDto shardingReq);

  /**
   * 根据订单号查询，并指定查询范围
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/query/by-scale/batch")
  ResponseBase<List<OfflineOrderDetailResDto>> getOrderInfoBatchByScale(@RequestBody @Valid OfflineOrderInfoQryByScaleBatchReqDto request);

  /**
   * 根据订单号查询，并指定查询范围
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/query/by-scale")
  ResponseBase<OfflineOrderDetailResDto> getOrderInfoByScale(OfflineOrderInfoQryByScaleReqDto request);

  /**
   * 查询退款单，并指定查询范围
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/query/by-scale/batch")
  ResponseBase<List<OfflineRefundOrderDetailResDto>> getRefundInfoBatchByScale(@RequestBody @Valid OfflineRefundInfoQryBatchReqDto request);

  /**
   * 查询退款单，并指定查询范围
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/query/by-scale")
  ResponseBase<OfflineRefundOrderDetailResDto> getRefundInfoByScale(OfflineRefundInfoQryReqDto request);
}
