package com.yxt.order.atom.sdk.imextask;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.IMPORT_EXPORT_ENDPOINT;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月01日 14:31
 * @email: <EMAIL>
 */
public interface ImportExportAtomApi {


  @PostMapping(IMPORT_EXPORT_ENDPOINT + "/save")
  ResponseBase<Boolean> save(
      @RequestBody @Valid SaveImportExportTaskReq req);


  @PostMapping(IMPORT_EXPORT_ENDPOINT + "/list")
  ResponseBase<PageDTO<ImportExportTaskRes>> list(
      @RequestBody QueryImportExportTaskReq queryImportExportTaskReq);



}
