package com.yxt.order.atom.sdk.order_sync.req;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OrderSyncMappingSearchReq {

  /**
   * 同步前业务单号
   */
  private String originBusinessNo;

  /**
   * 同步后业务单号
   */
  private String targetBusinessNo;

  /**
   * 三方业务单号
   */
  private String thirdBusinessNo;

  /**
   * 三方平台编码
   */
  private String thirdPlatformCode;

  /**
   * 机构编码
   */
  private String orgCode;

  /**
   * O2O，B2C
   */
  private String businessType;

  /**
   * ORDER_TO_NEW：正单老模型同步到新模型
   * ORDER_TO_OLD：正单新模型同步到老模型
   * AFTER_SALE_TO_NEW：售后单老模型同步到新模型
   * AFTER_SALE_TO_OLD：售后单新模型同步到老模型
   */
  private String syncType;

}
