package com.yxt.order.atom.sdk.account_info.req;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AccountStateEnum {
  WAIT("待下账"),
  PROCESS("处理中"),
  SUCCESS("成功"),
  FAIL("失败"),
  ;
  private final String desc;

  /**
   * 转换下账状态
   * @param accountState
   * @return
   */
  public static Integer convertAccountState2OrderErpStatus(String accountState){
    if(WAIT.name().equals(accountState)){
      return ErpStateEnum.WAIT_SALE.getCode();
    }else if(SUCCESS.name().equals(accountState)){
      return ErpStateEnum.HAS_SALE.getCode();
    }else if(FAIL.name().equals(accountState)){
      return ErpStateEnum.HAS_SALE_FAIL.getCode();
    }
    return ErpStateEnum.HAS_SALE_FAIL.getCode();
  }
}
