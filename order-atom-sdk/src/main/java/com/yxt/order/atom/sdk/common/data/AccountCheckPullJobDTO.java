package com.yxt.order.atom.sdk.common.data;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class AccountCheckPullJobDTO {

  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 平台编码
   */
  private String platformCode;

  /**
   * 门店编码
   */
  private String clientCode;

  /**
   * 执行状态 1 待执行 2 执行中 3 执行成功 4 执行失败
   */
  private Integer status;

  /**
   * 备注信息
   */
  private String remark;

  /**
   * 拉取开始时间
   */
  private LocalDateTime pullStartTime;

  /**
   * 拉取结束时间
   */
  private LocalDateTime pullEndTime;

  /**
   * 上下文ID
   */
  private String contextId;
}
