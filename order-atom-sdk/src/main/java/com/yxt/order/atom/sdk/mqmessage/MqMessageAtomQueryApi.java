package com.yxt.order.atom.sdk.mqmessage;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.MQ_MESSAGE_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageQueryReqDto;
import com.yxt.order.atom.sdk.mqmessage.res.MqMessageResDto;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月01日 14:31
 * @email: <EMAIL>
 */
public interface MqMessageAtomQueryApi {

  @PostMapping(MQ_MESSAGE_ENDPOINT + "/list")
  ResponseBase<List<MqMessageResDto>> list(
      @RequestBody MqMessageQueryReqDto mqMessageQueryReqDto);

}
