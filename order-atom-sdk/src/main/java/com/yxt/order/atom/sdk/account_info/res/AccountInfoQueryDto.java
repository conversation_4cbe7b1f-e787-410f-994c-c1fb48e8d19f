package com.yxt.order.atom.sdk.account_info.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class AccountInfoQueryDto {

  private Long id;

  /**
   * 三方平台订单号
   */
  @ApiModelProperty(value = "三方平台订单号")
  private String thirdOrderNo;

  /**
   * 系统订单号
   */
  @JsonSerialize(using = ToStringSerializer.class)
  @ApiModelProperty(value = "系统订单号")
  private Long orderNo;

  /**
   * O2O / B2C
   */
  @ApiModelProperty(value = "O2O / B2C")
  private String serviceMode;

  /**
   * 订单类型 NORMAL-普通订单  POST_FEE-邮费单
   */
  @ApiModelProperty(value = "订单类型 NORMAL-普通订单  POST_FEE-邮费单")
  private String orderType;

  /**
   * HD_H1-海典H1  HD_H2-海典H2  KC-科传
   */
  @ApiModelProperty(value = "HD_H1-海典H1  HD_H2-海典H2  KC-科传")
  private String posMode;

  /**
   * OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS
   */
  @ApiModelProperty(value = "OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS")
  private String pickType;

  /**
   * 三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康
   */
  @ApiModelProperty(value = "三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康")
  private String thirdPlatCode;

  /**
   * 所属机构编码
   */
  @ApiModelProperty(value = "所属机构编码")
  private String organizationCode;

  @ApiModelProperty(value = "所属机构名称")
  private String organizationName;

  /**
   * 下账机构编码 传入到pos下账的机构编码
   */
  @ApiModelProperty(value = "下账机构编码 传入到pos下账的机构编码")
  private String accOrganizationCode;

  @ApiModelProperty(value = "下账机构名称 传入到pos下账的机构名称")
  private String accOrganizationName;


  /**
   * 下账机构店铺id
   * */
  @ApiModelProperty(value = "下账机构店铺id")
  private Long  accOnlineStoreId;

  /**
   * 买家实付金额
   */
  @ApiModelProperty(value = "买家实付金额")
  private BigDecimal buyerActualAmount;

  /**
   * 商家实收金额 = 商品明细的下账金额汇总+商家配送+平台配送费+商家包装费+平台包装费+商家优惠金额+平台优惠金额+商品明细优惠+平台收取佣金
   */
  @ApiModelProperty(value = "商家实收金额 = 商品明细的下账金额汇总+商家配送+平台配送费+商家包装费+平台包装费+商家优惠金额+平台优惠金额+商品明细优惠+平台收取佣金")
  private BigDecimal merchantActualReceive;

  /**
   * 商品总额 = 商品明细的商品金额汇总
   */
  @ApiModelProperty(value = "商品总额 = 商品明细的商品金额汇总")
  private BigDecimal goodsTotalAmount;

  /**
   * 下账商品总金额
   */
  @ApiModelProperty(value = "下账商品总金额")
  private BigDecimal billCommodityAmount;

  /**
   * 商家配送费，金额大于等于0
   */
  @ApiModelProperty(value = "商家配送费，金额大于等于0")
  private BigDecimal deliveryFee;

  /**
   * 商家包装费，金额大于等于0
   */
  @ApiModelProperty(value = "商家包装费，金额大于等于0")
  private BigDecimal packageFee;

  /**
   * 商家优惠金额
   */
  @ApiModelProperty(value = "商家优惠金额")
  private BigDecimal merchantDiscount;

  /**
   * 医保金额
   */
  @ApiModelProperty(value = "医保金额")
  private BigDecimal medicareAmount;

  /**
   * 支付方式编码
   */
  @ApiModelProperty(value = "支付方式编码")
  private String payCode;

  /**
   * 支付渠道 如微信支付
   */
  @ApiModelProperty(value = "支付渠道 如微信支付")
  private String payChannel;

  /**
   * 订单接单时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "订单接单时间")
  private LocalDateTime orderAcceptTime;

  /**
   * 订单操作人  O2O-拣货人 id  B2C-发货人id
   */
  @ApiModelProperty(value = "订单操作人  O2O-拣货人 id  B2C-发货人id")
  private String orderOperatorId;

  /**
   * 微商城会员编号
   */
  @ApiModelProperty(value = "微商城会员编号")
  private String memberNo;

  /**
   * B2C订单下账专用字段 成本中心编码
   */
  @ApiModelProperty(value = "B2C订单下账专用字段 成本中心编码")
  private String costCenterCode;

  /**
   * 下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
   */
  @ApiModelProperty(value = "下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败")
  private String state;

  /**
   * 下账时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "下账时间")
  private LocalDateTime accountTime;

  /**
   * erp零售流水号 下账成功返回
   */
  @ApiModelProperty(value = "erp零售流水号 下账成功返回")
  private String saleNo;

  /**
   * 下账失败原因 下账失败返回
   */
  @ApiModelProperty(value = "下账失败原因 下账失败返回")
  private String accountErrMsg;


  /**
   * 创建时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "创建时间")
  private LocalDateTime createTime;

  /**
   * 更新时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "更新时间")
  private LocalDateTime updateTime;


  @ApiModelProperty(value = "商品信息")
  private List<AccountItemDto> items;
}
