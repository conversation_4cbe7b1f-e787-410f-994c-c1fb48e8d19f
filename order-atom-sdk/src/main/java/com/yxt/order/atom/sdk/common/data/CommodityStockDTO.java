package com.yxt.order.atom.sdk.common.data;


import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @author: xin.tu
 * @date: 2023/1/6 10:06
 * @menu:
 */
@Data
public class CommodityStockDTO implements Serializable {


  private Long id;

  private String merCode;

  private String orderNo;

  private String erpCode;

  private String onlineStoreCode;

  private Long orderDetailId;

  private Integer stockQty;

  private Integer type;

  private Date createTime;

  private String organizationCode;

  /**
   * 流水号
   */
  private Long serialNumber;

  private String storeId;

  /**
   * 第三方详情ID
   */
  private String thirdDetailId;
}
