package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.common.es.EsPageRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("门店订单分页查询")
public class EsOrderWorldOrderPageQueryReq extends EsPageRequestDTO {

  @ApiModelProperty(value = "查询条件列表")
  @NotEmpty(message = "筛选条件不能为空")
  private List<EsOrderWorldOrderSearchCondition> searchConditionList;

}
