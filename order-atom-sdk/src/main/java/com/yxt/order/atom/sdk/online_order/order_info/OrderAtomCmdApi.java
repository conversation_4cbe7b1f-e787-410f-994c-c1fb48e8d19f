package com.yxt.order.atom.sdk.online_order.order_info;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.SaveOrderOptionalReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.UpdateOrderOptionalReq;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年02月23日 14:11
 * @email: <EMAIL>
 */
public interface OrderAtomCmdApi {

  @PostMapping(ORDER_ENDPOINT + "/order/save")
  ResponseBase<Boolean> saveOptional(@RequestBody SaveOrderOptionalReq req);


  @PostMapping(ORDER_ENDPOINT + "/order/update")
  ResponseBase<Boolean> updateOptional(@RequestBody UpdateOrderOptionalReq req);


}
