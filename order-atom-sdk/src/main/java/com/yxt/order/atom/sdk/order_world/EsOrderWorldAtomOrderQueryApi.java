package com.yxt.order.atom.sdk.order_world;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderBaseReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderCountStatisticReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderCountWithConditionReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderPageQueryReq;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldCountStatisticRes;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldOrderInfoRes;
import com.yxt.order.common.es.EsPageDTO;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface EsOrderWorldAtomOrderQueryApi {

  String ORDER_ENDPOINT = "/1.0";

  @PostMapping(ORDER_ENDPOINT + "/es/order-world/order/count/statistic")
  ResponseBase<EsOrderWorldCountStatisticRes> orderCountStatistic(@RequestBody @Valid EsOrderWorldOrderCountStatisticReq request);

  @PostMapping(ORDER_ENDPOINT + "/es/order-world/order/page/query")
  ResponseBase<EsPageDTO<EsOrderWorldOrderInfoRes>> orderPageQuery(@RequestBody @Valid EsOrderWorldOrderPageQueryReq request);

  @PostMapping(ORDER_ENDPOINT + "/es/order-world/order/detail")
  ResponseBase<EsOrderWorldOrderInfoRes> orderDetailQuery(@RequestBody @Valid EsOrderWorldOrderBaseReq request);

  @PostMapping(ORDER_ENDPOINT + "/es/order-world/order/count/with-condition")
  ResponseBase<String> orderCountWithCondition(@RequestBody @Valid EsOrderWorldOrderCountWithConditionReq request);
}
