package com.yxt.order.atom.sdk.order_info.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/19 9:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderPageReqDto extends Authority {

    @ApiModelProperty(value = "订单状态")
    private Integer orderState;

    @ApiModelProperty(value = "线下门店编码")
    private String organizationCode;

    @ApiModelProperty("发货单号")
    private String omsShipNo;

    @ApiModelProperty(value = "三方订单号")
    private String thirdOrderNo;

    @ApiModelProperty(value = "买家昵称")
    private String buyerName;

    @ApiModelProperty(value = "收货人名称")
    private String receiverName;

    @ApiModelProperty(value = "下单开始时间")
    private String beginTime;

    @ApiModelProperty(value = "下单结束时间")
    private String endTime;

    @ApiModelProperty(value = "系统订单号")
    private String orderNo;
    @JsonIgnore
    private Integer isPrescription;

    @ApiModelProperty(value = "平台审方状态： -1 无需审方 0待审方 1审方通过")
    @Min(value = -1, message = "平台审方状态暂只支持: 无需审方、待审方、审方通过")
    @Max(value = 1, message = "平台审方状态暂只支持: 无需审方、待审方、审方通过")
    private Integer prescriptionStatus;

    @ApiModelProperty(value = "时间类型：1下单时间，2付款时间，3审核时间，4发货时间，5完成时间，6下账时间")
    private Integer timeType;

    private Integer orderType;
    @ApiModelProperty(value = "订单拆分状态：0 未拆单，1 拆分订单的源头订单，2 非源头的拆分订单")
    private Integer splitStatus;

    private String areas;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "手机号码")
    private String receiverPhone;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "系统备注")
    private String remark;


    @ApiModelProperty(value = "金额下限")
    private BigDecimal minAmount;

    @ApiModelProperty(value = "金额上限")
    private BigDecimal maxAmount;

    @ApiModelProperty(value = "快递公司")
    private Long expressId;

    @ApiModelProperty(value = "支付方式,1是在线支付,2是货到付款")
    private String payType;

    @ApiModelProperty(value = "店铺分类id")
    private Integer classifyId;

    @ApiModelProperty(value = "发货状态: 10 待拣货, 20 待生成面单, 25 待打印面单, 30 待发货, 40 已发货")
    private Integer shipStatus;

    @ApiModelProperty(value = "快递面单状态：0 未生成，1 未打印，2 已打印")
    private String sheetStatus;

    @ApiModelProperty(value = "打印平台")
    private String printPlatformCode;
    @ApiModelProperty(value = "打印模板id")
    private String templateId;

    @ApiModelProperty("最小订单商品总数量")
    private Integer goodsQtyMin;

    @ApiModelProperty("最大订单商品总数量")
    private Integer goodsQtyMax;

    @ApiModelProperty("最小订单商品种类数")
    private Integer goodsCategoryQtyMin;

    @ApiModelProperty("最大订单商品种类数")
    private Integer goodsCategoryQtyMax;

    @ApiModelProperty("自定义排序配置")
    //   1按订单下单时间，先下单的在前边
    //   2按订单下单时间，后下单的在前边
    //   3将相同商品挨在一起，商品数量由多到少排序
    //   4将相同商品挨在一起，按编码大小排序
    //   5按审核时间，先审核的前面
    //   6按审核时间，后审核的前面
    private String normalSearchOrderConfig;

    @ApiModelProperty("1 有备注 2 无备注 0 查全部")
    private Integer remarkType = 0;

    @ApiModelProperty("备注信息")
    private String remarkText;

    private OrderTag tag;

    @ApiModelProperty(value="服务商编码  用于查询传参 不需要作为前台入参" )
    private String supplierCode;

    @ApiModelProperty(value="商户编码  用于查询传参 不需要作为前台入参")
    private String merCode;

    @ApiModelProperty("推广门店集合")
    private List<String> spreadStoreCodes;

    @ApiModelProperty("供应商编码集合 无供应商的默认填空 履约方-商家履约则为0，服务商履约则为服务商编码")
    private List<String> supplierCodes;

    @ApiModelProperty(value="商户编码集合，用于服务商登录查询")
    private List<String> merCodes;
    @ApiModelProperty(value="订单类型 0为商户，1为供应商" )
    private Integer orderOwnerType;

    @ApiModelProperty(value = "备货状态 2-已备货 1-待备货 -1-全部",notes = "云仓订单专用")
    private String stockState;

    @ApiModelProperty(value = "是否有退款标记，0-无,1-有 -1-全部",notes = "云仓订单专用")
    private String refundFlag;

    @ApiModelProperty(value="待审核状态订单查询创建时间" )
    private String waitAuditCreateTime;

    @ApiModelProperty("ERP审核中")
    private Integer erpAuditStatus;


    @ApiModelProperty(value = "需要排除的商品编码")
    private List<String> commodityCodeList;

    @ApiModelProperty(value = "平台订单号集合")
    private List<String> thirdOrderNoList;


    @ApiModelProperty(value = "是否查询异常单 1-查询")
    private Integer isQueryException;

    @ApiModelProperty(value = "发货单打印状态 0-未打印 1-已打印")
    private Integer shipPrintStatus;
}
