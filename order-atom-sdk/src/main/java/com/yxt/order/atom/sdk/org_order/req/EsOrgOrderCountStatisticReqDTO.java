package com.yxt.order.atom.sdk.org_order.req;

import com.yxt.order.types.order.enums.OrgOrderCountStatisticType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@ApiModel("订单数量统计")
@Data
public class EsOrgOrderCountStatisticReqDTO {

  @ApiModelProperty(value = "筛选条件")
  private List<EsOrgOrderSearchConditionDTO> searchConditionList;

  @ApiModelProperty(value = "聚合条件")
  @NotNull(message = "聚合条件不能为空")
  private OrgOrderCountStatisticType statisticType;
}
