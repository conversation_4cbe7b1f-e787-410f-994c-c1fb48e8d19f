package com.yxt.order.atom.sdk.common.order_world;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class RefundOrderPayDTO {

    private Long id;

    /**
     * 内部退单号,自己生成
     */
    private String refundNo;

    /**
     * 退款支付类型
     */
    private String refundPayType;

    /**
     * 退款支付名称
     */
    private String refundPayName;

    /**
     * 退款支付金额
     */
    private BigDecimal refundPayAmount;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 数据版本，每次update+1
     */
    private Long version;

    /**
     * 平台编码
     */
    private String thirdPlatformCode;

    /**
     * 平台售后单号
     */
    private String thirdAfterOrderNo;

    /**
     * 退款唯一编码
     */
    private String thirdRefundPayNo;

    /**
     * 平台创建时间
     */
    private LocalDateTime created;

    /**
     * 平台更新时间
     */
    private LocalDateTime updated;

    /**
     * 系统创建时间
     */
    private LocalDateTime sysCreateTime;

    /**
     * 系统更新时间
     */
    private LocalDateTime sysUpdateTime;

}
