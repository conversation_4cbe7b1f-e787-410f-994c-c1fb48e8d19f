package com.yxt.order.atom.sdk.offline_order.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月23日 11:25
 * @email: <EMAIL>
 */
@Data
public class GetOfflineOrderByDateCondition {

  /**
   * 查询条件 格式2024-04-22 00:00:00
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startDate;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endDate;


  /**
   * 分页大小
   */
  private Integer pageSize;


  /**
   * 起始ID
   */
  private Long startId;

}
