package com.yxt.order.atom.sdk.online_order.store.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class The3DsOnlineClientResDto {

  @ApiModelProperty(value = "商户编码")
  private String merCode;


  @ApiModelProperty(value = "appid")
  private String appid;

  @ApiModelProperty(value = "app_secret")
  private String appSecret;

  @ApiModelProperty(value = "网店编码")
  private String onlineClientCode;


  @ApiModelProperty(value = "外部网店编码")
  private String onlineClientOutCode;


  /**
   * 配送方式 1平台配送  3自配送
   */
  @ApiModelProperty(value = "配送方式 1平台配送  3自配送")
  private String deliveryType;


  /**
   * 阿里健康access_token
   */
  private String accessToken;

  /**
   * o2o平台标记
   */
  private String platformCode;


  @ApiModelProperty(value = "平台网店编码")
  private String platformShopId;


  /**
   * 门店编码
   */
  private String onlineStoreCode;

}
