package com.yxt.order.atom.sdk.common.data;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单明细成本价表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderDetailCommodityCostPriceDTO implements Serializable {

  private static final long serialVersionUID = 1L;


  private Long id;

  /**
   * 订单号
   */
  private Long orderNo;

  /**
   * 商品erp编码
   */
  private String erpCode;

  /**
   * 商品批号
   */
  private String makeNo;

  /**
   * 商品批次
   */
  private String batchNo;

  /**
   * 商品成本单价
   */
  private BigDecimal costPrice;

  /**
   * 商品加权成本单价
   */
  private BigDecimal averagePrice;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;


}
