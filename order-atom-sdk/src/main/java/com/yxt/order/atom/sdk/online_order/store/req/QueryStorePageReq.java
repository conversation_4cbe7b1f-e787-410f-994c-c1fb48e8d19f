package com.yxt.order.atom.sdk.online_order.store.req;

import com.yxt.lang.dto.api.PageRequestDTO;
import com.yxt.order.types.order.MerCode;
import com.yxt.order.types.order.ThirdPlatformCode;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStorePageReq extends PageRequestDTO {

  private ThirdPlatformCode platformCode;

  private MerCode merCode;

  private String serviceMode;

  private String onlineClientCode;

  private List<String> storeCodeList;

  private List<String> platformShopIdList;

}
