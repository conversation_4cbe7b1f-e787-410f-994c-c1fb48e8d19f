package com.yxt.order.atom.sdk.offline_order;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.OFFLINE_ORDER_MANAGE_ENDPOINT;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineOrderListReq;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineRefundOrderListReq;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineOrderRes;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineRefundOrderRes;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月01日 14:31
 * @email: <EMAIL>
 */
public interface OfflineOrderAtomManageQueryApi {

  /**
   * 订单列表
   *
   * @param req
   * @return
   */
  @PostMapping(OFFLINE_ORDER_MANAGE_ENDPOINT + "/offline-order/list")
  ResponseBase<PageDTO<OfflineOrderRes>> offlineOrderList(@RequestBody @Valid OfflineOrderListReq req);

  /**
   * 退款列表
   *
   * @param req
   * @return
   */
  @PostMapping(OFFLINE_ORDER_MANAGE_ENDPOINT + "/offline-refund-order/list")
  ResponseBase<PageDTO<OfflineRefundOrderRes>> offlineRefundOrderList(
      @RequestBody @Valid OfflineRefundOrderListReq req);

  // 明细使用之前的明细接口

  @PostMapping(OFFLINE_ORDER_MANAGE_ENDPOINT + "/createOfflineOrderManageIndex")
  Boolean createOfflineOrderManageIndex();

  @PostMapping(OFFLINE_ORDER_MANAGE_ENDPOINT + "/createOfflineRefundOrderManageIndex")
  Boolean createOfflineRefundOrderManageIndex();


}
