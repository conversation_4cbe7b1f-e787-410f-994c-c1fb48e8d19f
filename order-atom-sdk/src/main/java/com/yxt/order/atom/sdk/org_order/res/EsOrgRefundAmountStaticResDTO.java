package com.yxt.order.atom.sdk.org_order.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("退单金额统计")
@AllArgsConstructor
@NoArgsConstructor
public class EsOrgRefundAmountStaticResDTO {

  @ApiModelProperty(value = "退款金额")
  private BigDecimal refundAmount;

}
