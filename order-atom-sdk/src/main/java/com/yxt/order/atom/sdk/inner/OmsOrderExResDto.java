//package com.yxt.order.atom.sdk.inner;
//
//import io.swagger.annotations.ApiModelProperty;
//import java.time.LocalDateTime;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.experimental.Accessors;
//
///**
// * <AUTHOR>
// * @Description
// * @createTime 2021年04月15日
// */
//@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
//public class OmsOrderExResDto {
//    /**
//     * 记录ID
//     */
//    private Long id;
//
//    /**
//     * oms订单id
//     */
//    private Long omsOrderNo;
//
//    /**
//     * oms订单状态：10待审核,30待配送,40待收货,100已完成,102已取消,101已关闭, (特别注意 5是处方单)
//     */
//    private Integer orderStatus;
//
//    /**
//     * 异常状态:  0.无异常 1.异常,具体异常查看枚举类
//     */
//    @ApiModelProperty("异常状态:  0.无异常 1.异常,具体异常查看枚举类")
//    private Integer exType;
//
//    /**
//     * 异常状态描述
//     */
//    @ApiModelProperty("异常状态描述")
//    private String exTypeDesc;
//
//    /**
//     * 异常原因
//     */
//    @ApiModelProperty("异常原因")
//    private String exReason;
//    /**
//     * 异常处理结果 0-未处理 1-已处理
//     */
//    @ApiModelProperty("异常处理结果 0-未处理 1-已处理")
//    private Integer operateStatus;
//
//    /**
//     * 异常处理人
//     */
//    private String operator;
//
//    /**
//     * 异常发生时间
//     */
//    private LocalDateTime busiTime;
//
//    /**
//     * 异常处理时间
//     */
//    private LocalDateTime operateTime;
//
//    /**
//     * 插入数据库时间
//     */
//    private LocalDateTime createTime;
//
//
//    /**
//     * 日志描述 --链路中记录
//     */
//
//    private String extDesc;
//
//}
