package com.yxt.order.atom.sdk.online_order.store.req;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 16:57
 * @email: <EMAIL>
 */

@Data
public class QueryBillConfigReq {

  @ApiModelProperty("商家编码")
  @NotBlank(message = "商家编码不可为空")
  private String merCode;
  @ApiModelProperty("平台编码")
  @NotBlank(message = "平台编码不可为空")
  private String platformCode;
  @ApiModelProperty("网店编码")
  @NotBlank(message = "网店编码不可为空")
  private String clientCode;
  @ApiModelProperty("门店编码")
  private String storeCode;
}
