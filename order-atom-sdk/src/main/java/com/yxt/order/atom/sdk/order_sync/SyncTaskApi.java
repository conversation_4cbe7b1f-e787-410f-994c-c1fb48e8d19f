package com.yxt.order.atom.sdk.order_sync;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.order_world.OrderSyncMappingDTO;
import com.yxt.order.atom.sdk.common.order_world.SyncTaskModelDTO;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncMappingSearchReq;
import com.yxt.order.atom.sdk.order_sync.req.SyncTaskSearchReq;
import java.util.List;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface SyncTaskApi {

  String ORDER_ENDPOINT = "/1.0";

  @PostMapping(ORDER_ENDPOINT + "/sync-task/search")
  ResponseBase<List<SyncTaskModelDTO>> syncTaskSearch(@RequestBody @Valid SyncTaskSearchReq request);

  @PostMapping(ORDER_ENDPOINT + "/sync-task/save/batch")
  ResponseBase<Void> syncTaskSaveBatch(@RequestBody List<SyncTaskModelDTO> request);

  @PostMapping(ORDER_ENDPOINT + "/sync-task/updateById/batch")
  ResponseBase<Void> syncTaskUpdateByIdBatch(@RequestBody List<SyncTaskModelDTO> request);
}
