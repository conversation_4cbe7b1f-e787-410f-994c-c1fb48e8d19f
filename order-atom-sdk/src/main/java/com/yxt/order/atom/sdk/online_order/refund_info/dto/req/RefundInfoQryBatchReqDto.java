package com.yxt.order.atom.sdk.online_order.refund_info.dto.req;

import com.yxt.order.types.order.RefundOrderNo;
import com.yxt.order.types.order.enums.RefundQryScaleEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundInfoQryBatchReqDto {

  @ApiModelProperty(value = "查询规模")
  private List<RefundQryScaleEnum> qryScaleList;

  @ApiModelProperty(value = "内部退款单号")
  private List<RefundOrderNo> refundOrderNoList;


}
