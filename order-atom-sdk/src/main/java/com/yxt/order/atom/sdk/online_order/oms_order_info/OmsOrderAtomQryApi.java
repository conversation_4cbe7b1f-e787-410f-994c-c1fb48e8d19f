package com.yxt.order.atom.sdk.online_order.oms_order_info;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.OmsOrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.SimpleOmsOrderInfoDTO;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.req.OmsOrderInfoQryReqDto;
import com.yxt.order.types.order.OrderNo;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年02月23日 14:11
 * @email: <EMAIL>
 */
public interface OmsOrderAtomQryApi {

  @PostMapping(ORDER_ENDPOINT + "/omsOrder/getOmsOrderInfoList")
  ResponseBase<List<SimpleOmsOrderInfoDTO>> getSimpleOmsOrderList(@RequestBody OrderNo orderno);


  @PostMapping(ORDER_ENDPOINT + "/omsOrder/getOmsOrderInfo")
  ResponseBase<OmsOrderInfoResDto> getOmsOrderInfo(@RequestBody OmsOrderInfoQryReqDto orderNoDto);


}
