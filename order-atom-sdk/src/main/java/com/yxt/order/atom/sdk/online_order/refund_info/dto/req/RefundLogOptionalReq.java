package com.yxt.order.atom.sdk.online_order.refund_info.dto.req;

import com.yxt.order.atom.sdk.common.data.OrderLogDTO;
import com.yxt.order.atom.sdk.common.data.RefundCheckDTO;
import com.yxt.order.atom.sdk.common.data.RefundLogDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundLogOptionalReq {

  private OrderLogDTO orderLog;
  private RefundLogDTO refundLog;
  private RefundCheckDTO refundCheck;

}
