package com.yxt.order.atom.sdk.common.order_world;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class RefundOrderAmountDTO {

  private Long id;
  /**
   * 心云售后单号
   */
  private String refundNo;
  /**
   * 退买家总金额
   */
  private BigDecimal consumerRefund;
  /**
   * 商家退款总金额
   */
  private BigDecimal shopRefund;
  /**
   * 退商家配送费优惠
   */
  private BigDecimal merchantDeliveryDiscountRefund;
  /**
   * 退商家订单级总优惠
   */
  private BigDecimal merchantOrderDiscountRefund;
  /**
   * 退商家商品总优惠
   */
  private BigDecimal merchantCommodityDiscountRefund;
  /**
   * 退交易佣金
   */
  private BigDecimal brokerageRefund;
  /**
   * 退平台配送费优惠
   */
  private BigDecimal platformDeliveryDiscountRefund;
  /**
   * 退平台订单级优惠汇总
   */
  private BigDecimal platformOrderDiscountRefund;
  /**
   * 退平台商品优惠金额
   */
  private BigDecimal platformCommodityDiscountRefund;
  /**
   * 退商家包装费
   */
  private BigDecimal merchantPackRefund;
  /**
   * 退平台包装费
   */
  private BigDecimal platformPackRefund;
  /**
   * 退商家配送费
   */
  private BigDecimal merchantDeliveryRefund;
  /**
   * 退平台配送费
   */
  private BigDecimal platformDeliveryRefund;
  /**
   * 退商品总金额
   */
  private BigDecimal totalRefund;
  /**
   * 退医保金额
   */
  private BigDecimal medicareRefund;
  /**
   * 剩余交易佣金(实时)
   */
  private BigDecimal remainBrokerageAmount;

}
