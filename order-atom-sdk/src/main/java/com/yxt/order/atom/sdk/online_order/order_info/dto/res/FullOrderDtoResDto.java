package com.yxt.order.atom.sdk.online_order.order_info.dto.res;

import com.yxt.order.atom.sdk.common.data.ErpBillInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderDeliveryAddressDTO;
import com.yxt.order.atom.sdk.common.data.OrderDeliveryRecordDTO;
import com.yxt.order.atom.sdk.common.data.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPayInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPrescriptionDTO;
import com.yxt.order.atom.sdk.common.data.OriThirdOrderDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 15:16
 * @email: <EMAIL>
 */
@Data
public class FullOrderDtoResDto implements Serializable {

  private List<OrderDetailDTO> detailDtoList;
  private OrderPayInfoDTO orderPayInfoDto;
  private List<OrderPrescriptionDTO> orderPrescriptionDtoList;
  private OrderInfoDTO orderInfoDto;
  private OrderDeliveryRecordDTO orderDeliveryRecordDto;
  // o2c订单发货地址
  private OrderDeliveryAddressDTO orderDeliveryAddressDto;
  // b2c订单发货地址
  private List<OrderDeliveryAddressDTO> b2cOrderDeliveryAddressDtoList;
  //下账信息
  private ErpBillInfoDTO erpBillInfoDto;
  private List<OrderPickInfoDTO> orderPickInfoDtoList;
  //三方原始明细
  private List<OriThirdOrderDetailDTO> thirdOrderDetailDtoList;


  // orderInfo.getFreightOrderNo() 有值时,才构建下面两个属性
  private OrderInfoDTO freightOrderDto;
  private RefundOrderDTO latestFreightRefundOrderDto; // 获取最新的运费单退单
}
