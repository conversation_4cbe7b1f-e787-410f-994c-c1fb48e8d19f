package com.yxt.order.atom.sdk.common.order_world;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 平台订单优惠信息
 */
@Data
public class PlatformOrderCouponDTO {

  /**
   * 主键
   */
  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 优惠券编码
   */
  private String couponNo;

  /**
   * 优惠券导出编码
   */
  private String openCode;

  /**
   * 优惠券名称
   */
  private String couponName;

  /**
   * 优惠券类型
   */
  private String couponType;

  /**
   * 优惠券面值(String)
   */
  private String couponDenomination;

  /**
   * 使用优惠券金额
   */
  private BigDecimal usedCouponAmount;

  /**
   * 优惠来源: platform 平台 ; oneself 自己; provider
   */
  private String couponSourceType;

  /**
   * 优惠来源方编码
   */
  private String couponSourceCode;

  /**
   * 拓展字段
   */
  private String extendJson;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
