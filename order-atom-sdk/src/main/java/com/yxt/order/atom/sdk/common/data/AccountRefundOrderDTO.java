package com.yxt.order.atom.sdk.common.data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/8/16
 */
@Data
public class AccountRefundOrderDTO {


  /**
   * 系统订单号
   */
  private Long orderNo;

  /**
   * 系统退款单号
   */
  private Long refundNo;

  /**
   * 平台退款单号
   */
  private String thirdRefundNo;

  /**
   * 三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康
   */
  private String thirdPlatCode;

  /**
   * O2O / B2C
   */
  private String serviceMode;

  /**
   * HD_H1-海典H1  HD_H2-海典H2  KC-科传
   */
  private String posMode;

  /**
   * OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS
   */
  private String pickType;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 组织机构父路径id链路 1000-1100-1110-
   */
  private String orgParentPath;

  /**
   * 下账机构编码 传入到pos下账的机构编码
   */
  private String accOrganizationCode;

  /**
   * 下账机构父路径链路
   */
  private String accOrgParentPath;

  /**
   * 退货类型 ：仅退款-ONLY_REFUND 退货退款-RETURN_GOODS_REFUND
   */
  private String refundType;

  /**
   * 退款类型 PART-部分退款 ALL-全额退款
   */
  private String type;

  /**
   * 退款总金额 = 退款商品明细的退款金额汇总+商家配送费退款金额+平台配送费退款金额+商家包装费退款金额+平台包装费退款金额+佣金退款金额+商家优惠退款金额+平台优惠退款金额+商品明细优惠退款金额
   */
  private BigDecimal refundAmount;

  /**
   * 下账退款商品总金额
   */
  private BigDecimal refundGoodsTotal;

  /**
   * 商家配送费，金额大于等于0
   */
  private BigDecimal refundPostFee;

  /**
   * 商品明细优惠
   */
  private BigDecimal discountAmount;

  /**
   * 订单总额 = 商品明细的商品金额汇总+商家配送+平台配送费+商家包装费+平台包装费
   */
  private BigDecimal packageFee;

  /**
   * 退款单接收时间
   * */
  private LocalDateTime refundAcceptTime;

  /**
   * 退款理由
   * */
  private String refundReason;


  /**
   * 下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
   */
  private String state;

  /**
   * erp零售流水号 下账成功返回
   */
  private String saleNo;

  /**
   * 下账时间
   */
  private LocalDateTime accountTime;

  /**
   * 下账失败原因 下账失败返回
   */
  private String accountErrMsg;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 是否删除 0-未删除 时间戳-已删除
   */
  private Long deleted;

  /**
   *  数据版本，每次update+1
   */
  private Long version;

}
