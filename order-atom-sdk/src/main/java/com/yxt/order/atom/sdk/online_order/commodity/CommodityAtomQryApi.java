package com.yxt.order.atom.sdk.online_order.commodity;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.online_order.commodity.req.CommodityStockQueryReq;
import com.yxt.order.atom.sdk.online_order.commodity.res.CommodityStockRes;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 10:13
 * @email: <EMAIL>
 */
public interface CommodityAtomQryApi {

  @PostMapping(ORDER_ENDPOINT + "/commodity-stock/query")
  ResponseBase<List<CommodityStockRes>> queryCommodityStock(
      @RequestBody CommodityStockQueryReq req);

}
