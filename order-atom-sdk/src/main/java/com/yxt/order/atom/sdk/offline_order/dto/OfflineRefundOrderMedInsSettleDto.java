package com.yxt.order.atom.sdk.offline_order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 线下单医保结算信息Dto
 *
 * <AUTHOR> (moatkon)
 * @date 2024年06月21日 11:22
 * @email: <EMAIL>
 */
@Data
public class OfflineRefundOrderMedInsSettleDto {

  private String refundNo;
  private String serialNo;          // 流水号
  private String thirdRefundNo;           // 销售单号
  private String invoiceNo;         // 发票号码(结算ID setl_id)
  private String hospitalName;      // 定点医疗机构名称
  private String name;              // 人员姓名(psn_name)
  private String personType;        // 人员类别(psn_type)
  private String personTypeName;        // 人员类别(psn_type)
  private String personNo;          // 个人编号(人员编号 psn_no)
  private String prescriptionNo;    // 处方号(就诊ID mdtrt_id)
  private String icCardNo;          // IC卡号
  private BigDecimal acctPay;       // 个人账户支付(个人账户支出 acct_pay)
  private BigDecimal fundPay;       // 统筹支出(基金支付总额 fund_pay_sumamt)
  private BigDecimal cashPay;       // 自付现金
  private String medType;           // 医疗类别
  private String medTypeName;           // 医疗类别
  private String transactionType;    // 1-付款 2-退款
  private String transactionCode;   // 交易编码
  private String hospitalCode;      // 定点医疗机构编码
  private Long setlTime;            // 结算时间
  private String clearingType;      // 清算类别
  private String clearingTypeName;      // 清算类别
  private String isRefunded;         // 是否已退款 0-未退款 1-已退款
  private Long refundTime;          // 退款时间
  private String origSerialNo;      // 原流水号
  private Long billTime;            // 下账时间

  private String createdBy;

  private String updatedBy;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdTime;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updatedTime;

  private Long version;

  public void absAmount() {
    acctPay = acctPay.abs();
    fundPay = fundPay.abs();
    cashPay = cashPay.abs();
  }
}
