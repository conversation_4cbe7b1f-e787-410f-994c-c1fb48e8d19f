package com.yxt.order.atom.sdk.online_order.order_info.dto.req;

import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.enums.OrderQryScaleEnum;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderInfoQryByScaleReqDto {

  /**
   * 查询规模
   */
  private List<OrderQryScaleEnum> qryScaleList;

  /**
   * 内部订单号
   */
  private OrderNo orderNo;

}
