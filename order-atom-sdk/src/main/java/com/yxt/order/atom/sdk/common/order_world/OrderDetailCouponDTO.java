package com.yxt.order.atom.sdk.common.order_world;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 线下单明细coupon
 */
@Data
public class OrderDetailCouponDTO {

  /**
   * 主键
   */
  private Long id;

  /**
   * 内部订单号,自己生成
   */
  private String orderNo;

  /**
   * 内部明细编号,自己生成
   */
  private String orderDetailNo;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 优惠券编码
   */
  private String couponNo;

  /**
   * 优惠券名称
   */
  private String couponName;

  /**
   * 优惠券类型 CASH-现金卷 FULL_DISCOUNT - 满减券 DISCOUNT - 折扣券 FULL_REBATE - 满返券
   */
  private String couponType;

  /**
   * 优惠券面值(String)
   */
  private String couponDenomination;

  /**
   * 使用优惠券金额
   */
  private BigDecimal usedCouponAmount;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
