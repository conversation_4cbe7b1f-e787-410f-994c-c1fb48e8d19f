package com.yxt.order.atom.sdk.order_sync.req;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OrderSyncInitErrorLogRemoveReq {


  @ApiModelProperty(value = "日志ID列表")
  private List<Long> logIdList;

  @ApiModelProperty(value = "业务标识ID列表")
  private List<String> bizNoList;

  @ApiModelProperty(value = "刷数类型")
  private String flashType;
}
