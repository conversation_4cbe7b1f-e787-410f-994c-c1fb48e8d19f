package com.yxt.order.atom.sdk.common.order_world;

import java.util.Date;
import lombok.Data;

@Data
public class ExtendDataDTO {

  /**
   * 主键ID
   */
  private Long id;

  /**
   * 业务单号
   */
  private String businessNo;

  /**
   * 子业务单号
   */
  private String subBusinessNo;

  /**
   * 数据类型 需要维护枚举
   */
  private Integer dataType;

  /**
   * 拓展字段
   */
  private String extendJson;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}