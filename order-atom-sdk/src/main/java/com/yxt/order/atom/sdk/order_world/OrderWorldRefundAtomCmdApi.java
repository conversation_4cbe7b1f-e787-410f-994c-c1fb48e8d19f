package com.yxt.order.atom.sdk.order_world;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_world.req.SaveOrderWorldRefundOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderWorldRefundOptionalReq;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface OrderWorldRefundAtomCmdApi {

  @PostMapping(ORDER_ENDPOINT + "/order-world/refund/save/optional")
  ResponseBase<Void> saveOptional(@RequestBody SaveOrderWorldRefundOptionalReq req);

  @PostMapping(ORDER_ENDPOINT + "/order-world/refund/update/optional")
  ResponseBase<Void> updateOptional(@RequestBody UpdateOrderWorldRefundOptionalReq req);

}
