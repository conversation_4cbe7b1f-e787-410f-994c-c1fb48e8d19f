package com.yxt.order.atom.sdk.online_order.order_info.dto.req;

import com.yxt.order.types.order.OrderNo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月06日 10:52
 * @email: <EMAIL>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderInfoQryReqDto {

  /**
   * 查询规模
   */
  private OrderQryScaleEnum qryScale;
  /**
   * 内部订单号
   */
  private OrderNo orderNo;


  @Getter
  public enum OrderQryScaleEnum {
    MAIN(50, "主信息"),
    DETAIL(60, "详细信息"),
    OTHER(80, "其他信息"),
    ALL(100, "所有信息");
    private final Integer code;
    private final String msg;

    OrderQryScaleEnum(Integer code, String msg) {
      this.code = code;
      this.msg = msg;
    }
  }


}
