package com.yxt.order.atom.sdk.online_order.account_check.req;

import com.yxt.lang.dto.api.PageRequestDTO;
import com.yxt.order.types.order.MerCode;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class AccountCheckPullJobPageQueryReqDto extends PageRequestDTO {

  @ApiModelProperty(value = "对账单拉取job状态")
  private Integer jobStatus;

  @ApiModelProperty(value = "上下文id")
  private String contextId;

  @ApiModelProperty(value = "商户编码")
  private MerCode merCode;

  @ApiModelProperty(value = "平台编码")
  private PlatformCodeEnum platformCode;

  @ApiModelProperty(value = "网店编码")
  private List<String> clientCodeList;
}
