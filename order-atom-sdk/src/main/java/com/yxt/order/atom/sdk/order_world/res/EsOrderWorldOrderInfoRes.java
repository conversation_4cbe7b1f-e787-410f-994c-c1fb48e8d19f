package com.yxt.order.atom.sdk.order_world.res;

import com.yxt.order.types.offline.enums.StoreDirectJoinTypeEnum;
import com.yxt.order.types.order_world.OrderFlag;
import com.yxt.order.types.order_world.OrderType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class EsOrderWorldOrderInfoRes {

  /**
   * 系统单号
   */
  private String orderNo;

  /**
   * 父系统单号
   */
  private String parentOrderNo;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 父三方订单号
   */
  private String parentThirdOrderNo;


  /**
   * 平台下单时间
   */
  private LocalDateTime created;

  /**
   * 平台下单日期
   */
  private String createdDay;

  /**
   * 创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 支付时间
   */
  private LocalDateTime payTime;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 线上门店编码
   */
  private String onlineStoreCode;

  /**
   * 下单线下机构编码
   */
  private String organizationCode;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  private String launchOrganizationCode;

  /**
   * 子公司编码
   */
  private String companyCode;

  /**
   * 订单状态
   *
   * @see com.yxt.order.types.order_world.OrderMainStatus
   */
  private String orderMainStatus;

  /**
   * 支付状态
   *
   * @see com.yxt.order.types.order_world.OrderPaymentStatus
   */
  private String paymentStatus;

  /**
   * 交易场景 ONLINE-线上订单 OFFLINE-线下订单
   */
  private String transChannel;

  /**
   * 三方平台编码
   *
   * @see com.yxt.order.types.order.enums.PlatformCodeEnum
   */
  private String thirdPlatformCode;

  /**
   * 服务模式 O2O B2C JOIN_B2B
   *
   * @see com.yxt.order.types.order_world.BusinessType
   */
  private String businessType;

  /**
   * 订单标记，通过空格分割，使用match匹配
   *
   * @see OrderFlag
   */
  private List<String> orderFlags;

  /**
   * 订单类型，通过空格分割，使用match匹配
   *
   * @see OrderType
   */
  private List<String> orderTypes;

  /**
   * 异常类型
   */
  private List<String> abnormalType;

  /**
   * 订单金额
   */
  private BigDecimal orderAmount;

  /**
   * 订单明细
   */
  private List<EsOrderWorldOrderDetailResDTO> detailList;

  /**
   * 会员编码(唯一值)
   */
  private String userCardNo;

  /**
   * 会员ID (心云)
   */
  private String userId;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   *
   * @see StoreDirectJoinTypeEnum
   */
  private String storeDirectJoinType;

  /**
   * 支付方式,多个使用空格分割，使用match匹配
   */
  private List<String> payTypes;

  /**
   * 是否起效
   */
  private Long valid;

  /**
   * 全局拦截锁,0正常  ,非0都需要拦截
   */
  private Integer lockForWorld;

}
