package com.yxt.order.atom.sdk.common.data;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退款单日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RefundLogDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;

  /**
   * 退款单号
   */
  private Long refundNo;

  /**
   * 订单号
   */
  private Long orderNo;

  /**
   * 操作员id
   */
  private String operatorId;

  /**
   * 操作描述
   */
  private String operateDesc;

  /**
   * 操作后状态
   */
  private Integer state;

  /**
   * 下账状态
   */
  private Integer erpState;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;

  /**
   * 操作前的状态
   */
  private Integer stateBefore;


}
