package com.yxt.order.atom.sdk.online_order.merchant;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.online_order.merchant.req.MerchantConfigReq;
import com.yxt.order.atom.sdk.online_order.merchant.res.MerchantConfigRes;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 9:34
 * @email: <EMAIL>
 */
public interface MerchantAtomQryApi {

  @PostMapping(ORDER_ENDPOINT + "/merchant/query")
  ResponseBase<MerchantConfigRes> queryMerchantConfig(@RequestBody MerchantConfigReq req);

}
