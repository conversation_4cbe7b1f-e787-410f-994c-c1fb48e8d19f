package com.yxt.order.atom.sdk.mongo.req;

import java.util.Date;
import lombok.Data;
/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月05日 11:00
 * @email: <EMAIL>
 */
@Data
public class MqDataReqDto {

  private MqData mqData;

  private String collectionName;

  @Data
  public static class MqData {
    private String id;  // 使用 ObjectId 类型

    private String message;
    // 存储消息自身的创建时间
    private Long createTime = System.currentTimeMillis() / 1000;

    private Date createDateTime;
  }

}
