package com.yxt.order.atom.sdk.offline_order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class OfflineOrderPromotionDTO implements Serializable {
    private Long id;

    private String orderNo;

    private String erpCode;

    private BigDecimal commodityCount;

    private String thirdOrderNo;

    private String promotionNo;

    private String subPromotionNo;

    private String promotionType;

    private BigDecimal promotionAmount;

    private String createdBy;

    private String updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    private Long version;

    private String type;

    private String extendJson;

}