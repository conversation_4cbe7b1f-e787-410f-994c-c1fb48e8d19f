package com.yxt.order.atom.sdk.online_order.store.res;

import com.yxt.order.atom.sdk.common.data.DsOnlineStoreConfigDTO;
import com.yxt.order.atom.sdk.common.data.DsOnlineStoreDTO;
import com.yxt.order.atom.sdk.common.data.DsStoreOrderConfigDTO;
import com.yxt.order.atom.sdk.common.data.DsStoreRiderPollingConfigDTO;
import com.yxt.order.atom.sdk.common.data.DsStoreSoundConfigDTO;
import com.yxt.order.atom.sdk.common.data.StoreBillConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class StoreQueryByScaleResDto {

  @ApiModelProperty(value = "门店信息")
  private DsOnlineStoreDTO onlineStore;

  @ApiModelProperty(value = "门店配置")
  private DsOnlineStoreConfigDTO storeConfig;

  @ApiModelProperty(value = "订单配置")
  private DsStoreOrderConfigDTO orderConfig;

  @ApiModelProperty(value = "骑手循环呼叫配置")
  private List<DsStoreRiderPollingConfigDTO> riderPollingConfigList;

  @ApiModelProperty(value = "语音播报配置")
  private DsStoreSoundConfigDTO soundConfig;

  @ApiModelProperty(value = "下账配置")
  private StoreBillConfigDTO billConfig;
}
