package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.types.order_world.OrderSearchConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "订单查询条件")
public class EsOrderWorldOrderSearchCondition {

  @ApiModelProperty("查询条件类型")
  private OrderSearchConditionEnum searchType;

  @ApiModelProperty("条件值")
  private String searchData;

}
