package com.yxt.order.atom.sdk.online_order.refund_info;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.data.ErpRefundInfoDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.ErpRefundQueryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryBatchReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundWithOrderQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.res.RefundInfoQryResDto;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface RefundAtomQryApi {

  /**
   * 退款单信息查询
   */
  @PostMapping(ORDER_ENDPOINT + "/refund/query/by-scale")
  ResponseBase<RefundInfoQryResDto> queryRefundInfo(@RequestBody RefundInfoQryReqDto request);


  /**
   * 退款单信息批量查询
   */
  @PostMapping(ORDER_ENDPOINT + "/refund/query/by-scale/batch")
  ResponseBase<List<RefundInfoQryResDto>> queryRefundInfoBatch(@RequestBody RefundInfoQryBatchReqDto request);

  /**
   * 退单主信息+明细
   */
  @PostMapping(ORDER_ENDPOINT + "/refund/latest/query")
  ResponseBase<RefundOrderDTO> queryLatestRefundInfo(@RequestBody RefundWithOrderQryReqDto request);

  /**
   * 退单主信息+明细
   */
  @PostMapping(ORDER_ENDPOINT + "/refund/erp/query")
  ResponseBase<List<ErpRefundInfoDTO>> queryErpRefund(@RequestBody ErpRefundQueryReqDto request);


}
