package com.yxt.order.atom.sdk.org_order.req;

import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class OrgOrderFlashToEsWithJobReqDTO {

  /**
   * 开始刷数时间
   */
  @NotBlank(message = "flashStartTime can not null")
  private String flashStartTime;

  /**
   * 结束刷数时间
   */
  @NotBlank(message = "flashEndTime can not null")
  private String flashEndTime;

  /**
   * 线上单：ONLINE 线下单：OFFLINE
   */
  private String orderSource;

  /**
   * 正单：ORDER 退款单：REFUND
   */
  private String orderType;

}
