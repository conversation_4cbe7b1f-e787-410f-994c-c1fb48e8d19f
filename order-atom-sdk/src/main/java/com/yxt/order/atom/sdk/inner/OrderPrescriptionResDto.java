//package com.yxt.order.atom.sdk.inner;
//
//import io.swagger.annotations.ApiModelProperty;
//import java.io.Serializable;
//import java.util.Date;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.experimental.Accessors;
//
///**
// * <p>
// * 订单处方信息表
// * </p>
// *
// * <AUTHOR>
// * @since 2020-03-10
// */
//@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
//public class OrderPrescriptionResDto implements Serializable {
//
//    private static final long serialVersionUID=1L;
//
//    @ApiModelProperty(value="记录id")
//    private Long id;
//
//    /**
//     * 订单号
//     */
//    @ApiModelProperty(value="电商云平台订单号")
//    private Long orderNo;
//
//
//    private Long omsOrderNo;
//    /**
//     * 用药人
//     */
//    @ApiModelProperty(value="用药人")
//    private String usedrugName;
//
//    /**
//     * 性别 1:男 0：女
//     */
//    @ApiModelProperty(value="性别 1:男  0:女")
//    private Integer sex;
//
//    /**
//     * 身份证
//     */
//    @ApiModelProperty(value="身份证号")
//    private String identityNumber;
//
//    /**
//     * 生日
//     */
//    @ApiModelProperty(value="出生日期")
//    private Date birthday;
//
//    /**
//     * 手机号码
//     */
//    @ApiModelProperty(value="手机号码")
//    private String phoneNumber;
//
//    /**
//     * 处方原始图片地址
//     */
//    @ApiModelProperty(value="处方原始图片")
//    private String picurl;
//
//    /**
//     * 处方图片地址
//     */
//    @ApiModelProperty(value="处方地址图片")
//    private String cfpicurl;
//
//    /**
//     * 平台编码
//     */
//    @ApiModelProperty(value="平台编码")
//    private String ectype;
//
//    /**
//     * 审核状态码
//     */
//    @ApiModelProperty(value="审核状态码 0：未审核，1：审核成功，2：审核失败")
//    private Integer status;
//    /**
//     * 审核描述
//     */
//    @ApiModelProperty(value="审核描述")
//    private String  description;
//
//    /**
//     * 审核人
//     */
//    @ApiModelProperty(value="审核人")
//    private String  checkName;
//
//    /**
//     * 审核图片地址
//     */
//    @ApiModelProperty(value="审核后的地址图片")
//    private String checkPic;
//
//    /**
//     * 处方类型
//     */
//    @ApiModelProperty(value="处方类型  1：西药 0：中药")
//    private  Integer prescriptionType;
//
//    /**
//     * 备注
//     */
//    @ApiModelProperty(value="备注")
//    private String  remark;
//    /**
//     * 用药人年龄
//     */
//    @ApiModelProperty(value="用药人年龄")
//    private Integer useAge;
//    /**
//     * 开方时间
//     */
//    @ApiModelProperty(value="开方时间")
//    private Date openTime;
//    /**
//     * 审方时间
//     */
//    @ApiModelProperty(value="审方时间")
//    private Date checkTime;
//
//    @ApiModelProperty(value="上次推送时间")
//    private Date lastPushTime;
//
//    @ApiModelProperty(value = "药事云处方申请单号")
//    private String presNo;
//
//    @ApiModelProperty(value = "平台处方id")
//    private String rpId;
//
//    @ApiModelProperty(value = "开方医院名称")
//    private String hospitalName;
//
//    @ApiModelProperty(value = "开方医生")
//    private String doctorName;
//
//    @ApiModelProperty(value = "诊断")
//    private String icdName;
//
//}
