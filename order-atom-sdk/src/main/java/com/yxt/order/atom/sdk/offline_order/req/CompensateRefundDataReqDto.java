package com.yxt.order.atom.sdk.offline_order.req;

import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderOrganizationDTO;
import lombok.Data;

/**
 * 补充退款数据
 * @author: moatkon
 * @time: 2024/11/28 15:52
 */
@Data
public class CompensateRefundDataReqDto {
  private OfflineRefundOrderDTO offlineRefundOrderDTO;
  private OfflineRefundOrderOrganizationDTO offlineRefundOrderOrganizationDTO;
  private OfflineRefundOrderCashierDeskDTO offlineRefundOrderCashierDeskDTO;
}
