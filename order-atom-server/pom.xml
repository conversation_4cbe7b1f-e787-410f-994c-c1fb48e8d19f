<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.order</groupId>
    <artifactId>order-atom-service</artifactId>
    <version>1.0.0</version>
  </parent>

  <groupId>com.yxt.order.atom</groupId>
  <artifactId>order-atom-server</artifactId>
  <packaging>jar</packaging>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    <hippo4j.version>1.4.3</hippo4j.version>
    <spring-amqp.version>2.2.0.RELEASE</spring-amqp.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>orderSync-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>grey-spring-boot-lib</artifactId>
          <groupId>cn.hydee.starter</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.yxt.order.types</groupId>
      <artifactId>order-types</artifactId>
    </dependency>

    <!-- 动态数据源 -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
      <version>3.3.2</version>
    </dependency>
    <!--分库分表-->
    <dependency>
      <groupId>org.apache.shardingsphere</groupId>
      <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
      <version>4.1.1</version>
    </dependency>

    <!-- MyBatis-Plus -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <version>${mybatis-plus.version}</version>
    </dependency>

    <!-- MyBatis-Plus Generator -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
      <version>${mybatis-plus.version}</version>
    </dependency>

    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis-typehandlers-jsr310</artifactId>
      <version>1.0.2</version>
    </dependency>
    <!-- 数据源 -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <version>${druid.version}</version>
    </dependency>

    <!-- 数据库驱动 -->
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-xxljob-spring-boot-starter</artifactId>
      <version>4.1.0</version>
      <exclusions>
        <exclusion>
          <artifactId>logback-classic</artifactId>
          <groupId>ch.qos.logback</groupId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>cn.hydee.ydjia</groupId>
      <artifactId>hydee-middle-member-sdk</artifactId>
      <version>1.2.3-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>com.sap.cloud.db.jdbc</groupId>
      <artifactId>ngdbc</artifactId>
      <version>2.4.70</version>
    </dependency>

    <dependency>
      <groupId>com.yxt.order.atom.open.sdk</groupId>
      <artifactId>order-atom-open-sdk</artifactId>
      <version>1.7.0-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-mongodb</artifactId>
    </dependency>

    <dependency>
      <groupId>com.yxt.order.common</groupId>
      <artifactId>order-common</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>grey-spring-boot-lib</artifactId>
          <groupId>cn.hydee.starter</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--redis依赖-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
      <version>3.21.3</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-web</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-webflux</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.redisson</groupId>
          <artifactId>redisson-spring-data-30</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-data-21</artifactId>
      <version>3.21.3</version>
    </dependency>

    <!--rocket-mq-->
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-spring-boot-starter</artifactId>
      <version>2.2.3</version>
    </dependency>


    <dependency>
      <groupId>org.elasticsearch.client</groupId>
      <artifactId>elasticsearch-rest-high-level-client</artifactId>
      <version>7.14.0</version>
    </dependency>
    <dependency>
      <groupId>org.elasticsearch</groupId>
      <artifactId>elasticsearch</artifactId>
      <version>7.14.0</version>
    </dependency>
    <dependency>
      <groupId>org.dromara.easy-es</groupId>
      <artifactId>easy-es-boot-starter</artifactId>
      <version>2.0.0</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.kafka</groupId>
      <artifactId>spring-kafka</artifactId>
      <version>2.2.6.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-common-wechatrobot</artifactId>
      <version>4.10.3</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba.csp</groupId>
      <artifactId>sentinel-datasource-nacos</artifactId>
      <version>1.8.0</version>
    </dependency>

    <!-- EasyExcel -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>3.2.1</version>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>5.2.3</version>
    </dependency>

    <!-- Apache POI for OOXML format (XLSX) -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>5.2.3</version>
    </dependency>

    <!-- Apache POI OOXML Schemas -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml-schemas</artifactId>
      <version>4.1.2</version>
    </dependency>

    <!-- XMLBeans -->
    <dependency>
      <groupId>org.apache.xmlbeans</groupId>
      <artifactId>xmlbeans</artifactId>
      <version>5.1.1</version>
    </dependency>

    <!--业务通用能力-->
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-common-logic</artifactId>
      <version>4.10.0</version>
    </dependency>

    <dependency>
      <groupId>com.yxt.org</groupId>
      <artifactId>yxt-org-read-open-sdk</artifactId>
      <version>1.0.8</version>
    </dependency>
    <dependency>
      <groupId>com.yxt.org</groupId>
      <artifactId>yxt-org-write-common</artifactId>
      <version>1.0.7</version>
    </dependency>
  </dependencies>


</project>