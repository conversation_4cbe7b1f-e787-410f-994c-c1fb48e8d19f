package com.yxt.order.atom.migration.service;

import static com.yxt.order.atom.common.utils.OrderDateUtils.convertToBeijingTime;

import cn.hutool.core.date.DateUtil;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import junit.framework.TestCase;
import org.junit.Test;

public class MigrationPlatformCodeHandlerTest extends TestCase {

  @Test
  public void testCompareTime() {
    LocalDateTime lo = LocalDateTime.now();
    LocalDateTime localDateTime = lo.minusDays(10);

    Date lastDate = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

    Date now = new Date();
    System.out.println("now: " + now);
    System.out.println("lastDate: " + lastDate);

    System.out.println("lastDate->now: " + DateUtil.compare(lastDate, now));//-1 lastDate在当前日志之前
    System.out.println(
        "now->lastDate: " + DateUtil.compare(now, lastDate));// 1 当前日期在lastDate之后,new Date超前

    System.out.println("now->now: " + DateUtil.compare(now, now));
  }

  @Test
  public void testBeijingTime() {
    Date now = new Date();
    System.out.println(now);
    System.out.println(convertToBeijingTime(now));
  }


}