package com.yxt.order.atom.migration.config;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import org.junit.Test;

public class MigrationConfigTest {

  String s = "YNHX_DATA01\n"
      + "GXHX_USERS\n"
      + "GZHX_USERS\n"
      + "SCHX_USERS\n"
      + "SXHX_USERS\n"
      + "CQHX_USERS\n"
      + "CDHX_USERS\n"
      + "SHHX_DATA01\n"
      + "TJHX_DATA01\n"
      + "HNHX_DATA01\n"
      + "HENHX_DATA01\n"
      + "SXGSHX_DATA01\n"
      + "TJQCHX_DATA01\n"
      + "HENNYHX_DATA01\n"
      + "ZYHX_USERS";

  @Test
  public void printSchema() {

    String[] split = s.split("\n");
    for (String string : split) {
      System.out.println(string);
    }

  }
  @Test
  public void generate() {

    String[] split = s.split("\n");
    for (String string : split) {
      System.out.println(String.format("    - schema: %s\n"
          + "      enable: false\n"
          + "      start-time: 2024-06-21 14:30:00\n"
          + "      end-time: 2024-07-21 14:30:00", string));
    }

  }

  /**
   * 统计数据量
   */
  @Test
  public void generateCount() {

    String[] split = s.split("\n");
    for (String str : split) {
      System.out.println(String.format("select count(1) from %s.XF_TRANSSALESTOTAL union all\n"
          + "select count(1) from %s.XF_TRANSSALESITEM union all\n"
          + "select count(1) from %s.XF_TRANSSALESTENDER; \n", str, str, str));
    }

  }

  /**
   * 统计占用磁盘大小
   */
  @Test
  public void generateDiskSize() {

    String[] split = s.split("\n");
    for (String str : split) {
      String size = "SELECT TABLE_NAME, SCHEMA_NAME,DISK_SIZE  / 1024 / 1024 / 1024  AS GB "
          + "FROM M_TABLE_PERSISTENCE_STATISTICS WHERE SCHEMA_NAME = '" + str
          + "' AND TABLE_NAME = 'XF_TRANSSALESTOTAL' ";

      String size2 = "SELECT TABLE_NAME, SCHEMA_NAME,DISK_SIZE  / 1024 / 1024 / 1024  AS GB "
          + "FROM M_TABLE_PERSISTENCE_STATISTICS WHERE SCHEMA_NAME = '" + str
          + "' AND TABLE_NAME = 'XF_TRANSSALESITEM' ";

      String size3 = "SELECT TABLE_NAME, SCHEMA_NAME,DISK_SIZE  / 1024 / 1024 / 1024  AS GB "
          + "FROM M_TABLE_PERSISTENCE_STATISTICS WHERE SCHEMA_NAME = '" + str
          + "' AND TABLE_NAME = 'XF_TRANSSALESTENDER' ";

      String ot = size + " \n "+ " union all " + size2 + " \n "+ " union all " + size3 + " \n ";
      System.out.println(ot);

    }

  }

  @Test
  public void jsonTest() {
    HanaMigrationDO migrationConfig = new HanaMigrationDO();
    System.out.println(JsonUtils.toJson(migrationConfig));
  }

  @Test
  public void generateSchemaTable() {
    String createTable = "CREATE TABLE XF_TRANSSALESTOTAL_${schema} (\n"
        + "    id INT AUTO_INCREMENT PRIMARY KEY,\n"
        + "    XF_STORECODE VARCHAR(6),\n"
        + "    XF_TILLID VARCHAR(3),\n"
        + "    XF_TXDATE DATE,\n"
        + "    XF_TXSERIAL DOUBLE,\n"
        + "    XF_TXTIME VARCHAR(6),\n"
        + "    XF_TXBATCH DOUBLE,\n"
        + "    XF_DOCNO VARCHAR(10),\n"
        + "    XF_VOIDDOCNO VARCHAR(10),\n"
        + "    XF_TXTYPE DOUBLE,\n"
        + "    XF_TXHOUR DOUBLE,\n"
        + "    XF_CASHIER VARCHAR(10),\n"
        + "    XF_SALESMAN VARCHAR(10),\n"
        + "    XF_CLIENTCODE VARCHAR(12),\n"
        + "    XF_PURCHASESTAFFCODE VARCHAR(10),\n"
        + "    XF_PURCHASEDEPENDENT DOUBLE,\n"
        + "    XF_DEMOGRAPHICCODE VARCHAR(4),\n"
        + "    XF_DEMOGRAPHICDATA VARCHAR(4),\n"
        + "    XF_NETQTY DECIMAL(16, 4),\n"
        + "    XF_ORIGINALAMOUNT DECIMAL(16, 4),\n"
        + "    XF_SELLINGAMOUNT DECIMAL(16, 4),\n"
        + "    XF_DISCOUNTAPPROVE VARCHAR(10),\n"
        + "    XF_DISCOUNTAMOUNT DECIMAL(16, 4),\n"
        + "    XF_TTLTAXAMOUNT1 DECIMAL(16, 4),\n"
        + "    XF_TTLTAXAMOUNT2 DECIMAL(16, 4),\n"
        + "    XF_NETAMOUNT DECIMAL(16, 4),\n"
        + "    XF_PAIDAMOUNT DECIMAL(16, 4),\n"
        + "    XF_CHANGEAMOUNT DECIMAL(16, 4),\n"
        + "    XF_DEFAULTTENDER VARCHAR(2),\n"
        + "    XF_NUMOFITEM DOUBLE,\n"
        + "    XF_NUMOFTENDER DOUBLE,\n"
        + "    XF_PRICEINCLUDETAX VARCHAR(2),\n"
        + "    XF_SHOPTAXGROUP VARCHAR(40),\n"
        + "    XF_EXTENDPARAM VARCHAR(128),\n"
        + "    XF_DESTLOCATIONLIST VARCHAR(250),\n"
        + "    XF_POSTDATE VARCHAR(21),\n"
        + "    XF_CREATETIME VARCHAR(21),\n"
        + "    XF_SALESMODE VARCHAR(2),\n"
        + "    CRM_EXECUTED VARCHAR(1),\n"
        + "    CRM_EXECUTED1 VARCHAR(1),\n"
        + "    XF_REFFROM VARCHAR(20),\n"
        + "    XF_REFDOCNO VARCHAR(50),\n"
        + "    UNIQUE (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL)\n"
        + ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;"
        + "CREATE TABLE XF_TRANSSALESITEM_${schema} (\n"
        + "    id INT AUTO_INCREMENT PRIMARY KEY,\n"
        + "    XF_STORECODE VARCHAR(6),\n"
        + "    XF_TILLID VARCHAR(3),\n"
        + "    XF_TXDATE DATE,\n"
        + "    XF_TXSERIAL DOUBLE,\n"
        + "    XF_TXTIME VARCHAR(6),\n"
        + "    XF_TXBATCH DOUBLE,\n"
        + "    XF_DOCNO VARCHAR(10),\n"
        + "    XF_VOIDDOCNO VARCHAR(10),\n"
        + "    XF_TXTYPE DOUBLE,\n"
        + "    XF_TXHOUR DOUBLE,\n"
        + "    XF_CASHIER VARCHAR(10),\n"
        + "    XF_SALESMAN VARCHAR(10),\n"
        + "    XF_VIPCODE VARCHAR(12),\n"
        + "    XF_DEMOGRAPCODE VARCHAR(15),\n"
        + "    XF_DEMOGRAPDATA VARCHAR(15),\n"
        + "    XF_PLU VARCHAR(30),\n"
        + "    XF_STYLE VARCHAR(30),\n"
        + "    XF_COLOR VARCHAR(6),\n"
        + "    XF_SIZE VARCHAR(14),\n"
        + "    XF_ITEMLOTNUM VARCHAR(30),\n"
        + "    XF_QTYSOLD DECIMAL(16, 4),\n"
        + "    XF_AMTSOLD DECIMAL(16, 4),\n"
        + "    XF_COSTSOLD DECIMAL(18, 6),\n"
        + "    XF_MARKDOWNAMT DECIMAL(16, 4),\n"
        + "    XF_DISCOUNTAMT DECIMAL(16, 4),\n"
        + "    XF_PROMOTIONAMT DECIMAL(16, 4),\n"
        + "    XF_TAXAMOUNT1 DECIMAL(16, 4),\n"
        + "    XF_TAXAMOUNT2 DECIMAL(16, 4),\n"
        + "    XF_TAXRATE1 DECIMAL(16, 4),\n"
        + "    XF_TAXRATE2 DECIMAL(16, 4),\n"
        + "    XF_EXSTK2SALES DECIMAL(12, 4),\n"
        + "    XF_ORGUPRICE DECIMAL(16, 4),\n"
        + "    XF_ISDEPOSIT VARCHAR(1),\n"
        + "    XF_ISWHOLESALE VARCHAR(1),\n"
        + "    XF_ISPRICEALTERNATE VARCHAR(1),\n"
        + "    XF_ISPRICEOVERRIDE VARCHAR(1),\n"
        + "    XF_ISNEWITEM VARCHAR(1),\n"
        + "    XF_PRICEAPPROVE VARCHAR(10),\n"
        + "    XF_COUPONNUMBER VARCHAR(12),\n"
        + "    XF_DISCOUNTAPPROVE VARCHAR(10),\n"
        + "    XF_ITEMDISCOUNTAMT DECIMAL(16, 4),\n"
        + "    XF_TTLDISCOUNTLESS DECIMAL(16, 4),\n"
        + "    XF_PROMID1 VARCHAR(6),\n"
        + "    XF_PROMAMT1 DECIMAL(16, 4),\n"
        + "    XF_PROMQTY1 DECIMAL(16, 4),\n"
        + "    XF_PROMID2 VARCHAR(6),\n"
        + "    XF_PROMAMT2 DECIMAL(16, 4),\n"
        + "    XF_PROMQTY2 DECIMAL(16, 4),\n"
        + "    XF_PROMID3 VARCHAR(6),\n"
        + "    XF_PROMAMT3 DECIMAL(16, 4),\n"
        + "    XF_PROMQTY3 DECIMAL(16, 4),\n"
        + "    XF_PROMID4 VARCHAR(6),\n"
        + "    XF_PROMAMT4 DECIMAL(16, 4),\n"
        + "    XF_PROMQTY4 DECIMAL(16, 4),\n"
        + "    XF_PROMID5 VARCHAR(6),\n"
        + "    XF_PROMAMT5 DECIMAL(16, 4),\n"
        + "    XF_PROMQTY5 DECIMAL(16, 4),\n"
        + "    XF_SALESITEMREMARK VARCHAR(80),\n"
        + "    XF_EXTENDPARAM VARCHAR(250),\n"
        + "    XF_DESTLOCATIONLIST VARCHAR(250),\n"
        + "    XF_PRICECENTER VARCHAR(6),\n"
        + "    XF_COSTCENTER VARCHAR(6),\n"
        + "    XF_POSTDATE VARCHAR(21),\n"
        + "    XF_CREATETIME VARCHAR(21),\n"
        + "    XF_ISPOSTING VARCHAR(1),\n"
        + "    CRM_EXECUTED VARCHAR(1),\n"
        + "    CRM_EXECUTED1 VARCHAR(1),\n"
        + "    XF_HXALLDISCLESS1 DECIMAL(16, 4),\n"
        + "    UNIQUE KEY (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL)\n"
        + ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;"
        + "CREATE TABLE XF_TRANSSALESTENDER_${schema} (\n"
        + "    id INT AUTO_INCREMENT PRIMARY KEY,\n"
        + "    XF_STORECODE VARCHAR(6),\n"
        + "    XF_TILLID VARCHAR(3),\n"
        + "    XF_TXDATE DATE,\n"
        + "    XF_TXSERIAL DOUBLE,\n"
        + "    XF_POSTDATE VARCHAR(21),\n"
        + "    XF_TXTIME VARCHAR(6),\n"
        + "    XF_TXBATCH DOUBLE,\n"
        + "    XF_DOCNO VARCHAR(10),\n"
        + "    XF_VOIDDOCNO VARCHAR(10),\n"
        + "    XF_TXTYPE DOUBLE,\n"
        + "    XF_TXHOUR DOUBLE,\n"
        + "    XF_CASHIERCODE VARCHAR(10),\n"
        + "    XF_TENDERCODE VARCHAR(2),\n"
        + "    XF_SPECIFICEDTYPE DOUBLE,\n"
        + "    XF_PAYAMOUNT DECIMAL(16, 4),\n"
        + "    XF_BASEAMOUNT DECIMAL(16, 4),\n"
        + "    XF_EXTENDPARAM VARCHAR(250),\n"
        + "    XF_CREATETIME VARCHAR(21),\n"
        + "    XF_DESTLOCATIONLIST VARCHAR(250),\n"
        + "    XF_EXCESSMONEY DECIMAL(16, 4),\n"
        + "    CRM_EXECUTED VARCHAR(1),\n"
        + "    CRM_EXECUTED1 VARCHAR(1),\n"
        + "    UNIQUE KEY (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL)\n"
        + ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    String[] split = s.split("\n");
    for (String string : split) {
      System.out.println(createTable.replaceAll("\\$\\{schema}", string));
    }
  }
}