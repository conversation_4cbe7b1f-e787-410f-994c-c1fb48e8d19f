<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.order.mapper.AutoCreateMapper">


  <resultMap id="createTableMap" type="java.lang.String">
    <id property="createTableStatement" column="Create Table"/>
  </resultMap>


  <select id="showCreateTable" resultMap="createTableMap">
    show create table  ${tableName}
  </select>


  <select id="createTable">
    ${sql}
  </select>
  <select id="checkTableExists" resultType="java.lang.Integer">
    SELECT COUNT(1)
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = #{tableSchema}
    AND TABLE_NAME = #{tableName}
  </select>


</mapper>
