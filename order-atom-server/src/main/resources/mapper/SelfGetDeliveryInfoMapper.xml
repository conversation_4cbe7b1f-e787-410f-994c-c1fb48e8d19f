<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.order.mapper.SelfGetDeliveryInfoMapper">
    <insert id="saveDeliveryInfo">
        insert into self_get_delivery_info (mer_code,platform_code,order_no,third_order_no,online_client_code)
        select #{param.merCode},#{param.thirdPlatformCode},#{param.orderNo},#{param.thirdOrderNo},#{param.clientCode} from dual
        where not exists (select id from self_get_delivery_info where order_no = #{param.orderNo})
    </insert>

</mapper>






