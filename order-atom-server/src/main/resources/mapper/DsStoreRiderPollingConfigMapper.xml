<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yxt.order.atom.order.mapper.DsStoreRiderPollingConfigMapper">


  <select id="queryConfigByStoreId"
          resultType="com.yxt.order.atom.sdk.online_order.store.res.RiderPollingConfigResDto">
    select platform_code,platform_name,waiting_time,priority
    from ds_store_rider_polling_config
    where online_store_id = #{onlineStoreId,jdbcType=BIGINT}
      and `status` = 1
  </select>

</mapper>