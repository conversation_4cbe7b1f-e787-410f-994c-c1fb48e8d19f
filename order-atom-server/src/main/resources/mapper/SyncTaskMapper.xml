<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.order_sync.mapper.SyncTaskMapper">


  <select id="syncTaskSearch"
          resultType="com.yxt.order.atom.order_sync.entity.SyncTaskModelDO">

    select
      *
    from
      sync_task
    <where>
      execute_count &lt; max_execute_count
      <if test="request.contextId != null">
        and context_id = #{request.contextId}
      </if>
      <if test="request.taskType != null">
        and task_type = #{request.taskType}
      </if>
      <if test="request.taskState != null">
        and task_state = #{request.taskState}
      </if>
      <if test="request.currentTime != null">
        and next_execute_time &lt;= #{request.currentTime}
      </if>
      <if test="request.taskParamCondition != null">
        and ${request.taskParamCondition}
      </if>
    </where>
    order by next_execute_time
    <if test="request.processBatchSize != null">
      limit #{request.processBatchSize}
    </if>
  </select>
</mapper>