<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.order.mapper.SyncEsMapper">



  <select id="selectDetailByOrderNo"
          resultType="com.yxt.order.atom.order.es.dto.OrderDetailDto">
    select
        `erp_code` as commodityCode,
      `commodity_name` as commodityName,
        `commodity_count`  as commodityCount,
        `status`  as status,
        `five_class`  as fiveClass
    from order_detail
    where `order_no`=#{orderNo, jdbcType=BIGINT}
  </select>

  <select id="selectDetailByOmsOrderNo"
    resultType="com.yxt.order.atom.order.es.dto.OrderDetailDto">
    select
      `erp_code` as commodityCode,
      `commodity_name` as commodityName,
      `commodity_count`  as commodityCount,
      `status`  as status
    from order_detail
    where `oms_order_no`=#{omsOrderNo, jdbcType=BIGINT}
  </select>

  <select id="selectRefundDetailByRefundNo"
    resultType="com.yxt.order.atom.order.es.dto.OrderDetailDto">
    select
      `erp_code` as commodityCode,
      `commodity_name` as commodityName,
      `refund_count`  as commodityCount,
      `status`  as status
    from refund_detail
    where `refund_no`=#{refundNo, jdbcType=BIGINT}
  </select>


  <select id="isPostOrder" resultType="java.lang.Integer">
    select
      is_post_fee_order
    from oms_order_info
    where `oms_order_no`=#{omsOrderNo, jdbcType=BIGINT}
  </select>
  <select id="selectDetailByRefundNo"
          resultType="com.yxt.order.atom.order.es.dto.RefundDetailDto">
        select
        `erp_code` as commodityCode,
        `commodity_name` as commodityName,
        `refund_count` as commodityCount
        from refund_detail
         where refund_no=#{refundNo, jdbcType=BIGINT}
  </select>

  <select id="selectMemberCardNo" resultType="java.lang.String">
    select
      member_no
    from order_info
    where `order_no`=#{orderNo, jdbcType=BIGINT}
  </select>


  <!--历史数据有脏数据,以及包含手动运维的数据,这里获取最新的一条有效数据(已确认)。这个数据只会在打印的时候用一下,无其他应用场景-->
  <select id="selectByOmsOrderNo" parameterType="java.lang.Long" resultType="com.yxt.order.atom.order.mapper.dto.OmsLogisticOrderDto">
    SELECT
    lo.oms_order_no as omsOrderNo,
    lo.status as status,
    lo.logistic_config_id as logisticConfigId,
    lo.platform_code as platformCode,
    lci.standard_template_id as standardTemplateId
    FROM logistic_order lo
    left JOIN logistic_config_info lci on lci.id = lo.logistic_config_id
    where lo.oms_order_no = #{omsOrderNo} and lo.status = 1 order by lo.create_time desc limit 1
  </select>

  <select id="omsOrderRefundCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
    SELECT
    COUNT(ro.id)
    FROM
    refund_order ro
    WHERE
    ro.oms_order_no = #{omsOrderNo} and <![CDATA[ ro.state != 102 AND ro.state != 103 ]]>
  </select>

  <select id="selectPlatformOrder" resultType="com.yxt.order.atom.order.mapper.dto.OmsPlatformOrderDto">
    select
      havecfy haveCfy,
      rx_audit_status rxAuditStatus
    from platform_order_info
    where
    olorderno = #{thirdOrderNo}
    and ectype= #{thirdPlatformCode}
  </select>
  <select id="selectOmsOrderPayInfo" resultType="com.yxt.order.atom.order.mapper.dto.OmsOrderPayInfoDto">
    select
      pay_type payType,
      buyer_actual_amount buyerActualAmount
    from order_pay_info
    where
    oms_order_no = #{omsOrderNo}
  </select>

  <select id="selectOmsOrderDeliveryAddress"  resultType="com.yxt.order.atom.order.mapper.dto.OmsAddressDto">
    select
    receiver_name receiverName,
    receiver_mobile receiverMobile,
    receiver_telephone receiverTelephone,
    full_address fullAddress
    from order_delivery_address
    where
    oms_order_no = #{omsOrderNo}
  </select>

  <!--list 不区分operate_status和type,作为条件索引来处理,请求参数可以查-->
  <select id="selectOmsOrderExList"  resultType="com.yxt.order.atom.order.mapper.dto.OmsOrderExDto">
    select
    ex_type exType,
    oms_order_no omsOrderNo,
    operate_status operateStatus
    from oms_order_ex
    where
    oms_order_no = #{omsOrderNo}
  </select>

  <select id="selectOmsOrderDetailList" resultType="com.yxt.order.atom.order.mapper.dto.OmsOrderDetailDto">
    select
    oms_order_no omsOrderNo,
    erp_code erpCode,
    status status,
    commodity_name commodityName
    from order_detail
    WHERE
    oms_order_no =#{omsOrderNo}
  </select>

<!--  <sql id="existStoreType">-->
<!--    <if test="param.classifyId != null and param.classifyId != ''">-->
<!--      AND EXISTS (-->
<!--        SELECT-->
<!--        1-->
<!--        FROM-->
<!--        ds_online_store dos-->
<!--        INNER JOIN ds_online_store_config dosc ON dos.id = dosc.online_store_id-->
<!--        AND FIND_IN_SET(#{param.classifyId}, dosc.store_type )-->
<!--        WHERE dos.mer_code = a.mer_code-->
<!--        AND dos.online_store_code = a.online_store_code-->
<!--      )-->
<!--    </if>-->
<!--  </sql>-->

  <!--针对B2C只会有一条记录,b2c店铺的onlineStoreCode是随机字符串,查出来的storeType是单挑数据-->
  <!-- storeType是店铺分类 -->
  <select id="onlineStoreType" resultType="java.lang.String">
    SELECT
      dosc.store_type
    FROM
      ds_online_store dos
        INNER JOIN ds_online_store_config dosc ON dos.id = dosc.online_store_id
    WHERE dos.mer_code = #{merCode}
      AND dos.online_store_code = #{onlineStoreCode} and dosc.store_type is not null
  </select>

  <update id="refreshOmsOrderInfoDataVersion">
    update oms_order_info  set version = version + 1
    where `oms_order_no`=#{omsOrderNo, jdbcType=BIGINT}
  </update>
  <update id="refreshOmsOrderInfoDataVersionByThirdOrderNoAndPlatformCode">
    update oms_order_info  set version = version + 1
    where `third_order_no`=#{thirdOrderNo}
    and `third_platform_code`=#{thirdPlatformCode}
  </update>


  <update id="refreshAccountInfoDataVersion">
    update account_order  set version = version + 1
    where `order_no`=#{orderNo, jdbcType=BIGINT}
  </update>
</mapper>
