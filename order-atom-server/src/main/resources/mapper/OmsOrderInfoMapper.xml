<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.order.mapper.OmsOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.yxt.order.atom.order.entity.OmsOrderInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="BIGINT" property="orderNo" />
    <result column="oms_order_no" jdbcType="BIGINT" property="omsOrderNo" />
    <result column="oms_ship_no" jdbcType="BIGINT" property="omsShipNo" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="express_name" jdbcType="VARCHAR" property="expressName" />
    <result column="express_id" jdbcType="INTEGER" property="expressId" />
    <result column="express_number" jdbcType="VARCHAR" property="expressNumber" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="split_status" jdbcType="BIT" property="splitStatus" />
    <result column="ship_time" jdbcType="TIMESTAMP" property="shipTime" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="is_refund" jdbcType="BIT" property="isRefund" />
    <result column="ship_operator_id" jdbcType="VARCHAR" property="shipOperatorId" />
    <result column="ship_operator_name" jdbcType="VARCHAR" property="shipOperatorName" />
    <result column="audit_operator_id" jdbcType="VARCHAR" property="auditOperatorId" />
    <result column="audit_operator_name" jdbcType="VARCHAR" property="auditOperatorName" />
    <result column="ex_operator_id" jdbcType="VARCHAR" property="exOperatorId" />
    <result column="ex_operator_name" jdbcType="VARCHAR" property="exOperatorName" />
    <result column="bill_operator_id" jdbcType="VARCHAR" property="billOperatorId" />
    <result column="bill_operator_name" jdbcType="VARCHAR" property="billOperatorName" />
    <result column="bill_time" jdbcType="TIMESTAMP" property="billTime" />
    <result column="ex_operator_time" jdbcType="TIMESTAMP" property="exOperatorTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="ex_status" jdbcType="TINYINT" property="exStatus" />
    <result column="erp_status" jdbcType="TINYINT" property="erpStatus" />
    <result column="erp_deliver_no" jdbcType="VARCHAR" property="erpDeliverNo" />
    <result column="erp_sale_no" jdbcType="VARCHAR" property="erpSaleNo" />
    <result column="intercept_status" jdbcType="TINYINT" property="interceptStatus" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="ex_reason" jdbcType="VARCHAR" property="exReason" />
    <result column="is_post_fee_order" jdbcType="BIT" property="isPostFeeOrder" />
    <result column="join_wms" jdbcType="TINYINT" property="joinWms" />
    <result column="ship_status" jdbcType="TINYINT" property="shipStatus" />
    <result column="third_platform_code" jdbcType="VARCHAR" property="thirdPlatformCode" />
    <result column="third_order_no" jdbcType="VARCHAR" property="thirdOrderNo" />
    <result column="mer_code" jdbcType="VARCHAR" property="merCode" />
    <result column="client_code" jdbcType="VARCHAR" property="clientCode" />
    <result column="online_store_code" jdbcType="VARCHAR" property="onlineStoreCode" />
    <result column="online_store_name" jdbcType="VARCHAR" property="onlineStoreName" />
    <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
    <result column="organization_name" jdbcType="VARCHAR" property="organizationName" />
    <result column="is_prescription" jdbcType="BIT" property="isPrescription" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="client_conf_id" jdbcType="BIGINT" property="clientConfId" />
    <result column="need_invoice" jdbcType="VARCHAR" property="needInvoice" />
    <result column="buyer_message" jdbcType="VARCHAR" property="buyerMessage" />
    <result column="seller_remark" jdbcType="VARCHAR" property="sellerRemark" />
    <result column="sheet_status" jdbcType="VARCHAR" property="sheetStatus" />
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="o2o_client_code" jdbcType="VARCHAR" property="o2oClientCode" />
    <result column="o2o_shop_id" jdbcType="VARCHAR" property="o2oShopId" />
    <result column="sendorder_print_num" jdbcType="INTEGER" property="sendorderPrintNum" />
    <result column="goods_qty" jdbcType="INTEGER" property="goodsQty" />
    <result column="goods_category_qty" jdbcType="INTEGER" property="goodsCategoryQty" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="erp_code_list" jdbcType="VARCHAR" property="erpCodeList" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="erp_error_code" jdbcType="VARCHAR" property="erpErrorCode" />
    <result column="virtual_merge_no" jdbcType="BIGINT" property="virtualMergeNo" />
    <result column="shipped_status" jdbcType="BIT" property="shippedStatus" />
    <result column="spread_store_code" jdbcType="VARCHAR" property="spreadStoreCode" />
    <result column="order_owner_type" jdbcType="BIT" property="orderOwnerType" />
    <result column="member_no" jdbcType="VARCHAR" property="memberNo" />
    <result column="stock_state" jdbcType="VARCHAR" property="stockState" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="sub_biz_type" jdbcType="VARCHAR" property="subBizType" />
    <result column="vip_level" jdbcType="VARCHAR" property="vipLevel" />
    <result column="erp_audit_status" jdbcType="BIT" property="erpAuditStatus" />
    <result column="logistics_back_status" jdbcType="BIT" property="logisticsBackStatus" />
    <result column="settlement_status" jdbcType="TINYINT" property="settlementStatus" />
    <result column="deleted" jdbcType="BIGINT" property="deleted" />
    <result column="is_procurement_erp" jdbcType="BIT" property="isProcurementErp" />
    <result column="procurement_no" jdbcType="BIGINT" property="procurementNo" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.yxt.order.atom.order.entity.OmsOrderInfoDO">
    <result column="tag" jdbcType="LONGVARCHAR" property="tag" />
    <result column="extend_info" jdbcType="LONGVARCHAR" property="extendInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, oms_order_no, oms_ship_no, order_status, warehouse_id, warehouse_name, 
    express_name, express_id, express_number, remark, create_time, creator, modify_time, 
    order_type, split_status, ship_time, audit_time, is_refund, ship_operator_id, ship_operator_name, 
    audit_operator_id, audit_operator_name, ex_operator_id, ex_operator_name, bill_operator_id, 
    bill_operator_name, bill_time, ex_operator_time, complete_time, cancel_time, ex_status, 
    erp_status, erp_deliver_no, erp_sale_no, intercept_status, warehouse_code, ex_reason, 
    is_post_fee_order, join_wms, ship_status, third_platform_code, third_order_no, mer_code, 
    client_code, online_store_code, online_store_name, organization_code, organization_name, 
    is_prescription, pay_time, created, buyer_name, client_conf_id, need_invoice, buyer_message, 
    seller_remark, sheet_status, warehouse_type, supplier_code, o2o_client_code, o2o_shop_id, 
    sendorder_print_num, goods_qty, goods_category_qty, version, buyer_id, erp_code_list, 
    seq, erp_error_code, virtual_merge_no, shipped_status, spread_store_code, order_owner_type, 
    member_no, stock_state, platform, sub_biz_type, vip_level, erp_audit_status, logistics_back_status, 
    settlement_status, deleted, is_procurement_erp, procurement_no
  </sql>
  <sql id="Blob_Column_List">
    tag, extend_info
  </sql>
  <select id="selectByOmsOrderNo" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from oms_order_info
    where oms_order_no = #{omsOrderNo,jdbcType=BIGINT}
  </select>

  <select id="selectByOmsOrderNoList" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from oms_order_info
    where oms_order_no in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>

  <select id="selectMaxId" resultType="java.lang.Long">
    select max(id) from oms_order_info
    <include refid="flashParamSql" />
  </select>

  <select id="selectMinId" resultType="java.lang.Long">
    select min(id) from oms_order_info
    <include refid="flashParamSql" />
  </select>

  <sql id="flashParamSql">
    where 1=1
    <if test="flashParam.start != null">
      and created <![CDATA[ >= ]]> #{flashParam.start}
    </if>
    <if test="flashParam.end != null">
      and created <![CDATA[ <= ]]> #{flashParam.end}
    </if>
    <if test="flashParam.noList != null and flashParam.noList.size() > 0">
      and oms_order_no in
      <foreach collection="flashParam.noList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </sql>

</mapper>