<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper">


  <select id="selectMaxId" resultType="java.lang.Long">
    select max(id) from offline_refund_order
    <include refid="flashParamSql" />
  </select>

  <select id="selectMinId" resultType="java.lang.Long">
    select min(id) from offline_refund_order
    <include refid="flashParamSql" />
  </select>

  <sql id="flashParamSql">
    where 1=1
    <if test="flashParam.start != null">
      and created_time <![CDATA[ >= ]]> #{flashParam.start}
    </if>
    <if test="flashParam.end != null">
      and created_time <![CDATA[ <= ]]> #{flashParam.end}
    </if>
    <if test="flashParam.noList != null and flashParam.noList.size() > 0">
      and refund_no in
      <foreach collection="flashParam.noList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </sql>


  <delete id="deleteAll0">
    delete from offline_refund_order_cashier_desk where refund_no = #{refundNo};
  </delete>
  <delete id="deleteAll1">
    delete from offline_refund_order_detail where refund_no = #{refundNo};
  </delete>
  <delete id="deleteAll2">
    delete from offline_refund_order_med_ins_settle where refund_no = #{refundNo};
  </delete>
  <delete id="deleteAll3">
    delete from offline_refund_order_organization where refund_no = #{refundNo};
  </delete>
  <delete id="deleteAll4">
    delete from offline_refund_order_pay where refund_no = #{refundNo};
  </delete>
  <delete id="deleteAll5">
    delete from offline_refund_order_user where refund_no = #{refundNo};
  </delete>

</mapper>