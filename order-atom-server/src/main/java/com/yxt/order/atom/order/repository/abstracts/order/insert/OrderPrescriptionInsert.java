package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderPrescriptionDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OrderPrescriptionBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:58
 * @email: <EMAIL>
 */
@Component
public class OrderPrescriptionInsert extends AbstractInsert<List<OrderPrescriptionDO>> {

  @Resource
  private OrderPrescriptionBatchRepository orderPrescriptionBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderPrescriptionList());
  }

  @Override
  protected Integer insert(List<OrderPrescriptionDO> list) {
    return orderPrescriptionBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderPrescriptionDO> data() {
    return saveDataOptional.getOrderPrescriptionList();
  }

}
