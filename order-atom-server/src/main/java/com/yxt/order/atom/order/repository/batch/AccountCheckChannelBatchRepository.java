package com.yxt.order.atom.order.repository.batch;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.AccountCheckChannelDO;
import com.yxt.order.atom.order.mapper.AccountCheckChannelMapper;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.order.MerCode;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public class AccountCheckChannelBatchRepository extends ServiceImpl<AccountCheckChannelMapper, AccountCheckChannelDO> {

  @DS(DsConstants.DB_ORDER_SLAVE)
  public Long getMaxIdByAccountTime(MerCode merCode, List<PlatformCodeEnum> platformCodeList, LocalDateTime cleanDate) {
    Wrapper<AccountCheckChannelDO> wrapper = Wrappers.<AccountCheckChannelDO>lambdaQuery()
        .eq(AccountCheckChannelDO::getMerCode, merCode.getMerCode())
        .in(CollUtil.isNotEmpty(platformCodeList), AccountCheckChannelDO::getThirdPlatformCode, platformCodeList)
        .lt(AccountCheckChannelDO::getAccountTime, cleanDate)
        .orderByDesc(AccountCheckChannelDO::getId)
        .last(" limit 1 ");
    List<AccountCheckChannelDO> doList = this.baseMapper.selectList(wrapper);
    if(CollUtil.isEmpty(doList)){
      return null;
    }
    return doList.get(0).getId();
  }
}
