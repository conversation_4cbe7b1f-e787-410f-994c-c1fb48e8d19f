package com.yxt.order.atom.order.es.sync.member_transaction.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_REFUND_ORDER_REGEX;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.sync.clean.ExpireDaysConstant;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberOrderSource;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberRefundStatus;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberRefundOrderModel;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberRefundOrderModel.MemberRefundOrderDetailModel;
import com.yxt.order.atom.order.mapper.dto.OrganizationInfoDto;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.AfterSaleTypeEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2024/12/9 11:49
 */
@Component
public class MemberOfflineRefundOrderTransactionHandler extends
    AbstractCanalHandler<CanalOfflineRefundOrder, MemberRefundOrderModel> {


  @Resource
  protected SyncComponent syncComponent;

  public MemberOfflineRefundOrderTransactionHandler() {
    super(CanalOfflineRefundOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD_OFFLINE.equals(database) && Table.tableRegex(OFFLINE_REFUND_ORDER_REGEX,
        table);
  }

  @Override
  protected List<MemberRefundOrderModel> assemble() {
    List<OfflineRefundOrder> offlineRefundOrderList = getData().getData();
    if (CollectionUtils.isEmpty(offlineRefundOrderList)) {
      return Lists.newArrayList();
    }
    return offlineRefundOrderList.stream()
        .filter(this::efficientData)
        .map(offlineRefundOrder -> {
          MemberRefundOrderModel memberRefundOrderModel = new MemberRefundOrderModel();
          memberRefundOrderModel.setStoreCode(offlineRefundOrder.getStoreCode());
          memberRefundOrderModel.setRefundType(offlineRefundOrder.getRefundType());
          memberRefundOrderModel.setRefundStatus(MemberRefundStatus.COMPLETED.getStatus());
          memberRefundOrderModel.setCreated(offlineRefundOrder.getCreated());
          memberRefundOrderModel.setOrderSource(MemberOrderSource.POS.name());
          memberRefundOrderModel.setRefundNo(offlineRefundOrder.getRefundNo());
          memberRefundOrderModel.setOrderNo(offlineRefundOrder.getOrderNo());
          memberRefundOrderModel.setAfterSaleType(String.valueOf(AfterSaleTypeEnum.parse(offlineRefundOrder.getAfterSaleType())));
          memberRefundOrderModel.setCreateTime(offlineRefundOrder.getCreatedTime());
          memberRefundOrderModel.setUserId(offlineRefundOrder.getUserId());
          memberRefundOrderModel.setUserCardNo(syncComponent.getUserCardNoByRefundNo(offlineRefundOrder.getRefundNo(),offlineRefundOrder.getNeedRoute()));
          memberRefundOrderModel.setThirdRefundNo(offlineRefundOrder.getThirdRefundNo());
          memberRefundOrderModel.setThirdOrderNo(offlineRefundOrder.getThirdOrderNo());
          memberRefundOrderModel.setPlatformCode(String.valueOf(ThirdPlatformCodeEnum.parse(offlineRefundOrder.getThirdPlatformCode())));
          OrganizationInfoDto organization = syncComponent.getOrganizationByRefundNo(offlineRefundOrder.getRefundNo(),offlineRefundOrder.getNeedRoute());
          if(Objects.nonNull(organization)){
            memberRefundOrderModel.setStoreType(organization.getStoreType());
          }

          List<OfflineRefundOrderDetailDO> refundDetailList = syncComponent.getOfflineRefundDetailByRefundNo(
              offlineRefundOrder.getRefundNo(), offlineRefundOrder.getNeedRoute());
          if(!CollectionUtils.isEmpty(refundDetailList)){
            memberRefundOrderModel.setMemberRefundOrderDetailModelList(refundDetailList.stream()
                .map(item->{
                  MemberRefundOrderDetailModel refundOrderDetailModel = new MemberRefundOrderDetailModel();
                  refundOrderDetailModel.setErpCode(item.getErpCode());
                  refundOrderDetailModel.setErpName(item.getErpName());
                  return refundOrderDetailModel;
                }).collect(Collectors.toList()));
          }
          return memberRefundOrderModel;
        }).collect(Collectors.toList());
  }

  public Boolean efficientData(OfflineRefundOrder offlineRefundOrder){
    return
        !offlineRefundOrder.migrateRefundOrder() &&
        !OrderDateUtils.isExpired(offlineRefundOrder.getCreated(), ExpireDaysConstant.EsMemberOrderEfficientDays)
            && !StringUtils.isEmpty(offlineRefundOrder.getUserId())
            && ThirdPlatformCodeEnum.isValid(offlineRefundOrder.getThirdPlatformCode())
        ;
  }
}
