package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OfflineRefundOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailTraceDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderPayDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderUserDO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailPickDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailTraceDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderMedInsSettleDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderUserDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface OfflineRefundOrderConverter {

  OfflineRefundOrderConverter INSTANCE = Mappers.getMapper(OfflineRefundOrderConverter.class);

  OfflineRefundOrderDO toDO(OfflineRefundOrderDTO dto);


  List<OfflineRefundOrderPayDO> toRefundPayDO(List<OfflineRefundOrderPayDTO> dtoList);

  OfflineRefundOrderMedInsSettleDO toDO(OfflineRefundOrderMedInsSettleDto dto);

  OfflineRefundOrderUserDO toDO(OfflineRefundOrderUserDTO dto);

  OfflineRefundOrderOrganizationDO toDO(OfflineRefundOrderOrganizationDTO offlineRefundOrderOrganizationDTO);

  OfflineRefundOrderCashierDeskDO toDO(OfflineRefundOrderCashierDeskDTO offlineRefundOrderCashierDeskDTO);

  OfflineRefundOrderDetailDO toDO(OfflineRefundOrderDetailDTO refundDetailDto);

  List<OfflineRefundOrderDetailTraceDO> toDOList(List<OfflineRefundOrderDetailTraceDTO> refundDetailTraceList);

  List<OfflineRefundOrderDetailPickDO> toDetailPickList(List<OfflineRefundOrderDetailPickDTO> offlineRefundOrderDetailPickDTOList);
}
