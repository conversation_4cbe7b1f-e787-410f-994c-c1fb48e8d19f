package com.yxt.order.atom.order.es.sync.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.doc.EsB2cRefundAccountInfo;
import com.yxt.order.atom.order.es.mapper.EsB2cRefundAccountInfoMapper;
import com.yxt.order.atom.order.es.sync.b2c_account_info.EsB2cRefundAccountInfoModel;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: yang jun feng
 * @time: 2024/11/12 11:18
 */
@Component
@Slf4j
public class EsB2cRefundAccountInfoModelOperate extends AbstractEsOperate<EsB2cRefundAccountInfoModel> {

  @Resource
  private EsB2cRefundAccountInfoMapper esB2cRefundAccountInfoMapper;

  public EsB2cRefundAccountInfoModelOperate() {
    super(EsB2cRefundAccountInfoModel.class);
  }

  @Override
  protected Boolean exec() {

    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;

  }


  private Boolean delete(EsB2cRefundAccountInfoModel accountInfoModel) {
    return esB2cRefundAccountInfoMapper.deleteById(accountInfoModel.getId()) > 0;
  }

  private Boolean save(EsB2cRefundAccountInfoModel accountInfoModel) {
    // 适配逻辑删除
    if(Objects.nonNull(accountInfoModel.getDeleted()) && accountInfoModel.getDeleted() != 0L){// 不为0,表示删除
      return delete(accountInfoModel);
    }

    EsB2cRefundAccountInfo esRefundB2cAccountInfo = accountInfoModel.create();

    LambdaEsQueryWrapper<EsB2cRefundAccountInfo> query = new LambdaEsQueryWrapper<>();
    query.eq(EsB2cRefundAccountInfo::getId, accountInfoModel.getId());
    query.eq(EsB2cRefundAccountInfo::getDeleted, 0L);
    Long count = esB2cRefundAccountInfoMapper.selectCount(query);
    if (count > 0) {
      boolean update = esB2cRefundAccountInfoMapper.updateById (esRefundB2cAccountInfo) > 0;
      if (!update) {
        log.warn("退款下账单更新索引数据失败,{}", JsonUtils.toJson(esRefundB2cAccountInfo));
      }
      return update;

    } else {
      boolean create = esB2cRefundAccountInfoMapper.insert(esRefundB2cAccountInfo) > 0;
      if (!create) {
        log.warn("下账单创建索引数据失败,{}", JsonUtils.toJson(esRefundB2cAccountInfo));
      }
      return create;
    }
  }
}
