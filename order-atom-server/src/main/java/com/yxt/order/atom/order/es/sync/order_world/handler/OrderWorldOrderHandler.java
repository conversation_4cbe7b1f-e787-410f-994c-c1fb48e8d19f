package com.yxt.order.atom.order.es.sync.order_world.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_ORDER_REGEX;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.es.components.OrderWorldSyncComponent;
import com.yxt.order.atom.order.es.sync.data.CanalOrderWorldOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOrderWorldOrder.OrderInfo;
import com.yxt.order.atom.order.es.sync.order_world.model.EsOrderWorldOrderDetailModel;
import com.yxt.order.atom.order.es.sync.order_world.model.EsOrderWorldOrderModel;
import com.yxt.order.atom.order_world.entity.OrderAmountDO;
import com.yxt.order.atom.order_world.entity.OrderDetailDO;
import com.yxt.order.atom.order_world.entity.OrderInfoDO;
import com.yxt.order.atom.order_world.entity.OrderPayDO;
import com.yxt.order.atom.order_world.entity.OrderUserInfoDO;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.order_world.BusinessType;
import com.yxt.order.types.order_world.OrderFlag;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class OrderWorldOrderHandler extends AbstractCanalHandler<CanalOrderWorldOrder, EsOrderWorldOrderModel> {

  @Resource
  private OrderWorldSyncComponent orderWorldSyncComponent;

  protected OrderWorldOrderHandler() {
    super(CanalOrderWorldOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD_OFFLINE.equals(database) && (Table.tableRegex(OFFLINE_ORDER_REGEX, table));
  }

  @Override
  protected List<EsOrderWorldOrderModel> assemble() {

    List<OrderInfo> orderInfoList = getData().getData();
    if(CollUtil.isEmpty(orderInfoList)){
      return new ArrayList<>(0);
    }
    List<OrderInfo> filterOrderList = orderInfoList.stream().filter(this::efficientData).collect(Collectors.toList());
    if(CollUtil.isEmpty(filterOrderList)){
      return new ArrayList<>(0);
    }
    List<EsOrderWorldOrderModel> esOrderList = new ArrayList<>();
    for (OrderInfo canalOrderInfo : filterOrderList) {
      EsOrderWorldOrderModel esOrder = new EsOrderWorldOrderModel();

      //根据orderNo查询订单主信息
      OrderInfoDO dbOrderInfo = orderWorldSyncComponent.getOrderInfoByOrderNo(canalOrderInfo.getOrderNo(), canalOrderInfo.getNeedRoute());
      if(ObjectUtil.isNull(dbOrderInfo)){
        continue;
      }
      //根据orderNo查询订单明细信息
      List<OrderDetailDO> dbOrderDetailList = orderWorldSyncComponent.getOrderDetailByOrderNo(canalOrderInfo.getOrderNo(), canalOrderInfo.getNeedRoute());

      //根据 orderNo 查询支付方式 信息
      List<OrderPayDO> orderPayTypeList = orderWorldSyncComponent.getOrderPayTypeByOrderNo(canalOrderInfo.getOrderNo(), canalOrderInfo.getNeedRoute());
      //根据 orderNo 查询订单金额信息
      OrderAmountDO orderAmount = orderWorldSyncComponent.getOrderAmountByOrderNo(canalOrderInfo.getOrderNo(), canalOrderInfo.getNeedRoute());
      esOrder.setOrderNo(dbOrderInfo.getOrderNo());
      esOrder.setParentOrderNo(dbOrderInfo.getParentOrderNo());
      esOrder.setThirdOrderNo(dbOrderInfo.getThirdOrderNo());
      esOrder.setParentThirdOrderNo(dbOrderInfo.getParentThirdOrderNo());
      esOrder.setCreated(LocalDateTimeUtil.of(dbOrderInfo.getCreated()));
      esOrder.setCreatedDay(dbOrderInfo.getCreatedDay());
      esOrder.setSysCreateTime(LocalDateTimeUtil.of(dbOrderInfo.getSysCreateTime()));
      esOrder.setPayTime(LocalDateTimeUtil.of(dbOrderInfo.getPayTime()));
      esOrder.setMerCode(dbOrderInfo.getMerCode());
      esOrder.setOnlineStoreCode(dbOrderInfo.getOnlineStoreCode());
      esOrder.setOrganizationCode(dbOrderInfo.getOrganizationCode());
      esOrder.setLaunchOrganizationCode(dbOrderInfo.getLaunchOrganizationCode());
      esOrder.setLaunchUserId(dbOrderInfo.getLaunchUserId());
      esOrder.setCompanyCode(dbOrderInfo.getCompanyCode());
      esOrder.setOrderMainStatus(dbOrderInfo.getOrderMainStatus());
      esOrder.setPaymentStatus(dbOrderInfo.getPaymentStatus());
      esOrder.setTransChannel(dbOrderInfo.getTransactionChannel());
      esOrder.setThirdPlatformCode(dbOrderInfo.getThirdPlatformCode());
      esOrder.setBusinessType(dbOrderInfo.getBusinessType());
      esOrder.setAbnormalType(StrUtil.split(dbOrderInfo.getAbnormalType(),","));
      esOrder.setOrderTypes(StrUtil.split(dbOrderInfo.getOrderType(),","));
      esOrder.setStoreDirectJoinType(dbOrderInfo.getStoreDirectJoinType());
      //暂时放实收金额
      esOrder.setOrderAmount(orderAmount.getActualCollectAmount());
      esOrder.setUserId(dbOrderInfo.getUserId());
      esOrder.setLockForWorld(dbOrderInfo.getLockForWorld());
      //查用户信息
      if(StrUtil.isNotBlank(dbOrderInfo.getUserId())){
        OrderUserInfoDO orderUserInfo = orderWorldSyncComponent.getOrderUserInfoByOrderNo(canalOrderInfo.getOrderNo(), canalOrderInfo.getNeedRoute());
        if(ObjectUtil.isNotNull(orderUserInfo)){
          esOrder.setUserCardNo(orderUserInfo.getUserCardNo());
        }
      }
      esOrder.setValid(dbOrderInfo.getIsValid());
      //先置为null
      esOrder.setOrderFlags(null);
      if(StrUtil.isNotBlank(dbOrderInfo.getAbnormalType())){
        esOrder.addOrderFlags(OrderFlag.EXCEPTION);
      }
      if(BooleanUtil.toBoolean(dbOrderInfo.getBookingFlag())){
        esOrder.addOrderFlags(OrderFlag.BOOKING);
      }
      List<EsOrderWorldOrderDetailModel> orderDetailModels = dbOrderDetailList.stream().map(detail -> {
        EsOrderWorldOrderDetailModel orderDetailModel = new EsOrderWorldOrderDetailModel();
        orderDetailModel.setOrderDetailNo(detail.getOrderDetailNo());
        orderDetailModel.setErpCode(detail.getErpCode());
        orderDetailModel.setErpName(detail.getErpName());
        return orderDetailModel;
      }).collect(Collectors.toList());
      esOrder.setDetailList(orderDetailModels);

      if(CollUtil.isNotEmpty(orderPayTypeList)){
        List<String> payTypeList = orderPayTypeList.stream().map(OrderPayDO::getPayType).collect(Collectors.toList());
        esOrder.setPayTypes(payTypeList);
      }
      esOrderList.add(esOrder);
    }

    return esOrderList;
  }

  public boolean efficientData(OrderInfo orderInfo) {
    //暂时只筛选B2B
    if(BusinessType.JOIN_B2B.getType().equalsIgnoreCase(orderInfo.getBusinessType())){
      return true;
    }
    return false;
  }
}