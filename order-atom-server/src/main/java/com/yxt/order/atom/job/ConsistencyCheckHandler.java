package com.yxt.order.atom.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.es.sync.AbstractOrderConsistencyCheck;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 一致性检查
 *
 * @author: moatkon
 * @time: 2024/12/25 10:57
 */
@Component
public class ConsistencyCheckHandler {

  @Resource
  private List<AbstractOrderConsistencyCheck> abstractConsistencyCheckList;

  @Value("${consistencyCheckHandlerEndDateSpecify:10}")
  private Integer consistencyCheckEndDateSpecify; // 指定查多少分钟前的订单

  @XxlJob("consistencyCheckHandler")
  public void execute() {
    for (AbstractOrderConsistencyCheck abstractConsistencyCheck : abstractConsistencyCheckList) {
      Date endDate = OrderDateUtils.previousDateForMinute(new Date(), consistencyCheckEndDateSpecify);
      Date startDate = OrderDateUtils.previousDateForMinute(endDate, consistencyCheckEndDateSpecify);
      abstractConsistencyCheck.checkIfCompensate(startDate,endDate);
    }
  }
}
