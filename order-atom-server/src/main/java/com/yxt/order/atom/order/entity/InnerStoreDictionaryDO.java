package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/14
 */
@Data
@TableName("inner_store_dictionary")
public class InnerStoreDictionaryDO implements Serializable {


    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     *线下门店编码
     */
    private String organizationCode;
    /**
     * 线下门店名称
     */
    private String organizationName;
    /**
     * 内部app_id
     */
    private String innerAppId;
    /**
     * 内部app_secret
     */
    private String innerAppSecret;


    /**
     * 是否开启新流程
     * 1:开启
     * 2:不开启
     */
    private String isOpenNew;

    /**
     *token
     */
    private String token;

    /**
     * 过期时间
     */
    private Long expirationTime;


    /**
     * 是否有自动下账流量(0-没有 1-有)
     */
    private int isAutoBillFlow;

    /**
     *  最近一次 自动下账心跳时间
     */
    private Date autoBillTimestamp;

    /**
     *  pos机模式（1：海典H1，2：海典H2，3：科传）
     * @see  cn.hydee.middle.business.order.Enums.PosModeEnum
     */
    private Integer posMode;

    /**
     * pos请求路径，暂时只有H2配置， H2区分了区域门店
     */
    private String posUrl;

    /**
     * 父级机构编码，关联ds_organization_pos_config
     */
    private String parentOrganizationCode;

}
