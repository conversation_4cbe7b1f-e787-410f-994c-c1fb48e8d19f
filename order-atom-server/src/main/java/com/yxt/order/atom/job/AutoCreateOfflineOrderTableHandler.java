package com.yxt.order.atom.job;



import com.baomidou.dynamic.datasource.annotation.DS;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.OfflineAlarmComponent;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.order.mapper.AutoCreateMapper;
import com.yxt.order.common.CommonDateUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Mq消息表处理器
 *
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年03月06日 15:20
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class AutoCreateOfflineOrderTableHandler {

  @Resource
  private AutoCreateMapper autoCreateMapper;

  @Value("${defaultDsCloudOfflineName:dscloud_offline}")
  private String defaultDsCloudOfflineName;

  @Resource
  private OfflineAlarmComponent offlineAlarmComponent;

  /**
   * @return
   * @throws Exception
   */
  @XxlJob("autoCreateOfflineOrderTableHandler")
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public void execute()  {
    XxlJobHelper.log("autoCreateOfflineOrderTableHandler start-----");
    try {
      Set<String> logicTableSet = OfflineOrderTableShardingHintAlgorithm.logicTableSet();
      XxlJobHelper.log("table list:" + JsonUtils.toJson(logicTableSet));

      List<String> createdTableList = new ArrayList<>();
      for (String table : logicTableSet) {
        String currentTable = table + String.format("_%s", CommonDateUtils.yyMMCurrent(new Date()));
        String tableCreateSql = autoCreateMapper.showCreateTable(currentTable);
        XxlJobHelper.log("原始建表语句:" + tableCreateSql);

        String newTable = table + String.format("_%s", CommonDateUtils.yyMMNext(new Date()));
        String createTable = tableCreateSql
            .replace("CREATE TABLE", "CREATE TABLE IF NOT EXISTS ")
            .replace(currentTable, newTable);

        if (createTable.contains("AUTO_INCREMENT")) {
          createTable = removeAutoIncrement(createTable);
        }

        Integer checkTableExists = autoCreateMapper.checkTableExists(defaultDsCloudOfflineName,
            newTable);
        if (checkTableExists <= 0) {
          autoCreateMapper.createTable(createTable);
          XxlJobHelper.log("新建表语句:" + createTable);
          createdTableList.add(newTable);
        }
      }

      if (!CollectionUtils.isEmpty(createdTableList)) {
        offlineAlarmComponent.autoCreateTableNotify(
            String.format("自动建表成功,新建立的表如下:%s", JsonUtils.toJson(createdTableList)));
      }


    } catch (Exception e) {
      XxlJobHelper.log(e);
      XxlJobHelper.handleFail("autoCreateOfflineOrderTableHandler 执行失败-----");
      return;
    }

    XxlJobHelper.log("autoCreateOfflineOrderTableHandler end-----");
    XxlJobHelper.handleSuccess();
  }

  public static String removeAutoIncrement(String sql) {
    // 正则表达式匹配 AUTO_INCREMENT=数字
    String regex = "(AUTO_INCREMENT=)\\d+";
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(sql);

    // 将 AUTO_INCREMENT 的值替换为 1
    return matcher.replaceAll("$1" + "1");
  }

  public static void main(String[] args) {
    String table = "offline_order";
    System.out.println(table.substring(0, table.length() - 4));
//    System.out.println(CommonDateUtils.yyyyMMNext(new Date()));
    System.out.println(CommonDateUtils.yyMMNext(new Date()));

    String sql = "CREATE TABLE `offline_order_2408` (\n"
        + "  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',\n"
        + "  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部订单号,自己生成',\n"
        + "  `parent_order_no` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内部父单号',\n"
        + "  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员id',\n"
        + "  `third_platform_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台编码,HAIDIAN,内部定义',\n"
        + "  `third_order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台订单号',\n"
        + "  `parent_third_order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台订单号(主)',\n"
        + "  `day_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '每日号',\n"
        + "  `order_state` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单状态 ',\n"
        + "  `created` datetime DEFAULT NULL COMMENT '创单时间',\n"
        + "  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',\n"
        + "  `bill_time` datetime DEFAULT NULL COMMENT '下账时间',\n"
        + "  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',\n"
        + "  `actual_pay_amount` decimal(16,6) DEFAULT NULL COMMENT '实付金额',\n"
        + "  `actual_collect_amount` decimal(16,6) DEFAULT NULL COMMENT '实收金额',\n"
        + "  `coupon_codes` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '劵码(多个,逗号分隔)',\n"
        + "  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',\n"
        + "  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',\n"
        + "  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n"
        + "  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n"
        + "  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',\n"
        + "  `serial_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠券核销流水号(单级别)',\n"
        + "  `store_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店编码',\n"
        + "  `migration` varchar(6) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否迁移订单 TRUE、FALSE',\n"
        + "  `is_on_promotion` varchar(6) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否参加促销 TRUE、FALSE',\n"
        + "  PRIMARY KEY (`id`) USING BTREE,\n"
        + "  UNIQUE KEY `uk_order_no` (`order_no`) USING BTREE,\n"
        + "  UNIQUE KEY `uk_storeCode_third_platform_code_no` (`store_code`,`third_platform_code`,`third_order_no`,`created`) USING BTREE,\n"
        + "  KEY `idx_order_no` (`order_no`) USING BTREE,\n"
        + "  KEY `idx_created_time` (`created_time`) USING BTREE,\n"
        + "  KEY `idx_third_platform_code_no` (`third_platform_code`,`third_order_no`) USING BTREE,\n"
        + "  KEY `idx_parent_order_no` (`parent_order_no`) USING BTREE,\n"
        + "  KEY `idx_updated_time` (`updated_time`) USING BTREE\n"
        + ") ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='线下单主表'";
    System.out.println(sql);
    System.out.println(removeAutoIncrement(sql));

  }

}
