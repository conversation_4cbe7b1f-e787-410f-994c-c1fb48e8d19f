package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.RefundOrderDetailPickDO;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderDetailPickMapper;
import org.springframework.stereotype.Repository;

/**
 * 退单明细拣货信息 Repository
 *
 * <AUTHOR>
 */
@Repository
public class NewRefundOrderDetailPickBatchRepository extends ServiceImpl<NewRefundOrderDetailPickMapper, RefundOrderDetailPickDO> {

}
