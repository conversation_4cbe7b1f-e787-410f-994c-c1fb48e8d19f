package com.yxt.order.atom.order;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.ClientRepository;
import com.yxt.order.atom.order.repository.StoreRepository;
import com.yxt.order.atom.sdk.common.data.StoreBillConfigDTO;
import com.yxt.order.atom.sdk.online_order.client.ClientAtomQueryApi;
import com.yxt.order.atom.sdk.online_order.client.req.OnlineClientQueryReqDto;
import com.yxt.order.atom.sdk.online_order.store.StoreAtomQueryApi;
import com.yxt.order.atom.sdk.online_order.store.req.GetOnlineStoreByPlatformShopIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigByIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryClientReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStoreAccessReq;
import com.yxt.order.atom.sdk.online_order.store.req.QuerySysStoreInfoReq;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreResDto;
import com.yxt.order.atom.sdk.online_order.store.res.OnlineStoreInfoResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsStoreResDto;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ClientAtomController implements ClientAtomQueryApi {

  @Resource
  private ClientRepository clientRepository;

  @Override
  public ResponseBase<List<DsOnlineClientResDto>> queryClient(OnlineClientQueryReqDto req) {
    return ResponseBase.success(clientRepository.queryClient(req));
  }
}
