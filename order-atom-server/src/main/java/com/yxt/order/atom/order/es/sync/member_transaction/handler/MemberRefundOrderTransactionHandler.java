package com.yxt.order.atom.order.es.sync.member_transaction.handler;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.sync.clean.ExpireDaysConstant;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberOrderSource;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberRefundStatus;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberRefundOrderModel;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberRefundOrderModel.MemberRefundOrderDetailModel;
import com.yxt.order.atom.order.es.sync.member_transaction.utils.OnlineOrderStoreTypeUtils;
import com.yxt.order.atom.order.mapper.SyncEsMapper;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.AfterSaleTypeEnum;
import com.yxt.order.types.offline.enums.RefundTypeEnum;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2024/12/9 11:49
 */
@Component
@Slf4j
public class MemberRefundOrderTransactionHandler extends
    AbstractCanalHandler<CanalRefundOrder, MemberRefundOrderModel> {

  @Resource
  protected SyncEsMapper syncEsMapper;

  @Resource
  private SyncComponent syncComponent;
  public MemberRefundOrderTransactionHandler() {
    super(CanalRefundOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.REFUND_ORDER);
  }

  @Override
  protected List<MemberRefundOrderModel> assemble() {
    List<RefundOrder> refundOrderList = getData().getData();
    if (CollectionUtils.isEmpty(refundOrderList)) {
      return Lists.newArrayList();
    }
    return refundOrderList.stream()
        // 需要有userId的数据
        .filter(this::efficientData)
        .map(refundOrder -> {
          MemberRefundOrderModel memberRefundOrderModel = new MemberRefundOrderModel();
          memberRefundOrderModel.setStoreCode(refundOrder.getOrganizationCode());
          memberRefundOrderModel.setRefundType(refundOrderRefundType(refundOrder.getRefundNo(),refundOrder.getType()));
          memberRefundOrderModel.setRefundStatus(MemberRefundStatus.getByState(refundOrder.getState()));
          memberRefundOrderModel.setOrderSource(MemberOrderSource.ONLINE.name());
          memberRefundOrderModel.setRefundNo(refundOrder.getRefundNo());
          memberRefundOrderModel.setOrderNo(refundOrder.getOrderNo());
          memberRefundOrderModel.setAfterSaleType(refundOrderAfterSaleType(refundOrder.getRefundNo(),refundOrder.getRefundType()));
          memberRefundOrderModel.setCreated(refundOrder.getCreateTime());
          memberRefundOrderModel.setCreateTime(refundOrder.getCreateTime());
          memberRefundOrderModel.setUserCardNo(syncEsMapper.selectMemberCardNo(Long.valueOf(refundOrder.getOrderNo())));
          memberRefundOrderModel.setUserId(syncComponent.queryRefundOrderNo(refundOrder.getOrderNo()));
          memberRefundOrderModel.setThirdRefundNo(refundOrder.getThirdRefundNo());
          memberRefundOrderModel.setThirdOrderNo(refundOrder.getThirdOrderNo());
          memberRefundOrderModel.setPlatformCode(String.valueOf(PlatformCodeEnum.getByCode(refundOrder.getThirdPlatformCode())));
          memberRefundOrderModel.setStoreType(OnlineOrderStoreTypeUtils.storeType(refundOrder.getOrganizationCode()).name());

          List<RefundDetailDO> refundDetailList = syncComponent.getRefundDetailListByRefundNo(
              Lists.newArrayList(Long.valueOf(refundOrder.getRefundNo())));
          if(!CollectionUtils.isEmpty(refundDetailList)){
            memberRefundOrderModel.setMemberRefundOrderDetailModelList(refundDetailList.stream()
                .map(item->{
                  MemberRefundOrderDetailModel detailModel = new MemberRefundOrderDetailModel();
                  detailModel.setErpCode(item.getErpCode());
                  detailModel.setErpName(item.getCommodityName());
                  return detailModel;
                })
                .collect(Collectors.toList())
            );
          }

          return memberRefundOrderModel;
        }).collect(Collectors.toList());
  }

  public Boolean efficientData(RefundOrder refundOrder){
    return
        !OrderDateUtils.isExpired(refundOrder.getCreateTime(), ExpireDaysConstant.EsMemberOrderEfficientDays)
            && !StringUtils.isEmpty(syncComponent.queryRefundOrderNo(refundOrder.getOrderNo()));
  }

  /**
   *   // `refund_type` '退货类型  0、仅退款，1、退货退款',
   * @param refundType
   * @return
   */
  private String refundOrderAfterSaleType(String refundNo,String refundType) {
    if("0".equals(refundType)){
      return AfterSaleTypeEnum.AFTER_SALE_AMOUNT.name();
    }else if("1".equals(refundType)){
      return AfterSaleTypeEnum.AFTER_SALE_AMOUNT_GOODS.name();
    }else {
      log.info(String.format("退单号:%s, %s 未匹配到售后单类型",refundNo,refundType));
      return Strings.EMPTY;
    }
  }

  /**
   * 退款类型,0部分退款，1全额退款
   * @param type
   * @return
   */
  private String refundOrderRefundType(String refundNo,String type) {
    if("0".equals(type)){
      return RefundTypeEnum.PART.name();
    }else if("1".equals(type)){
      return RefundTypeEnum.ALL.name();
    }else {
      log.info(String.format("退单号:%s,%s 未匹配到退款类型",refundNo,type));
      return Strings.EMPTY;
    }
  }
}
