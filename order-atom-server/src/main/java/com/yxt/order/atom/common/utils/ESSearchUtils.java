package com.yxt.order.atom.common.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

public class ESSearchUtils {

  public static List<Object> parseSearchAfter(String searchAfter) {
    if (StrUtil.isBlank(searchAfter)) {
      return null;
    }
    return JSON.parseArray(searchAfter, Object.class).stream().map(temp -> {
      if (temp instanceof BigDecimal) {
        BigDecimal bigDecimalValue = (BigDecimal) temp;
        return bigDecimalValue.doubleValue();
      }
      return temp;
    }).collect(Collectors.toList());
  }
}
