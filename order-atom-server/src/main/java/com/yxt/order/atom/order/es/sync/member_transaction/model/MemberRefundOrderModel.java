package com.yxt.order.atom.order.es.sync.member_transaction.model;

import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsMemberRefundOrder;
import com.yxt.order.atom.order.es.doc.EsMemberRefundOrderDetail;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * 会员订单
 * @author: moatkon
 * @time: 2024/12/9 10:29
 */
@Data
public class MemberRefundOrderModel  extends BaseEsIndexModel {

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   */
  private String storeType;

  /**
   * 门店
   */
  private String storeCode;

  /**
   * 退款类型, PART_REFUND-部分退款，REFUND_AMOUNT-全额退款
   */
  private String refundType;

  /**
   * 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消
   */
  private Integer refundStatus;

  /**
   * 退款时间
   */
  private Date created;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  private String orderSource;

  /**
   * 系统退单号
   */
  private String refundNo;

  /**
   * 系统单号
   */
  private String orderNo;

  /**
   * 售后单类型 AFTER_SALE_AMOUNT-退款 、AFTER_SALE_GOODS-退货 、AFTER_SALE_AMOUNT_GOODS-退货退款
   */
  private String afterSaleType;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 会员编码(唯一值)
   */
  private String userCardNo;

  /**
   * 会员ID (心云)
   */
  private String userId;

  /**
   * 三方退单号
   */
  private String thirdRefundNo;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传
   */
  private String platformCode;

  private List<MemberRefundOrderDetailModel> memberRefundOrderDetailModelList;

  public EsMemberRefundOrder create() {
    EsMemberRefundOrder esMemberRefundOrder = new EsMemberRefundOrder();
    esMemberRefundOrder.setStoreType(this.getStoreType());
    esMemberRefundOrder.setStoreCode(this.getStoreCode());
    esMemberRefundOrder.setRefundType(this.getRefundType());
    esMemberRefundOrder.setRefundStatus(this.getRefundStatus());
    esMemberRefundOrder.setCreated(this.getCreated());
    esMemberRefundOrder.setOrderSource(this.getOrderSource());
    esMemberRefundOrder.setRefundNo(this.getRefundNo());
    esMemberRefundOrder.setOrderNo(this.getOrderNo());
    esMemberRefundOrder.setAfterSaleType(this.getAfterSaleType());
    esMemberRefundOrder.setCreateTime(this.getCreateTime());
    esMemberRefundOrder.setUserCardNo(this.getUserCardNo());
    esMemberRefundOrder.setUserId(this.getUserId());
    esMemberRefundOrder.setThirdRefundNo(this.getThirdRefundNo());
    esMemberRefundOrder.setThirdOrderNo(this.getThirdOrderNo());
    esMemberRefundOrder.setPlatformCode(this.getPlatformCode());
    esMemberRefundOrder.setId(defineId());// 自定义Id,避免重复d
    List<MemberRefundOrderDetailModel> refundOrderDetailModelList = this.getMemberRefundOrderDetailModelList();
    if(!CollectionUtils.isEmpty(refundOrderDetailModelList)){
      esMemberRefundOrder.setEsMemberRefundOrderDetailList(refundOrderDetailModelList.stream()
          .map(item->{
            EsMemberRefundOrderDetail detail = new EsMemberRefundOrderDetail();
            detail.setErpCode(item.getErpCode());
            detail.setErpName(item.getErpName());
            return detail;
          })
          .collect(Collectors.toList()));
    }

    return esMemberRefundOrder;
  }

  public String routeKey(){
    return this.userId;
  }

  public String defineId(){
    return this.getPlatformCode()+"-"+this.getRefundNo();
  }


  @Data
  public static class MemberRefundOrderDetailModel {

    private String erpCode;

    private String erpName;

  }

}
