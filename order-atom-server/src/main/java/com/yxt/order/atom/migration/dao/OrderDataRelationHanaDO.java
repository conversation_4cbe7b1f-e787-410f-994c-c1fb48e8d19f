package com.yxt.order.atom.migration.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 订单数据和Hana数据之间的映射
 * @author: moatkon
 * @time: 2025/3/15 11:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("order_data_relation_hana")
public class OrderDataRelationHanaDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 类型 OFFLINE_ORDER\OFFLINE_REFUND_ORDER
   */
  private String type;

  /**
   * 业务编码
   */
  private String businessNo;

  /**
   * 目标schema
   */
  private String targetSchema;

  /**
   * hana归档库Id
   */
  private Long hanaId;
}
