package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.order.entity.MqMessageDO;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageDeleteReqDto;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageQueryReqDto;
import com.yxt.order.atom.sdk.mqmessage.res.MqMessageResDto;
import java.util.List;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:38
 * @email: <EMAIL>
 */
public interface MqMessageRepository {

  void save(MqMessageDO mqMessageDO);

  List<MqMessageResDto> list(MqMessageQueryReqDto mqMessageQueryReqDto);

  Boolean delete(MqMessageDeleteReqDto deleteReqDto);

}
