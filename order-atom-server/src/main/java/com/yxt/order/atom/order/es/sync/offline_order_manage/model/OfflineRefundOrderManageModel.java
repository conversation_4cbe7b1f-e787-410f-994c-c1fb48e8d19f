package com.yxt.order.atom.order.es.sync.offline_order_manage.model;

import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsOfflineRefundOrderManage;
import com.yxt.order.atom.order.es.doc.EsOfflineRefundOrderManageDetail;
import com.yxt.order.types.offline.enums.AfterSaleTypeEnum;
import com.yxt.order.types.offline.enums.RefundTypeEnum;
import com.yxt.order.types.offline.enums.StoreDirectJoinTypeEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/4/1 10:55
 */
@Data
public class OfflineRefundOrderManageModel extends BaseEsIndexModel {


  /**
   * 系统退款单号
   */
  private String refundNo;

  /**
   * 系统订单号
   */
  private String orderNo;

  /**
   * POS
   *
   * @see ThirdPlatformCodeEnum
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 平台退款单号
   */
  private String thirdRefundNo;

  /**
   * 退款类型
   *
   * @see RefundTypeEnum
   */
  private String refundType;

  /**
   * 售后类型
   *
   * @see AfterSaleTypeEnum
   */
  private String afterSaleType;

  /**
   * 门店直营加盟类型
   *
   * @see StoreDirectJoinTypeEnum
   */
  private String storeDirectJoinType;

  /**
   * 门店
   */
  private String storeCode;

  /**
   * 退款时间
   */
  private Date created;

  /**
   * 公司编码
   */
  private String companyCode;

  private BigDecimal consumerRefund;


  /**
   * 退单商品明细
   */
  private List<OfflineRefundOrderManageDetailModel> offlineRefundOrderManageDetailModelList;

  @Data
  public static class OfflineRefundOrderManageDetailModel {

    private String erpCode;
  }


  public EsOfflineRefundOrderManage create() {

    EsOfflineRefundOrderManage refundOrderManage = new EsOfflineRefundOrderManage();
    refundOrderManage.setId(defineId());
    refundOrderManage.setRefundNo(this.getRefundNo());
    refundOrderManage.setOrderNo(this.getOrderNo());
    refundOrderManage.setThirdPlatformCode(this.getThirdPlatformCode());
    refundOrderManage.setThirdOrderNo(this.getThirdOrderNo());
    refundOrderManage.setThirdRefundNo(this.getThirdRefundNo());
    refundOrderManage.setRefundType(this.getRefundType());
    refundOrderManage.setAfterSaleType(this.getAfterSaleType());
    refundOrderManage.setStoreDirectJoinType(this.getStoreDirectJoinType());
    refundOrderManage.setStoreCode(this.getStoreCode());
    refundOrderManage.setCreated(this.getCreated());
    refundOrderManage.setCompanyCode(this.getCompanyCode());
    refundOrderManage.setConsumerRefund(this.getConsumerRefund());

    if (!CollectionUtils.isEmpty(this.offlineRefundOrderManageDetailModelList)) {
      refundOrderManage.setEsOfflineRefundOrderManageDetailList(
          this.offlineRefundOrderManageDetailModelList.stream().map(item -> {
            EsOfflineRefundOrderManageDetail detailModel = new EsOfflineRefundOrderManageDetail();
            detailModel.setErpCode(item.getErpCode());
            return detailModel;
          }).collect(Collectors.toList()));
    }

    return refundOrderManage;
  }


  /**
   * 每个分表内refundNo是唯一的,不涉及到线上单,所以可以直接使用业务唯一键
   *
   * @return
   */
  public String defineId() {
    return this.getRefundNo();
  }


}
