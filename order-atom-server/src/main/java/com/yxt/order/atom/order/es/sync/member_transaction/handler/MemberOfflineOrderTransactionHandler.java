package com.yxt.order.atom.order.es.sync.member_transaction.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_ORDER_REGEX;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.sync.clean.ExpireDaysConstant;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberOrderSource;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberOrderStatus;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberOrderModel;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberOrderModel.MemberOrderDetailModel;
import com.yxt.order.atom.order.mapper.dto.OrganizationInfoDto;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2024/12/9 11:49
 */
@Component
public class MemberOfflineOrderTransactionHandler extends
    AbstractCanalHandler<CanalOfflineOrder, MemberOrderModel> {

  @Resource
  protected SyncComponent syncComponent;

  public MemberOfflineOrderTransactionHandler() {
    super(CanalOfflineOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();

    return Database.DSCLOUD_OFFLINE.equals(database) && Table.tableRegex(OFFLINE_ORDER_REGEX,
        table);
  }

  @Override
  protected List<MemberOrderModel> assemble() {
    List<OfflineOrder> offlineOrderList = getData().getData();
    if (CollectionUtils.isEmpty(offlineOrderList)) {
      return Lists.newArrayList();
    }

    return offlineOrderList.stream()
        .filter(this::efficientData)
        .map(offlineOrder -> {
          MemberOrderModel memberOrderModel = new MemberOrderModel();
          memberOrderModel.setUserId(offlineOrder.getUserId());
          memberOrderModel.setCreated(offlineOrder.getCreated());
          memberOrderModel.setCreateTime(offlineOrder.getCreatedTime());
          memberOrderModel.setStoreCode(offlineOrder.getStoreCode());
          memberOrderModel.setUserCardNo(syncComponent.getUserCardNoByOrderNo(offlineOrder.getOrderNo(),offlineOrder.getNeedRoute()));
          memberOrderModel.setOrderSource(MemberOrderSource.POS.name());
          memberOrderModel.setPlatformCode(String.valueOf(ThirdPlatformCodeEnum.parse(offlineOrder.getThirdPlatformCode())));
          memberOrderModel.setOrderStatus(MemberOrderStatus.COMPLETED.getStatus()); // 线下单都是已完成
          memberOrderModel.setThirdOrderNo(offlineOrder.getThirdOrderNo());
          memberOrderModel.setOrderNo(offlineOrder.getOrderNo());
          OrganizationInfoDto organization = syncComponent.getOrganizationByOrderNo(offlineOrder.getOrderNo(),offlineOrder.getNeedRoute());
          if(Objects.nonNull(organization)){
            memberOrderModel.setCompanyCode(organization.getCompanyCode());
            memberOrderModel.setStoreType(organization.getStoreType());
          }

          List<OfflineOrderDetailDO> detailList = syncComponent.getOfflineOrderDetailByOrderNo(
              offlineOrder.getOrderNo(), offlineOrder.getNeedRoute());
          if(!CollectionUtils.isEmpty(detailList)){
            memberOrderModel.setOrderDetailModelList(detailList.stream().map(item->{
              MemberOrderDetailModel detailModel = new MemberOrderDetailModel();
              detailModel.setErpCode(item.getErpCode());
              detailModel.setErpName(item.getErpName());
              return detailModel;
            }).collect(Collectors.toList()));
          }

          return memberOrderModel;
        }).collect(Collectors.toList());
  }

  public Boolean efficientData(OfflineOrder offlineOrder){
    return
        !offlineOrder.migrateOrder() &&
        !OrderDateUtils.isExpired(offlineOrder.getCreated(), ExpireDaysConstant.EsMemberOrderEfficientDays)
        && !StringUtils.isEmpty(offlineOrder.getUserId())
        && ThirdPlatformCodeEnum.isValid(offlineOrder.getThirdPlatformCode())
        ;
  }
}
