package com.yxt.order.atom.order.es.doc;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * OmsOrderInfo条件索引
 */
@Data
@IndexName(value = "es_oms_order_info",keepGlobalPrefix = true)
public class EsOmsOrderInfo implements Serializable {

    @IndexId(type = IdType.CUSTOMIZE)
    private Long id;

    @IndexField(fieldType = FieldType.LONG)
    private Long omsOrderNo;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String clientCode;


    /**
     * 逻辑删除字段 默认0-未删除
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long deleted;

    /**
     * 商户编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String merCode;

    /**
     * 订单归属类型 0-为商户，默认；1-为供应商订单
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer orderOwnerType;
    /**
     * 供应商编码 无供应商的默认填0,便于查询
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String supplierCode;

    /**
     * 订单状态:10待审核,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭, (特别注意 5是处方单)
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer orderStatus;

    @IndexField(fieldType = FieldType.INTEGER)
    private Integer erpStatus;



    /**
     * 下单真实时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date created;


    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 审核时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 发货时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date shipTime;

    @IndexField(fieldType = FieldType.INTEGER)
    private Integer shipStatus;

    /**
     * 取消时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    /**
     * 完成时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /**
     * 完成时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date billTime;

    /**
     * 推广门店
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String spreadStoreCode;

    /**
     * 是否为邮费单 0否，1是
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer isPostFeeOrder;

    /**
     * 异常状态:  0.无异常 1.异常
     * 1,疑似刷单订单
     * 2，订单金额异常
     * 3，商品库存不足
     * 4，商品数量异常
     * 5，商品不存在
     * 6，订单预估毛利异常
     * 7，仓库发货失败
     * 8，平台发货失败
     * 11. 修改地址失败
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer exStatus;


    /**
     * 创建时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 快递id
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer expressId;

    /**
     * 仓库id
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String warehouseId;

    /**
     * 是否是审方单（结合审方配置）
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer isPrescription;

    /**
     * 平台编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String thirdOrderNo;

    /**
     * 发货单号
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long omsShipNo;

    /**
     * 快递单号
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String expressNumber;

    /**
     * 三方平台买家昵称
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String buyerName;

    /**
     * 买家留言
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String buyerMessage;

    /**
     * 卖家备注
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String sellerRemark;

    /**
     * 订单类型，1 平台订单（常规的）、2 导入订单（导）、3 手工订单（手）、4 拆分订单（拆）
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer orderType;

    /**
     * 订单拆分状态：0 未拆单，1 拆分订单的源头订单，2 拆分的最终订单，3 拆分中间状态
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer splitStatus;


    /**
     * 快递面单状态：0-未生成，1-未打印，2-已打印
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String sheetStatus;

    /**
     * 系统备注
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String remark;

    /**
     * 线上门店编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String onlineStoreCode;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String onlineStoreType;

    /**
     * 订单商品总数量
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer goodsQty;

    /**
     * 订单商品种类数
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer goodsCategoryQty;

    /**
     * tag字段里的json解析出来后
     * 空单标识：1
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer tagEmptyOrderStatus;

    /**
     * 合单标识：2
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer tagMergeOrderStatus;

    /**
     * 预售单标识：3
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer tagPreSellOrderStatus;

    /**
     * 修改地址标识：4
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer tagModifyAddressTagStatus;

    /**
     * 拦截失败标识：5
     */
    @IndexField(fieldType = FieldType.NESTED, nestedClass = LogisticsTagItem.class)
    private List<LogisticsTagItem> tagLogisticsIntercept;

    /**
     * 物流修改地址标识：6
     */
    @IndexField(fieldType = FieldType.NESTED, nestedClass = LogisticsTagItem.class)
    private List<LogisticsTagItem> tagLogisticsUpAddress;

    /**
     * 代发单标识：7
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer tagAgentDeliveryOrder;

    /**
     * 备货状态 2-已备货 1-待备货
     *
     * 云仓订单专用
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String stockState;

    /**
     * 发货单打印次数
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer sendOrderPrintNum; // 原字段 sendorderPrintNum

    @IndexField(fieldType = FieldType.INTEGER)
    private Integer refundCount;

    /**
     * ERP审核中
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer erpAuditStatus;


    /**
     * erp_code_list
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String erpCodeList;


    // *********platform_order_info条件索引平铺**********

    /**
     * 是否处方药 0普通订单，1 处方药订单
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String havecfy;

    /**
     * 处方药审核状态 0 未审核 1 已审核 -1无需审核 2审核不通过 3未知状态
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String rxAuditStatus;

    // *********order_pay_info 条件索引平铺**********
    /**
     * 支付方式,1是在线支付,2是货到付款吧
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String payType;

    /**
     * 客户实付
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal buyerActualAmount;

    // *********order_delivery_address  条件索引平铺**********
    /**
     * 收货人手机
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String receiverMobile;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String receiverTelephone;

    /**
     * 收货人名
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String receiverName;

    /**
     * 完整详细地址
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String fullAddress;

    // *********oms_order_ex 条件索引平铺**********
    /**
     * 异常处理结果 0-未处理 1-已处理
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer operateStatus;

    /**
     * 异常状态:  0.无异常 1.异常,具体异常查看枚举类
     */
    @ApiModelProperty("异常状态:  0.无异常 1.异常,具体异常查看枚举类")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer exType;


    @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOmsOrderItem.class)
    private List<EsOmsOrderItem> esOmsOrderItemList;

    @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOmsOrderItem.class)
    private List<EsOmsOrderEx> esOmsOrderExList;



    //--------------------------- logistic_order表
    /**
     * 系统订单号
     */
//  private Long omsOrderNo;
    /**
     * 平台code
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String logisticOrderOfPlatformCode;

    /**
     * 1有效 0失效
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer logisticOrderOfStatus;

    /**
     * 物流配置id 关联的是logistic_config_info
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long logisticOrderOfLogisticConfigId;

    //--------------------------- left join所以平铺 logistic_config_info
    /**
     * 模板id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long logisticConfigInfoOfStandardTemplateId;

}
