package com.yxt.order.atom.order.es.sync;

import com.yxt.common.logic.flash.Scene;
import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.order.enums.OrderSource;


public abstract class AbstractFlashEnhance<Source, CanalData, BusinessScene extends Scene> extends AbstractFlash<Source, CanalData, BusinessScene> {

  public abstract OrderSource getOrderSource();

  public abstract NumberType getOrderType();

}
