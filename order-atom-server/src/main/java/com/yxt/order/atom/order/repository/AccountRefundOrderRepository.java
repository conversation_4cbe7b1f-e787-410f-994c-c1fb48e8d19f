package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.order.entity.AccountOrderDO;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountOrderReqDto;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountRefundOrderReqDto;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:28
 * @email: <EMAIL>
 */
public interface AccountRefundOrderRepository {



  Boolean insertAccountRefundOrder(ApplyAccountRefundOrderReqDto req);



}
