package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.OrderCouponDO;
import com.yxt.order.atom.order_world.entity.OrderInfoDO;
import com.yxt.order.atom.order_world.mapper.NewOrderCouponMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderInfoMapper;
import org.springframework.stereotype.Repository;

@Repository
public class NewOrderInfoBatchRepository extends ServiceImpl<NewOrderInfoMapper, OrderInfoDO> {

}
