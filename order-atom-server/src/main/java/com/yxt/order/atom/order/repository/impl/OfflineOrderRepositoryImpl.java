package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.configration.AtomExceptionUtil;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.sharding.YxtOrderSharding;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.common.utils.ShardingUtils;
import com.yxt.order.atom.dto.BigDataRefundInfoDto;
import com.yxt.order.atom.dto.SimpleOfflineOrderDetailDto;
import com.yxt.order.atom.dto.SimpleOfflineRefundOrderDetailDto;
import com.yxt.order.atom.dto.SimpleOfflineRefundOrderDetailDto.AppendOfflineOrderInfo;
import com.yxt.order.atom.dto.SimpleOfflineRefundOrderDetailDto.OfflineOrderDetailPickEnhance;
import com.yxt.order.atom.job.compensate.mongo.CompensateResultV1;
import com.yxt.order.atom.job.compensate.mongo.CompensateRetry;
import com.yxt.order.atom.job.compensate.mongo.CompensateType;
import com.yxt.order.atom.migration.config.ThreadPoolConfig;
import com.yxt.order.atom.migration.dao.mongo.MigrationDelayMapping;
import com.yxt.order.atom.migration.fix.component.RemoveEsOrderRepeatedData;
import com.yxt.order.atom.migration.service.MigrationDelayOrderService;
import com.yxt.order.atom.migration.utils.DateRangeUtil;
import com.yxt.order.atom.migration.utils.DateRangeUtil.StartEndDate;
import com.yxt.order.atom.order.converter.OfflineOrder2DtoConverter;
import com.yxt.order.atom.order.entity.BigDataRefundInfoDO;
import com.yxt.order.atom.order.entity.DeletedDataDO;
import com.yxt.order.atom.order.entity.OfflineOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineOrderCouponDO;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailTraceDO;
import com.yxt.order.atom.order.entity.OfflineOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineOrderPayDO;
import com.yxt.order.atom.order.entity.OfflineOrderPrescriptionDO;
import com.yxt.order.atom.order.entity.OfflineOrderPromotionDO;
import com.yxt.order.atom.order.entity.OfflineOrderUserDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailTraceDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderPayDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderUserDO;
import com.yxt.order.atom.order.mapper.BigDataRefundInfoMapper;
import com.yxt.order.atom.order.mapper.CommonMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderCashierDeskMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderCouponMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailPickMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailTraceMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMedInsSettleMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderOrganizationMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderPayMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderPrescriptionMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderPromotionMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderUserMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderCashierDeskMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderDetailMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderDetailPickMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderDetailTraceMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMedInsSettleMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderOrganizationMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderPayMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderUserMapper;
import com.yxt.order.atom.order.repository.DeleteDataRepository;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.order.repository.OfflineOrderSearchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineOrderCouponBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineOrderDetailBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineOrderDetailPickBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineOrderDetailTraceBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineOrderPayBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineOrderPromotionBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineRefundOrderDetailBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineRefundOrderDetailPickBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineRefundOrderDetailTraceBatchRepository;
import com.yxt.order.atom.order.repository.batch.OfflineRefundOrderPayBatchRepository;
import com.yxt.order.atom.sdk.AtomSdkErrorCode;
import com.yxt.order.atom.sdk.offline_order.dto.ExistOfflineOrderResDto;
import com.yxt.order.atom.sdk.offline_order.dto.ExistOfflineRefundOrderResDto;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineOrderByDateResDto;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineOrderByDateResDto.OrderData;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineRefundOrderByDateResDto;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineRefundOrderByDateResDto.RefundData;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.req.CommonOfflineRefundQueryReqDto;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineOrderByDateCondition;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineOrderByDateReqDto;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineRefundOrderByDateReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoQryByScaleBatchReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundAmountReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundInfoQryBatchReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundInfoQryReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.UnionOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.res.CommonOfflineRefundResDto;
import com.yxt.order.atom.sdk.offline_order.res.ExistOrderInfo;
import com.yxt.order.atom.sdk.offline_order.res.ExistRefundOrderInfo;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderInfoResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundAmountResDTO;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundAmountResDTO.RefundOrderAmount;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.UnionOrderResDto;
import com.yxt.order.common.utils.CompletableFutureUtils;
import com.yxt.order.common.utils.OrderJsonUtils;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.DataDimensionTypeEnum;
import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.RefundOrderNo;
import com.yxt.order.types.order.enums.OrderQryScaleEnum;
import com.yxt.order.types.order.enums.RefundQryScaleEnum;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.shardingsphere.api.hint.HintManager;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月07日 16:26
 * @email: <EMAIL>
 */
@Repository
@Slf4j
public class OfflineOrderRepositoryImpl implements OfflineOrderRepository {

  @Value("${spring.application.name:未配置应用程序名}")
  protected String applicationName;

  @Resource
  protected DeleteDataRepository deleteDataRepository;

  @Resource
  private RemoveEsOrderRepeatedData removeEsOrderRepeatedData;

  @Resource
  private CommonMapper commonMapper;
  @Resource
  private MongoTemplate mongoTemplate;
  @Resource
  private OfflineOrderMapper offlineOrderMapper;
  @Resource
  private OfflineOrderMedInsSettleMapper offlineOrderMedInsSettleMapper;
  @Resource
  private OfflineRefundOrderMedInsSettleMapper offlineRefundOrderMedInsSettleMapper;
  @Resource
  private OfflineRefundOrderUserMapper offlineRefundOrderUserMapper;
  @Resource
  private OfflineOrderOrganizationMapper offlineOrderOrganizationMapper;
  @Resource
  private OfflineOrderCashierDeskMapper offlineOrderCashierDeskMapper;
  @Resource
  private OfflineOrderUserMapper offlineOrderUserMapper;
  @Resource
  private OfflineOrderPrescriptionMapper offlineOrderPrescriptionMapper;
  @Resource
  private OfflineOrderPayMapper offlineOrderPayMapper;
  @Resource
  private OfflineOrderDetailMapper offlineOrderDetailMapper;
  @Resource
  private OfflineOrderDetailPickMapper offlineOrderDetailPickMapper;
  @Resource
  private OfflineRefundOrderOrganizationMapper offlineRefundOrderOrganizationMapper;
  @Resource
  private OfflineRefundOrderCashierDeskMapper offlineRefundOrderCashierDeskMapper;
  @Resource
  private OfflineOrderPromotionMapper offlineOrderPromotionMapper;

  @Resource
  private OfflineOrderCouponMapper offlineOrderCouponMapper;

  @Resource
  private OfflineOrderPromotionBatchRepository offlineOrderPromotionBatchRepository;

  @Resource
  private OfflineOrderCouponBatchRepository offlineOrderCouponBatchRepository;

  @Resource
  private OfflineOrderPayBatchRepository offlineOrderPayBatchRepository;

  @Resource
  private OfflineOrderDetailBatchRepository offlineOrderDetailBatchRepository;

  @Resource
  private OfflineOrderDetailTraceBatchRepository offlineOrderDetailTraceBatchRepository;

  @Resource
  private OfflineRefundOrderDetailTraceBatchRepository offlineRefundOrderDetailTraceBatchRepository;

  @Resource
  private OfflineRefundOrderDetailPickBatchRepository offlineRefundOrderDetailPickBatchRepository;

  @Resource
  private OfflineOrderDetailPickBatchRepository offlineOrderDetailPickBatchRepository;
  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private OfflineRefundOrderDetailMapper offlineRefundOrderDetailMapper;
  @Resource
  private OfflineRefundOrderDetailTraceMapper offlineRefundOrderDetailTraceMapper;
  @Resource
  private OfflineRefundOrderDetailPickMapper offlineRefundOrderDetailPickMapper;

  @Resource
  private OfflineRefundOrderPayMapper offlineRefundOrderPayMapper;

  @Resource
  private OfflineRefundOrderDetailBatchRepository offlineRefundOrderDetailBatchRepository;
  @Resource
  private OfflineRefundOrderPayBatchRepository offlineRefundOrderPayBatchRepository;

  @Resource
  private MigrationDelayOrderService migrationDelayOrderService;

  @Value("${compensateYYmms:2406,2407,2408,2409,2410,2411,2412}") // 当前年月表,刷数只用一次
  private String compensateYYmms;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_POOL)
  private Executor orderSearchPool;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_SUB_POOL)
  private Executor orderSearchSubPool;

  @Resource
  private BigDataRefundInfoMapper bigDataRefundInfoMapper;

  @Resource
  private OfflineOrderSearchRepository offlineOrderSearchRepository;
  @Autowired
  private OfflineOrderDetailTraceMapper offlineOrderDetailTraceMapper;

  @Override
  @Transactional
  public void saveOfflineOrderDO(OfflineOrderDO offlineOrderDO) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(offlineOrderDO.getOrderNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      // 入库前校验是否存在
      OfflineOrderExistsReqDto existsReqDto = new OfflineOrderExistsReqDto();
      existsReqDto.setStoreCode(offlineOrderDO.getStoreCode());
      existsReqDto.setThirdOrderNo(offlineOrderDO.getThirdOrderNo());
      existsReqDto.setThirdPlatformCode(offlineOrderDO.getThirdPlatformCode());
      existsReqDto.setThirdCreated(offlineOrderDO.getCreated());
      if (offlineThirdOrderExists(existsReqDto)) {
        log.warn("理论上无,重复数据,{}", JSONUtil.toJsonStr(offlineOrderDO));
        return;
      }

      if (Objects.nonNull(offlineOrderDO.getOrganizationDO())) {
        offlineOrderOrganizationMapper.insert(offlineOrderDO.getOrganizationDO());
      }

      // 订单医保信息
      OfflineOrderMedInsSettleDO orderMedInsSettleDO = offlineOrderDO.getOrderMedInsSettleDO();
      if (Objects.nonNull(orderMedInsSettleDO)) {
        offlineOrderMedInsSettleMapper.insert(orderMedInsSettleDO);
      }

      if (Objects.nonNull(offlineOrderDO.getCashierDeskDO())) {
        offlineOrderCashierDeskMapper.insert(offlineOrderDO.getCashierDeskDO());
      }

      // 会员信息可能无
      if (Objects.nonNull(offlineOrderDO.getUserDO())) {
        offlineOrderUserMapper.insert(offlineOrderDO.getUserDO());
      }

      // 处方信息可能无
      if (Objects.nonNull(offlineOrderDO.getPrescriptionDO())) {
        offlineOrderPrescriptionMapper.insert(offlineOrderDO.getPrescriptionDO());
      }

      offlineOrderPayBatchRepository.saveBatch(offlineOrderDO.getPayDOList());
      // 明细
      List<OfflineOrderDetailDO> detailDOList = offlineOrderDO.getDetailDOList();
      offlineOrderDetailBatchRepository.saveBatch(detailDOList);

      // 订单促销信息
      List<OfflineOrderPromotionDO> offlineOrderPromotionDOList = offlineOrderDO.getOfflineOrderPromotionDOList();
      if (!CollectionUtils.isEmpty(offlineOrderPromotionDOList)) {
        offlineOrderPromotionBatchRepository.saveBatch(offlineOrderPromotionDOList);
      }

      // 订单coupon信息
      List<OfflineOrderCouponDO> offlineOrderCouponDOList = offlineOrderDO.getOfflineOrderCouponDOList();
      if (!CollectionUtils.isEmpty(offlineOrderCouponDOList)) {
        offlineOrderCouponBatchRepository.saveBatch(offlineOrderCouponDOList);
      }

      // 明细拣货信息
      List<OfflineOrderDetailPickDO> detailPickDOS = detailDOList.stream()
          .map(OfflineOrderDetailDO::getDetailPickDOList)
          .filter(detailPickDOList -> !CollectionUtils.isEmpty(detailPickDOList))
          .flatMap(Collection::stream).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(detailPickDOS)) {
        offlineOrderDetailPickBatchRepository.saveBatch(detailPickDOS);
      }

      // 明细追溯码信息
      List<OfflineOrderDetailTraceDO> detailTraceDOS = detailDOList.stream()
          .map(OfflineOrderDetailDO::getOfflineOrderDetailTraceDOList)
          .filter(detailTraceDOList -> !CollectionUtils.isEmpty(detailTraceDOList))
          .flatMap(Collection::stream).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(detailTraceDOS)) {
        offlineOrderDetailTraceBatchRepository.saveBatch(detailTraceDOS);
      }

      // 主单
      offlineOrderMapper.insert(offlineOrderDO);
    }
  }

  @Override
  public void updateOfflineParentOrderData(OfflineOrderDO offlineOrderDO) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(offlineOrderDO.getOrderNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      String parentThirdOrderNo = offlineOrderDO.getParentThirdOrderNo();
      if (StringUtils.isEmpty(parentThirdOrderNo)) {
        return;
      }

      String storeCode = offlineOrderDO.getStoreCode();
      String thirdPlatformCode = offlineOrderDO.getThirdPlatformCode();

      LambdaQueryWrapper<OfflineOrderDO> cq = new LambdaQueryWrapper<>();
      cq.eq(OfflineOrderDO::getStoreCode, storeCode);
      cq.eq(OfflineOrderDO::getThirdPlatformCode, thirdPlatformCode);
      cq.eq(OfflineOrderDO::getParentThirdOrderNo, parentThirdOrderNo);
      Integer count = offlineOrderMapper.selectCount(cq);
      if (Objects.isNull(count)) {
        return;
      }

      if (count < 2) {
        log.warn("需要等待拆单数据完全到达,再处理,{}-{}-{}", storeCode, thirdPlatformCode,
            parentThirdOrderNo);
        return;
      }

      if (count == 2) { // 线下单只会拆两单
        List<OfflineOrderDO> offlineOrderDOS = offlineOrderMapper.selectList(cq);

        String parentOrderNo = Strings.EMPTY;
        for (OfflineOrderDO orderDO : offlineOrderDOS) {
          if (orderDO.getThirdOrderNo().equals(orderDO.getParentThirdOrderNo())) {
            parentOrderNo = orderDO.getOrderNo();
            break;
          }
        }

        for (OfflineOrderDO orderDO : offlineOrderDOS) {
          orderDO.setParentOrderNo(parentOrderNo);
          offlineOrderMapper.updateById(orderDO);
        }
      } else {
        log.error("非产品设计场景-拆单条数多余2条,订单数据,{}", JsonUtils.toJson(offlineOrderDO));
      }

    }
  }

  @Override
  @Transactional
  public void updateOfflineParentRefundData(OfflineRefundOrderDO offlineRefundOrderDO) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(offlineRefundOrderDO.getRefundNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      String parentThirdRefundNo = offlineRefundOrderDO.getParentThirdRefundNo();
      if (StringUtils.isEmpty(parentThirdRefundNo)) {
        return;
      }

      String storeCode = offlineRefundOrderDO.getStoreCode();
      String thirdPlatformCode = offlineRefundOrderDO.getThirdPlatformCode();

      LambdaQueryWrapper<OfflineRefundOrderDO> cq = new LambdaQueryWrapper<>();
      cq.eq(OfflineRefundOrderDO::getStoreCode, storeCode);
      cq.eq(OfflineRefundOrderDO::getThirdPlatformCode, thirdPlatformCode);
      cq.eq(OfflineRefundOrderDO::getParentThirdRefundNo, parentThirdRefundNo);
      Integer count = offlineRefundOrderMapper.selectCount(cq);
      if (Objects.isNull(count)) {
        return;
      }

      if (count < 2) {
        log.warn("【退单】需要等待拆单数据完全到达,再处理,{}-{}-{}", storeCode, thirdPlatformCode,
            parentThirdRefundNo);
        return;
      }

      if (count == 2) { // 线下单只会拆两单
        List<OfflineRefundOrderDO> offlineRefundDOS = offlineRefundOrderMapper.selectList(cq);

        String parentRefundNo = Strings.EMPTY;
        for (OfflineRefundOrderDO refundDo : offlineRefundDOS) {
          if (refundDo.getThirdRefundNo().equals(refundDo.getParentThirdRefundNo())) {
            parentRefundNo = refundDo.getRefundNo();
            break;
          }
        }

        for (OfflineRefundOrderDO refundDO : offlineRefundDOS) {
          refundDO.setParentRefundNo(parentRefundNo);
          offlineRefundOrderMapper.updateById(refundDO);
        }
      } else {
        log.error("非产品设计场景-拆单条数多余2条,退单数据,{}",
            JsonUtils.toJson(offlineRefundOrderDO));
      }


    }
  }


  @Override
  @Transactional
  public void saveOfflineRefundOrderDO(OfflineRefundOrderDO offlineRefundOrderDO) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(offlineRefundOrderDO.getRefundNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      // 入库前校验是否存在
      OfflineRefundOrderExistsReqDto reqDto = queryByThirdPlatform(offlineRefundOrderDO);
      if (offlineThirdRefundOrderExists(reqDto)) {
        log.warn("理论上无,重复数据,不处理,{}", JSONUtil.toJsonStr(offlineRefundOrderDO));
        return;
      }

      // 订单医保信息
      OfflineRefundOrderMedInsSettleDO medInsSettleDO = offlineRefundOrderDO.getOfflineRefundOrderMedInsSettleDO();
      if (Objects.nonNull(medInsSettleDO)) {
        offlineRefundOrderMedInsSettleMapper.insert(medInsSettleDO);
      }

      // 保存退单用户信息
      OfflineRefundOrderUserDO offlineRefundOrderUserDO = offlineRefundOrderDO.getOfflineRefundOrderUserDO();
      if (Objects.nonNull(offlineRefundOrderUserDO)) {
        offlineRefundOrderUserMapper.insert(offlineRefundOrderUserDO);
      }

      //明细
      List<OfflineRefundOrderDetailDO> refundOrderDetailDOList = offlineRefundOrderDO.getRefundOrderDetailDOList();
      offlineRefundOrderDetailBatchRepository.saveBatch(refundOrderDetailDOList);

      //明细追溯码信息
      List<OfflineRefundOrderDetailTraceDO> refundDetailTraceDOS = refundOrderDetailDOList.stream()
          .map(OfflineRefundOrderDetailDO::getOfflineRefundOrderDetailTraceDOList)
          .filter(refundDetailTraceDOList -> !CollectionUtils.isEmpty(refundDetailTraceDOList))
          .flatMap(Collection::stream).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(refundDetailTraceDOS)) {
        offlineRefundOrderDetailTraceBatchRepository.saveBatch(refundDetailTraceDOS);
      }

      // 退款明细拣货信息
      List<OfflineRefundOrderDetailPickDO> refundDetailPickDOS = refundOrderDetailDOList.stream()
          .map(OfflineRefundOrderDetailDO::getOfflineRefundOrderDetailPickDOList)
          .filter(refundDetailTraceDOList -> !CollectionUtils.isEmpty(refundDetailTraceDOList))
          .flatMap(Collection::stream).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(refundDetailPickDOS)) {
        offlineRefundOrderDetailPickBatchRepository.saveBatch(refundDetailPickDOS);
      }

      // 支付信息
      List<OfflineRefundOrderPayDO> refundOrderPayDOList = offlineRefundOrderDO.getRefundOrderPayDOList();
      if (!CollectionUtils.isEmpty(refundOrderPayDOList)) {
        offlineRefundOrderPayBatchRepository.saveBatch(refundOrderPayDOList);
      }

      OfflineRefundOrderCashierDeskDO offlineRefundOrderCashierDeskDO = offlineRefundOrderDO.getOfflineRefundOrderCashierDeskDO();
      if (Objects.nonNull(offlineRefundOrderCashierDeskDO)) {
        offlineRefundOrderCashierDeskMapper.insert(offlineRefundOrderCashierDeskDO);
      }

      OfflineRefundOrderOrganizationDO offlineRefundOrderOrganizationDO = offlineRefundOrderDO.getOfflineRefundOrderOrganizationDO();
      if (Objects.nonNull(offlineRefundOrderOrganizationDO)) {
        offlineRefundOrderOrganizationMapper.insert(offlineRefundOrderOrganizationDO);
      }

      // 退款单
      offlineRefundOrderMapper.insert(offlineRefundOrderDO);
    }
  }


  @Override
  public OfflineOrderInfoResDto get(OfflineOrderInfoReqDto reqDto) {
    Assert.isTrue(!StringUtils.isEmpty(reqDto.getStoreCode()), "storeCode empty");
    Assert.isTrue(!StringUtils.isEmpty(reqDto.getNumberHelper()), "numberHelper empty");
    Assert.isTrue(!StringUtils.isEmpty(reqDto.getThirdOrderNo()), "thirdOrderNo empty");
    Assert.isTrue(!StringUtils.isEmpty(reqDto.getThirdPlatformCode()), "thirdPlatformCode empty");

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(reqDto.getNumberHelper());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineOrderDO> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(OfflineOrderDO::getStoreCode, reqDto.getStoreCode());
      queryWrapper.eq(OfflineOrderDO::getThirdPlatformCode, reqDto.getThirdPlatformCode());
      queryWrapper.eq(OfflineOrderDO::getThirdOrderNo, reqDto.getThirdOrderNo());
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(queryWrapper);
      if (Objects.isNull(offlineOrderDO)) {
        log.warn(String.format("订单不存在,参入:%s", JSONUtil.toJsonStr(reqDto)));
        AtomSdkErrorCode error = AtomSdkErrorCode.REFUND_ORDER_NOT_HAVA_NORMAL_ORDER;
        throw AtomExceptionUtil.getWarnException(error.getSdkErrorCode().toString(),
            String.format("%s,入参:%s", error.getMsg(), JsonUtils.toJson(reqDto)));
      }
      return OfflineOrder2DtoConverter.INSTANCE.toDTO(offlineOrderDO);
    }
  }


  @Override
  public OfflineRefundAmountResDTO getOrderRefundAmount(
      OfflineRefundAmountReqDto offlineRefundAmountReqDto) {
    OfflineRefundAmountResDTO resDTO = new OfflineRefundAmountResDTO();

    String thirdOrderNo = offlineRefundAmountReqDto.getThirdOrderNo();
    String thirdPlatformCode = offlineRefundAmountReqDto.getThirdPlatformCode();
    String storeCode = offlineRefundAmountReqDto.getStoreCode();
    String numberHelper = offlineRefundAmountReqDto.getNumberHelper();

    Assert.isTrue(!StringUtils.isEmpty(storeCode), "storeCode empty");
    Assert.isTrue(!StringUtils.isEmpty(numberHelper), "numberHelper empty");
    Assert.isTrue(!StringUtils.isEmpty(thirdOrderNo), "thirdOrderNo empty");
    Assert.isTrue(!StringUtils.isEmpty(thirdPlatformCode), "thirdPlatformCode empty");

    // 获取正单
    OfflineOrderInfoReqDto reqDto = new OfflineOrderInfoReqDto();
    reqDto.setThirdOrderNo(thirdOrderNo);
    reqDto.setStoreCode(storeCode);
    reqDto.setThirdPlatformCode(thirdPlatformCode);
    reqDto.setNumberHelper(numberHelper);
    OfflineOrderInfoResDto offlineOrderInfoResDto = get(reqDto); // business exception

    // 获取退单
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(numberHelper);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineRefundOrderDO> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(OfflineRefundOrderDO::getStoreCode, storeCode);
      queryWrapper.eq(OfflineRefundOrderDO::getThirdPlatformCode, thirdPlatformCode);
      queryWrapper.eq(OfflineRefundOrderDO::getThirdOrderNo, thirdOrderNo);
      List<OfflineRefundOrderDO> refundOrderDOList = offlineRefundOrderMapper.selectList(
          queryWrapper);

      resDTO.setActualCollectAmount(offlineOrderInfoResDto.getActualCollectAmount());
      resDTO.setRefundOrderAmountList(refundAmount(refundOrderDOList));
    }
    return resDTO;
  }

  private List<RefundOrderAmount> refundAmount(List<OfflineRefundOrderDO> refundOrderDOList) {
    if (CollectionUtils.isEmpty(refundOrderDOList)) {
      return Lists.newArrayList();
    }

    return refundOrderDOList.stream().map(refundOrder -> {
      RefundOrderAmount refundOrderAmount = new RefundOrderAmount();
      refundOrderAmount.setRefundAmount(refundOrder.getTotalAmount());
      return refundOrderAmount;
    }).collect(Collectors.toList());
  }

  @Override
  @YxtOrderSharding(shardingNo = "#detailReqDto.orderNo")
  public OfflineOrderDetailResDto detail(OfflineOrderDetailReqDto detailReqDto) {
    OfflineOrderDetailResDto resDto = new OfflineOrderDetailResDto();
    OfflineOrder2DtoConverter instance = OfflineOrder2DtoConverter.INSTANCE;

    // 查询主单
    OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(
        new LambdaQueryWrapper<OfflineOrderDO>().eq(OfflineOrderDO::getOrderNo,
            detailReqDto.getOrderNo()));
    Assert.isTrue(Objects.nonNull(offlineOrderDO),
        String.format("正单%s不存在,req:%s", detailReqDto.getOrderNo(),
            JsonUtils.toJson(detailReqDto)));

    // 查询组织信息
    OfflineOrderOrganizationDO orderOrganizationDO = offlineOrderOrganizationMapper.selectOne(
        new LambdaQueryWrapper<OfflineOrderOrganizationDO>().eq(
            OfflineOrderOrganizationDO::getOrderNo, detailReqDto.getOrderNo()));

    // 查询收银台信息
    OfflineOrderCashierDeskDO cashierDeskDO = offlineOrderCashierDeskMapper.selectOne(
        new LambdaQueryWrapper<OfflineOrderCashierDeskDO>().eq(
            OfflineOrderCashierDeskDO::getOrderNo, detailReqDto.getOrderNo()));

    // 查询用户信息
    OfflineOrderUserDO userDO = offlineOrderUserMapper.selectOne(
        new LambdaQueryWrapper<OfflineOrderUserDO>().eq(OfflineOrderUserDO::getOrderNo,
            detailReqDto.getOrderNo()));

    // 查询处方信息
    OfflineOrderPrescriptionDO prescriptionDO = offlineOrderPrescriptionMapper.selectOne(
        new LambdaQueryWrapper<OfflineOrderPrescriptionDO>().eq(
            OfflineOrderPrescriptionDO::getOrderNo, detailReqDto.getOrderNo()));

    // 查询支付信息
    List<OfflineOrderPayDO> payDOList = offlineOrderPayMapper.selectList(
        new LambdaQueryWrapper<OfflineOrderPayDO>().eq(OfflineOrderPayDO::getOrderNo,
            detailReqDto.getOrderNo()));

    // 查询明细信息
    List<OfflineOrderDetailDO> offlineOrderDetailDOS = offlineOrderDetailMapper.selectList(
        new LambdaQueryWrapper<OfflineOrderDetailDO>().eq(OfflineOrderDetailDO::getOrderNo,
            detailReqDto.getOrderNo()));
    Assert.isTrue(!CollectionUtils.isEmpty(offlineOrderDetailDOS),
        String.format("正单明细%s不存在", detailReqDto.getOrderNo()));
    List<OfflineOrderDetailDTO> offlineOrderDetailDTOList = new ArrayList<>();

    // 补充明细信息
    for (OfflineOrderDetailDO offlineOrderDetailDO : offlineOrderDetailDOS) {
      OfflineOrderDetailDTO offlineOrderDetailDTO = instance.toDto(offlineOrderDetailDO);
      // 获取明细拣货信息
      List<OfflineOrderDetailPickDO> orderDetailPickDOList = offlineOrderDetailPickMapper.selectList(
          new LambdaQueryWrapper<OfflineOrderDetailPickDO>().eq(
                  OfflineOrderDetailPickDO::getOrderNo, detailReqDto.getOrderNo())
              .eq(OfflineOrderDetailPickDO::getOrderDetailNo,
                  offlineOrderDetailDO.getOrderDetailNo()));
      if (!CollectionUtils.isEmpty(orderDetailPickDOList)) {
        offlineOrderDetailDTO.setOfflineOrderDetailPickDTOList(
            orderDetailPickDOList.stream().map(instance::toDto).collect(Collectors.toList()));
      }

      offlineOrderDetailDTOList.add(offlineOrderDetailDTO);
    }

    // 查询医保信息
    OfflineOrderMedInsSettleDO offlineOrderMedInsSettleDO = offlineOrderMedInsSettleMapper.selectOne(
        new LambdaQueryWrapper<OfflineOrderMedInsSettleDO>().eq(
            OfflineOrderMedInsSettleDO::getOrderNo, detailReqDto.getOrderNo()));

    // 查询促销和券信息
    List<OfflineOrderPromotionDO> offlineOrderPromotionDOS = offlineOrderPromotionMapper.selectList(
        new LambdaQueryWrapper<OfflineOrderPromotionDO>().eq(
            OfflineOrderPromotionDO::getOrderNo, detailReqDto.getOrderNo()));

    List<OfflineOrderCouponDO> offlineOrderCouponDOList = offlineOrderCouponMapper.selectList(
        new LambdaQueryWrapper<OfflineOrderCouponDO>().eq(
            OfflineOrderCouponDO::getOrderNo, detailReqDto.getOrderNo()));

    resDto.setOfflineOrderDTO(instance.toDto(offlineOrderDO));
    resDto.setOfflineOrderOrganizationDTO(instance.toDto(orderOrganizationDO));
    resDto.setOfflineOrderCashierDeskDTO(instance.toDto(cashierDeskDO));
    resDto.setOfflineOrderUserDTO(instance.toDto(userDO));
    resDto.setOfflineOrderPrescriptionDTO(instance.toDto(prescriptionDO));
    resDto.setOfflineOrderPayDTOList(instance.toDto(payDOList));
    resDto.setOfflineOrderDetailDTOList(offlineOrderDetailDTOList);
    resDto.setOfflineOrderMedInsSettleDto(instance.toDTO(offlineOrderMedInsSettleDO));
    resDto.setOfflineOrderPromotionDTOList(instance.toPromotionDTOList(offlineOrderPromotionDOS));
    resDto.setOfflineOrderCouponDTOList(instance.toCouponDTOList(offlineOrderCouponDOList));


    return resDto;
  }

  @Override
  public OfflineRefundOrderDetailResDto refundDetail(
      OfflineRefundOrderDetailReqDto refundDetailReqDto) {
    OfflineRefundOrderDetailResDto resDto = new OfflineRefundOrderDetailResDto();
    OfflineOrder2DtoConverter instance = OfflineOrder2DtoConverter.INSTANCE;

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundDetailReqDto.getRefundNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      // 查询主单
      OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(
          new LambdaQueryWrapper<OfflineRefundOrderDO>().eq(OfflineRefundOrderDO::getRefundNo,
              refundDetailReqDto.getRefundNo()));
      Assert.isTrue(Objects.nonNull(offlineRefundOrderDO),
          String.format("退单%s不存在,req:%s", refundDetailReqDto.getRefundNo(),
              JsonUtils.toJson(refundDetailReqDto)));

      // 查询退单医保
      OfflineRefundOrderMedInsSettleDO offlineRefundOrderMedInsSettleDO = offlineRefundOrderMedInsSettleMapper.selectOne(
          new LambdaQueryWrapper<OfflineRefundOrderMedInsSettleDO>().eq(
              OfflineRefundOrderMedInsSettleDO::getRefundNo, refundDetailReqDto.getRefundNo()));

      // 查询退款明细
      List<OfflineRefundOrderDetailDO> refundOrderDetailDOS = offlineRefundOrderDetailMapper.selectList(
          new LambdaQueryWrapper<OfflineRefundOrderDetailDO>().eq(
              OfflineRefundOrderDetailDO::getRefundNo, refundDetailReqDto.getRefundNo()));
      Assert.isTrue(!CollectionUtils.isEmpty(refundOrderDetailDOS),
          String.format("退单明细%s不存在", refundDetailReqDto.getRefundNo()));

      // 查退单用户
      OfflineRefundOrderUserDO offlineRefundOrderUserDO = offlineRefundOrderUserMapper.selectOne(
          new LambdaQueryWrapper<OfflineRefundOrderUserDO>().eq(
              OfflineRefundOrderUserDO::getRefundNo, refundDetailReqDto.getRefundNo()));

      // 查退单用户组织信息
      OfflineRefundOrderOrganizationDO offlineRefundOrderOrganizationDO = offlineRefundOrderOrganizationMapper.selectOne(
          new LambdaQueryWrapper<OfflineRefundOrderOrganizationDO>().eq(
              OfflineRefundOrderOrganizationDO::getRefundNo, refundDetailReqDto.getRefundNo()));

      // 查退单收银员信息
      OfflineRefundOrderCashierDeskDO offlineRefundOrderCashierDeskDO = offlineRefundOrderCashierDeskMapper.selectOne(
          new LambdaQueryWrapper<OfflineRefundOrderCashierDeskDO>().eq(
              OfflineRefundOrderCashierDeskDO::getRefundNo, refundDetailReqDto.getRefundNo()));

      // 查支付信息
      List<OfflineRefundOrderPayDO> offlineRefundOrderPayDOList = offlineRefundOrderPayMapper.selectList(
          new LambdaQueryWrapper<OfflineRefundOrderPayDO>().eq(
              OfflineRefundOrderPayDO::getRefundNo, refundDetailReqDto.getRefundNo()));

      resDto.setOfflineRefundOrderDTO(instance.toDto(offlineRefundOrderDO));
      resDto.setOfflineRefundOrderUserDTO(instance.toDto(offlineRefundOrderUserDO));
      resDto.setOfflineRefundOrderDetailDTOList(
          refundOrderDetailDOS.stream().map(instance::toDto).collect(Collectors.toList()));
      resDto.setOfflineRefundOrderMedInsSettleDto(instance.toDTO(offlineRefundOrderMedInsSettleDO));
      resDto.setOfflineRefundOrderOrganizationDTO(instance.toDTO(offlineRefundOrderOrganizationDO));
      resDto.setOfflineRefundOrderCashierDeskDTO(instance.toDTO(offlineRefundOrderCashierDeskDO));
      resDto.setOfflineRefundOrderPayDTOList(instance.toDTOList(offlineRefundOrderPayDOList));
    }

    return resDto;
  }

  @Override
  public GetOfflineOrderByDateResDto orderData2MqQuery(GetOfflineOrderByDateReqDto reqDto) {
    GetOfflineOrderByDateResDto getOfflineOrderByDateResDto = new GetOfflineOrderByDateResDto();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      GetOfflineOrderByDateCondition condition = reqDto.getCondition();
      hit.setQueryHit(QueryHit.builder().seq(String.valueOf(reqDto.getSeq())).build());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<OfflineOrderDO>().select(
              OfflineOrderDO::getId, OfflineOrderDO::getOrderNo, OfflineOrderDO::getCreatedTime)
          .gt(OfflineOrderDO::getId, condition.getStartId())
          .ge(OfflineOrderDO::getCreatedTime, condition.getStartDate())
          .le(OfflineOrderDO::getCreatedTime, condition.getEndDate())
          .orderByAsc(OfflineOrderDO::getId).last(" limit " + condition.getPageSize());

      List<OfflineOrderDO> offlineOrderDOS = offlineOrderMapper.selectList(query);
      if (!CollectionUtils.isEmpty(offlineOrderDOS)) {

        List<String> orderNoList = offlineOrderDOS.stream().map(OfflineOrderDO::getOrderNo)
            .collect(Collectors.toList());
        Map<String, OfflineOrderUserDO> orderNoUserMap = queryUserByOrderNo(orderNoList);

        List<OrderData> orderDataList = offlineOrderDOS.stream().map(offlineOrderDO -> {
          OrderData orderData = new OrderData();
          orderData.setId(offlineOrderDO.getId());
          OfflineOrderUserDO userDO = orderNoUserMap.get(offlineOrderDO.getOrderNo());
          if (Objects.nonNull(userDO)) {
            orderData.setUserId(userDO.getUserId());
          }
          orderData.setOrderNo(offlineOrderDO.getOrderNo());
          orderData.setCreateTime(offlineOrderDO.getCreatedTime());
          return orderData;
        }).collect(Collectors.toList());
        getOfflineOrderByDateResDto.setOrderDataList(orderDataList);
      }

      return getOfflineOrderByDateResDto;
    }
  }

  @Override
  public GetOfflineRefundOrderByDateResDto refundData2MqQuery(
      GetOfflineRefundOrderByDateReqDto reqDto) {
    GetOfflineRefundOrderByDateResDto resDto = new GetOfflineRefundOrderByDateResDto();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      GetOfflineOrderByDateCondition condition = reqDto.getCondition();
      hit.setQueryHit(QueryHit.builder().seq(String.valueOf(reqDto.getSeq())).build());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<OfflineRefundOrderDO>().select(
              OfflineRefundOrderDO::getId, OfflineRefundOrderDO::getOrderNo,
              OfflineRefundOrderDO::getRefundNo, OfflineRefundOrderDO::getCreatedTime)
          .gt(OfflineRefundOrderDO::getId, condition.getStartId())
          .ge(OfflineRefundOrderDO::getCreatedTime, condition.getStartDate())
          .le(OfflineRefundOrderDO::getCreatedTime, condition.getEndDate())
          .orderByAsc(OfflineRefundOrderDO::getId).last(" limit " + condition.getPageSize());

      List<OfflineRefundOrderDO> refundOrderList = offlineRefundOrderMapper.selectList(query);
      if (!CollectionUtils.isEmpty(refundOrderList)) {

        List<String> orderNoList = refundOrderList.stream().map(OfflineRefundOrderDO::getOrderNo)
            .collect(Collectors.toList());
        Map<String, OfflineOrderUserDO> orderNoUserMap = queryUserByOrderNo(orderNoList);

        List<RefundData> refundDataList = refundOrderList.stream().map(refundOrder -> {
          RefundData refundData = new RefundData();
          refundData.setId(refundOrder.getId());
          OfflineOrderUserDO userDO = orderNoUserMap.get(refundOrder.getOrderNo());
          if (Objects.nonNull(userDO)) {
            refundData.setUserId(userDO.getUserId());
          }
          refundData.setRefundNo(refundOrder.getRefundNo());
          refundData.setCreateTime(refundOrder.getCreatedTime());
          return refundData;
        }).collect(Collectors.toList());
        resDto.setRefundDataList(refundDataList);
      }

      return resDto;
    }
  }

  private Map<String, OfflineOrderUserDO> queryUserByOrderNo(List<String> orderNoList) {
    if (CollectionUtils.isEmpty(orderNoList)) {
      return Maps.newHashMap();
    }
    // 去重
    List<String> distinctOrderNoList = orderNoList.stream()
        .filter(orderNo -> !StringUtils.isEmpty(orderNo)).distinct().collect(Collectors.toList());
    if (CollectionUtils.isEmpty(distinctOrderNoList)) {
      return Maps.newHashMap();
    }
    LambdaQueryWrapper<OfflineOrderUserDO> queryUser = new LambdaQueryWrapper<OfflineOrderUserDO>().in(
        OfflineOrderUserDO::getOrderNo, distinctOrderNoList);

    List<OfflineOrderUserDO> userList = offlineOrderUserMapper.selectList(queryUser);
    if (CollectionUtils.isEmpty(userList)) {
      return Maps.newHashMap();
    }

    return userList.stream()
        .collect(Collectors.toMap(OfflineOrderUserDO::getOrderNo, Function.identity()));
  }

  @Override
  public Boolean offlineThirdOrderExists(OfflineOrderExistsReqDto dto) {
    LambdaQueryWrapper<OfflineOrderDO> queryCount = buildOrderExistsQuery(dto);
    return offlineOrderMapper.selectCount(queryCount) >= 1;
  }



  @Override
  public ExistOrderInfo offlineThirdOrderExistsInfo(OfflineOrderExistsReqDto dto) {
    if (!offlineThirdOrderExists(dto)) {
      return null;
    }
    LambdaQueryWrapper<OfflineOrderDO> query = buildOrderExistsQuery(dto);
    OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(query);
    ExistOrderInfo existOrderInfo = new ExistOrderInfo();
    existOrderInfo.setOrderNo(offlineOrderDO.getOrderNo());
    return existOrderInfo;
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OfflineOrderDO offlineOrderInfoForRepair(OfflineOrderExistsReqDto dto) {
    LambdaQueryWrapper<OfflineOrderDO> query = buildOrderExistsQuery(dto);
    OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(query);
    if(Objects.isNull(offlineOrderDO)){
      return null;
    }

    LambdaQueryWrapper<OfflineOrderDetailDO> queryDetail = new LambdaQueryWrapper<>();
    queryDetail.eq(OfflineOrderDetailDO::getOrderNo,offlineOrderDO.getOrderNo());
    offlineOrderDO.setDetailDOList(offlineOrderDetailMapper.selectList(queryDetail));
    return offlineOrderDO;
  }

  @NotNull
  public static LambdaQueryWrapper<OfflineOrderDO> buildOrderExistsQuery(
      OfflineOrderExistsReqDto dto) {
    Assert.isTrue(!StringUtils.isEmpty(dto.getStoreCode()), "storeCode empty");
    Assert.isTrue(!StringUtils.isEmpty(dto.getThirdOrderNo()), "thirdOrderNo empty");
    Assert.isTrue(!StringUtils.isEmpty(dto.getThirdPlatformCode()), "thirdPlatformCode empty");

    LambdaQueryWrapper<OfflineOrderDO> queryCount = new LambdaQueryWrapper<>();
    queryCount.eq(OfflineOrderDO::getStoreCode, dto.getStoreCode());
    queryCount.eq(OfflineOrderDO::getThirdPlatformCode, dto.getThirdPlatformCode());
    queryCount.eq(OfflineOrderDO::getThirdOrderNo, dto.getThirdOrderNo());
    Date thirdCreated = dto.getThirdCreated();
    // 兼容迁移场景标识主单场景
    queryCount.eq(Objects.nonNull(thirdCreated), OfflineOrderDO::getCreated, thirdCreated);
    return queryCount;
  }

  @NotNull
  public static LambdaQueryWrapper<OfflineOrderDO> buildOrderExistsQueryOnlyForKechuna(
      OfflineOrderExistsReqDto dto) {
    Assert.isTrue(!StringUtils.isEmpty(dto.getStoreCode()), "storeCode empty");
    Assert.isTrue(!StringUtils.isEmpty(dto.getThirdOrderNo()), "thirdOrderNo empty");
    Assert.isTrue(!StringUtils.isEmpty(dto.getThirdPlatformCode()), "thirdPlatformCode empty");

    LambdaQueryWrapper<OfflineOrderDO> queryCount = new LambdaQueryWrapper<>();
    queryCount.eq(OfflineOrderDO::getStoreCode, dto.getStoreCode());
    queryCount.eq(OfflineOrderDO::getThirdPlatformCode, dto.getThirdPlatformCode());
    queryCount.eq(OfflineOrderDO::getThirdOrderNo, dto.getThirdOrderNo());
    Date thirdCreated = dto.getThirdCreated();
    // 兼容迁移场景标识主单场景
//    queryCount.eq(Objects.nonNull(thirdCreated), OfflineOrderDO::getCreated, thirdCreated);

    if(Objects.nonNull(thirdCreated)){
      StartEndDate startEndDate = DateRangeUtil.generateDayRange(
          OrderDateUtils.formatYYMMDD(thirdCreated));
      queryCount.ge(OfflineOrderDO::getCreated,startEndDate.getStart());
      queryCount.lt(OfflineOrderDO::getCreated,startEndDate.getEnd());
    }


    return queryCount;
  }

  @Override
  public Boolean offlineThirdRefundOrderExists(OfflineRefundOrderExistsReqDto dto) {
    LambdaQueryWrapper<OfflineRefundOrderDO> queryRefundCount = buildRefundOrderExistsQuery(dto);
    return offlineRefundOrderMapper.selectCount(queryRefundCount) >= 1;
  }

  @Override
  public ExistRefundOrderInfo offlineThirdRefundOrderExistsInfo(
      OfflineRefundOrderExistsReqDto req) {
    if(!offlineThirdRefundOrderExists(req)){
      return null;
    }
    LambdaQueryWrapper<OfflineRefundOrderDO> query = buildRefundOrderExistsQuery(req);
    OfflineRefundOrderDO refundOrderDO = offlineRefundOrderMapper.selectOne(query);
    ExistRefundOrderInfo existRefundOrderInfo = new ExistRefundOrderInfo();
    existRefundOrderInfo.setRefundNo(refundOrderDO.getRefundNo());
    return existRefundOrderInfo;
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OfflineRefundOrderDO offlineRefundOrderForRepair(
      OfflineRefundOrderExistsReqDto req) {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = buildRefundOrderExistsQuery(req);
    OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(query);

    if(Objects.isNull(offlineRefundOrderDO)){
      return null;
    }

    LambdaQueryWrapper<OfflineRefundOrderDetailDO> refundDetailQuery = new LambdaQueryWrapper<>();
    refundDetailQuery.eq(OfflineRefundOrderDetailDO::getRefundNo,offlineRefundOrderDO.getRefundNo());
    offlineRefundOrderDO.setRefundOrderDetailDOList(offlineRefundOrderDetailMapper.selectList(refundDetailQuery));
    return offlineRefundOrderDO;
  }

  public OfflineRefundOrderDO existedRefundOrder(OfflineRefundOrderExistsReqDto dto) {
    LambdaQueryWrapper<OfflineRefundOrderDO> queryRefundCount = buildRefundOrderExistsQuery(dto);
    return offlineRefundOrderMapper.selectOne(queryRefundCount);
  }

  @NotNull
  public static LambdaQueryWrapper<OfflineRefundOrderDO> buildRefundOrderExistsQuery(
      OfflineRefundOrderExistsReqDto dto) {
    Assert.isTrue(!StringUtils.isEmpty(dto.getStoreCode()), "storeCode empty");
    Assert.isTrue(!StringUtils.isEmpty(dto.getThirdRefundNo()), "thirdRefundNo empty");
    Assert.isTrue(!StringUtils.isEmpty(dto.getThirdPlatformCode()), "thirdPlatformCode empty");

    LambdaQueryWrapper<OfflineRefundOrderDO> queryRefundCount = new LambdaQueryWrapper<>();
    queryRefundCount.eq(OfflineRefundOrderDO::getStoreCode, dto.getStoreCode());
    queryRefundCount.eq(OfflineRefundOrderDO::getThirdPlatformCode, dto.getThirdPlatformCode());
    queryRefundCount.eq(OfflineRefundOrderDO::getThirdRefundNo, dto.getThirdRefundNo());
    // 兼容迁移场景标识主单场景
    Date thirdCreated = dto.getThirdCreated();
    queryRefundCount.eq(Objects.nonNull(thirdCreated), OfflineRefundOrderDO::getCreated,
        thirdCreated);
    return queryRefundCount;
  }

  @NotNull
  public static LambdaQueryWrapper<OfflineRefundOrderDO> buildRefundOrderExistsQueryOnlyForKeChuan(
      OfflineRefundOrderExistsReqDto dto) {
    Assert.isTrue(!StringUtils.isEmpty(dto.getStoreCode()), "storeCode empty");
    Assert.isTrue(!StringUtils.isEmpty(dto.getThirdRefundNo()), "thirdRefundNo empty");
    Assert.isTrue(!StringUtils.isEmpty(dto.getThirdPlatformCode()), "thirdPlatformCode empty");

    LambdaQueryWrapper<OfflineRefundOrderDO> queryRefundCount = new LambdaQueryWrapper<>();
    queryRefundCount.eq(OfflineRefundOrderDO::getStoreCode, dto.getStoreCode());
    queryRefundCount.eq(OfflineRefundOrderDO::getThirdPlatformCode, dto.getThirdPlatformCode());
    queryRefundCount.eq(OfflineRefundOrderDO::getThirdRefundNo, dto.getThirdRefundNo());
    // 兼容迁移场景标识主单场景
    Date thirdCreated = dto.getThirdCreated();
    if(Objects.nonNull(thirdCreated)){
      StartEndDate startEndDate = DateRangeUtil.generateDayRange(
          OrderDateUtils.formatYYMMDD(thirdCreated));
      queryRefundCount.ge(OfflineRefundOrderDO::getCreated,startEndDate.getStart());
      queryRefundCount.lt(OfflineRefundOrderDO::getCreated,startEndDate.getEnd());
    }
    return queryRefundCount;
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ExistOfflineOrderResDto queryOfflineOrderByDto(OfflineOrderExistsReqDto reqDto) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(reqDto.getDefineNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineOrderDO> queryCount = buildOrderExistsQuery(reqDto);
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(queryCount);
      if (Objects.isNull(offlineOrderDO)) {
        return null;
      }

      ExistOfflineOrderResDto existOfflineOrderResDto = new ExistOfflineOrderResDto();
      existOfflineOrderResDto.setOrderNo(offlineOrderDO.getOrderNo());
      return existOfflineOrderResDto;
    }
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ExistOfflineRefundOrderResDto queryOfflineRefundOrderByDto(
      OfflineRefundOrderExistsReqDto reqDto) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(reqDto.getDefineNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      LambdaQueryWrapper<OfflineRefundOrderDO> queryWrapper = buildRefundOrderExistsQuery(reqDto);
      OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(queryWrapper);
      if (Objects.isNull(offlineRefundOrderDO)) {
        return null;
      }

      ExistOfflineRefundOrderResDto refundOrderResDto = new ExistOfflineRefundOrderResDto();
      refundOrderResDto.setRefundNo(offlineRefundOrderDO.getRefundNo());
      return refundOrderResDto;
    }
  }

  @Override
  public UnionOrderResDto unionOrder(UnionOrderReqDto unionOrderReqDto) {
    UnionOrderResDto unionOrderResDto = UnionOrderResDto.empty();

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      String orderNo = unionOrderReqDto.getOrderNo();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      // 查询主单
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(
          new LambdaQueryWrapper<OfflineOrderDO>().eq(OfflineOrderDO::getOrderNo, orderNo));
      Assert.isTrue(Objects.nonNull(offlineOrderDO),
          String.format("正单%s不存在,req:%s", orderNo, JsonUtils.toJson(unionOrderReqDto)));

      // 判断主单号是否为空,不为空则查出父单号对应的子单
      String parentOrderNo = offlineOrderDO.getParentOrderNo();
      if (StringUtils.isEmpty(parentOrderNo)) {
        return unionOrderResDto;
      }

      LambdaQueryWrapper<OfflineOrderDO> byParentQuery = new LambdaQueryWrapper<OfflineOrderDO>().eq(
          OfflineOrderDO::getParentOrderNo, parentOrderNo);
      List<OfflineOrderDO> offlineOrderDoList = offlineOrderMapper.selectList(byParentQuery);
      if (CollectionUtils.isEmpty(offlineOrderDoList)) {
        return unionOrderResDto;
      }

      List<String> orderNoList = offlineOrderDoList.stream().map(OfflineOrderDO::getOrderNo)
          .distinct().collect(Collectors.toList());
      unionOrderResDto.union(orderNoList);

      return unionOrderResDto;
    }
  }

  @Override
  public void identifyMainOrder(OfflineOrderDO offlineOrderDO) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(offlineOrderDO.getOrderNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      String thirdOrderNo = offlineOrderDO.getThirdOrderNo();
      String parentThirdOrderNo = offlineOrderDO.getParentThirdOrderNo();

      String storeCode = offlineOrderDO.getStoreCode();
      String thirdPlatformCode = offlineOrderDO.getThirdPlatformCode();

      if (!StringUtils.isEmpty(thirdOrderNo) && StringUtils.isEmpty(parentThirdOrderNo)) {
        // 查看thirdOrderNo是否是父单号
        MigrationDelayMapping parent = migrationDelayOrderService.isParentThirdOrderNo(thirdOrderNo,
            storeCode, thirdPlatformCode);
        if (Objects.isNull(parent)) {
          return;
        }
        //offlineOrderDO 是父单

        // thirdOrderNo 是父单号
        offlineOrderDO.setParentThirdOrderNo(thirdOrderNo);
        offlineOrderDO.setParentOrderNo(offlineOrderDO.getOrderNo());
        offlineOrderMapper.updateById(offlineOrderDO);

        // 再处理子单的parentOrderNo
        String childOrderNo = parent.getChildOrderNo();//字段的orderNo
        LambdaQueryWrapper<OfflineOrderDO> cq = new LambdaQueryWrapper<>();
        cq.eq(OfflineOrderDO::getOrderNo, childOrderNo);
        OfflineOrderDO childOrder = offlineOrderMapper.selectOne(cq);
        if (Objects.isNull(childOrder)) {
          log.error("迁移订单找不到子单,{},父单:{}", childOrderNo, offlineOrderDO.getOrderNo());
          return;
        }

        // 填充子单的内部父单号
        childOrder.setParentOrderNo(offlineOrderDO.getOrderNo());
        offlineOrderMapper.updateById(childOrder);
      } else if (!StringUtils.isEmpty(thirdOrderNo) && !StringUtils.isEmpty(parentThirdOrderNo)) {
        // 子单后到,查父单,然后设置父单

        LambdaQueryWrapper<OfflineOrderDO> cq = new LambdaQueryWrapper<>();
        cq.eq(OfflineOrderDO::getStoreCode, storeCode);
        cq.eq(OfflineOrderDO::getThirdPlatformCode, thirdPlatformCode);
        cq.eq(OfflineOrderDO::getThirdOrderNo, parentThirdOrderNo);
        OfflineOrderDO parentOrder = offlineOrderMapper.selectOne(cq);
        if (Objects.isNull(parentOrder)) {
          log.error("迁移订单找不到子单,{},{},{}", storeCode, thirdPlatformCode,
              parentThirdOrderNo);
          return;
        }

        offlineOrderDO.setParentOrderNo(parentOrder.getOrderNo());
        offlineOrderMapper.updateById(offlineOrderDO);

        parentOrder.setParentThirdOrderNo(parentOrder.getThirdOrderNo());
        parentOrder.setParentOrderNo(parentOrder.getOrderNo());
        offlineOrderMapper.updateById(parentOrder);

      }
    }
  }

  /**
   * 整体来说退款数据不是很多
   *
   * @param offlineRefundOrderDO
   * @note: 有一部分会员订单查不到, 后面补偿又查到了, 但是找不到数据, 这种情况不处理, 到时候校验一下数量, 在核对一下数目就行
   */
  @Override
  public void compensateRefundOrderData(OfflineRefundOrderDO offlineRefundOrderDO) {

    String refundNo = offlineRefundOrderDO.getRefundNo();
    String tableIndexByNo = ShardingHelper.getTableIndexByNo(refundNo);
    if (tableIndexByNo.length() == 4) {
      // 非会员订单 归档表
      String[] yyMMList = compensateYYmms.split(",");
      for (String yyMM : yyMMList) {
        String newRefundNo = refundNo.substring(0, refundNo.length() - 4) + yyMM;
        offlineRefundOrderDO.setRefundNo(newRefundNo); // 构建新的年月分区单号
        if (startNoVipCompensate(offlineRefundOrderDO)) {
          return;
        }
      }

      // 非会员补充失败
      CompensateResultV1 compensateResultV1 = new CompensateResultV1();
      compensateResultV1.setDataJson(JsonUtils.toJson(offlineRefundOrderDO));
      compensateResultV1.setCompensateType(
          CompensateType.NO_VIP_FAILED);
      compensateResultV1.setCreateTime(new Date());
      compensateResultV1.setRetry(CompensateRetry.UN_RETRY);
      compensateResultV1.setResult(false);
      compensateResultV1.setReason("");
      mongoTemplate.insert(compensateResultV1);

    } else {
      // 会员订单
      startVipCompensate(offlineRefundOrderDO);
    }
  }



  private void startVipCompensate(OfflineRefundOrderDO offlineRefundOrderDO) {
    try {
      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setDefineNo(offlineRefundOrderDO.getRefundNo());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

        OfflineRefundOrderDO existedRefundOrder = queryOfflineRefundOrderDO(offlineRefundOrderDO);
        if (Objects.isNull(existedRefundOrder)) {
          return;
        }

        // 补偿退单用户组织信息
        toCompensateRefundData(offlineRefundOrderDO, existedRefundOrder);
      }
    } catch (Exception e) {
      CompensateResultV1 compensateResultV1 = new CompensateResultV1();
      compensateResultV1.setDataJson(JsonUtils.toJson(offlineRefundOrderDO));
      compensateResultV1.setCompensateType(CompensateType.VIP_EX);
      compensateResultV1.setCreateTime(new Date());
      compensateResultV1.setRetry(CompensateRetry.UN_RETRY);
      compensateResultV1.setResult(false);
      compensateResultV1.setReason(e.getMessage());
      mongoTemplate.insert(compensateResultV1);
    }
  }

  private void toCompensateRefundData(OfflineRefundOrderDO offlineRefundOrderDO,
      OfflineRefundOrderDO existedRefundOrder) {
    String dbRefundNo = existedRefundOrder.getRefundNo();
    OfflineRefundOrderOrganizationDO dbRefundOrganizationDO = offlineRefundOrderOrganizationMapper.selectOne(
        new LambdaQueryWrapper<OfflineRefundOrderOrganizationDO>().eq(
            OfflineRefundOrderOrganizationDO::getRefundNo, dbRefundNo));
    if (Objects.isNull(dbRefundOrganizationDO)) { // 为空则插入
      OfflineRefundOrderOrganizationDO offlineRefundOrderOrganizationDO = offlineRefundOrderDO.getOfflineRefundOrderOrganizationDO();
      offlineRefundOrderOrganizationDO.setRefundNo(dbRefundNo);
      offlineRefundOrderOrganizationDO.setCreatedTime(existedRefundOrder.getCreatedTime());
      offlineRefundOrderOrganizationDO.setUpdatedTime(existedRefundOrder.getUpdatedTime());
      offlineRefundOrderOrganizationDO.setUpdatedBy("补偿"); // 标记
      offlineRefundOrderOrganizationDO.setVersion(existedRefundOrder.getVersion());
      offlineRefundOrderOrganizationMapper.insert(offlineRefundOrderOrganizationDO);
    }

    // 补偿退单收银员信息
    OfflineRefundOrderCashierDeskDO dbRefundCashierDeskDO = offlineRefundOrderCashierDeskMapper.selectOne(
        new LambdaQueryWrapper<OfflineRefundOrderCashierDeskDO>().eq(
            OfflineRefundOrderCashierDeskDO::getRefundNo, dbRefundNo));
    if (Objects.isNull(dbRefundCashierDeskDO)) {// 为空则插入
      OfflineRefundOrderCashierDeskDO offlineRefundOrderCashierDeskDO = offlineRefundOrderDO.getOfflineRefundOrderCashierDeskDO();
      offlineRefundOrderCashierDeskDO.setRefundNo(dbRefundNo);
      offlineRefundOrderCashierDeskDO.setCreatedTime(existedRefundOrder.getCreatedTime());
      offlineRefundOrderCashierDeskDO.setUpdatedTime(existedRefundOrder.getUpdatedTime());
      offlineRefundOrderCashierDeskDO.setUpdatedBy("补偿"); // 标记
      offlineRefundOrderCashierDeskDO.setVersion(existedRefundOrder.getVersion());
      offlineRefundOrderCashierDeskMapper.insert(offlineRefundOrderCashierDeskDO);
    }
  }

  private Boolean startNoVipCompensate(OfflineRefundOrderDO offlineRefundOrderDO) {
    try {
      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setDefineNo(offlineRefundOrderDO.getRefundNo());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

        OfflineRefundOrderDO existedRefundOrder = queryOfflineRefundOrderDO(offlineRefundOrderDO);
        if (Objects.isNull(existedRefundOrder)) {
          return false;
        }

        // 补偿退单用户组织信息
        toCompensateRefundData(offlineRefundOrderDO, existedRefundOrder);
        return true;
      }
    } catch (Exception e) {
      CompensateResultV1 compensateResultV1 = new CompensateResultV1();
      compensateResultV1.setDataJson(JsonUtils.toJson(offlineRefundOrderDO));
      compensateResultV1.setCompensateType(
          CompensateType.NO_VIP_EX);
      compensateResultV1.setCreateTime(new Date());
      compensateResultV1.setRetry(CompensateRetry.UN_RETRY);
      compensateResultV1.setResult(false);
      compensateResultV1.setReason(e.getMessage());
      mongoTemplate.insert(compensateResultV1);
      return false;
    }
  }

  private OfflineRefundOrderDO queryOfflineRefundOrderDO(OfflineRefundOrderDO offlineRefundOrderDO) {
    OfflineRefundOrderExistsReqDto reqDto = queryByThirdPlatform(offlineRefundOrderDO);
    return existedRefundOrder(reqDto);
  }

  @NotNull
  private static OfflineRefundOrderExistsReqDto queryByThirdPlatform(
      OfflineRefundOrderDO offlineRefundOrderDO) {
    // 入库前校验是否存在
    OfflineRefundOrderExistsReqDto reqDto = new OfflineRefundOrderExistsReqDto();
    reqDto.setStoreCode(offlineRefundOrderDO.getStoreCode());
    reqDto.setThirdRefundNo(offlineRefundOrderDO.getThirdRefundNo());
    reqDto.setThirdPlatformCode(offlineRefundOrderDO.getThirdPlatformCode());
    reqDto.setThirdCreated(offlineRefundOrderDO.getCreated());
    return reqDto;
  }

  @Override
  public CommonOfflineRefundResDto commonRefundInfo(CommonOfflineRefundQueryReqDto dto) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(dto.getOrderNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      Assert.isTrue(!StringUtils.isEmpty(dto.getOrderNo()),
          "订单号不能为空"); // 目前只有一个orderNo的查询,故这里强制校验,后续拓展可按需调整校验

      CommonOfflineRefundResDto res = CommonOfflineRefundResDto.empty();

      LambdaQueryWrapper<OfflineRefundOrderDO> commonQuery = new LambdaQueryWrapper<>();
      commonQuery.eq(OfflineRefundOrderDO::getOrderNo, dto.getOrderNo());

      List<OfflineRefundOrderDO> refundOrderDOList = offlineRefundOrderMapper.selectList(
          commonQuery);
      if (CollectionUtils.isEmpty(refundOrderDOList)) {
        return res;
      }

      List<String> refundNoList = refundOrderDOList.stream().map(OfflineRefundOrderDO::getRefundNo)
          .distinct().collect(Collectors.toList());
      res.setRefundNoList(refundNoList);
      return res;

    }

  }

  @Override
  @Transactional
  public void writeRefundPromotionCouponData(OfflineRefundOrderDO refundOrderDO) {
    String orderNo = refundOrderDO.getOrderNo();
    if (StringUtils.isEmpty(orderNo)) {
      return; // 未匹配到正单
    }

    String refundNo = refundOrderDO.getRefundNo();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<BigDataRefundInfoDO> existQuery = new LambdaQueryWrapper<>();
      existQuery.eq(BigDataRefundInfoDO::getRefundNo, refundNo);
      if (bigDataRefundInfoMapper.selectCount(existQuery) >= 1) {
        log.error("BigDataRefundInfoDO refundNo:{},已经存在", refundNo); // 幂等
        return;
      }

      // 退单对应的正单促销信息
      LambdaQueryWrapper<OfflineOrderDO> orderQuery = new LambdaQueryWrapper<>();
      orderQuery.eq(OfflineOrderDO::getOrderNo, orderNo);
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(orderQuery);
      if (Objects.isNull(offlineOrderDO)) {
        return; // 找不到正单
      }

      // 退单对应的 正单促销信息
      LambdaQueryWrapper<OfflineOrderPromotionDO> promotionQuery = new LambdaQueryWrapper<>();
      promotionQuery.eq(OfflineOrderPromotionDO::getOrderNo, orderNo);
      promotionQuery.eq(OfflineOrderPromotionDO::getType, DataDimensionTypeEnum.ORIGIN.name());
      List<OfflineOrderPromotionDO> offlineOrderPromotionList = offlineOrderPromotionMapper.selectList(
          promotionQuery);

      // 退单对应的正单券
      LambdaQueryWrapper<OfflineOrderCouponDO> couponQuery = new LambdaQueryWrapper<>();
      couponQuery.eq(OfflineOrderCouponDO::getOrderNo, orderNo);
      couponQuery.eq(OfflineOrderCouponDO::getType, DataDimensionTypeEnum.ORIGIN.name());
      List<OfflineOrderCouponDO> offlineOrderCouponList = offlineOrderCouponMapper.selectList(
          couponQuery);

      if (CollectionUtils.isEmpty(offlineOrderPromotionList) && CollectionUtils.isEmpty(
          offlineOrderCouponList)) {
        log.info("refundOrder:{} 无促销和券信息", refundNo);
        return;
      }

      // 大数据对象
      BigDataRefundInfoDto bigDataRefundInfoDto = new BigDataRefundInfoDto();
      bigDataRefundInfoDto.setRefundNo(refundNo);
      bigDataRefundInfoDto.setOrderNo(orderNo);
      bigDataRefundInfoDto.setRefundCreated(refundOrderDO.getCreated());
      bigDataRefundInfoDto.setOrderCreated(offlineOrderDO.getCreated());
      bigDataRefundInfoDto.setOfflineOrderPromotionList(offlineOrderPromotionList);
      bigDataRefundInfoDto.setOfflineOrderCouponList(offlineOrderCouponList);

      BigDataRefundInfoDO bigDataRefundInfoDO = new BigDataRefundInfoDO();
      bigDataRefundInfoDO.setRefundNo(refundNo);
      bigDataRefundInfoDO.setData(JsonUtils.toJson(bigDataRefundInfoDto));
      bigDataRefundInfoDO.setCreatedBy("");
      bigDataRefundInfoDO.setUpdatedBy("");
      bigDataRefundInfoDO.setCreatedTime(refundOrderDO.getCreatedTime());
      bigDataRefundInfoDO.setUpdatedTime(refundOrderDO.getUpdatedTime());
      bigDataRefundInfoDO.setVersion(1L);
      bigDataRefundInfoMapper.insert(bigDataRefundInfoDO);

    }
  }

  @Override
  public void compensateHdMissPromotionCouponData(Long compensateId,OfflineOrderDO offlineOrderDO) {
    String orderNo = offlineOrderDO.getOrderNo();
    String tableIndexByNo = ShardingHelper.getTableIndexByNo(orderNo);
    if (tableIndexByNo.length() == 4) {
      // 非会员订单 归档表
      String[] yyMMList = compensateYYmms.split(",");
      for (String yyMM : yyMMList) {
        String newOrderNo = orderNo.substring(0, orderNo.length() - 4) + yyMM;
        offlineOrderDO.setOrderNo(newOrderNo); // 构建新的年月分区单号
        if (hdMissPromotionCouponNoVip(compensateId,offlineOrderDO)) {
          commonMapper.updateCompensateResult(compensateId,"SUCCESS");
          return;
        }
      }
      commonMapper.compensateError(compensateId,"ERROR","未匹配到所有年月归档表中的任何一条订单数据");
    } else {
      // 会员订单
      hdMissPromotionCouponVip(compensateId,offlineOrderDO);
    }
  }

  private boolean hdMissPromotionCouponNoVip(Long compensateId,OfflineOrderDO offlineOrderDO) {

    try {
      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setDefineNo(offlineOrderDO.getOrderNo());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

        OfflineOrderDO dbOfflineOrder = queryExistsOfflineOrderDO(offlineOrderDO);
        if(Objects.isNull(dbOfflineOrder)){
          return false;
        }

        toCompensateHdMissPromotionCouponData(offlineOrderDO, dbOfflineOrder);
        return true;
      }
    } catch (Exception e) {
      commonMapper.compensateError(compensateId,"ERROR",e.getMessage());
      return false;
    }

  }

  private OfflineOrderDO queryExistsOfflineOrderDO(OfflineOrderDO offlineOrderDO) {
    OfflineOrderExistsReqDto reqDto = new OfflineOrderExistsReqDto();
    reqDto.setStoreCode(offlineOrderDO.getStoreCode());
    reqDto.setThirdOrderNo(offlineOrderDO.getThirdOrderNo());
    reqDto.setThirdPlatformCode(offlineOrderDO.getThirdPlatformCode());
    reqDto.setThirdCreated(offlineOrderDO.getCreated());
    LambdaQueryWrapper<OfflineOrderDO> query = buildOrderExistsQuery(reqDto);
    return offlineOrderMapper.selectOne(query);
  }

  private void toCompensateHdMissPromotionCouponData(OfflineOrderDO offlineOrderDO, OfflineOrderDO dbOfflineOrder) {
    String dbOrderNo = dbOfflineOrder.getOrderNo();

    // 补充订单订单促销信息
    List<OfflineOrderPromotionDO> offlineOrderPromotionDOList = offlineOrderDO.getOfflineOrderPromotionDOList();
    LambdaQueryWrapper<OfflineOrderPromotionDO> queryPromotion = new LambdaQueryWrapper<>();
    queryPromotion.eq(OfflineOrderPromotionDO::getOrderNo,dbOrderNo);
    Integer promotionCount = offlineOrderPromotionMapper.selectCount(queryPromotion);
    if(promotionCount <= 0 && !CollectionUtils.isEmpty(offlineOrderPromotionDOList)){
      for (OfflineOrderPromotionDO data : offlineOrderPromotionDOList) {
        data.setOrderNo(dbOrderNo);
        data.setCreatedBy("");
        data.setUpdatedBy("补偿");
        data.setCreatedTime(dbOfflineOrder.getCreatedTime());
        data.setUpdatedTime(dbOfflineOrder.getUpdatedTime());
        data.setVersion(1L);
      }
      offlineOrderPromotionBatchRepository.saveBatch(offlineOrderPromotionDOList);
    }

    // 补充券信息
    List<OfflineOrderCouponDO> offlineOrderCouponDOList = offlineOrderDO.getOfflineOrderCouponDOList();
    LambdaQueryWrapper<OfflineOrderCouponDO> queryCoupon = new LambdaQueryWrapper<>();
    queryCoupon.eq(OfflineOrderCouponDO::getOrderNo,dbOrderNo);
    Integer couponCount = offlineOrderCouponMapper.selectCount(queryCoupon);
    if(couponCount <= 0 && !CollectionUtils.isEmpty(offlineOrderCouponDOList)){
      for (OfflineOrderCouponDO data : offlineOrderCouponDOList) {
        data.setOrderNo(dbOrderNo);
        data.setCreatedBy("");
        data.setUpdatedBy("补偿");
        data.setCreatedTime(dbOfflineOrder.getCreatedTime());
        data.setUpdatedTime(dbOfflineOrder.getUpdatedTime());
        data.setVersion(1L);
      }
      offlineOrderCouponBatchRepository.saveBatch(offlineOrderCouponDOList);
    }
  }

  private void hdMissPromotionCouponVip(Long compensateId,OfflineOrderDO offlineOrderDO) {
    try {
      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setDefineNo(offlineOrderDO.getOrderNo());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

        OfflineOrderDO dbOfflineOrder = queryExistsOfflineOrderDO(offlineOrderDO);
        if(Objects.isNull(dbOfflineOrder)){
          commonMapper.compensateError(compensateId,"ERROR","未匹配到会员的任何一条订单");
          return;
        }

        toCompensateHdMissPromotionCouponData(offlineOrderDO, dbOfflineOrder);
        commonMapper.updateCompensateResult(compensateId,"SUCCESS");
      }
    } catch (Exception e) {
      commonMapper.compensateError(compensateId,"ERROR",e.getMessage());
    }

  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public SimpleOfflineOrderDetailDto querySimpleOfflineOrderDetail(String offlineOrderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(offlineOrderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      // 查询主单
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(
          new LambdaQueryWrapper<OfflineOrderDO>().eq(OfflineOrderDO::getOrderNo,
              offlineOrderNo));
      Assert.isTrue(Objects.nonNull(offlineOrderDO),String.format("正单%s不存在", offlineOrderNo));

      // 查询明细信息
      List<OfflineOrderDetailDO> offlineOrderDetailDOS = offlineOrderDetailMapper.selectList(
          new LambdaQueryWrapper<OfflineOrderDetailDO>().eq(OfflineOrderDetailDO::getOrderNo,offlineOrderNo));
      Assert.isTrue(!CollectionUtils.isEmpty(offlineOrderDetailDOS), String.format("正单明细%s不存在", offlineOrderNo));

      // 查询明细追溯码信息
      List<OfflineOrderDetailTraceDO> offlineOrderDetailTraceDOList = offlineOrderDetailTraceMapper.selectList(
          new LambdaQueryWrapper<OfflineOrderDetailTraceDO>().eq(OfflineOrderDetailTraceDO::getOrderNo,offlineOrderNo));

      // 查询明细拣货信息
      List<OfflineOrderDetailPickDO> offlineOrderDetailPickDOList = offlineOrderDetailPickMapper.selectList(
          new LambdaQueryWrapper<OfflineOrderDetailPickDO>().eq(OfflineOrderDetailPickDO::getOrderNo,offlineOrderNo));


      // 查询组织结构
      OfflineOrderOrganizationDO orderOrganizationDO = offlineOrderOrganizationMapper.selectOne(
          new LambdaQueryWrapper<OfflineOrderOrganizationDO>().eq(
              OfflineOrderOrganizationDO::getOrderNo, offlineOrderNo));

      // 查询收银员信息
      OfflineOrderCashierDeskDO cashierDeskDO = offlineOrderCashierDeskMapper.selectOne(
          new LambdaQueryWrapper<OfflineOrderCashierDeskDO>().eq(
              OfflineOrderCashierDeskDO::getOrderNo, offlineOrderNo));

      LambdaQueryWrapper<OfflineRefundOrderDO> refundOrderQuery = new LambdaQueryWrapper<>();
      refundOrderQuery.eq(OfflineRefundOrderDO::getOrderNo,offlineOrderNo);
      Integer refundCount = offlineRefundOrderMapper.selectCount(refundOrderQuery);

      SimpleOfflineOrderDetailDto simpleOfflineOrderDetailDto = new SimpleOfflineOrderDetailDto();
      simpleOfflineOrderDetailDto.setOfflineOrder(offlineOrderDO);
      simpleOfflineOrderDetailDto.setOfflineOrderDetailList(offlineOrderDetailDOS);
      simpleOfflineOrderDetailDto.setOfflineOrderDetailPickList(offlineOrderDetailPickDOList);
      simpleOfflineOrderDetailDto.setOfflineOrderDetailTraceList(offlineOrderDetailTraceDOList);
      simpleOfflineOrderDetailDto.setOfflineOrderOrganizationDO(orderOrganizationDO);
      simpleOfflineOrderDetailDto.setOfflineOrderCashierDeskDO(cashierDeskDO);
      simpleOfflineOrderDetailDto.setHasRefundOrder(refundCount>0);
      return simpleOfflineOrderDetailDto;
    }
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public SimpleOfflineRefundOrderDetailDto querySimpleOfflineRefundOrderDetail(
      String offlineRefundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(offlineRefundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      // 查询退单
      OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(
          new LambdaQueryWrapper<OfflineRefundOrderDO>().eq(OfflineRefundOrderDO::getRefundNo,
              offlineRefundNo));
      Assert.isTrue(Objects.nonNull(offlineRefundOrderDO),String.format("退单%s不存在", offlineRefundNo));

      // 查询退单明细信息
      List<OfflineRefundOrderDetailDO> offlineRefundOrderDetailDOS = offlineRefundOrderDetailMapper.selectList(
          new LambdaQueryWrapper<OfflineRefundOrderDetailDO>().eq(OfflineRefundOrderDetailDO::getRefundNo,offlineRefundNo));
      Assert.isTrue(!CollectionUtils.isEmpty(offlineRefundOrderDetailDOS), String.format("退单明细%s不存在", offlineRefundNo));

      // 查询退单明细拣货信息
      List<OfflineRefundOrderDetailPickDO> offlineRefundOrderDetailPickDOS = offlineRefundOrderDetailPickMapper.selectList(
          new LambdaQueryWrapper<OfflineRefundOrderDetailPickDO>().eq(OfflineRefundOrderDetailPickDO::getRefundNo,offlineRefundNo));

      // 查询退单明细追溯码信息
      List<OfflineRefundOrderDetailTraceDO> offlineRefundOrderDetailTraceDOS = offlineRefundOrderDetailTraceMapper.selectList(
          new LambdaQueryWrapper<OfflineRefundOrderDetailTraceDO>().eq(OfflineRefundOrderDetailTraceDO::getRefundNo,offlineRefundNo));

      OfflineRefundOrderOrganizationDO refundOrderOrganizationDO = offlineRefundOrderOrganizationMapper.selectOne(
          new LambdaQueryWrapper<OfflineRefundOrderOrganizationDO>().eq(
              OfflineRefundOrderOrganizationDO::getRefundNo, offlineRefundNo));

      OfflineRefundOrderCashierDeskDO offlineRefundOrderCashierDeskDO = offlineRefundOrderCashierDeskMapper.selectOne(
          new LambdaQueryWrapper<OfflineRefundOrderCashierDeskDO>().eq(
              OfflineRefundOrderCashierDeskDO::getRefundNo, offlineRefundNo));


      SimpleOfflineRefundOrderDetailDto simpleOfflineOrderDetailDto = new SimpleOfflineRefundOrderDetailDto();
      simpleOfflineOrderDetailDto.setOfflineRefundOrderDO(offlineRefundOrderDO);
      simpleOfflineOrderDetailDto.setOfflineRefundOrderDetailDOList(offlineRefundOrderDetailDOS);
      simpleOfflineOrderDetailDto.setOfflineRefundOrderDetailTraceDOList(offlineRefundOrderDetailTraceDOS);
      simpleOfflineOrderDetailDto.setOfflineRefundOrderDetailPickDOList(offlineRefundOrderDetailPickDOS);
      simpleOfflineOrderDetailDto.setOfflineRefundOrderOrganizationDO(refundOrderOrganizationDO);
      simpleOfflineOrderDetailDto.setOfflineRefundOrderCashierDeskDO(offlineRefundOrderCashierDeskDO);

      // 如果退单明细拣货信息为空,则查正单的明细拣货信息逻辑-放在这个位置写
      if(CollectionUtils.isEmpty(offlineRefundOrderDetailPickDOS) && !StringUtils.isEmpty(offlineRefundOrderDO.getOrderNo())){
        List<OfflineOrderDetailPickDO> offlineOrderDetailPickDOList = offlineOrderDetailPickMapper.selectList(
            new LambdaQueryWrapper<OfflineOrderDetailPickDO>().eq(
                OfflineOrderDetailPickDO::getOrderNo, offlineRefundOrderDO.getOrderNo()));

        List<OfflineOrderDetailDO> offlineOrderDetailDOList = offlineOrderDetailMapper.selectList(
            new LambdaQueryWrapper<OfflineOrderDetailDO>().eq(
                OfflineOrderDetailDO::getOrderNo, offlineRefundOrderDO.getOrderNo()));

        Map<String, OfflineOrderDetailDO> offlineDetailMap = offlineOrderDetailDOList.stream()
            .collect(
                Collectors.toMap(OfflineOrderDetailDO::getOrderDetailNo, v -> v, (v1, v2) -> v1));


        List<OfflineOrderDetailPickEnhance> offlineOrderDetailPickEnhances = BeanUtil.copyToList(
            offlineOrderDetailPickDOList, OfflineOrderDetailPickEnhance.class);
        offlineOrderDetailPickEnhances.forEach(enhance -> {
          OfflineOrderDetailDO offlineOrderDetailDO = offlineDetailMap.get(
              enhance.getOrderDetailNo());
          Optional.ofNullable(offlineOrderDetailDO).ifPresent(dto ->{
            enhance.setRowNo(dto.getRowNo());
          });
        });


        AppendOfflineOrderInfo appendOfflineOrderInfo = AppendOfflineOrderInfo.empty();
        appendOfflineOrderInfo.setOfflineOrderDetailPickEnhanceList(offlineOrderDetailPickEnhances);
        appendOfflineOrderInfo.setOfflineOrderDetailDOList(offlineOrderDetailDOList);

        simpleOfflineOrderDetailDto.setAppendOfflineOrderInfo(appendOfflineOrderInfo);
      }
      return simpleOfflineOrderDetailDto;
    }
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<OfflineOrderDetailResDto> getOrderInfoBatchByScale(OfflineOrderInfoQryByScaleBatchReqDto request) {

    Function<OrderNo, Supplier<OfflineOrderDetailResDto>> function = (orderNo) -> () -> {
      OfflineOrderInfoQryByScaleReqDto tempRequest = new OfflineOrderInfoQryByScaleReqDto(request.getQryScaleList(), orderNo);
      return SpringUtil.getBean(OfflineOrderRepository.class).getOrderInfoByScale(tempRequest);
    };
    return CompletableFutureUtils.supplyAsync(function, request.getOrderNoList(), 10, orderSearchPool).stream().filter(Objects::nonNull).collect(Collectors.toList());
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @Override
  public OfflineOrderDetailResDto getOrderInfoByScale(OfflineOrderInfoQryByScaleReqDto request) {
    String orderNo = request.getOrderNo().getOrderNo().toString();
    AtomicReference<OfflineOrderDetailResDto> resDto =new AtomicReference<>(new OfflineOrderDetailResDto());
    OfflineOrder2DtoConverter instance = OfflineOrder2DtoConverter.INSTANCE;

    OfflineOrderDO offlineOrderDO = null;
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      // 查询主单
      offlineOrderDO = offlineOrderMapper.selectOne(new LambdaQueryWrapper<OfflineOrderDO>().eq(OfflineOrderDO::getOrderNo, orderNo));
    }
    Assert.isTrue(Objects.nonNull(offlineOrderDO), String.format("正单%s不存在,req:%s", orderNo, JsonUtils.toJson(request)));
    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    for (OrderQryScaleEnum qryScaleEnum : request.getQryScaleList())
    {
      switch (qryScaleEnum){
        case MAIN:
          resDto.get().setOfflineOrderDTO(instance.toDto(offlineOrderDO));
          // 查询组织信息
          CompletableFuture<Void> orgFuture = CompletableFuture.runAsync(() -> {
            OfflineOrderOrganizationDO orderOrganizationDO = offlineOrderSearchRepository.getOfflineOrderOrganization(orderNo);
            resDto.get().setOfflineOrderOrganizationDTO(instance.toDto(orderOrganizationDO));
          } ,orderSearchSubPool);
          futureList.add(orgFuture);
          // 查询收银台信息
          CompletableFuture<Void> cashierDeskFuture = CompletableFuture.runAsync(() -> {
            OfflineOrderCashierDeskDO cashierDeskDO = offlineOrderSearchRepository.getOfflineOrderCashierDesk(orderNo);
            resDto.get().setOfflineOrderCashierDeskDTO(instance.toDto(cashierDeskDO));
          } ,orderSearchSubPool);
          futureList.add(cashierDeskFuture);
          // 查询用户信息
          CompletableFuture<Void> userFuture = CompletableFuture.runAsync(() -> {
            OfflineOrderUserDO userDO = offlineOrderSearchRepository.getOfflineOrderUser(orderNo);
            resDto.get().setOfflineOrderUserDTO(instance.toDto(userDO));
          } ,orderSearchSubPool);
          futureList.add(userFuture);
          break;
        case DETAIL:
          if(request.getQryScaleList().contains(OrderQryScaleEnum.BILL)){
            break;
          }
        case BILL:
          // 查询明细信息
          CompletableFuture<Void> orderDetailFuture = CompletableFuture.runAsync(() -> {
            List<OfflineOrderDetailDO> offlineOrderDetailDOS = offlineOrderSearchRepository.getOfflineOrderDetail(orderNo);
            if(CollUtil.isEmpty(offlineOrderDetailDOS)){
              return;
            }
            // 补充明细信息
            // 获取明细拣货信息
            List<String> orderDetailNoList = offlineOrderDetailDOS.stream().map(OfflineOrderDetailDO::getOrderDetailNo).collect(Collectors.toList());
            List<OfflineOrderDetailPickDO> orderDetailPickList = offlineOrderSearchRepository.getOfflineOrderDetailPick(orderNo, orderDetailNoList);
            List<OfflineOrderDetailDTO> offlineOrderDetailDTOList = new ArrayList<>();
            Map<String, List<OfflineOrderDetailPickDO>> orderDetailPickMap = orderDetailPickList.stream().collect(Collectors.groupingBy(OfflineOrderDetailPickDO::getOrderDetailNo));
            for (OfflineOrderDetailDO offlineOrderDetailDO : offlineOrderDetailDOS) {
              OfflineOrderDetailDTO offlineOrderDetailDTO = instance.toDto(offlineOrderDetailDO);
              if (orderDetailPickMap.containsKey(offlineOrderDetailDO.getOrderDetailNo())) {
                offlineOrderDetailDTO.setOfflineOrderDetailPickDTOList(orderDetailPickMap.get(offlineOrderDetailDO.getOrderDetailNo()).stream().map(instance::toDto).collect(Collectors.toList()));
              }
              offlineOrderDetailDTOList.add(offlineOrderDetailDTO);
            }
            resDto.get().setOfflineOrderDetailDTOList(offlineOrderDetailDTOList);
          }, orderSearchSubPool);
          futureList.add(orderDetailFuture);
          break;
        case PRESCRIPTION:
          // 查询处方信息
          CompletableFuture<Void> prescriptionFuture = CompletableFuture.runAsync(() -> {
            OfflineOrderPrescriptionDO prescriptionDO = offlineOrderSearchRepository.getOfflineOrderPrescription(orderNo);
            resDto.get().setOfflineOrderPrescriptionDTO(instance.toDto(prescriptionDO));
          }, orderSearchSubPool);
          futureList.add(prescriptionFuture);
          break;
        case PAY:
          // 查询支付信息
          CompletableFuture<Void> payFuture = CompletableFuture.runAsync(() -> {
            List<OfflineOrderPayDO> payDOList = offlineOrderSearchRepository.getOfflineOrderPay(orderNo);
            resDto.get().setOfflineOrderPayDTOList(instance.toDto(payDOList));
          }, orderSearchSubPool);
          futureList.add(payFuture);
          break;
        case MEDICARE:
          // 查询医保信息
          CompletableFuture<Void> medInsSettleFuture = CompletableFuture.runAsync(() -> {
            OfflineOrderMedInsSettleDO offlineOrderMedInsSettleDO = offlineOrderSearchRepository.getOfflineOrderMedInsSettle(orderNo);
            resDto.get().setOfflineOrderMedInsSettleDto(instance.toDTO(offlineOrderMedInsSettleDO));
          }, orderSearchSubPool);
          futureList.add(medInsSettleFuture);
          break;
        default:
          break;
      }
    }
    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    return resDto.get();
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<OfflineRefundOrderDetailResDto> getRefundInfoBatchByScale(OfflineRefundInfoQryBatchReqDto request) {

    Function<RefundOrderNo,Supplier<OfflineRefundOrderDetailResDto>> function = (refundNo) -> () -> {
      OfflineRefundInfoQryReqDto tempRequest = new OfflineRefundInfoQryReqDto();
      tempRequest.setQryScaleList(request.getQryScaleList());
      tempRequest.setRefundOrderNo(refundNo);
      return SpringUtil.getBean(OfflineOrderRepository.class).getRefundInfoByScale(tempRequest);
    };
    return CompletableFutureUtils.supplyAsync(function, request.getRefundOrderNoList(), 10, orderSearchPool).stream().filter(Objects::nonNull).collect(Collectors.toList());
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @Override
  public OfflineRefundOrderDetailResDto getRefundInfoByScale(OfflineRefundInfoQryReqDto request) {
    AtomicReference<OfflineRefundOrderDetailResDto> resDto = new AtomicReference<>(new OfflineRefundOrderDetailResDto());
    OfflineOrder2DtoConverter instance = OfflineOrder2DtoConverter.INSTANCE;
    OfflineRefundOrderDO offlineRefundOrderDO = null;
    String refundNo = request.getRefundOrderNo().getRefundOrderNo().toString();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      // 查询主单
      offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(new LambdaQueryWrapper<OfflineRefundOrderDO>().eq(OfflineRefundOrderDO::getRefundNo, refundNo));
    }
    Assert.isTrue(Objects.nonNull(offlineRefundOrderDO), String.format("退单%s不存在,req:%s", request.getRefundOrderNo().toString(), JsonUtils.toJson(request)));
    List<RefundQryScaleEnum> qryScaleList = request.getQryScaleList();
    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    for (RefundQryScaleEnum qryScaleEnum : qryScaleList) {
      switch (qryScaleEnum) {
        case MAIN:
          resDto.get().setOfflineRefundOrderDTO(instance.toDto(offlineRefundOrderDO));
          // 查退单用户组织信息
          CompletableFuture<Void> orgFuture = CompletableFuture.runAsync(() -> {
            OfflineRefundOrderOrganizationDO offlineRefundOrderOrganizationDO = offlineOrderSearchRepository.getOfflineRefundOrderOrganization(refundNo);
            resDto.get().setOfflineRefundOrderOrganizationDTO(instance.toDTO(offlineRefundOrderOrganizationDO));
          }, orderSearchSubPool);
          futureList.add(orgFuture);
          // 查退单收银员信息
          CompletableFuture<Void> cashierFuture = CompletableFuture.runAsync(() -> {
            OfflineRefundOrderCashierDeskDO offlineRefundOrderCashierDeskDO = offlineOrderSearchRepository.getOfflineRefundOrderCashierDesk(refundNo);
            resDto.get().setOfflineRefundOrderCashierDeskDTO(instance.toDTO(offlineRefundOrderCashierDeskDO));
          }, orderSearchSubPool);
          futureList.add(cashierFuture);
          break;
        case DETAIL:
          if(qryScaleList.contains(RefundQryScaleEnum.ERP)){
            break;
          }
        case ERP:
          CompletableFuture<Void> detailFuture = CompletableFuture.runAsync(() -> {
            List<OfflineRefundOrderDetailDO> refundOrderDetailDOS = offlineOrderSearchRepository.getOfflineRefundOrderDetail(refundNo);
            resDto.get().setOfflineRefundOrderDetailDTOList(refundOrderDetailDOS.stream().map(instance::toDto).collect(Collectors.toList()));
          }, orderSearchSubPool);
          futureList.add(detailFuture);
          break;
        case MEDICARE:
          // 查询退单医保
          CompletableFuture<Void> medInsSettleFuture = CompletableFuture.runAsync(() -> {
            OfflineRefundOrderMedInsSettleDO offlineRefundOrderMedInsSettleDO = offlineOrderSearchRepository.getOfflineRefundOrderMedInsSettle(refundNo);
            resDto.get().setOfflineRefundOrderMedInsSettleDto(instance.toDTO(offlineRefundOrderMedInsSettleDO));
          }, orderSearchSubPool);
          futureList.add(medInsSettleFuture);
          break;
        case PAY:
          CompletableFuture<Void> payFuture = CompletableFuture.runAsync(() -> {
          List<OfflineRefundOrderPayDO> refundOrderPayDOList = offlineOrderSearchRepository.getOfflineRefundOrderPay(refundNo);
          resDto.get().setOfflineRefundOrderPayDTOList(refundOrderPayDOList.stream().map(instance::toDTO).collect(Collectors.toList()));
          }, orderSearchSubPool);
          futureList.add(payFuture);
          break;
        default:
          break;
      }
    }
    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    return resDto.get();
  }
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @Override
  @Transactional
  public Boolean deletedOfflineOrder(OfflineOrderDO offlineOrderDO,String reason,Boolean deleteAll) {
    String orderNo = offlineOrderDO.getOrderNo();

    // 删除数据记录到已删除的数据表
    DeletedDataDO deletedDataDo = new DeletedDataDO();
    deletedDataDo.setBusinessNo(orderNo);
    deletedDataDo.setServiceName(applicationName);
    deletedDataDo.setDatabaseName(Database.DSCLOUD_OFFLINE);
    deletedDataDo.setTableName(Table.OFFLINE_ORDER_REGEX);
    deletedDataDo.setCreatedTime(new Date());
    deletedDataDo.setCreatedBy(applicationName);
    deletedDataDo.setDeletedData(OrderJsonUtils.toJson(offlineOrderDO));
    deletedDataDo.setReason(reason);
    deleteDataRepository.toDeleteData(deletedDataDo);

    if(StringUtils.isEmpty(offlineOrderDO.getOrderNo())){
      throw new RuntimeException("orderNO不能为空");
    }
    LambdaQueryWrapper<OfflineOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OfflineOrderDO::getOrderNo,offlineOrderDO.getOrderNo());
    boolean result = offlineOrderMapper.delete(queryWrapper) > 0;
    if(deleteAll && !StringUtils.isEmpty(offlineOrderDO.getOrderNo())){
      // 临时写
      offlineOrderMapper.deleteAll0(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll1(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll2(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll3(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll4(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll5(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll6(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll7(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll8(offlineOrderDO.getOrderNo());
      offlineOrderMapper.deleteAll9(offlineOrderDO.getOrderNo());
    }

    try {
      removeEsOrderRepeatedData.removeEsDataByDeletedDataDO(Lists.newArrayList(deletedDataDo));
    } catch (Exception e) {
      log.error("deletedOfflineOrder to removeEsData",e);
    }
    return result;
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @Override
  @Transactional
  public Boolean deletedOfflineRefundOrder(OfflineRefundOrderDO offlineRefundOrderDO,String reason,Boolean deleteAll) {
    String refundNo = offlineRefundOrderDO.getRefundNo();

    // 删除数据记录到已删除的数据表
    DeletedDataDO deletedDataDo = new DeletedDataDO();
    deletedDataDo.setBusinessNo(refundNo);
    deletedDataDo.setServiceName(applicationName);
    deletedDataDo.setDatabaseName(Database.DSCLOUD_OFFLINE);
    deletedDataDo.setTableName(Table.OFFLINE_REFUND_ORDER_REGEX);
    deletedDataDo.setCreatedTime(new Date());
    deletedDataDo.setCreatedBy(applicationName);
    deletedDataDo.setDeletedData(OrderJsonUtils.toJson(offlineRefundOrderDO));
    deletedDataDo.setReason(reason);
    deleteDataRepository.toDeleteData(deletedDataDo);

    if(Objects.isNull(offlineRefundOrderDO.getRefundNo())){
      throw new RuntimeException("refundNo不能为空");
    }
    LambdaQueryWrapper<OfflineRefundOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OfflineRefundOrderDO::getRefundNo,offlineRefundOrderDO.getRefundNo());
    boolean result = offlineRefundOrderMapper.delete(queryWrapper) > 0;
    if(deleteAll && !StringUtils.isEmpty(offlineRefundOrderDO.getRefundNo())){
      // 临时写
      offlineRefundOrderMapper.deleteAll0(offlineRefundOrderDO.getRefundNo());
      offlineRefundOrderMapper.deleteAll1(offlineRefundOrderDO.getRefundNo());
      offlineRefundOrderMapper.deleteAll2(offlineRefundOrderDO.getRefundNo());
      offlineRefundOrderMapper.deleteAll3(offlineRefundOrderDO.getRefundNo());
      offlineRefundOrderMapper.deleteAll4(offlineRefundOrderDO.getRefundNo());
      offlineRefundOrderMapper.deleteAll5(offlineRefundOrderDO.getRefundNo());
    }
    try {
      removeEsOrderRepeatedData.removeEsDataByDeletedDataDO(Lists.newArrayList(deletedDataDo));
    } catch (Exception e) {
      log.error("deletedOfflineRefundOrder to removeEsData",e);
    }
    return result;
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @Override
  @Transactional
  public Boolean chaiLingOfflineOrderUpdate(OfflineOrderDO dbOfflineOrder) {
    int count = 0;
    if (Objects.nonNull(dbOfflineOrder.getId())) {
      count = count + offlineOrderMapper.updateById(dbOfflineOrder);

      for (OfflineOrderDetailDO offlineOrderDetailDO : dbOfflineOrder.getDetailDOList()) {
        if (Objects.isNull(offlineOrderDetailDO.getId())) {
          continue;
        }
        count = count + offlineOrderDetailMapper.updateById(offlineOrderDetailDO);
      }

    }
    return count > 0;
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @Override
  @Transactional
  public Boolean chaiLingOfflineRefundOrderUpdate(OfflineRefundOrderDO dbRefund) {
    int count = 0;
    if (Objects.nonNull(dbRefund.getId())) {
      count = count + offlineRefundOrderMapper.updateById(dbRefund);

      for (OfflineRefundOrderDetailDO offlineRefundOrderDetailDO : dbRefund.getRefundOrderDetailDOList()) {
        if (Objects.isNull(offlineRefundOrderDetailDO.getId())) {
          continue;
        }
        count = count + offlineRefundOrderDetailMapper.updateById(offlineRefundOrderDetailDO);
      }
    }
    return count > 0;
  }

  @Override
  public List<OfflineRefundOrderDetailDO> queryBatchRefundDetail(List<String> offlineRefundNoList) {
    List<OfflineRefundOrderDetailDO> all = Lists.newArrayList();

    Set<String> shardingIndexSet = ShardingUtils.shardingIndexSet(offlineRefundNoList);
    for (String shardingIndex : shardingIndexSet) {
      List<String> refundNoList = offlineRefundNoList.stream()
          .filter(s -> shardingIndexSet.contains(ShardingHelper.getTableIndexByNo(s)))
          .collect(Collectors.toList());
      if(CollectionUtils.isEmpty(refundNoList)){
        continue;
      }

      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setQueryHit(QueryHit.builder().seq(shardingIndex).build());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

        LambdaQueryWrapper<OfflineRefundOrderDetailDO> query = new LambdaQueryWrapper<>();
        query.in(OfflineRefundOrderDetailDO::getRefundNo,refundNoList);
        List<OfflineRefundOrderDetailDO> list = offlineRefundOrderDetailMapper.selectList(query);
        if(!CollectionUtils.isEmpty(list)){
          all.addAll(list);
        }
      }
    }
    return all;
  }

  @Override
  public List<OfflineOrderDetailDO> queryBatchOrderDetail(List<String> orderNoList) {
    List<OfflineOrderDetailDO> all = Lists.newArrayList();

    Set<String> shardingIndexSet = ShardingUtils.shardingIndexSet(orderNoList);
    for (String shardingIndex : shardingIndexSet) {
      List<String> noList = orderNoList.stream()
          .filter(s -> shardingIndexSet.contains(ShardingHelper.getTableIndexByNo(s)))
          .collect(Collectors.toList());
      if(CollectionUtils.isEmpty(noList)){
        continue;
      }

      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setQueryHit(QueryHit.builder().seq(shardingIndex).build());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

        LambdaQueryWrapper<OfflineOrderDetailDO> query = new LambdaQueryWrapper<>();
        query.in(OfflineOrderDetailDO::getOrderNo,noList);
        List<OfflineOrderDetailDO> list = offlineOrderDetailMapper.selectList(query);
        if(!CollectionUtils.isEmpty(list)){
          all.addAll(list);
        }
      }
    }
    return all;
  }
}
