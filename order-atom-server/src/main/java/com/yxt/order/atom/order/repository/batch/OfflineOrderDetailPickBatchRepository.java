package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailPickMapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 17:05
 * @email: <EMAIL>
 */
@Repository
public class OfflineOrderDetailPickBatchRepository extends
    ServiceImpl<OfflineOrderDetailPickMapper, OfflineOrderDetailPickDO> {

}
