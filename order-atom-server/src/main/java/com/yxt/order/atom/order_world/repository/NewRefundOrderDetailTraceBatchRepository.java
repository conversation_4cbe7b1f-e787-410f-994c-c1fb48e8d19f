package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.RefundOrderDetailTraceDO;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderDetailTraceMapper;
import org.springframework.stereotype.Repository;

/**
 * 退单明细追溯码信息 Repository
 *
 * <AUTHOR>
 */
@Repository
public class NewRefundOrderDetailTraceBatchRepository extends ServiceImpl<NewRefundOrderDetailTraceMapper, RefundOrderDetailTraceDO> {

}
