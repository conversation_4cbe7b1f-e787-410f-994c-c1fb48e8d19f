package com.yxt.order.atom.order_sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 业务单号同步记录
 */
@Data
@TableName("order_sync_mapping")
public class OrderSyncMappingDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 同步前业务单号
   */
  private String originBusinessNo;

  /**
   * 同步后业务单号
   */
  private String targetBusinessNo;

  /**
   * 三方业务单号
   */
  private String thirdBusinessNo;

  /**
   * 三方平台编码
   */
  private String thirdPlatformCode;

  /**
   * 机构编码
   */
  private String orgCode;

  /**
   * O2O，B2C
   */
  private String businessType;

  /**
   * ORDER_TO_NEW：正单老模型同步到新模型
   * ORDER_TO_OLD：正单新模型同步到老模型
   * AFTER_SALE_TO_NEW：售后单老模型同步到新模型
   * AFTER_SALE_TO_OLD：售后单新模型同步到老模型
   */
  private String syncType;

  /**
   * 触发类型  JOB,MQ,API
   */
  private String triggerType;

  /**
   * 追踪id
   */
  private String traceId;

  /**
   * 备注信息
   */
  private String remark;

  /**
   * 扩展字段
   */
  private String extendData;

  /**
   * 最新的更新时间，回环问题
   */
  private Date lastModifyTime;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;
}
