package com.yxt.order.atom.order.repository.abstracts.refund.insert;

import cn.hutool.core.collection.CollUtil;
import com.yxt.order.atom.order.entity.RefundCheckDO;
import com.yxt.order.atom.order.entity.RefundLogDO;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundInsert;
import com.yxt.order.atom.order.repository.batch.RefundCheckBatchRepository;
import com.yxt.order.atom.order.repository.batch.RefundLogBatchRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RefundCheckInsert extends AbstractRefundInsert<List<RefundCheckDO>> {

  @Autowired
  private RefundCheckBatchRepository refundCheckBatchRepository;

  @Override
  protected Boolean canInsert() {
    return CollUtil.isNotEmpty(data());
  }

  @Override
  protected Integer insert(List<RefundCheckDO> refundCheckList) {
    return refundCheckBatchRepository.saveBatch(refundCheckList) ? refundCheckList.size() : 0;
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<RefundCheckDO> data() {
    return this.saveData.getRefundCheckList();
  }
}
