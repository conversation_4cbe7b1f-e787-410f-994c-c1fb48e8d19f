package com.yxt.order.atom.order.es.sync.clean;

import static com.yxt.order.atom.common.utils.OrderDateUtils.formatYYMMDD;

import com.yxt.order.atom.order.es.doc.EsOrgRefund;
import com.yxt.order.atom.order.es.mapper.EsOrgRefundMapper;
import com.yxt.order.atom.order.es.sync.AbstractClean;
import java.util.Date;
import javax.annotation.Resource;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


@Component
public class EsOrgRefundClean extends AbstractClean {

  @Resource
  private EsOrgRefundMapper esOrgRefundMapper;

  @Override
  protected Integer expireDays() {
    return 365 * 2 + 20; // 保存2年,多冗余20天
  }

  @Override
  protected void clean(String startDate, String endDate) {
    LambdaEsQueryWrapper<EsOrgRefund> query = new LambdaEsQueryWrapper<>();
    query.gt(EsOrgRefund::getCreateTime, startDate);
    query.le(EsOrgRefund::getCreateTime, endDate);
    esOrgRefundMapper.delete(query);
  }

  @Override
  protected Boolean checkHasData(String endDate) {
    LambdaEsQueryWrapper<EsOrgRefund> query = new LambdaEsQueryWrapper<>();
    query.le(EsOrgRefund::getCreateTime, endDate);
    Long count = esOrgRefundMapper.selectCount(query);
    return count > 0;
  }


  @Override
  protected Date getLatestdEndDate(Date endDate) {
    LambdaEsQueryWrapper<EsOrgRefund> query = new LambdaEsQueryWrapper<>();
    query.le(EsOrgRefund::getCreateTime, formatYYMMDD(endDate));
    query.orderByDesc(EsOrgRefund::getCreateTime);
    EsPageInfo<EsOrgRefund> esPageInfo = esOrgRefundMapper.pageQuery(query, 1, 1);
    if(CollectionUtils.isEmpty(esPageInfo.getList())){
      return endDate;
    }
    return esPageInfo.getList().get(0).getCreateTime();
  }
}
