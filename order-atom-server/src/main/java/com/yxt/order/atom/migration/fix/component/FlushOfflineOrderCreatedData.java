//package com.yxt.order.atom.migration.fix.component;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.yxt.lang.util.JsonUtils;
//import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
//import com.yxt.order.atom.migration.fix.FlushOfflineOrderCreatedScene;
//import com.yxt.order.atom.order.entity.OfflineOrderDO;
//import com.yxt.order.atom.order.es.sync.AbstractFlash;
//import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
//import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
//import com.yxt.order.atom.order.repository.OfflineOrderRepository;
//import java.util.List;
//import java.util.Objects;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.dao.DuplicateKeyException;
//import org.springframework.stereotype.Component;
//
///**
// * 如果billTime != created ,使用billTime给createTime赋值
// *
// * @author: moatkon
// * @time: 2024/12/13 10:21
// * <p>
// */
//@Component
//@Slf4j
//@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
//@Deprecated
//public class FlushOfflineOrderCreatedData extends
//    AbstractFlash<OfflineOrderDO, OfflineOrderDO, FlushOfflineOrderCreatedScene> {
//
//  @Value("${keChuanTotalAmountDataGetLimit:2000}")
//  private Integer keChuanTotalAmountDataGetLimit;
//
//  @Resource
//  private OfflineOrderMapper offlineOrderMapper;
//
//  @Resource
//  private OfflineOrderRepository offlineOrderRepository;
//
//
//  @Override
//  protected Long queryCursorStartId() {
//    CustomData customData = getCustomData();
//    Long startId = customData.getStartId();
//
//    return Objects.nonNull(startId) ? startId : offlineOrderMapper.selectMinId(getFlashParam());
//  }
//
//  @Override
//  protected Long queryCursorEndId() {
//    CustomData customData = getCustomData();
//    Long endId = customData.getEndId();
//
//    return Objects.nonNull(endId) ? endId : offlineOrderMapper.selectMaxId(getFlashParam());
//  }
//
//  @Override
//  protected List<OfflineOrderDO> getSourceList() {
//    LambdaQueryWrapper<OfflineOrderDO> query = FlashQueryWrapper.offlineOrderFlashQuery(
//        getFlashParam(), defaultLimit());
//    return offlineOrderMapper.selectList(query);
//  }
//
//  @Override
//  protected List<OfflineOrderDO> assembleTargetData(List<OfflineOrderDO> offlineOrderDOList) {
//    return offlineOrderDOList;
//  }
//
//  /**
//   * 因为无输入逻辑,可以直接刷数
//   *
//   * @param offlineOrderList
//   */
//  @Override
//  protected void flash(List<OfflineOrderDO> offlineOrderList) {
//
//    for (OfflineOrderDO offlineOrderDO : offlineOrderList) {
//      String migration = offlineOrderDO.getMigration();
//      if (StringUtils.isEmpty(migration)) {
//        continue;
//      }
//      if (!Boolean.TRUE.toString().equals(migration)) {
//        continue;
//      }
//
//      if (Objects.isNull(offlineOrderDO.getId())) {
//        continue;
//      }
//
//      if (offlineOrderDO.getBillTime().compareTo(offlineOrderDO.getCreated()) != 0) {
//        try {
//          offlineOrderDO.setCreated(offlineOrderDO.getBillTime()); // 删除重复的是在内存中判断的,现在迁移的单子时间要持久化
//          offlineOrderMapper.updateById(offlineOrderDO);
//        } catch (Exception ignore) {
//          if(ignore instanceof DuplicateKeyException){
//            log.warn("FlushOfflineOrderCreatedData uk重复,{}", JsonUtils.toJson(offlineOrderDO));
//            offlineOrderRepository.deletedOfflineOrder(offlineOrderDO,
//                DELETED_MIGRATION_REPEATED, Boolean.TRUE);
//          }
//        }
//      }
//
//    }
//
//  }
//
//
//  @Override
//  protected Boolean isSharding() {
//    return Boolean.TRUE;
//  }
//
//  @Override
//  protected Boolean isHandleNonVipData() {
//    return Boolean.TRUE;
//  }
//
//  @Override
//  protected Integer defaultLimit() {
//    return keChuanTotalAmountDataGetLimit;
//  }
//}
