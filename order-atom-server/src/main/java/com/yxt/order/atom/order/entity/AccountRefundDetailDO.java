package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 退款单下账单明细表
 *
 * @TableName account_refund_detail
 */
@Data
@TableName(value = "account_refund_detail")
public class AccountRefundDetailDO implements Serializable {

  /**
   *
   */
  @TableId(type = IdType.AUTO)
  private Long id;

  /**
   * 系统退款单号
   */
  private Long refundNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String goodsName;

  /**
   * 商品类型, 普通商品- NORMAL、赠品-GIFT
   */
  private String goodsType;

  /**
   * 生产批号
   */
  private String batchNo;

  /**
   * 商品数量
   */
  private Integer goodsCount;

  /**
   * 商品售价
   */
  private BigDecimal price;

  /**
   * 退款单价
   */
  private BigDecimal refundPrice;

  /**
   * 退款金额 (退款数量*单价)
   */
  private BigDecimal refundGoodsAmount;

  /**
   * 分摊金额
   */
  private BigDecimal shareAmount;

  /**
   * 退款商品原价
   */
  private BigDecimal goodsOrderPrice;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 是否删除 0-未删除 时间戳-已删除
   */
  private Long deleted;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

}