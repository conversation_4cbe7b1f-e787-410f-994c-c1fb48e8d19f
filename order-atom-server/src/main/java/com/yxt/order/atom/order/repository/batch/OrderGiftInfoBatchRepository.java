package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.OrderGiftInfoDO;
import com.yxt.order.atom.order.mapper.OrderGiftInfoMapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 15:41
 * @email: <EMAIL>
 */
@Repository
public class OrderGiftInfoBatchRepository extends
    ServiceImpl<OrderGiftInfoMapper, OrderGiftInfoDO> {

}

