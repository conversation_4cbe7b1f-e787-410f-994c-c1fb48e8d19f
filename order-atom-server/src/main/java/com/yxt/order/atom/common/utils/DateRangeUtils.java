package com.yxt.order.atom.common.utils;

import com.google.common.collect.Sets;
import com.yxt.order.common.CommonDateUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;

public class DateRangeUtils {

  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(
      "yyyy-MM-dd HH:mm:ss");

  @Data
  public static class RangeDay {

    private String start;
    private String end;

    public RangeDay(String start, String end) {
      this.start = start;
      this.end = end;
    }

    public String vStr(String prefix) {
      return String.format("%s_%s_%s",prefix, getReplace(start), getReplace(end)
      );
    }

    private String getReplace(String value) {
      return value
          .replace(" ", "")
          .replace("-", "")
          .replace(":", "");
    }
  }

  /**
   * 按指定天数切分时间段
   *
   * @param startDateStr 开始时间 格式：yyyy-MM-dd HH:mm:ss
   * @param endDateStr   结束时间 格式：yyyy-MM-dd HH:mm:ss
   * @param days         每段的天数
   * @return 返回按指定天数切分的时间段列表
   */
  public static List<RangeDay> splitByDays(String startDateStr, String endDateStr, int days) {
    if (days <= 0) {
      throw new IllegalArgumentException("Days must be positive");
    }

    LocalDateTime startDateTime = LocalDateTime.parse(startDateStr, formatter);
    LocalDateTime endDateTime = LocalDateTime.parse(endDateStr, formatter);

    // 确保开始时间在结束时间之前
    if (startDateTime.isAfter(endDateTime)) {
      throw new IllegalArgumentException("Start date must be before end date");
    }

    List<RangeDay> result = new ArrayList<>();
    LocalDateTime currentStart = startDateTime;

    while (currentStart.isBefore(endDateTime)) {
      // 计算当前段的结束时间
      LocalDateTime currentEnd = currentStart.plusDays(days);

      // 如果当前段的结束时间超过了总的结束时间，使用总的结束时间
      if (currentEnd.isAfter(endDateTime)) {
        currentEnd = endDateTime;
      }

      result.add(new RangeDay(
          currentStart.format(formatter),
          currentEnd.format(formatter)
      ));

      // 移动到下一段的开始时间
      currentStart = currentEnd;
    }

    return result;
  }

  /**
   * 按天切分时间段
   *
   * @param startDateStr 开始时间 格式：yyyy-MM-dd HH:mm:ss
   * @param endDateStr   结束时间 格式：yyyy-MM-dd HH:mm:ss
   * @return 返回按天切分的时间段列表
   */
  public static List<RangeDay> splitByDay(String startDateStr, String endDateStr) {
    LocalDateTime startDateTime = LocalDateTime.parse(startDateStr, formatter);
    LocalDateTime endDateTime = LocalDateTime.parse(endDateStr, formatter);

    List<RangeDay> result = new ArrayList<>();

    LocalDate startDate = startDateTime.toLocalDate();
    LocalDate endDate = endDateTime.toLocalDate();

    // 如果开始和结束是同一天，直接返回原始时间段
    if (startDate.equals(endDate)) {
      result.add(new RangeDay(startDateStr, endDateStr));
      return result;
    }

    // 处理第一天
    LocalDateTime firstDayEnd = startDate.plusDays(1).atTime(0, 0, 0);
    result.add(new RangeDay(
        startDateTime.format(formatter),
        firstDayEnd.format(formatter)
    ));

    // 处理中间的完整天
    LocalDate currentDate = startDate.plusDays(1);
    while (currentDate.isBefore(endDate)) {
      LocalDateTime dayStart = currentDate.atStartOfDay();
      LocalDateTime dayEnd = currentDate.plusDays(1).atStartOfDay();

      result.add(new RangeDay(
          dayStart.format(formatter),
          dayEnd.format(formatter)
      ));

      currentDate = currentDate.plusDays(1);
    }

    // 处理最后一天
    if (!startDate.equals(endDate)) {
      LocalDateTime lastDayStart = endDate.atStartOfDay();
      result.add(new RangeDay(
          lastDayStart.format(formatter),
          endDateTime.format(formatter)
      ));
    }

    return result;
  }


  // 使用示例
  public static void main(String[] args) {
    String startDate = CommonDateUtils.formatDate(OrderDateUtils.previousDate(new Date(), 180));
    String endDate = "2025-01-15 14:50:00";

    Set<String> monitorKeySet = Sets.newHashSet();
    List<RangeDay> customRanges33s = splitByDays(startDate, endDate, 2);
    for (RangeDay range : customRanges33s) {
      String start = range.getStart();
      String end = range.getEnd();
      String monitorKey =
          "Monitor:FiveClass:" + OrderAtomUtils.getNumStr(start) + "_" + OrderAtomUtils.getNumStr(
              end);
      if (monitorKeySet.contains(monitorKey)) {
        throw new RuntimeException("重复" + monitorKey);
      }
      System.out.println(
          "curl --location 'http://localhost:8080/1.0/flashFiveClassMiss' --header 'Content-Type: application/json' --data '{\"startDate\": \""
              + start + "\",\"endDate\": \"" + end + "\",\"monitorKey\":\"" + monitorKey + "\"}'");

      monitorKeySet.add(monitorKey);
    }

  }
}