package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单多支付关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("order_multi_pay_info")
public class OrderMultiPayInfoDO implements Serializable {

  private static final long serialVersionUID = 3111593572192650622L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  @ApiModelProperty(value = "系统订单号")
  private Long orderNo;

  @ApiModelProperty(value = "退款单号")
  private Long refundNo;

  @ApiModelProperty(value = "支付编码,-对应ERP支付编码")
  private String payCode;

  @ApiModelProperty(value = "支付类型")
  private String payName;

  @ApiModelProperty(value = "支付类型对应的金额")
  private BigDecimal payPrice;

  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  @ApiModelProperty(value = "末次修改时间")
  private Date modifyTime;

}
