package com.yxt.order.atom.manage;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineOrderListReq;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineRefundOrderListReq;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineOrderRes;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineRefundOrderRes;

/**
 * 线下订单管理服务
 * @author: moatkon
 * @time: 2025/4/1 13:50
 */
public interface OfflineOrderManageService {

  PageDTO<OfflineOrderRes> offlineOrderList(OfflineOrderListReq req);
  PageDTO<OfflineRefundOrderRes> offlineRefundOrderList(OfflineRefundOrderListReq req);

  Boolean createOfflineOrderManageIndex();

  Boolean createOfflineRefundOrderManageIndex();
}
