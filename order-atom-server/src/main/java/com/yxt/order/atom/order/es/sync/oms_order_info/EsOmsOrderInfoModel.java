package com.yxt.order.atom.order.es.sync.oms_order_info;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsOmsOrderEx;
import com.yxt.order.atom.order.es.doc.EsOmsOrderInfo;
import com.yxt.order.atom.order.es.doc.EsOmsOrderItem;
import com.yxt.order.atom.order.es.doc.LogisticsTagItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * OmsOrderInfo条件索引
 */
@Data
public class EsOmsOrderInfoModel extends BaseEsIndexModel implements Serializable {

  private Long omsOrderNo;
  private String clientCode;


  /**
   * 逻辑删除字段 默认0-未删除
   */
  private Long deleted;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 订单归属类型 0-为商户，默认；1-为供应商订单
   */
  private Integer orderOwnerType;
  /**
   * 供应商编码 无供应商的默认填0,便于查询
   */
  private String supplierCode;

  /**
   * 订单状态:10待审核,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭, (特别注意 5是处方单)
   */
  private Integer orderStatus;
  private Integer erpStatus;

  /**
   * 下单真实时间
   */
  private Date created;


  private Date payTime;

  /**
   * 审核时间
   */
  private Date auditTime;

  /**
   * 发货时间
   */
  private Date shipTime;
  private Integer shipStatus;

  /**
   * 取消时间
   */
  private Date cancelTime;
  /**
   * 完成时间
   */
  private Date completeTime;

  /**
   * 完成时间
   */
  private Date billTime;

  /**
   * 推广门店
   */
  private String spreadStoreCode;

  /**
   * 是否为邮费单 0否，1是
   */
  private Integer isPostFeeOrder;

  /**
   * 异常状态:  0.无异常 1.异常 1,疑似刷单订单 2，订单金额异常 3，商品库存不足 4，商品数量异常 5，商品不存在 6，订单预估毛利异常 7，仓库发货失败 8，平台发货失败 11.
   * 修改地址失败
   */
  private Integer exStatus;


  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 快递id
   */
  private Integer expressId;

  /**
   * 仓库id
   */
  private String warehouseId;

  /**
   * 是否是审方单（结合审方配置）
   */
  private Integer isPrescription;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 发货单号
   */
  private Long omsShipNo;

  /**
   * 快递单号
   */
  private String expressNumber;

  /**
   * 三方平台买家昵称
   */
  private String buyerName;

  /**
   * 买家留言
   */
  private String buyerMessage;
  /**
   * 卖家备注
   */
  private String sellerRemark;
  /**
   * 订单类型，1 平台订单（常规的）、2 导入订单（导）、3 手工订单（手）、4 拆分订单（拆）
   */
  private Integer orderType;

  /**
   * 订单拆分状态：0 未拆单，1 拆分订单的源头订单，2 拆分的最终订单，3 拆分中间状态
   */
  private Integer splitStatus;


  /**
   * 快递面单状态：0-未生成，1-未打印，2-已打印
   */
  private String sheetStatus;

  /**
   * 系统备注
   */
  private String remark;

  /**
   * 线上门店编码
   */
  private String onlineStoreCode;
  private String onlineStoreType;

  @ApiModelProperty("订单商品总数量")
  private Integer goodsQty;

  @ApiModelProperty("订单商品种类数")
  private Integer goodsCategoryQty;

  /**
   * tag字段里的json解析出来后
   */
  private Integer tagEmptyOrderStatus;
  private Integer tagMergeOrderStatus;
  private Integer tagPreSellOrderStatus;
  private Integer tagModifyAddressTagStatus;
  private List<LogisticsInterceptOrder> tagLogisticsIntercept;
  private List<LogisticsUpAddressOrder> tagLogisticsUpAddress;
  private Integer tagAgentDeliveryOrder;

  /**
   * 备货状态 2-已备货 1-待备货
   * <p>
   * 云仓订单专用
   */
  private String stockState;

  /**
   * 发货单打印次数
   */
  private Integer sendOrderPrintNum; // 原字段 sendorderPrintNum

  private Integer refundCount;

  /**
   * ERP审核中
   */
  private Integer erpAuditStatus;


  /**
   * erp_code_list
   */
  private String erpCodeList;

  // *********platform_order_info条件索引平铺**********

  /**
   * 是否处方药 0普通订单，1 处方药订单
   */
  private String havecfy;

  /**
   * 处方药审核状态 0 未审核 1 已审核 -1无需审核 2审核不通过 3未知状态
   */
  private String rxAuditStatus;

  // *********order_pay_info 条件索引平铺**********
  /**
   * 支付方式,1是在线支付,2是货到付款吧
   */
  private String payType;
  /**
   * 客户实付
   */
  private BigDecimal buyerActualAmount;

  // *********order_delivery_address  条件索引平铺**********
  /**
   * 收货人手机
   */
  private String receiverMobile;
  private String receiverTelephone;
  /**
   * 收货人名
   */
  private String receiverName;

  /**
   * 完整详细地址
   */
  private String fullAddress;

  // *********oms_order_ex 条件索引列表**********
  private List<OmsOrderExModel> omsOrderExModelList;


  private List<OmsOrderItemModel> omsOrderItemModelList;


  //--------------------------- logistic_order表
  /**
   * 平台code
   */
  private String logisticOrderOfPlatformCode;

  /**
   * 1有效 0失效
   */
  private Integer logisticOrderOfStatus;

  /**
   * 物流配置id 关联的是logistic_config_info
   */
  private Long logisticOrderOfLogisticConfigId;

  //--------------------------- left join所以平铺 logistic_config_info
  /**
   * 模板id
   */
  private Long logisticConfigInfoOfStandardTemplateId;


  public EsOmsOrderInfo create() {
    EsOmsOrderInfo esOmsOrderInfo = new EsOmsOrderInfo();
    esOmsOrderInfo.setId(this.getOmsOrderNo());
    esOmsOrderInfo.setOmsOrderNo(this.getOmsOrderNo());
    esOmsOrderInfo.setClientCode(this.getClientCode());
    esOmsOrderInfo.setDeleted(this.getDeleted());
    esOmsOrderInfo.setMerCode(this.getMerCode());
    esOmsOrderInfo.setOrderOwnerType(this.getOrderOwnerType());
    esOmsOrderInfo.setSupplierCode(this.getSupplierCode());
    esOmsOrderInfo.setOrderStatus(this.getOrderStatus());
    esOmsOrderInfo.setErpStatus(this.getErpStatus());
    // 平铺logistic_order start
    esOmsOrderInfo.setLogisticOrderOfPlatformCode(this.getLogisticOrderOfPlatformCode());
    esOmsOrderInfo.setLogisticOrderOfStatus(this.getLogisticOrderOfStatus());
    esOmsOrderInfo.setLogisticOrderOfLogisticConfigId(this.getLogisticOrderOfLogisticConfigId());
    esOmsOrderInfo.setLogisticConfigInfoOfStandardTemplateId(
        this.getLogisticConfigInfoOfStandardTemplateId());
    // 平铺logistic_order end
    esOmsOrderInfo.setCreated(this.getCreated());
    esOmsOrderInfo.setPayTime(this.getPayTime());
    esOmsOrderInfo.setAuditTime(this.getAuditTime());
    esOmsOrderInfo.setShipTime(this.getShipTime());
    esOmsOrderInfo.setShipStatus(this.getShipStatus());
    esOmsOrderInfo.setCancelTime(this.getCancelTime());
    esOmsOrderInfo.setCompleteTime(this.getCompleteTime());
    esOmsOrderInfo.setBillTime(this.getBillTime());
    esOmsOrderInfo.setSpreadStoreCode(this.getSpreadStoreCode());
    esOmsOrderInfo.setIsPostFeeOrder(this.getIsPostFeeOrder());
    esOmsOrderInfo.setExStatus(this.getExStatus());
    esOmsOrderInfo.setCreateTime(this.getCreateTime());
    esOmsOrderInfo.setExpressId(this.getExpressId());
    esOmsOrderInfo.setWarehouseId(this.getWarehouseId());
    esOmsOrderInfo.setIsPrescription(this.getIsPrescription());
    esOmsOrderInfo.setThirdPlatformCode(this.getThirdPlatformCode());
    esOmsOrderInfo.setThirdOrderNo(this.getThirdOrderNo());
    esOmsOrderInfo.setOmsShipNo(this.getOmsShipNo());
    esOmsOrderInfo.setExpressNumber(this.getExpressNumber());
    esOmsOrderInfo.setBuyerName(this.getBuyerName());
    esOmsOrderInfo.setBuyerMessage(this.getBuyerMessage());
    esOmsOrderInfo.setSellerRemark(this.getSellerRemark());
    esOmsOrderInfo.setOrderType(this.getOrderType());
    esOmsOrderInfo.setSplitStatus(this.getSplitStatus());
    esOmsOrderInfo.setSheetStatus(this.getSheetStatus());
    esOmsOrderInfo.setRemark(this.getRemark());
    esOmsOrderInfo.setOnlineStoreCode(this.getOnlineStoreCode());
    esOmsOrderInfo.setOnlineStoreType(this.getOnlineStoreType());
    esOmsOrderInfo.setGoodsQty(this.getGoodsQty());
    esOmsOrderInfo.setGoodsCategoryQty(this.getGoodsCategoryQty());
    esOmsOrderInfo.setTagEmptyOrderStatus(this.getTagEmptyOrderStatus());
    esOmsOrderInfo.setTagMergeOrderStatus(this.getTagMergeOrderStatus());
    esOmsOrderInfo.setTagPreSellOrderStatus(this.getTagPreSellOrderStatus());
    esOmsOrderInfo.setTagModifyAddressTagStatus(this.getTagModifyAddressTagStatus());
    esOmsOrderInfo.setTagLogisticsIntercept(BeanUtil.copyToList(this.getTagLogisticsIntercept(), LogisticsTagItem.class));
    esOmsOrderInfo.setTagLogisticsUpAddress(BeanUtil.copyToList(this.getTagLogisticsUpAddress(), LogisticsTagItem.class));
    esOmsOrderInfo.setTagAgentDeliveryOrder(this.getTagAgentDeliveryOrder());
    esOmsOrderInfo.setStockState(this.getStockState());
    esOmsOrderInfo.setSendOrderPrintNum(this.getSendOrderPrintNum());
    esOmsOrderInfo.setRefundCount(this.getRefundCount());
    esOmsOrderInfo.setErpAuditStatus(this.getErpAuditStatus());
    esOmsOrderInfo.setErpCodeList(this.getErpCodeList());
    esOmsOrderInfo.setHavecfy(this.getHavecfy());
    esOmsOrderInfo.setRxAuditStatus(this.getRxAuditStatus());
    esOmsOrderInfo.setPayType(this.getPayType());
    esOmsOrderInfo.setBuyerActualAmount(this.getBuyerActualAmount());
    esOmsOrderInfo.setReceiverMobile(this.getReceiverMobile());
    esOmsOrderInfo.setReceiverTelephone(this.getReceiverTelephone());
    esOmsOrderInfo.setReceiverName(this.getReceiverName());
    esOmsOrderInfo.setFullAddress(this.getFullAddress());
    if (!CollectionUtils.isEmpty(this.getOmsOrderItemModelList())) {
      esOmsOrderInfo.setEsOmsOrderItemList(this.getOmsOrderItemModelList().stream().map(item -> {
        EsOmsOrderItem esOmsOrderItem = new EsOmsOrderItem();
        esOmsOrderItem.setOmsOrderNo(item.getOmsOrderNo());
        esOmsOrderItem.setStatus(item.getStatus());
        esOmsOrderItem.setErpCode(item.getErpCode());
        esOmsOrderItem.setCommodityName(item.getCommodityName());
        return esOmsOrderItem;
      }).collect(Collectors.toList()));
    }

    if (!CollectionUtils.isEmpty(this.getOmsOrderExModelList())) {
      esOmsOrderInfo.setEsOmsOrderExList(this.getOmsOrderExModelList().stream().map(item -> {
        EsOmsOrderEx esOmsOrderEx = new EsOmsOrderEx();
        esOmsOrderEx.setExType(item.getExType());
        esOmsOrderEx.setOperateStatus(item.getOperateStatus());
        return esOmsOrderEx;
      }).collect(Collectors.toList()));
    }

    return esOmsOrderInfo;
  }

  @Data
  public static class OmsOrderItemModel {
    private Long omsOrderNo;

    /**
     * 明细状态，0正常显示商品，1已退款，2被换货的商品
     */
    private Integer status;

    /**
     * 商品erp编码
     */
    private String erpCode;

    /**
     * 商品名称
     */
    private String commodityName;


  }

  @Data
  public static class OmsOrderExModel {

    /**
     * 异常状态:  0.无异常 1.异常,具体异常查看枚举类
     */
    private Integer exType;

    /**
     * 异常处理结果 0-未处理 1-已处理
     */
    private Integer operateStatus;

  }

  @Data
  public static class LogisticsInterceptOrder {
    private Integer status;
    private String logisticsNo;
  }

  @Data
  public static class LogisticsUpAddressOrder {
    private Integer status;
    private String logisticsNo;
  }

}
