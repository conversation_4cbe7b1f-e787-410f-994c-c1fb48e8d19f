package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单优惠券信息
 */
@Data
@TableName("offline_order_coupon")
public class OrderCouponDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 内部订单号,自己生成
   */
  private String orderNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 优惠券编码
   */
  private String couponNo;

  /**
   * 优惠券导出编码
   */
  private String openCode;

  /**
   * 优惠券名称
   */
  private String couponName;

  /**
   * 优惠券类型
   */
  private String couponType;

  /**
   * 优惠券面值(String)
   */
  private String couponDenomination;

  /**
   * 使用优惠券金额
   */
  private BigDecimal usedCouponAmount;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 拓展字段
   */
  private String extendJson;

  /**
   * ORDER,DETAIL
   */
  private String type;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;
}
