package com.yxt.order.atom.order.es.sync.order_world.model;


import cn.hutool.core.collection.CollUtil;
import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrder;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrderDetail;
import com.yxt.order.types.offline.enums.StoreDirectJoinTypeEnum;
import com.yxt.order.types.order_world.OrderFlag;
import com.yxt.order.types.order_world.OrderType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class EsOrderWorldOrderModel extends BaseEsIndexModel {

  /**
   * 系统单号
   */
  private String orderNo;

  /**
   * 父系统单号
   */
  private String parentOrderNo;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 父三方订单号
   */
  private String parentThirdOrderNo;


  /**
   * 平台下单时间
   */
  private LocalDateTime created;

  /**
   * 平台下单日期
   */
  private String createdDay;

  /**
   * 创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 支付时间
   */
  private LocalDateTime payTime;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 线上门店编码
   */
  private String onlineStoreCode;

  /**
   * 下单线下机构编码
   */
  private String organizationCode;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  private String launchOrganizationCode;

  /**
   * 发起人id，目前仅B2B有值
   */
  private String launchUserId;

  /**
   * 子公司编码
   */
  private String companyCode;

  /**
   * 订单状态
   *
   * @see com.yxt.order.types.order_world.OrderMainStatus
   */
  private String orderMainStatus;

  /**
   * 支付状态
   *
   * @see com.yxt.order.types.order_world.OrderPaymentStatus
   */
  private String paymentStatus;

  /**
   * 交易场景 ONLINE-线上订单 OFFLINE-线下订单
   */
  private String transChannel;

  /**
   * 三方平台编码
   *
   * @see com.yxt.order.types.order.enums.PlatformCodeEnum
   */
  private String thirdPlatformCode;

  /**
   * 服务模式 O2O B2C JOIN_B2B
   *
   * @see com.yxt.order.types.order_world.BusinessType
   */
  private String businessType;

  /**
   * 订单标记，通过空格分割，使用match匹配
   *
   * @see OrderFlag
   */
  private List<OrderFlag> orderFlags;

  /**
   * 订单类型，通过空格分割，使用match匹配
   *
   * @see OrderType
   */
  private List<String> orderTypes;

  /**
   * 异常类型
   */
  private List<String> abnormalType;

  /**
   * 订单金额
   */
  private BigDecimal orderAmount;

  /**
   * 订单明细
   */
  private List<EsOrderWorldOrderDetailModel> detailList;

  /**
   * 会员编码(唯一值)
   */
  private String userCardNo;

  /**
   * 会员ID (心云)
   */
  private String userId;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   *
   * @see StoreDirectJoinTypeEnum
   */
  private String storeDirectJoinType;

  /**
   * 支付方式,多个使用空格分割，使用match匹配
   */
  private List<String> payTypes;

  /**
   * 是否起效
   */
  private Long valid;

  /**
   * 全局拦截锁,0正常  ,非0都需要拦截
   */
  private Integer lockForWorld;

  public void addOrderFlags(OrderFlag orderFlag) {
    if (CollUtil.isEmpty(this.orderFlags)) {
      this.orderFlags = new ArrayList<>();
    }
    this.orderFlags.add(orderFlag);
  }


  public String routeKey() {
    return this.organizationCode;
  }

  public String defineId() {
    return this.getThirdPlatformCode() + "-" + this.getOrderNo();
  }

  public EsOrderWorldOrder create() {
    EsOrderWorldOrder esOrderWorldOrder = new EsOrderWorldOrder();
    if (CollUtil.isNotEmpty(this.orderFlags)) {
      List<String> orderFlagList = this.orderFlags.stream().map(OrderFlag::name)
          .collect(Collectors.toList());
      esOrderWorldOrder.fillOrderFlags(orderFlagList);
    }
    esOrderWorldOrder.fillPayTypes(this.getPayTypes());
    esOrderWorldOrder.setId(this.defineId());
    esOrderWorldOrder.setOrderNo(this.getOrderNo());
    esOrderWorldOrder.setParentOrderNo(this.getParentOrderNo());
    esOrderWorldOrder.setThirdOrderNo(this.getThirdOrderNo());
    esOrderWorldOrder.setParentThirdOrderNo(this.getParentThirdOrderNo());
    esOrderWorldOrder.setCreated(this.getCreated());
    esOrderWorldOrder.setCreatedDay(this.getCreatedDay());
    esOrderWorldOrder.setSysCreateTime(this.getSysCreateTime());
    esOrderWorldOrder.setPayTime(this.getPayTime());
    esOrderWorldOrder.setMerCode(this.getMerCode());
    esOrderWorldOrder.setOnlineStoreCode(this.getOnlineStoreCode());
    esOrderWorldOrder.setOrganizationCode(this.getOrganizationCode());
    esOrderWorldOrder.setLaunchOrganizationCode(this.getLaunchOrganizationCode());
    esOrderWorldOrder.setLaunchUserId(this.getLaunchUserId());
    esOrderWorldOrder.setCompanyCode(this.getCompanyCode());
    esOrderWorldOrder.setOrderMainStatus(this.getOrderMainStatus());
    esOrderWorldOrder.setPaymentStatus(this.getPaymentStatus());
    esOrderWorldOrder.setTransChannel(this.getTransChannel());
    esOrderWorldOrder.setThirdPlatformCode(this.getThirdPlatformCode());
    esOrderWorldOrder.setBusinessType(this.getBusinessType());
    esOrderWorldOrder.fillAbnormalType(this.getAbnormalType());
    esOrderWorldOrder.fillOrderTypes(this.getOrderTypes());
    esOrderWorldOrder.setOrderAmount(this.getOrderAmount());
    if (CollUtil.isNotEmpty(this.getDetailList())) {
      List<EsOrderWorldOrderDetail> esOrgOrderDetails = this.getDetailList().stream()
          .map(detail -> {
            EsOrderWorldOrderDetail orderDetail = new EsOrderWorldOrderDetail();
            orderDetail.setOrderDetailNo(detail.getOrderDetailNo());
            orderDetail.setErpCode(detail.getErpCode());
            orderDetail.setErpName(detail.getErpName());
            return orderDetail;
          }).collect(Collectors.toList());
      esOrderWorldOrder.setDetailList(esOrgOrderDetails);
    }
    esOrderWorldOrder.setUserCardNo(this.getUserCardNo());
    esOrderWorldOrder.setUserId(this.getUserId());
    esOrderWorldOrder.setStoreDirectJoinType(this.getStoreDirectJoinType());
    esOrderWorldOrder.setValid(this.getValid());
    esOrderWorldOrder.setLockForWorld(this.getLockForWorld());
    return esOrderWorldOrder;
  }
}
