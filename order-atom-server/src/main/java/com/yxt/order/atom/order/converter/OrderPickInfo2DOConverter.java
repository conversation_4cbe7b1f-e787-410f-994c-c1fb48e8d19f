package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OrderPickInfoDO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface OrderPickInfo2DOConverter {

  OrderPickInfo2DOConverter INSTANCE = Mappers.getMapper(OrderPickInfo2DOConverter.class);

  List<OrderPickInfoDO> toDO(List<OrderPickInfoDTO> obj);

}
