package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("platform_order_amount")
public class PlatformOrderAmountDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 实付金额
   */
  private BigDecimal actualPayAmount;

  /**
   * 应收金额
   */
  private BigDecimal actualCollectAmount;

  /**
   * 交易佣金
   */
  private BigDecimal brokerageAmount;

  /**
   * 商品总金额
   */
  private BigDecimal totalAmount;

  /**
   * 应收配送费
   */
  private BigDecimal deliveryAmount;

  /**
   * 应收包装费
   */
  private BigDecimal packAmount;

  /**
   * 商家包装费
   */
  private BigDecimal merchantPackAmount;

  /**
   * 商家配送费
   */
  private BigDecimal merchantDeliveryAmount;

  /**
   * 商家订单级总优惠
   */
  private BigDecimal merchantOrderDiscountAmount;

  /**
   * 商家配送费优惠
   */
  private BigDecimal merchantDeliveryDiscountAmount;

  /**
   * 商家商品总优惠
   */
  private BigDecimal merchantCommodityDiscountAmount;

  /**
   * 平台配送费
   */
  private BigDecimal platformDeliveryAmount;

  /**
   * 平台配送费优惠
   */
  private BigDecimal platformDeliveryDiscountAmount;

  /**
   * 平台订单级优惠汇总
   */
  private BigDecimal platformOrderDiscountAmount;

  /**
   * 平台商品优惠金额
   */
  private BigDecimal platformCommodityDiscountAmount;

  /**
   * 医保金额
   */
  private BigDecimal medicareAmount;

  /**
   * 剩余交易佣金(实时)
   */
  private BigDecimal remainBrokerageAmount;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

}