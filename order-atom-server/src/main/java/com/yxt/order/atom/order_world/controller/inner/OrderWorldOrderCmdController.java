package com.yxt.order.atom.order_world.controller.inner;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldOrderMapper;
import com.yxt.order.atom.order.mapper.AutoCreateMapper;
import com.yxt.order.types.utils.ShardingHelper;
import java.time.LocalDate;
import javax.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OrderWorldOrderCmdController {


  @Resource
  private EsOrderWorldOrderMapper esOrderWorldOrderMapper;

  @Resource
  private AutoCreateMapper autoCreateMapper;


  /**
   * 创建正单索引
   */
  @PostMapping(ORDER_ENDPOINT + "/es/order-world/order/createIndex")
  public ResponseBase<Boolean> createOrderWorldOrderIndex() {
    return ResponseBase.success(esOrderWorldOrderMapper.createIndex());
  }

  @PostMapping(ORDER_ENDPOINT + "/db/order-world/order/sql/exec")
  @Transactional(rollbackFor = Exception.class)
  public ResponseBase<String> sqlExec(@RequestParam String sql, @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate, @RequestParam(required = false) Integer startIndex, @RequestParam(required = false) Integer endIndex) {
    String actualSql = "";

    if (startIndex != null && endIndex != null) {
      for (int i = startIndex; i < endIndex; i++) {
        actualSql += StrUtil.replace(sql, "${seq}", "_" + i);
        System.out.println(actualSql);
//        autoCreateMapper.createTable(actualSql);
      }
    }
    //格式：2024-02-01
    if (StrUtil.isNotBlank(startDate) && StrUtil.isNotBlank(endDate)) {
      LocalDate tempStartDate = LocalDateTimeUtil.parseDate(startDate, "yyyy-MM-dd");
      LocalDate tempEndDate = LocalDateTimeUtil.parseDate(endDate, "yyyy-MM-dd");
      while (!tempStartDate.isAfter(tempEndDate)) {
        String tableIndex = ShardingHelper.archiveByYearMonth(tempStartDate.atStartOfDay());
        actualSql += StrUtil.replace(sql, "${seq}", "_" + tableIndex);
//        autoCreateMapper.createTable(actualSql);
        System.out.println(actualSql);
        tempStartDate = tempStartDate.plusMonths(1);
      }
    }
    return ResponseBase.success(actualSql);
  }
}
