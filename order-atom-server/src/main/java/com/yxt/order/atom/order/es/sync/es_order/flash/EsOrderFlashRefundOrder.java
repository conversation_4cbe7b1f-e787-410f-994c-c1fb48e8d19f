package com.yxt.order.atom.order.es.sync.es_order.flash;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.SupportChronicDisease;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;
import com.yxt.order.atom.order.es.sync.es_order.handler.RefundOrderCanalHandler;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/20 17:31
 */@Component
public class EsOrderFlashRefundOrder extends
    AbstractFlash<RefundOrderDO, RefundOrder, SupportChronicDisease> {
  @Resource
  private RefundOrderMapper refundOrderMapper;

  @Resource
  private RefundOrderCanalHandler refundOrderCanalHandler;


  @Override
  protected List<RefundOrderDO> getSourceList() {
    LambdaQueryWrapper<RefundOrderDO> query = FlashQueryWrapper.refundOrderFlashQuery(getFlashParam(),defaultLimit());
    return refundOrderMapper.selectList(query);
  }

  @Override
  protected Long queryCursorStartId() {
    return refundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return refundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<RefundOrder> assembleTargetData(List<RefundOrderDO> refundOrderDOS) {
    return refundOrderDOS.stream().map(DoToCanalDtoWrapper::getRefundOrder).collect(Collectors.toList());
  }


  @Override
  protected void flash(List<RefundOrder> refundOrders) {
    CanalRefundOrder canalRefundOrder = new CanalRefundOrder();
    canalRefundOrder.setData(refundOrders);
    refundOrderCanalHandler.manualFlash(canalRefundOrder);
  }
}
