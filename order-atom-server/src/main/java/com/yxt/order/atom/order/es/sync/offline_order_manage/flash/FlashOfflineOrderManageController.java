package com.yxt.order.atom.order.es.sync.offline_order_manage.flash;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.COMMON_BUSINESS_POOL;

import com.yxt.common.logic.flash.FlashParam;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.AbstractFlash.CustomData;
import com.yxt.order.atom.order.es.sync.OfflineOrderManageScene;
import com.yxt.order.atom.order.es.sync.offline_order_manage.flash.req.FlashOfflineOrderManageDataReq;
import com.yxt.starter.controller.AbstractController;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: moatkon
 * @time: 2025/3/31 16:52
 */
@RestController
@Slf4j
public class FlashOfflineOrderManageController extends AbstractController {

  public static final String URL = "/1.0/offline-order-manage/flashDataToEs";

  @Qualifier(COMMON_BUSINESS_POOL)
  @Resource
  private ThreadPoolExecutor commonBusinessPool;

  @Resource
  private List<AbstractFlash<?, ?, OfflineOrderManageScene>> offlineOrderManageSceneFlashList;


  @PostMapping(URL)
  public ResponseBase<Boolean> flashOfflineOrderManageDataReq(
      @RequestBody @Valid FlashOfflineOrderManageDataReq req) {
    List<String> shardingValueList = req.getShardingValueList();
    if (CollectionUtils.isEmpty(shardingValueList)) {
      throw new RuntimeException("线下订单,shardingValueList 不能为空");
    }

    for (AbstractFlash<?, ?, OfflineOrderManageScene> abstractFlash : offlineOrderManageSceneFlashList) {
      CustomData customData = new CustomData();
      customData.setShardingValueList(shardingValueList);

      Date startDate = req.getStartDate();
      Date endDate = req.getEndDate();
      List<String> noList = req.getNoList();

      commonBusinessPool.submit(() -> {
        try {
          log.info("flush task running,{},req:{}", abstractFlash.getClass().getName(),
              JsonUtils.toJson(req));
          FlashParam flashParam = new FlashParam();
          flashParam.setStart(startDate);
          flashParam.setEnd(endDate);
          flashParam.setNoList(noList);
          flashParam.setMonitorKey(req.getMonitorKey());
          // 自定义数据
          abstractFlash.customData(customData);
          abstractFlash.startFlush(flashParam);
          log.info("flush task done,{},req:{}", abstractFlash.getClass().getName(),
              JsonUtils.toJson(flashParam));
        } catch (Exception e) {
          log.error("MemberTransaction flash error", e);
        }
      });
    }

    return generateSuccess(Boolean.TRUE);
  }


}
