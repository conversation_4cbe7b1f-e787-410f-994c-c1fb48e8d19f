package com.yxt.order.atom.order.repository;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.atom.sdk.common.data.AccountCheckPullJobDTO;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckCleanReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckPullJobPageQueryReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckPullJobSaveReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckSaveReqDto;

public interface AccountCheckRepository {

  void saveAccountCheck(AccountCheckSaveReqDto req);

  void saveAccountCheckPullJob(AccountCheckPullJobSaveReqDto req);

  PageDTO<AccountCheckPullJobDTO> queryAccountCheckPullJobPage(AccountCheckPullJobPageQueryReqDto req);

  void cleanAccountCheck(AccountCheckCleanReqDto req);

}
