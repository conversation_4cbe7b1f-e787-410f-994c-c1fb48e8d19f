package com.yxt.order.atom.migration.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Hana迁移配置,做DB配置,不使用Apollo了
 *
 * <AUTHOR> (moatkon)
 * @date 2024年06月06日 17:15
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("hana_migration_error")
public class HanaMigrationErrorDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  private Long hanaMigrationId; // hana_migration表id
  private String targetSchema; // 数据库schema(小写)
  private String conditionJson; // 条件
  private String entryRetryQueue;// 是否进入重试队列 true,false
  private String errorMessage;
  private String errorType;
  private Date createdTime;

  public String getTargetSchema() {
    return targetSchema.toLowerCase();
  }

}
