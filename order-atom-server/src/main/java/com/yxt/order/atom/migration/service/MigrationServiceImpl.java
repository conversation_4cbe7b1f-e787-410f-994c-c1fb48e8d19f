package com.yxt.order.atom.migration.service;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.MIGRATION_THREAD_POOL;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.MigrationEventMessageConsumer;
import com.yxt.order.atom.migration.MigrationHanaController.ReHandleErrorData;
import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import com.yxt.order.atom.migration.dao.HanaMigrationDOMapper;
import com.yxt.order.atom.migration.dao.HanaMigrationErrorDO;
import com.yxt.order.atom.migration.dao.HanaMigrationErrorDOMapper;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.dao.constant.MigrateError;
import com.yxt.order.atom.migration.dao.dto.QueryOrderInfoDto;
import com.yxt.order.atom.migration.exception_type.MigrationIgnoreException;
import com.yxt.order.atom.migration.message.MigrationEventMessage;
import com.yxt.order.atom.migration.message.MigrationEventReHandleMessage;
import com.yxt.order.atom.migration.req.SpecifyMigrationBatchReq;
import com.yxt.order.atom.migration.req.SpecifyMigrationId;
import com.yxt.order.atom.repair.dto.StartEndId;
import com.yxt.order.common.utils.OrderJsonUtils;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月06日 17:12
 * @email: <EMAIL>
 */
@Slf4j
@Service
public class MigrationServiceImpl implements MigrationService {

  @Value("${migrationHanaClose:false}")
  private Boolean migrationHanaClose;

  public static final String MIGRATION_TAG = "TAG_MIGRATION";

  @Resource
  private HanaMigrationMapper hanaMigrationMapper;

  @Resource
  private HanaMigrationDOMapper hanaMigrationDOMapper;

  @Resource
  private HanaMigrationErrorDOMapper hanaMigrationErrorDOMapper;

  @Resource
  private HanaMigrationService hanaMigrationService;

  @Qualifier(MIGRATION_THREAD_POOL)
  @Resource
  private ThreadPoolExecutor migrationThreadPool;

  @Resource
  private RocketMQTemplate template;

  @Resource
  private MigrationEventMessageConsumer migrationEventMessageConsumer;


  @Value("${mq.topic.producer.migrationHanaData}")
  private String migrationHanaDataProducer;

  @Value("${migrationBatchSize:2000}")
  private Long batchSize;

  @Override
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public List<HanaMigrationDO> queryEnableMigrationConfigList() {
    LambdaQueryWrapper<HanaMigrationDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(HanaMigrationDO::getOnOff, "true");
    List<HanaMigrationDO> hanaMigrationDOS = hanaMigrationDOMapper.selectList(queryWrapper);

    // check
    for (HanaMigrationDO hanaMigrationDO : hanaMigrationDOS) {
      if (Objects.isNull(hanaMigrationDO.getCursorId())) {
        throw new RuntimeException(
            "cursorId不能为空,可以配置默认值0:" + OrderJsonUtils.toJson(hanaMigrationDO));
      }

      if (StringUtils.isEmpty(hanaMigrationDO.getMigrationStartTime()) || StringUtils.isEmpty(
          hanaMigrationDO.getMigrationEndTime())) {
        throw new RuntimeException("migrationStart/EndTime不能为空");
      }
    }

    return hanaMigrationDOS;
  }

  @Override
  public Boolean specifyArchiveId(SpecifyMigrationId specifyMigrationId) {
    HanaOrderInfo hanaOrderInfo = hanaMigrationMapper.queryMigrationOrderById(
        specifyMigrationId.getHanaOrderInfoId(), specifyMigrationId.getSchema());
    if (Objects.isNull(hanaOrderInfo)) {
      throw new MigrationIgnoreException("未查到hana数据");
    }

    if (hanaOrderInfo.getMigration() != 0) {
      throw new MigrationIgnoreException("数据已经处理");
    }

    HanaMigrationDO hanaMigrationDO = hanaMigrationService.getHanaMigrationById(
        specifyMigrationId.getHanaMigrationId());

    try {
      migrationEventMessageConsumer.one2one(hanaOrderInfo, specifyMigrationId.getSchema(),
          hanaMigrationDO.taskKey(), Boolean.TRUE, hanaMigrationDO);
    } catch (Exception e) {
      log.info("指定ID处理失败:{},info日志", JsonUtils.toJson(hanaOrderInfo), e);
      hanaMigrationService.processingError(hanaMigrationDO, hanaOrderInfo, e.getMessage(),
          MigrateError.FROM_ARCHIVE_TO_OFFLINE_ORDER);
    }

    return Boolean.TRUE;
  }

  @Override
  public Boolean specifyArchiveBatch(SpecifyMigrationBatchReq specifyMigrationBatchReq) {
    String migrationSort = specifyMigrationBatchReq.getMigrationSort();
    List<Long> hanaOrderInfoIdList = specifyMigrationBatchReq.getHanaOrderInfoIdList();
    String targetSchema = specifyMigrationBatchReq.getTargetSchema();

    // 通过targetSchema和migrationSort查出所有的迁移任务
    LambdaQueryWrapper<HanaMigrationDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(HanaMigrationDO::getMigrateSort, migrationSort);
    queryWrapper.eq(HanaMigrationDO::getTargetSchema, targetSchema);
    queryWrapper.eq(HanaMigrationDO::getOnOff, Boolean.TRUE.toString());
    List<HanaMigrationDO> hanaMigrationDOList = hanaMigrationDOMapper.selectList(queryWrapper);
    if (CollectionUtils.isEmpty(hanaMigrationDOList)) {
      throw new RuntimeException("无有效迁移任务");
    }

    for (HanaMigrationDO hanaMigrationDO : hanaMigrationDOList) {
      for (Long hanaOrderInfoId : hanaOrderInfoIdList) {
        SpecifyMigrationId specifyMigrationId = new SpecifyMigrationId();
        specifyMigrationId.setSchema(hanaMigrationDO.getTargetSchema());
        specifyMigrationId.setHanaOrderInfoId(hanaOrderInfoId);
        specifyMigrationId.setHanaMigrationId(hanaMigrationDO.getId());

        try {
          specifyArchiveId(specifyMigrationId);
        } catch (Exception e) {
          if (!(e instanceof MigrationIgnoreException)) {
            throw new RuntimeException(e);
          }
        }
      }
    }

    return Boolean.TRUE;
  }

  /**
   *
   */
  @Override
  @DS(DATA_SOURCE.MIGRATION_TO_MYSQL)
  public void migration(HanaMigrationDO hanaMigrationDO) {
    migrationThreadPool.submit(() -> {
      String json = JsonUtils.toJson(hanaMigrationDO);
      log.info("迁移hana任务已提交到线程池,{},key:{}", json, hanaMigrationDO.taskKey());
      try {
        task(hanaMigrationDO);
      } catch (Exception e) {
        log.warn("迁移hana任务异常{},{},key:{}", e.getMessage(), json, hanaMigrationDO.taskKey());
      }
    });
  }

  private void task(HanaMigrationDO hanaMigrationDO) {
    StopWatch stopWatch = new StopWatch();
    String taskKey = hanaMigrationDO.taskKey();
    if (!stopWatch.isRunning()) {
      stopWatch.start(String.format("%s 计时器", taskKey));
    }
    log.info("开始同步,{}", JsonUtils.toJson(hanaMigrationDO));
    String schema = hanaMigrationDO.getTargetSchema();
    String migrationStartTime = hanaMigrationDO.getMigrationStartTime();
    String migrationEndTime = hanaMigrationDO.getMigrationEndTime();

    // 查询需要统计的数据
    QueryOrderInfoDto queryOrderInfoDto = new QueryOrderInfoDto();
    queryOrderInfoDto.setSchema(schema.toLowerCase());
    queryOrderInfoDto.setStartTime(migrationStartTime);
    queryOrderInfoDto.setEndTime(migrationEndTime);
    queryOrderInfoDto.setMigrateSort(hanaMigrationDO.getMigrateSort());

    String configJson = hanaMigrationDO.getConfigJson();
    StartEndId startEndId;
    if (!StringUtils.isEmpty(configJson)) {
      startEndId = JsonUtils.toObject(configJson, StartEndId.class);
      log.info("配置:{}", JsonUtils.toJson(startEndId));
    } else {
      startEndId = hanaMigrationMapper.queryUnMigrationMaxMinId(queryOrderInfoDto);
    }
    if (startEndId.empty()) {
      log.info("当前条件总数为0,{}", JsonUtils.toJson(queryOrderInfoDto));
      hanaMigrationDO.setMigrationResult(Boolean.TRUE.toString());
      hanaMigrationDO.setNote("无数据,直接成功");
      hanaMigrationService.refreshHanaMigration(hanaMigrationDO);
      return;
    }
    hanaMigrationDO.setMigrationTotalCount(-1L); // note: 重构为纯Id形式,不再计数
    hanaMigrationDO.setNote("请直接校验DB");
    hanaMigrationDO.setMigrationTotalCountSuccess(0L); // 成功迁移的数量
    hanaMigrationDO.setSendMqCount(0L); // 初始迁移,默认为0
    hanaMigrationService.refreshHanaMigration(hanaMigrationDO);

    while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
      if (migrationHanaClose) {
        log.warn("MigrationEventMessageConsumer 暂停消费,发送端也跳过");
        break;
      }

      try {
        List<HanaOrderInfo> hanaOrderInfoList = hanaMigrationMapper.queryUnMigrationOrder(
            startEndId.getStartId(), startEndId.getStartId() + batchSize, queryOrderInfoDto);

        if (CollectionUtils.isEmpty(hanaOrderInfoList)) {
          refreshStartId(startEndId);
          continue;
        }

        for (HanaOrderInfo hanaOrderInfo : hanaOrderInfoList) {
          if (hanaOrderInfo.getId() > startEndId.getEndId()) {
            continue; // 跳过多余的
          }
          MigrationEventMessage migrationEventMessage = new MigrationEventMessage();
          migrationEventMessage.setHanaOrderInfo(hanaOrderInfo);
          migrationEventMessage.setItemConfig(hanaMigrationDO);
          migrationEventMessage.setTaskKey(taskKey);
          migrationEventMessage.setSchema(schema.toLowerCase());
          try {
            String dataJson = JsonUtils.toJson(migrationEventMessage);
            SendResult sendResult = template.syncSend(
                migrationHanaDataProducer + ":" + MIGRATION_TAG, dataJson, 6000);
            if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
              throw new RuntimeException(
                  String.format("消息发送失败,%s", dataJson)); // 会被捕获,不会终止for loop
            }
          } catch (Exception e) {
            log.warn("发送消息处-订单处理失败:{}", JsonUtils.toJson(migrationEventMessage), e);
            hanaMigrationService.processingError(hanaMigrationDO, hanaOrderInfo, e.getMessage(),
                MigrateError.SEND_MQ_FAILED);
          }
//          hanaMigrationService.updateCursorId(hanaMigrationDO, startEndId.getEndId()); // 直接设置为endId
        }

      } catch (Exception e) {
        log.warn("当前批次数据处理有异常,->{}", e.getMessage(), e);
      }

      // 刷新起始Id
      refreshStartId(startEndId);

    }

    stopWatch.stop();
    log.info("所有页消息已发送到MQ,{},耗时:{}", JsonUtils.toJson(hanaMigrationDO),
        stopWatch.getTotalTimeSeconds());
  }

  private void refreshStartId(StartEndId startEndId) {
    startEndId.setStartId(startEndId.getStartId() + batchSize);
  }


  @Override
  public Boolean reHandleError(ReHandleErrorData reHandleErrorData) {
    Long errorId = reHandleErrorData.getErrorId();
    if (Objects.isNull(errorId)) {
      throw new RuntimeException("errorId can not be null");
    }

    LambdaQueryWrapper<HanaMigrationErrorDO> query = new LambdaQueryWrapper<>();
    query.eq(HanaMigrationErrorDO::getId, errorId);
    HanaMigrationErrorDO hanaMigrationErrorDO = hanaMigrationErrorDOMapper.selectOne(query);
    if (Objects.isNull(hanaMigrationErrorDO)) {
      throw new RuntimeException("hanaMigrationErrorDO can not be null");
    }

    MigrationEventReHandleMessage migrationEventReHandleMessage = new MigrationEventReHandleMessage();
    migrationEventReHandleMessage.setHanaMigrationErrorDO(hanaMigrationErrorDO);

    hanaMigrationService.reHandleMigrationErrorData(migrationEventReHandleMessage);
    return Boolean.TRUE;
  }

}
