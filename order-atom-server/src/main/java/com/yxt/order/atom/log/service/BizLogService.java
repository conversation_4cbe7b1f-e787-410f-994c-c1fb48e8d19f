package com.yxt.order.atom.log.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.order.atom.order.es.doc.EsBizLog;
import com.yxt.order.atom.order.es.mapper.EsBizLogMapper;
import com.yxt.order.atom.sdk.biz_log.req.SaveBizLogReq;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogBatchReq;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogReq;
import com.yxt.order.atom.sdk.common.order_world.BizLogInfoDTO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.springframework.stereotype.Service;

@Service
public class BizLogService {

  @Resource
  private EsBizLogMapper esBizLogMapper;

  public Boolean createLogIndex() {
    return esBizLogMapper.createIndex();
  }

  public void saveLog(SaveBizLogReq req) {
    esBizLogMapper.insertBatch(BeanUtil.copyToList(req.getBizLogList(), EsBizLog.class));
  }


  public List<BizLogInfoDTO> searchLog(SearchBizLogReq req) {
    LambdaEsQueryWrapper<EsBizLog> wrapper = EsWrappers.lambdaQuery(EsBizLog.class);
    wrapper.eq(EsBizLog::getBizNo, req.getBizNo());
    wrapper.orderByDesc(EsBizLog::getOperateTimeStamp);
    List<EsBizLog> esBizLogs = esBizLogMapper.selectList(wrapper);
    if(CollUtil.isEmpty(esBizLogs)){
      return new ArrayList<>(0);
    }
    return BeanUtil.copyToList(esBizLogs, BizLogInfoDTO.class);
  }

  public List<BizLogInfoDTO> searchLogBatch(SearchBizLogBatchReq req) {
    LambdaEsQueryWrapper<EsBizLog> wrapper = EsWrappers.lambdaQuery(EsBizLog.class);
    wrapper.in(EsBizLog::getBizNo, req.getBizNoList());
    wrapper.eq(StrUtil.isNotBlank(req.getBizAction()), EsBizLog::getBizAction, req.getBizAction());
    wrapper.orderByDesc(EsBizLog::getOperateTimeStamp);
    List<EsBizLog> esBizLogs = esBizLogMapper.selectList(wrapper);
    if(CollUtil.isEmpty(esBizLogs)){
      return new ArrayList<>(0);
    }
    return BeanUtil.copyToList(esBizLogs, BizLogInfoDTO.class);
  }

}
