package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.order.entity.OfflineOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineOrderPayDO;
import com.yxt.order.atom.order.entity.OfflineOrderPrescriptionDO;
import com.yxt.order.atom.order.entity.OfflineOrderUserDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderPayDO;
import com.yxt.order.atom.order.mapper.OfflineOrderCashierDeskMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailPickMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMedInsSettleMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderOrganizationMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderPayMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderPrescriptionMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderUserMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderCashierDeskMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderDetailMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMedInsSettleMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderOrganizationMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderPayMapper;
import com.yxt.order.atom.order.repository.OfflineOrderSearchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.stereotype.Repository;

@Repository
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class OfflineOrderSearchRepositoryImpl implements OfflineOrderSearchRepository {

  @Resource
  private OfflineOrderOrganizationMapper offlineOrderOrganizationMapper;
  @Resource
  private OfflineOrderCashierDeskMapper offlineOrderCashierDeskMapper;
  @Resource
  private OfflineOrderUserMapper offlineOrderUserMapper;
  @Resource
  private OfflineOrderPrescriptionMapper offlineOrderPrescriptionMapper;
  @Resource
  private OfflineOrderPayMapper offlineOrderPayMapper;
  @Resource
  private OfflineOrderDetailMapper offlineOrderDetailMapper;
  @Resource
  private OfflineOrderDetailPickMapper offlineOrderDetailPickMapper;
  @Resource
  private OfflineOrderMedInsSettleMapper offlineOrderMedInsSettleMapper;
  @Resource
  private OfflineRefundOrderOrganizationMapper offlineRefundOrderOrganizationMapper;
  @Resource
  private OfflineRefundOrderCashierDeskMapper offlineRefundOrderCashierDeskMapper;
  @Resource
  private OfflineRefundOrderDetailMapper offlineRefundOrderDetailMapper;
  @Resource
  private OfflineRefundOrderPayMapper offlineRefundOrderPayMapper;
  @Resource
  private OfflineRefundOrderMedInsSettleMapper offlineRefundOrderMedInsSettleMapper;


  @Override
  public OfflineOrderOrganizationDO getOfflineOrderOrganization(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderOrganizationMapper.selectOne(new LambdaQueryWrapper<OfflineOrderOrganizationDO>().eq(OfflineOrderOrganizationDO::getOrderNo, orderNo));
    }
  }

  @Override
  public OfflineOrderCashierDeskDO getOfflineOrderCashierDesk(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderCashierDeskMapper.selectOne(new LambdaQueryWrapper<OfflineOrderCashierDeskDO>().eq(OfflineOrderCashierDeskDO::getOrderNo, orderNo));
    }
  }

  @Override
  public OfflineOrderUserDO getOfflineOrderUser(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderUserMapper.selectOne(new LambdaQueryWrapper<OfflineOrderUserDO>().eq(OfflineOrderUserDO::getOrderNo, orderNo));
    }
  }

  @Override
  public List<OfflineOrderDetailDO> getOfflineOrderDetail(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderDetailMapper.selectList(new LambdaQueryWrapper<OfflineOrderDetailDO>().eq(OfflineOrderDetailDO::getOrderNo, orderNo));
    }
  }

  @Override
  public List<OfflineOrderDetailPickDO> getOfflineOrderDetailPick(String orderNo, List<String> orderDetailNoList) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderDetailPickMapper.selectList(new LambdaQueryWrapper<OfflineOrderDetailPickDO>()
          .eq(OfflineOrderDetailPickDO::getOrderNo, orderNo)
          .in(CollUtil.isNotEmpty(orderDetailNoList), OfflineOrderDetailPickDO::getOrderDetailNo, orderDetailNoList)
      );
    }
  }

  @Override
  public OfflineOrderPrescriptionDO getOfflineOrderPrescription(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderPrescriptionMapper.selectOne(new LambdaQueryWrapper<OfflineOrderPrescriptionDO>().eq(OfflineOrderPrescriptionDO::getOrderNo, orderNo));
    }
  }

  @Override
  public List<OfflineOrderPayDO> getOfflineOrderPay(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderPayMapper.selectList(new LambdaQueryWrapper<OfflineOrderPayDO>().eq(OfflineOrderPayDO::getOrderNo, orderNo));
    }
  }

  @Override
  public OfflineOrderMedInsSettleDO getOfflineOrderMedInsSettle(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderMedInsSettleMapper.selectOne(new LambdaQueryWrapper<OfflineOrderMedInsSettleDO>().eq(OfflineOrderMedInsSettleDO::getOrderNo, orderNo));
    }
  }

  @Override
  public OfflineRefundOrderOrganizationDO getOfflineRefundOrderOrganization(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineRefundOrderOrganizationMapper.selectOne(new LambdaQueryWrapper<OfflineRefundOrderOrganizationDO>().eq(OfflineRefundOrderOrganizationDO::getRefundNo, refundNo));
    }
  }

  @Override
  public OfflineRefundOrderCashierDeskDO getOfflineRefundOrderCashierDesk(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineRefundOrderCashierDeskMapper.selectOne(new LambdaQueryWrapper<OfflineRefundOrderCashierDeskDO>().eq(OfflineRefundOrderCashierDeskDO::getRefundNo, refundNo));
    }
  }

  @Override
  public List<OfflineRefundOrderDetailDO> getOfflineRefundOrderDetail(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineRefundOrderDetailMapper.selectList(new LambdaQueryWrapper<OfflineRefundOrderDetailDO>().eq(OfflineRefundOrderDetailDO::getRefundNo, refundNo));
    }
  }

  @Override
  public OfflineRefundOrderMedInsSettleDO getOfflineRefundOrderMedInsSettle(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineRefundOrderMedInsSettleMapper.selectOne(new LambdaQueryWrapper<OfflineRefundOrderMedInsSettleDO>().eq(OfflineRefundOrderMedInsSettleDO::getRefundNo, refundNo));
    }
  }

  @Override
  public List<OfflineRefundOrderPayDO> getOfflineRefundOrderPay(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineRefundOrderPayMapper.selectList(new LambdaQueryWrapper<OfflineRefundOrderPayDO>().eq(OfflineRefundOrderPayDO::getRefundNo, refundNo));
    }
  }
}
