package com.yxt.order.atom.order.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.converter.ErpBillInfo2DtoConverter;
import com.yxt.order.atom.order.converter.OmsOrderInfo2DtoConverter;
import com.yxt.order.atom.order.converter.OrderDetailConverter;
import com.yxt.order.atom.order.converter.OrderInfoConverter;
import com.yxt.order.atom.order.converter.OrderPayInfo2DtoConverter;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.OrderPayInfoDO;
import com.yxt.order.atom.order.entity.OrderPickInfoDO;
import com.yxt.order.atom.order.mapper.ErpBillInfoMapper;
import com.yxt.order.atom.order.mapper.OmsOrderInfoMapper;
import com.yxt.order.atom.order.mapper.OrderPayInfoMapper;
import com.yxt.order.atom.order.mapper.OrderPickInfoMapper;
import com.yxt.order.atom.order.repository.OmsOrderRepository;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.OmsOrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.OmsOrderInfoResDto.OmsOrderOtherInfo;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.SimpleOmsOrderInfoDTO;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.req.OmsOrderInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderDetailInfo;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryReqDto;
import com.yxt.order.common.base_order_dto.ErpBillInfo;
import com.yxt.order.common.base_order_dto.OrderPayInfo;
import com.yxt.order.types.order.OmsOrderNo;
import com.yxt.order.types.order.OrderNo;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年02月28日 18:22
 * @email: <EMAIL>
 */
@Repository
@Slf4j
public class OmsOrderRepositoryImpl implements OmsOrderRepository {


  @Resource
  private OmsOrderInfoMapper omsOrderInfoMapper;
  @Resource
  private OrderPickInfoMapper orderPickInfoMapper;

  @Resource
  private ErpBillInfoMapper erpBillInfoMapper;
  @Resource
  private OrderPayInfoMapper orderPayInfoMapper;

  @Override
  public List<SimpleOmsOrderInfoDTO> querySimpleOmsOrderList(OrderNo orderNo) {

    LambdaQueryWrapper<OmsOrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OmsOrderInfoDO::getOrderNo, orderNo.getOrderNo());
    List<OmsOrderInfoDO> omsOrderInfoDOList = omsOrderInfoMapper.selectList(queryWrapper);
    return omsOrderInfoDOList.stream().map(OmsOrderInfo2DtoConverter.INSTANCE::toDto).collect(
        Collectors.toList());
  }

  @Override
  public OmsOrderInfoResDto getOmsOrderInfo(OmsOrderInfoQryReqDto qryReqDto) {
    LambdaQueryWrapper<OmsOrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OmsOrderInfoDO::getOmsOrderNo, qryReqDto.getOmsOrderNo().getOmsOrderNo());
    OmsOrderInfoDO orderInfoDO = omsOrderInfoMapper.selectOne(queryWrapper);
    SimpleOmsOrderInfoDTO dto = OmsOrderInfo2DtoConverter.INSTANCE.toDto(orderInfoDO);
    OmsOrderInfoResDto omsOrderInfo = new OmsOrderInfoResDto(dto );
    if (qryReqDto.getQryScale().equals(OmsOrderInfoQryReqDto.OrderQryScaleEnum.MAIN)) {
      return omsOrderInfo;
    }  else  {
      LambdaQueryWrapper<ErpBillInfoDO> bindingWrapper =  new LambdaQueryWrapper<>();
      bindingWrapper.eq(ErpBillInfoDO::getOmsOrderNo, orderInfoDO.getOmsOrderNo());
      ErpBillInfoDO erpBillInfoDO = erpBillInfoMapper.selectOne(bindingWrapper);
      ErpBillInfo erpBillInfo =  ErpBillInfo2DtoConverter.INSTANCE.toDto(erpBillInfoDO);


      LambdaQueryWrapper<OrderPayInfoDO> payInfoWrapper =  new LambdaQueryWrapper<>();
      payInfoWrapper.eq(OrderPayInfoDO::getOmsOrderNo, orderInfoDO.getOmsOrderNo());
      OrderPayInfoDO orderPayInfoDO =  orderPayInfoMapper.selectOne(payInfoWrapper);
      OrderPayInfo orderPayInfo =  OrderPayInfo2DtoConverter.INSTANCE.toDto(orderPayInfoDO);




      omsOrderInfo.setOmsOrderOtherInfo(new OmsOrderOtherInfo(erpBillInfo,orderPayInfo));
    }

    return omsOrderInfo;




  }
}
