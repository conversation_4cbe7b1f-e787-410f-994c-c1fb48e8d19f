package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.mapper.ErpBillInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class ErpBillInfoDelete extends AbstractDelete {

  @Resource
  private ErpBillInfoMapper erpBillInfoMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getErpBillInfo();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<ErpBillInfoDO> query = new LambdaQueryWrapper<>();
    query.eq(ErpBillInfoDO::getOrderNo, orderNo);
    return erpBillInfoMapper.delete(query);
  }
}
