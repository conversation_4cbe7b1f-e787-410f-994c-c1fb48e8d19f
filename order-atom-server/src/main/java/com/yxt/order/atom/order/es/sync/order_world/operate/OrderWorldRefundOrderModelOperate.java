package com.yxt.order.atom.order.es.sync.order_world.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrder;
import com.yxt.order.atom.order.es.doc.EsOrderWorldRefundOrder;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldRefundOrderMapper;
import com.yxt.order.atom.order.es.sync.order_world.model.EsOrderWorldOrderModel;
import com.yxt.order.atom.order.es.sync.order_world.model.EsOrderWorldRefundOrderModel;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrderWorldRefundOrderModelOperate extends AbstractEsOperate<EsOrderWorldRefundOrderModel> {

  @Resource
  private EsOrderWorldRefundOrderMapper esOrderWorldRefundOrderMapper;

  public OrderWorldRefundOrderModelOperate() {
    super(EsOrderWorldRefundOrderModel.class);
  }

  @Override
  protected Boolean exec() {
    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;
  }

  private Boolean save(EsOrderWorldRefundOrderModel model) {

    EsOrderWorldRefundOrder esOrder = model.create();

    LambdaEsQueryWrapper<EsOrderWorldRefundOrder> wrapper = new LambdaEsQueryWrapper<>();
    wrapper.eq(EsOrderWorldRefundOrder::getId, model.defineId());
    //先添加路由查找,如果能查找到，直接更新
    wrapper.routing(model.routeKey());
    Long count = esOrderWorldRefundOrderMapper.selectCount(wrapper);
    if (count > 0) {
      esOrderWorldRefundOrderMapper.updateById(model.routeKey(), esOrder);
      return true;
    }
    //重新插入
    esOrderWorldRefundOrderMapper.insert(model.routeKey(), esOrder);
    return true;
  }

  private Boolean delete(EsOrderWorldRefundOrderModel model) {
    return esOrderWorldRefundOrderMapper.deleteById(model.routeKey(), model.defineId()) > 0;
  }
}
