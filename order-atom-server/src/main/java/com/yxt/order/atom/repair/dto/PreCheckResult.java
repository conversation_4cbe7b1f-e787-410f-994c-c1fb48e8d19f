package com.yxt.order.atom.repair.dto;

import com.yxt.order.types.offline.enums.PreCheckEnum;
import lombok.Data;

/**
 * 预检结果
 *
 * @author: moatkon
 * @time: 2025/1/23 14:38
 */
@Data
public class PreCheckResult {

  // 预检结果
  private PreCheckEnum preCheckResult;

  private String businessNo; // 业务单号

  // 检查笔记,记录检查的
  private String preCheckFailed;

  // 检查通过则记录变更前快照
  private String beforeImage;

  /**
   * 是否预检通过
   * @return
   */
  public Boolean checkIsPassed() {
    return PreCheckEnum.PASS.equals(this.preCheckResult);
  }

  public PreCheckResult passAndRecordBeforeImage(String beforeImage,String businessNo){
    this.preCheckResult = PreCheckEnum.PASS;
    this.beforeImage = beforeImage;
    this.businessNo = businessNo;
    return this;
  }

  public static PreCheckResult create(){
    return new PreCheckResult();
  }


  public PreCheckResult failed(String failedReason){
    this.preCheckResult = PreCheckEnum.FAILED;
    this.preCheckFailed = failedReason;
    return this;
  }



}
