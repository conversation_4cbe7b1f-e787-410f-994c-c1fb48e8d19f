package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderAssembleCommodityRelationDO;
import com.yxt.order.atom.order.mapper.OrderAssembleCommodityRelationMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2024/11/18 18:51
 */
@Component
public class OrderAssembleCommodityRelationDelete extends AbstractDelete {

  @Resource
  private OrderAssembleCommodityRelationMapper orderAssembleCommodityRelationMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getOrderAssembleCommodityRelation()
        && !StringUtils.isEmpty(dto.getThirdOrderNo())
        && !StringUtils.isEmpty(dto.getPlatformCode());
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<OrderAssembleCommodityRelationDO> query = new LambdaQueryWrapper<>();
    query.eq(OrderAssembleCommodityRelationDO::getThirdOrderNo,dto.getThirdOrderNo());
    query.eq(OrderAssembleCommodityRelationDO::getEctype,dto.getPlatformCode());
    return orderAssembleCommodityRelationMapper.delete(query);
  }
}
