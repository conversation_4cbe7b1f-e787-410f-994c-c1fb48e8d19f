package com.yxt.order.atom.migration.fix.component.mistake_platform_code.removeUnHandledEsData;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.fix.FixOfflineRefundOrderMistakeSceneUnhandleEs;
import com.yxt.order.atom.migration.fix.component.es.EsDataOperateComponent;
import com.yxt.order.atom.migration.fix.dto.RepeatedEsDataOperate;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 修复科传的单子,平台标识缺标识为海典 原因: 是因为之前的平台标识取值是根据 inner_store_dictionary 来获取导致的
 *
 * @author: moatkon
 * @time: 2024/12/13 10:21
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class FixOfflineRefundOrderMistakePlatformDataUnhandleEs extends
    AbstractFlash<OfflineRefundOrderDO, OfflineRefundOrderDO, FixOfflineRefundOrderMistakeSceneUnhandleEs> {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private EsDataOperateComponent esDataOperateComponent;

  @Override
  protected Long queryCursorStartId() {
    CustomData customData = getCustomData();
    Long startId = customData.getStartId();
    return Objects.nonNull(startId) ? startId
        : offlineRefundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    CustomData customData = getCustomData();
    Long endId = customData.getEndId();
    return Objects.nonNull(endId) ? endId : offlineRefundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(
        getFlashParam(), defaultLimit());
    return offlineRefundOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrderDO> assembleTargetData(
      List<OfflineRefundOrderDO> offlineOrderDOList) {
    return offlineOrderDOList;
  }

  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param offlineRefundOrderDOList
   */
  @Override
  protected void flash(List<OfflineRefundOrderDO> offlineRefundOrderDOList) {
    for (OfflineRefundOrderDO offlineRefundOrderDO : offlineRefundOrderDOList) {
      handle(offlineRefundOrderDO);
    }

  }

  private void handle(OfflineRefundOrderDO offlineRefundOrderDO) {
    String migration = offlineRefundOrderDO.getMigration();
    if (StringUtils.isEmpty(migration)) {
      return;
    }
    if (!Boolean.TRUE.toString().equals(migration)) {
      return;
    }

    String refundNo = offlineRefundOrderDO.getRefundNo();
    if (StringUtils.isEmpty(refundNo)) {
      return;
    }

    String thirdRefundNo = offlineRefundOrderDO.getThirdRefundNo();
    if (StringUtils.isEmpty(thirdRefundNo)) {
      return;
    }

    // 校验平台编码是否有效
    String thirdPlatformCode = offlineRefundOrderDO.getThirdPlatformCode();
    if (!ThirdPlatformCodeEnum.isValid(thirdPlatformCode)) {
      return;
    }

    // 之前的纠正逻辑: 三方单号不是16位,但是平台却是海典的,需要将平台变更为科传
    // 所以只需要处理科传平台的,如果不是科传的,直接跳过
    if (!ThirdPlatformCodeEnum.KE_CHUAN.name().equals(thirdPlatformCode)) {
      return;
    }

    // 错误未删除的是海典的,故在此设置为海典,以进行删除。找不到则删除影响行数为0,故也无影响
    offlineRefundOrderDO.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());

    RepeatedEsDataOperate repeatedEsDataOperate = new RepeatedEsDataOperate(offlineRefundOrderDO);
    esDataOperateComponent.removeEsEsUnhandled(repeatedEsDataOperate);

  }


  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}