package com.yxt.order.atom.common.utils;

import cn.hutool.extra.spring.SpringUtil;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * <AUTHOR>
 * @date 2021/12/08
 */
@Slf4j
public class RedisStringUtil {


  private static StringRedisTemplate redisTemplate;

  private static void getInstance() {
    if (null == redisTemplate) {
      redisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
    }
  }

  /**
   * @Description: 设置值
   * @Param: [key, value]
   * @return: void
   * @Author: syuson
   * @Date: 2021-12-8
   */
  public static void setValue(String key, String value) {
    getInstance();
    try {
      redisTemplate.opsForValue().set(key, value, 30, TimeUnit.DAYS);
    } catch (Exception e) {
      log.error("setValue key fail, key:[{}] value:[{}],cause:{}", key, value, e);
    }
  }


  public static boolean setValue(String key, String value, Long ttl, TimeUnit timeUnit) {
    getInstance();
    try {
      redisTemplate.opsForValue().set(key, value, ttl, timeUnit);
      return true;
    } catch (Exception e) {
      log.error("setValue key fail, key:[{}] value:[{}],cause:{}", key, value, e);
    }
    return false;
  }

  /**
   * @Description: 获取值
   * @Param: [key]
   * @return: java.lang.String
   * @Author: syuson
   * @Date: 2021-12-8
   */
  public static String getValue(String key) {
    getInstance();
    try {
      return redisTemplate.opsForValue().get(key);
    } catch (Exception e) {
      log.error("getValue key fail, key:[{}] ,cause:{}", key, e);
    }
    return "";
  }

  /**
   * @Description: 删除key
   * @Param: [key]
   * @return: void
   * @Author: syuson
   * @Date: 2021-12-8
   */
  public static void deleteKey(String key) {
    getInstance();
    try {
      redisTemplate.opsForValue().getOperations().delete(key);
    } catch (Exception e) {
      log.error("deleteKey key fail, key:[{}] ,cause:{}", key, e);
    }
  }


  /**
   * @Description: 查看key的剩余时间
   * @Param: [key]
   * @return: Long
   * @Author: yanghua
   * @Date: 2023-10-27
   */
  public static Long checkKey(String key) {
    getInstance();
    try {
      return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    } catch (Exception e) {
      log.error("checkKey key fail, key:[{}],cause", key, e);
    }
    return 0L;
  }


  public static void incr(String key) {
    getInstance();
    try {
      redisTemplate.opsForValue().increment(key);
    } catch (Exception e) {
      log.error("incr key fail, key:[{}],cause", key, e);
    }
  }

  public static String getIncrValue(String key) {
    getInstance();
    try {
      return redisTemplate.opsForValue().get(key);
    } catch (Exception e) {
      log.error("get incr key fail, key:[{}],cause", key, e);
    }
    return null;
  }
}
