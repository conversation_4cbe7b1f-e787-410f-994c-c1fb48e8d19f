package com.yxt.order.atom.order.repository.batch;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.OrgOrderFlashToEsJobDO;
import com.yxt.order.atom.order.mapper.OrgOrderFlashToEsJobMapper;
import com.yxt.order.types.DsConstants;
import org.springframework.stereotype.Repository;

@Repository
@DS(DsConstants.DB_ORDER_MASTER)
public class OrgOrderFlashToEsJobBatchRepository extends ServiceImpl<OrgOrderFlashToEsJobMapper, OrgOrderFlashToEsJobDO> {

}
