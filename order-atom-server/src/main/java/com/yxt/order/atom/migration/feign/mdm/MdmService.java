package com.yxt.order.atom.migration.feign.mdm;

import static com.yxt.order.types.DsConstants.MER_CODE_YXT;

import com.google.common.collect.Lists;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.migration.dao.HanaStore;
import com.yxt.order.common.Utils;
import com.yxt.order.types.offline.enums.StoreDirectJoinTypeEnum;
import com.yxt.org.read.opensdk.emp.dto.request.EmployeeGetReqDTO;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeSimpleResDTO;
import com.yxt.org.read.opensdk.emp.service.EmpQueryOpenApi;
import com.yxt.org.read.opensdk.org.dto.request.OrganizationConditionOpenReqDTO;
import com.yxt.org.read.opensdk.org.dto.response.OrgInfoQueryOpenResDTO;
import com.yxt.org.read.opensdk.org.service.OrgQueryOpenApi;
import com.yxt.org.write.common.constants.OrClassEnum;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import joptsimple.internal.Strings;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/3/3 15:13
 */
@Service
public class MdmService {

  @Resource
  private EmpQueryOpenApi empQueryOpenApi;

  @Resource
  private OrgQueryOpenApi orgQueryOpenApi;


  public String queryByEmpId(String empCode) {
    EmployeeGetReqDTO employeeGetReqDTO = new EmployeeGetReqDTO();
    employeeGetReqDTO.setMerCode(MER_CODE_YXT);
    employeeGetReqDTO.setEmpCode(empCode);
    ResponseBase<EmployeeSimpleResDTO> employeeSimple = empQueryOpenApi.getEmployeeSimple(
        employeeGetReqDTO);
    Utils.checkRespSuccess(employeeSimple);
    EmployeeSimpleResDTO data = employeeSimple.getData();
    if(Objects.isNull(data)){
      return Strings.EMPTY;
    }
    return data.getEmpName();
  }


  public HanaStore queryOrgInfo(String orgCode) {
    List<OrgInfoQueryOpenResDTO> list = commonQueryInfo(
        orgCode);
    if (CollectionUtils.isEmpty(list)) {
      return null;
    }

    OrgInfoQueryOpenResDTO res = list.get(0);

    HanaStore hanaStore = new HanaStore();
    hanaStore.setStoreCode(res.getOrCode());
    hanaStore.setStoreName(res.getOrName());
    hanaStore.setCompanyCode(res.getAffiliationCompany());
    hanaStore.setCompanyName(fetchCompanyName(res.getAffiliationCompany())); // 公司名称: affiliationCompany传入orgCodeList查公司名orName
    if(Objects.equals(res.getOrClass(), OrClassEnum.DIRECT_STORE.value())){
      hanaStore.setStoreDirectJoinType(StoreDirectJoinTypeEnum.DIRECT_SALES.name());
    }else if(Objects.equals(res.getOrClass(), OrClassEnum.FRANCHISE_STORE.value())){
      hanaStore.setStoreDirectJoinType(StoreDirectJoinTypeEnum.JOIN.name());
    }else {
//      hanaStore.setStoreDirectJoinType("UNKNOW");
      hanaStore.setStoreDirectJoinType(StoreDirectJoinTypeEnum.UNKNOWN.name());
    }
    return hanaStore;
  }

  private String fetchCompanyName(String orgCode) {
    List<OrgInfoQueryOpenResDTO> list = commonQueryInfo(orgCode);
    if (CollectionUtils.isEmpty(list)) {
      return null;
    }
    return list.get(0).getOrName();
  }

  @Nullable
  private List<OrgInfoQueryOpenResDTO> commonQueryInfo(String orgCode) {
    OrganizationConditionOpenReqDTO reqDTO = new OrganizationConditionOpenReqDTO();
    reqDTO.setMerCode(MER_CODE_YXT);
    reqDTO.setOrgCodeList(Lists.newArrayList(orgCode));
    ResponseBase<List<OrgInfoQueryOpenResDTO>> res = orgQueryOpenApi.listOrgByCondition(reqDTO);
    Utils.checkRespSuccess(res);
    List<OrgInfoQueryOpenResDTO> list = res.getData();
    if (CollectionUtils.isEmpty(list)) {
      return null;
    }
    return list;
  }


}
