package com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.consistency.AbstractConsistencyCheckEfficientCount;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.member_transaction.handler.MemberRefundOrderTransactionHandler;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/25 15:30
 */
@Component
public class EsMemberRefundOrderEfficientCount extends
    AbstractConsistencyCheckEfficientCount<RefundOrderDO> {

  @Resource
  private RefundOrderMapper refundOrderMapper;

  @Resource
  private MemberRefundOrderTransactionHandler memberRefundOrderTransactionHandler;

  @Override
  protected Long queryCursorStartId() {
    return refundOrderMapper.selectEfficientCountMinId(getParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return refundOrderMapper.selectEfficientCountMaxId(getParam());
  }

  @Override
  protected List<RefundOrderDO> dataList() {
    LambdaQueryWrapper<RefundOrderDO> query = new LambdaQueryWrapper<>();
    query.ge(RefundOrderDO::getId, getParam().getCursorStartId());
    query.lt(RefundOrderDO::getId, getParam().currentCursorEndId(defaultLimit()));
    return refundOrderMapper.selectList(query);
  }


  /**
   * @param list
   * @return
   */
  @Override
  protected Long efficientCount(List<RefundOrderDO> list, Long maximumId) {
    return list.stream().filter(s -> s.getId() <= maximumId)
        .filter(s -> OrderDateUtils.isEfficientDate(getParam().getStartDate(), getParam().getEndDate(), s.getCreateTime()))
        .map(DoToCanalDtoWrapper::getRefundOrder)
        .filter(refundOrder -> memberRefundOrderTransactionHandler.efficientData(refundOrder))
        .count();
  }
}
