package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderPrescriptionDO;
import com.yxt.order.atom.order.mapper.OrderPrescriptionMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class OrderPrescriptionDelete extends AbstractDelete {

  @Resource
  private OrderPrescriptionMapper orderPrescriptionMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getOrderPrescription();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<OrderPrescriptionDO> query = new LambdaQueryWrapper<>();
    query.eq(OrderPrescriptionDO::getOrderNo, orderNo);
    return orderPrescriptionMapper.delete(query);
  }
}
