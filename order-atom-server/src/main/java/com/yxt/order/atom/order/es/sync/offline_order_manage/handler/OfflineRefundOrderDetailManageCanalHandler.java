package com.yxt.order.atom.order.es.sync.offline_order_manage.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_REFUND_ORDER_DETAIL_REGEX;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrderDetail;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrderDetail.OfflineRefundOrderDetail;
import com.yxt.order.atom.order.es.sync.offline_order_manage.flash.OfflineRefundOrderManageFlash;
import com.yxt.order.atom.order.es.sync.offline_order_manage.model.OfflineRefundOrderManageModel;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月20日 14:21
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class OfflineRefundOrderDetailManageCanalHandler extends
    AbstractCanalHandler<CanalOfflineRefundOrderDetail, OfflineRefundOrderManageModel> {
  @Resource
  private OfflineRefundOrderManageFlash offlineRefundOrderManageFlash;

  public OfflineRefundOrderDetailManageCanalHandler() {
    super(CanalOfflineRefundOrderDetail.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD_OFFLINE.equals(database) && Table.tableRegex(
        OFFLINE_REFUND_ORDER_DETAIL_REGEX, table);
  }

  @Override
  protected List<OfflineRefundOrderManageModel> assemble() {

    for (OfflineRefundOrderDetail offlineRefundOrderDetail : getData().getData()) {
      if (StringUtils.isEmpty(offlineRefundOrderDetail.getRefundNo())) {
        continue;
      }

      FlashParam param =new FlashParam();
      param.setNoList(Lists.newArrayList(offlineRefundOrderDetail.getRefundNo()));
      offlineRefundOrderManageFlash.startFlush(param);
    }

    return Lists.newArrayList();
  }
}
