package com.yxt.order.atom.order.es.sync.b2c_account_info;

import static com.yxt.order.atom.common.configration.KafkaConfig.CANAL_MESSAGE_GROUP_ID_FOR_B2C_ACCOUNT;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.lang.util.JsonUtils;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2024/11/4 17:33
 */
@Slf4j
@Component
public class EsB2cAccountInfoConsumer {

  @Resource
  private List<AbstractCanalHandler<? extends BaseCanalData<?>, ? extends EsB2cAccountInfoModel>> abstractCanalHandlerList;

  @KafkaListener(topics = {
      "${canal.account-order}"}, groupId = CANAL_MESSAGE_GROUP_ID_FOR_B2C_ACCOUNT, containerFactory = "canalKafkaListenerFactory")
  public void esB2cAccountInfoConsumer(List<ConsumerRecord<String, String>> consumerRecordList,
      Acknowledgment ack) {
    try {

      if (CollectionUtils.isEmpty(consumerRecordList)) {
        return;
      }

      List<String> messageList = consumerRecordList.stream()
          .filter(s -> !StringUtils.isEmpty(s.value()))
          .filter(s -> !JsonUtils.toObject(s.value(), new TypeReference<BaseCanalData<?>>() {}).getIsDdl())
          .map(ConsumerRecord::value).collect(Collectors.toList());

      if (CollectionUtils.isEmpty(messageList)) {
        return;
      }

      for (String message : messageList) {
        try {
          for (AbstractCanalHandler<? extends BaseCanalData<?>, ? extends BaseEsIndexModel> abstractCommonCanalHandler : abstractCanalHandlerList) {
            if (abstractCommonCanalHandler.execBusiness(message)) {
              break;
            }
          }
        } catch (Exception e) {
          log.warn("下账单条件索引,同步到ES异常:{},message: {}",e.getMessage(), message,e);
        }
      }

      ack.acknowledge();
    } catch (Exception e) {
      log.error("EsB2cAccountInfoConsumer consumer error,cause:{},data:{}", e.getMessage(),
          consumerRecordList, e);
    }
  }


}
