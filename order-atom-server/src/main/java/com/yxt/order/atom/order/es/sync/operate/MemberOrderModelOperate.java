package com.yxt.order.atom.order.es.sync.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.doc.EsMemberOrder;
import com.yxt.order.atom.order.es.mapper.EsMemberOrderMapper;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberOrderModel;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/10 15:03
 */
@Component
@Slf4j
public class MemberOrderModelOperate extends AbstractEsOperate<MemberOrderModel> {

  @Resource
  private EsMemberOrderMapper esMemberOrderMapper;

  public MemberOrderModelOperate() {
    super(MemberOrderModel.class);
  }

  @Override
  protected Boolean exec() {

    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;
  }


  private Boolean delete(MemberOrderModel memberOrderModel) {
    return esMemberOrderMapper.deleteById(memberOrderModel.routeKey(),memberOrderModel.defineId()) > 0;
  }

  private Boolean save(MemberOrderModel memberOrderModel) {
    // 适配逻辑删除
    if (Objects.nonNull(memberOrderModel.getDeleted())
        && memberOrderModel.getDeleted() != 0L) {// 不为0,表示删除
      return delete(memberOrderModel);
    }

    EsMemberOrder esMemberOrder = memberOrderModel.create();

    LambdaEsQueryWrapper<EsMemberOrder> memberOrderQuery = new LambdaEsQueryWrapper<>();
    memberOrderQuery.eq(EsMemberOrder::getId, memberOrderModel.defineId());
    memberOrderQuery.eq(EsMemberOrder::getDeleted, 0L);
    memberOrderQuery.routing(memberOrderModel.routeKey());
    Long count = esMemberOrderMapper.selectCount(memberOrderQuery);
    if (count > 0) {
      boolean update = esMemberOrderMapper.updateById(memberOrderModel.routeKey(),esMemberOrder) > 0;
      if (!update) {
        log.warn("更新索引数据失败,{}", JsonUtils.toJson(esMemberOrder));
      }
      return update;

    } else {
      boolean create = esMemberOrderMapper.insert(memberOrderModel.routeKey(),esMemberOrder) > 0;
      if (!create) {
        log.warn("创建索引数据失败,{}", JsonUtils.toJson(esMemberOrder));
      }
      return create;
    }
  }
}
