package com.yxt.order.atom.order.es.sync.clean;

import static com.yxt.order.atom.common.utils.OrderDateUtils.formatYYMMDD;

import com.yxt.order.atom.order.es.doc.EsMemberRefundOrder;
import com.yxt.order.atom.order.es.mapper.EsMemberRefundOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractClean;
import java.util.Date;
import javax.annotation.Resource;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/12/13 15:41
 */
@Component
public class EsMemberRefundOrderClean extends AbstractClean {

  @Resource
  private EsMemberRefundOrderMapper esMemberRefundOrderMapper;

  @Override
  protected Integer expireDays() {
    return ExpireDaysConstant.EsMemberOrderEfficientDays;
  }

  @Override
  protected void clean(String startDate, String endDate) {
    LambdaEsQueryWrapper<EsMemberRefundOrder> query = new LambdaEsQueryWrapper<>();
    query.gt(EsMemberRefundOrder::getCreateTime, startDate);
    query.le(EsMemberRefundOrder::getCreateTime, endDate);
    esMemberRefundOrderMapper.delete(query);
  }

  @Override
  protected Boolean checkHasData(String endDate) {
    LambdaEsQueryWrapper<EsMemberRefundOrder> query = new LambdaEsQueryWrapper<>();
    query.le(EsMemberRefundOrder::getCreateTime, endDate);
    Long count = esMemberRefundOrderMapper.selectCount(query);
    return count > 0;
  }


  @Override
  protected Date getLatestdEndDate(Date endDate) {
    LambdaEsQueryWrapper<EsMemberRefundOrder> query = new LambdaEsQueryWrapper<>();
    query.le(EsMemberRefundOrder::getCreateTime, formatYYMMDD(endDate));
    query.orderByDesc(EsMemberRefundOrder::getCreateTime);
    EsPageInfo<EsMemberRefundOrder> esPageInfo = esMemberRefundOrderMapper.pageQuery(query, 1, 1);
    if(CollectionUtils.isEmpty(esPageInfo.getList())){
      return endDate;
    }
    return esPageInfo.getList().get(0).getCreateTime();
  }
}
