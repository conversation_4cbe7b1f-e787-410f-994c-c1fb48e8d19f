package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationListReq;
import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationReq;
import com.yxt.order.atom.sdk.reconciliation.res.ReconciliationListRes;
import com.yxt.order.atom.sdk.reconciliation.res.ReconciliationRes;

public interface ReconciliationRepository {

  /**
   * 正单和退单一起对账
   *
   * @param
   */

  ReconciliationRes offlineUnionReconciliation(ReconciliationReq req);

  /**
   * 获取对账列表
   * @param req
   * @return
   */
  ReconciliationListRes offlineUnionListReconciliation(ReconciliationListReq req);


  /**
   * 正单对账
   *
   * @param
   */

  ReconciliationRes offlineOrderReconciliation(ReconciliationReq req);


  /**
   * 退单对账
   *
   * @param req
   * @return
   */

  ReconciliationRes offlineRefundReconciliation(ReconciliationReq req);


}
