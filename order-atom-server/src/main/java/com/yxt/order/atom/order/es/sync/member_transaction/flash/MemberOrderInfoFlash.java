package com.yxt.order.atom.order.es.sync.member_transaction.flash;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.MemberTransaction;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo.Order;
import com.yxt.order.atom.order.es.sync.member_transaction.handler.MemberOrderTransactionHandler;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/13 10:21
 */
@Component
public class MemberOrderInfoFlash extends AbstractFlash<OrderInfoDO, Order, MemberTransaction> {
  @Value("${memberTransactionFlashLimit:2000}")
  private Integer memberTransactionFlashLimit;

  @Resource
  private OrderInfoMapper orderInfoMapper;

  @Resource
  private MemberOrderTransactionHandler memberOrderTransactionHandler;

  @Override
  protected List<OrderInfoDO> getSourceList() {
    LambdaQueryWrapper<OrderInfoDO> query = FlashQueryWrapper.orderInfoFlashQuery(getFlashParam(),defaultLimit());
    return orderInfoMapper.selectList(query);
  }

  @Override
  protected Long queryCursorStartId() {
    return orderInfoMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return orderInfoMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<Order> assembleTargetData(List<OrderInfoDO> orderInfoDOS) {
    return orderInfoDOS.stream().map(DoToCanalDtoWrapper::getOrder).collect(Collectors.toList());
  }



  @Override
  protected void flash(List<Order> orders) {
    CanalOrderInfo canalOrderInfo = new CanalOrderInfo();
    canalOrderInfo.setData(orders);
    memberOrderTransactionHandler.manualFlash(canalOrderInfo);
  }

  @Override
  protected Integer defaultLimit() {
    return memberTransactionFlashLimit;
  }
}
