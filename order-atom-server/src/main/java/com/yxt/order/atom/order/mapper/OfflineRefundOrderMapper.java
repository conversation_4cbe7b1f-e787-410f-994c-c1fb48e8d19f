package com.yxt.order.atom.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.consistency.EfficientParam;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface OfflineRefundOrderMapper extends BaseMapper<OfflineRefundOrderDO> {
  Long selectMaxId(@Param("flashParam") FlashParam flashParam);


  Long selectMinId(@Param("flashParam") FlashParam flashParam);


  @Select("select max(id) from offline_refund_order where created_time >= #{param.startDate} and created_time <= #{param.endDate}")
  Long selectEfficientCountMaxId(@Param("param")EfficientParam param);

  @Select("select min(id) from offline_refund_order where created_time >= #{param.startDate} and created_time <= #{param.endDate}")
  Long selectEfficientCountMinId(@Param("param")EfficientParam param);

  void deleteAll0(@Param("refundNo")String refundNo);
  void deleteAll1(@Param("refundNo")String refundNo);
  void deleteAll2(@Param("refundNo")String refundNo);
  void deleteAll3(@Param("refundNo")String refundNo);
  void deleteAll4(@Param("refundNo")String refundNo);
  void deleteAll5(@Param("refundNo")String refundNo);
}