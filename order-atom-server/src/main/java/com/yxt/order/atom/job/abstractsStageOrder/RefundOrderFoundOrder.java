package com.yxt.order.atom.job.abstractsStageOrder;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.offline.NumberType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月16日 16:00
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class RefundOrderFoundOrder extends AbstractStageOrderHandler {

  @Override
  protected String tag() {
    // 退款消息处理
    return buildFoundOrderTag("foundOrder");
  }

  @Override
  protected Boolean check() {
    //check refund
    NumberType numberType = stagingOrder.getNumberType();
    if (!NumberType.REFUND.equals(numberType)) {
      log.warn("StageOrder-非退款类型,{}", JsonUtils.toJson(stagingOrder));
      return false;
    }
    return true;
  }

  @Override
  protected Boolean isStageOrder() {
    return Boolean.TRUE;
  }


}
