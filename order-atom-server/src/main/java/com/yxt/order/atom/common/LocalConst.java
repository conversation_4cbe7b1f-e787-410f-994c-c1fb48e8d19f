package com.yxt.order.atom.common;

/**
 * <AUTHOR>
 * @Description 常量类
 * @Date 2020/3/18
 * @Param
 * @return
 */
public class LocalConst {

  //hydee-middle-merchandise
  public static final String MIDDLE_MERCHANDISE = "hydee-middle-merchandise";


  /**
   * 数据源
   */
  public static class DATA_SOURCE {

    public static final String HANA = "hana"; // hana
    /**
     * Hana数据同步到心云的mysql库,迁移业务从改库中查
     */
    public static final String MIGRATION_TO_MYSQL = "order_offline_archive"; // hana


    public static final String ORDER_OFFLINE = "order_offline"; // 线下单


    public static final String SHARDING_DATA_SOURCE_NAME = "sharding"; // 分表数据源名称
    
    public static final String ORDER_MASTER = "ordermaster"; //

  }
}
