package com.yxt.order.atom.order.repository.abstracts.order;

import com.yxt.order.atom.order.entity.*;

import java.util.List;

import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:23
 * @email: <EMAIL>
 */
@Data
public class SaveOrderOptionalDO {
  /**
   * 原始的平台订单明细
   */
  List<OriThirdOrderDetailDO> oriThirdOrderDetailList;
  private OrderInfoDO orderInfo;
  private OrderPayInfoDO orderPayInfo;
  private List<OrderMultiPayInfoDO> orderMultiPayInfoList;
  private List<OrderDetailDO> orderDetailList;
  private OrderDeliveryAddressDO orderDeliveryAddress;
  private OrderDeliveryRecordDO orderDeliveryRecord;
  private OrderDeliveryLogDO orderDeliveryLog;
  private List<OrderPrescriptionDO> orderPrescriptionList;
  private List<OrderGiftInfoDO> orderGiftInfoList;
  private List<OrderCouponInfoDO> orderCouponInfoList;
  private ErpBillInfoDO erpBillInfo;
  private List<OrderDetailCommodityCostPriceDO> orderDetailCommodityCostPriceList;
  private List<OrderCommodityDetailCostPriceDO> orderCommodityDetailCostPriceList;
  private List<CommodityStockDO> commodityStockList;
  private List<CommodityExceptionOrderDO> commodityExceptionOrderList;
  private OrderBusinessConsumerMessageDO orderBusinessConsumerMessage;
  private List<OrderAssembleCommodityRelationDO> orderAssembleCommodityRelationList;
  private OrderInfoDO saveDeliveryInfo;
  private List<OrderLogDO> orderLogList;
  private List<OrderPickInfoDO> orderPickInfoList;
}
