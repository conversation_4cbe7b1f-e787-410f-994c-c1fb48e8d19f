package com.yxt.order.atom.order.es.sync.b2c_account_info.handler;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.yxt.order.atom.order.es.doc.EsAccountItem;
import com.yxt.order.atom.order.es.dto.OrderDetailDto;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.es.sync.b2c_account_info.EsB2cAccountInfoModel;
import com.yxt.order.atom.order.es.sync.data.CanalB2cAccountInfo;
import com.yxt.order.atom.order.es.sync.data.CanalB2cAccountInfo.AccountInfo;
import com.yxt.order.atom.order.mapper.SyncEsMapper;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: yang jun feng
 * @time: 2024/11/12 17:50
 */
@Component
@Slf4j
public class B2cAccountCanalHandler extends
    AbstractCanalHandler<CanalB2cAccountInfo, EsB2cAccountInfoModel> {
  @Resource
  private SyncEsMapper syncEsMapper;


  public B2cAccountCanalHandler() {
    super(CanalB2cAccountInfo.class);
  }


  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.ACCOUNT_INFO);
  }

  @Override
  protected List<EsB2cAccountInfoModel> assemble() {
    List<AccountInfo> accountInfos = getData().getData();
    if (CollectionUtils.isEmpty(accountInfos)) {
      return Lists.newArrayList();
    }

    // 获取数据详情
    return accountInfos.stream().map(
        this::buildEsAccountInfo
    ).collect(Collectors.toList());
  }

  private EsB2cAccountInfoModel buildEsAccountInfo(AccountInfo accountInfoBO) {
    EsB2cAccountInfoModel esB2cAccountInfoModel = new EsB2cAccountInfoModel();
    esB2cAccountInfoModel.setId(accountInfoBO.getId());
    esB2cAccountInfoModel.setThirdOrderNo(accountInfoBO.getThirdOrderNo());
    esB2cAccountInfoModel.setOrderNo(accountInfoBO.getOrderNo());
    esB2cAccountInfoModel.setServiceMode(accountInfoBO.getServiceMode());
    esB2cAccountInfoModel.setOrderType(accountInfoBO.getOrderType());
    esB2cAccountInfoModel.setPosMode(accountInfoBO.getPosMode());
    esB2cAccountInfoModel.setPickType(accountInfoBO.getPickType());
    esB2cAccountInfoModel.setThirdPlatCode(accountInfoBO.getThirdPlatCode());
    esB2cAccountInfoModel.setSubCompanyCode(accountInfoBO.getSubCompanyCode());
    esB2cAccountInfoModel.setOrganizationCode(accountInfoBO.getOrganizationCode());
    esB2cAccountInfoModel.setOrgParentPath(accountInfoBO.getOrgParentPath());
    esB2cAccountInfoModel.setAccOrganizationCode(accountInfoBO.getAccOrganizationCode());
    esB2cAccountInfoModel.setAccOrgParentPath(accountInfoBO.getAccOrgParentPath());
    esB2cAccountInfoModel.setAccOnlineStoreId(accountInfoBO.getAccOnlineStoreId());
    esB2cAccountInfoModel.setBuyerActualAmount(accountInfoBO.getBuyerActualAmount());
    esB2cAccountInfoModel.setMerchantActualReceive(accountInfoBO.getMerchantActualReceive());
    esB2cAccountInfoModel.setGoodsTotalAmount(accountInfoBO.getGoodsTotalAmount());
    esB2cAccountInfoModel.setBillCommodityAmount(accountInfoBO.getBillCommodityAmount());
    esB2cAccountInfoModel.setDeliveryFee(accountInfoBO.getDeliveryFee());
    esB2cAccountInfoModel.setPackageFee(accountInfoBO.getPackageFee());
    esB2cAccountInfoModel.setMerchantDiscount(accountInfoBO.getMerchantDiscount());
    esB2cAccountInfoModel.setMedicareAmount(accountInfoBO.getMedicareAmount());
    esB2cAccountInfoModel.setPayCode(accountInfoBO.getPayCode());
    esB2cAccountInfoModel.setPayChannel(accountInfoBO.getPayChannel());
    esB2cAccountInfoModel.setOrderAcceptTime(accountInfoBO.getOrderAcceptTime());
    esB2cAccountInfoModel.setOrderOperatorId(accountInfoBO.getOrderOperatorId());
    esB2cAccountInfoModel.setMemberNo(accountInfoBO.getMemberNo());
    esB2cAccountInfoModel.setCostCenterCode(accountInfoBO.getCostCenterCode());
    esB2cAccountInfoModel.setState(accountInfoBO.getState());
    esB2cAccountInfoModel.setAccountTime(accountInfoBO.getAccountTime());
    esB2cAccountInfoModel.setSaleNo(accountInfoBO.getSaleNo());
    esB2cAccountInfoModel.setAccountErrMsg(accountInfoBO.getAccountErrMsg());
    esB2cAccountInfoModel.setCreateTime(accountInfoBO.getCreateTime());
    esB2cAccountInfoModel.setUpdateTime(accountInfoBO.getUpdateTime());
    esB2cAccountInfoModel.setDeleted(accountInfoBO.getDeleted());
    esB2cAccountInfoModel.setVersion(accountInfoBO.getVersion());
    List<OrderDetailDto> orderDetailList = null;
    if (accountInfoBO.getServiceMode().equals("B2C")) {
      // 商品信息
      orderDetailList = syncEsMapper.selectDetailByOmsOrderNo(esB2cAccountInfoModel.getOrderNo());
    } else {
      orderDetailList = syncEsMapper.selectDetailByOrderNo(esB2cAccountInfoModel.getOrderNo());
    }
    List<EsAccountItem> items=new ArrayList<>();
    if (ObjectUtil.isNotNull(orderDetailList)) {
      orderDetailList.forEach(item ->{
        EsAccountItem orderItem=new EsAccountItem();
        orderItem.setStatus(item.getStatus());
        orderItem.setErpCode(item.getCommodityCode());
        items.add(orderItem);
      });
    }
    esB2cAccountInfoModel.setItems(items);
    return esB2cAccountInfoModel;
  }
}
