package com.yxt.order.atom.order.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.converter.OrderDeliveryRecordConverter;
import com.yxt.order.atom.order.entity.OrderDeliveryRecordDO;
import com.yxt.order.atom.order.mapper.OrderDeliveryRecordMapper;
import com.yxt.order.atom.order.repository.DeliveryRepository;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto;
import com.yxt.order.atom.sdk.online_order.delivery.dto.req.UpdateOrderDeliveryRecordReqDto;
import com.yxt.order.types.order.OrderNo;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR> (moatkon)
 * @date 2024年02月28日 18:22
 * @email: <EMAIL>
 */
@Repository
public class DeliveryRepositoryImpl implements DeliveryRepository {

  @Resource
  private OrderDeliveryRecordMapper orderDeliveryRecordMapper;


  @Override
  public OrderDeliveryRecordResDto getOrderDeliveryRecord(OrderNo orderNo) {
    LambdaQueryWrapper<OrderDeliveryRecordDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderDeliveryRecordDO::getOrderNo, orderNo.getOrderNo());
    OrderDeliveryRecordDO orderInfoDO = orderDeliveryRecordMapper.selectOne(queryWrapper);
    return OrderDeliveryRecordConverter.toDto(
        orderInfoDO);
  }

  @Override
  public Boolean updateOrderDeliveryRecord(
      UpdateOrderDeliveryRecordReqDto updateDto) {
    OrderDeliveryRecordDO dbDO = OrderDeliveryRecordConverter.toDO(
        updateDto.getRecordResDto());
    int update = orderDeliveryRecordMapper.update(dbDO,
        new LambdaQueryWrapper<OrderDeliveryRecordDO>()
            .eq(OrderDeliveryRecordDO::getOrderNo, dbDO.getOrderNo())
            .eq(OrderDeliveryRecordDO::getModifyTime,
                updateDto.getRecordResDto().getDataVersion().getDataVersionTime()));
    return update == 1;
  }
}
