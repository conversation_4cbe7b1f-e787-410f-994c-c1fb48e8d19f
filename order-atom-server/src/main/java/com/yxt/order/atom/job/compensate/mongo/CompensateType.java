package com.yxt.order.atom.job.compensate.mongo;

/**
 * @author: moatkon
 * @time: 2024/11/28 16:41
 */
public enum CompensateType {
  /**
   * 会员订单补充退款的收银员和组织信息异常
   */
  VIP_EX,

  // 非会员补充退款的收银员和组织信息异常
  NO_VIP_EX,

  /**
   * 非会员补充退款的收银员和组织信息失败
   */
  NO_VIP_FAILED,

  //  非会员补充海典缺失的促销和券信息失败
  NO_VIP_FAILED_HD_MISS_PROMOTION_COUPON,
  // 非会员补充海典缺失的促销和券信息失败异常
  NO_VIP_EX_HD_MISS_PROMOTION_COUPON,
  //  会员补充海典缺失的促销和券信息失败
  VIP_EX_HD_MISS_PROMOTION_COUPON,

}
