package com.yxt.order.atom.migration.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.dao.dto.QueryErpGoodsDto;
import com.yxt.order.atom.migration.dao.dto.QueryHrcResourceDto;
import com.yxt.order.atom.migration.dao.dto.QueryOrderInfoDto;
import com.yxt.order.atom.migration.dao.dto.QueryOrderItem;
import com.yxt.order.atom.migration.dao.dto.QueryOrganizationInfo;
import com.yxt.order.atom.migration.dao.dto.QueryPayInfo;
import com.yxt.order.atom.repair.dto.StartEndId;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:40
 * @email: <EMAIL>
 */
@DS(DATA_SOURCE.MIGRATION_TO_MYSQL)
@Mapper
public interface HanaMigrationMapper {

  StartEndId queryUnMigrationMaxMinId(@Param("dto") QueryOrderInfoDto queryOrderInfoDto);


  /**
   * 只给退单使用,查正单要求 and XF_SELLINGAMOUNT > 0
   *
   * @param thirdOrderNo
   * @param storeCode
   * @param schema
   * @return
   */
  List<HanaOrderInfo> getOrderInfoForRefund(@Param("thirdOrderNo") String thirdOrderNo,
      @Param("storeCode") String storeCode, @Param("schema") String schema);

  HanaStore queryOrganizationInfo(@Param("dto") QueryOrganizationInfo queryOrganizationInfo);

  List<HanaOrderPay> queryPayInfo(@Param("dto") QueryPayInfo queryPayInfo);

  List<HanaOrderItem> queryOrderItem(@Param("dto") QueryOrderItem queryOrderItem);

  HanaGoods queryErpName(@Param("dto") QueryErpGoodsDto queryErpName);

  HanaHrmResource queryByLoginId(@Param("dto") QueryHrcResourceDto dto);


  String queryParentOrderNoFromDbmCoupon(@Param("childThirdOrderNo") String thirdOrderNo,
      @Param("schema") String schema, @Param("storeCode") String storeCode);

  String queryParentOrderNoFromXfZtGiftgivingMemoMap(
      @Param("childThirdOrderNo") String thirdOrderNo,
      @Param("schema") String schema, @Param("storeCode") String storeCode);


  Integer migrateResult(@Param("id") Long id,
      @Param("schema") String schema,
      @Param("migration") Integer migration,
      @Param("extendJson") String extendJson
  );

  List<HanaOrderInfo> queryUnMigrationOrder(@Param("startId") Long startId,
      @Param("endId") Long endId, @Param("dto") QueryOrderInfoDto queryOrderInfoDto);

  HanaOrderInfo queryMigrationOrderById(@Param("id") Long id, @Param("schema") String schema);

  List<HanaOrderPay> queryHdPayInfo(@Param("orderId") String orderId,
      @Param("storeCode") String storeCode);

  /**
   * @param startId
   * @param endId
   * @param migration
   * @return
   */
  List<HanaOrderInfo> queryNeedFixedHanaOrder(
      @Param("schema") String schema,
      @Param("startId") Long startId,
      @Param("endId") Long endId,
      @Param("migrationList") List<Integer> migrationList/*, @Param("migrateSort") String migrateSort*/);


}
