package com.yxt.order.atom.migration.fix.component.mistake_platform_code;

import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildOrderExistsQuery;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.dao.ModifyPlatformExistsDO;
import com.yxt.order.atom.migration.dao.ModifyPlatformExistsDOMapper;
import com.yxt.order.atom.migration.dao.enums.ModifyPlatformExistSceneEnum;
import com.yxt.order.atom.migration.dao.enums.ModifyPlatformExistStatusEnum;
import com.yxt.order.atom.migration.fix.FixOfflineOrderMistakeScene;
import com.yxt.order.atom.migration.fix.component.es.EsDataOperateComponent;
import com.yxt.order.atom.migration.fix.dto.RepeatedEsDataOperate;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 修复科传的单子,平台标识缺标识为海典 原因: 是因为之前的平台标识取值是根据 inner_store_dictionary 来获取导致的
 *
 * @author: moatkon
 * @time: 2024/12/13 10:21
 * <p>
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class FixOfflineOrderMistakePlatformData extends
    AbstractFlash<OfflineOrderDO, OfflineOrderDO, FixOfflineOrderMistakeScene> {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private ModifyPlatformExistsDOMapper modifyPlatformExistsDOMapper;

  @Resource
  private EsDataOperateComponent esDataOperateComponent;


  @Override
  protected Long queryCursorStartId() {
    CustomData customData = getCustomData();
    Long startId = customData.getStartId();

    return Objects.nonNull(startId) ? startId : offlineOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    CustomData customData = getCustomData();
    Long endId = customData.getEndId();

    return Objects.nonNull(endId) ? endId : offlineOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineOrderDO> query = FlashQueryWrapper.offlineOrderFlashQuery(
        getFlashParam(), defaultLimit());
    return offlineOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineOrderDO> assembleTargetData(List<OfflineOrderDO> offlineOrderDOList) {
    return offlineOrderDOList;
  }

  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param offlineOrderList
   */
  @Override
  protected void flash(List<OfflineOrderDO> offlineOrderList) {

    for (OfflineOrderDO offlineOrderDO : offlineOrderList) {
      handle(offlineOrderDO);
    }

  }

  private void handle(OfflineOrderDO offlineOrderDO) {
    String migration = offlineOrderDO.getMigration();
    if (StringUtils.isEmpty(migration)) {
      return;
    }
    if (!Boolean.TRUE.toString().equals(migration)) {
      return;
    }

    String orderNo = offlineOrderDO.getOrderNo();
    if (StringUtils.isEmpty(orderNo)) {
      return;
    }

    String thirdOrderNo = offlineOrderDO.getThirdOrderNo();
    if (StringUtils.isEmpty(thirdOrderNo)) {
      return;
    }

    String thirdPlatformCode = offlineOrderDO.getThirdPlatformCode();

    // 三方单号不是16位,但是平台却是海典的,需要将平台变更为科传
    if (thirdOrderNo.length() != 16 && ThirdPlatformCodeEnum.HAIDIAN.name()
        .equals(thirdPlatformCode)) {

      // 先检查下科传的订单存不存在
      String needCheckPlatform = ThirdPlatformCodeEnum.KE_CHUAN.name();
      OfflineOrderExistsReqDto dto = new OfflineOrderExistsReqDto();
      dto.setStoreCode(offlineOrderDO.getStoreCode());
      dto.setThirdOrderNo(offlineOrderDO.getThirdOrderNo());
      dto.setThirdPlatformCode(needCheckPlatform);
      dto.setThirdCreated(offlineOrderDO.getCreated());
      LambdaQueryWrapper<OfflineOrderDO> query = buildOrderExistsQuery(dto);
      Integer i = offlineOrderMapper.selectCount(query);
      if(i>=1){
        // 说明已经存在
        ModifyPlatformExistsDO existsDO = new ModifyPlatformExistsDO();
        existsDO.setScene(ModifyPlatformExistSceneEnum.ORDER.name());
        existsDO.setBusinessId(offlineOrderDO.getId().toString());
        existsDO.setBusinessNo(offlineOrderDO.getOrderNo());
        existsDO.setThirdPlatformCode(offlineOrderDO.getThirdPlatformCode());
        existsDO.setAllowOperate("false");
        existsDO.setStatus(ModifyPlatformExistStatusEnum.UN_HANDLE.name());
        existsDO.setNote("存在"+needCheckPlatform+"的订单");
        existsDO.setMigration(offlineOrderDO.getMigration());
        existsDO.setThirdBusinessNo(offlineOrderDO.getThirdOrderNo());
        existsDO.setStoreCode(offlineOrderDO.getStoreCode());
        existsDO.setCreated(offlineOrderDO.getCreated());
        existsDO.setShardingNo(ShardingHelper.getTableIndexByNo(offlineOrderDO.getOrderNo()));
        modifyPlatformExistsDOMapper.insert(existsDO);
        return;
      }

      offlineOrderDO.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());

      LambdaQueryWrapper<OfflineOrderDO> where = new LambdaQueryWrapper<>();
      where.eq(OfflineOrderDO::getOrderNo, orderNo);
      offlineOrderMapper.update(offlineOrderDO, where);

      // 将错误的ES索引删除
      OfflineOrderDO deleteOfflineOrderEs = BeanUtil.toBean(offlineOrderDO, OfflineOrderDO.class);
      deleteOfflineOrderEs.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
      RepeatedEsDataOperate repeatedEsDataOperate = new RepeatedEsDataOperate(deleteOfflineOrderEs);
      esDataOperateComponent.removeEsEsUnhandled(repeatedEsDataOperate);
    }
  }


  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}
