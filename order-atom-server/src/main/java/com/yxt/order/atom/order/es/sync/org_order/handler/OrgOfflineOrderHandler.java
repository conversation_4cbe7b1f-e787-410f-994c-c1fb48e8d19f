package com.yxt.order.atom.order.es.sync.org_order.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_ORDER_DETAIL_REGEX;
import static com.yxt.order.types.canal.Table.OFFLINE_ORDER_REGEX;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgOrderDetailModel;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgOrderModel;
import com.yxt.order.atom.order.mapper.dto.OrganizationInfoDto;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.order.enums.DeliveryTypeEnum;
import com.yxt.order.types.order.enums.OrderSource;
import com.yxt.order.types.order.enums.OrderStatusEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class OrgOfflineOrderHandler extends AbstractCanalHandler<CanalOfflineOrder, OrgOrderModel> {

  @Resource
  private SyncComponent syncComponent;

  @Value("${org-order.offline-order-es-keep-day:365}")
  private Integer offlineOrderEsKeepDays;

  public OrgOfflineOrderHandler() {
    super(CanalOfflineOrder.class);
  }

  /**
   * 检查
   *
   * @return
   */
  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD_OFFLINE.equals(database) && (Table.tableRegex(OFFLINE_ORDER_REGEX, table) || Table.tableRegex(OFFLINE_ORDER_DETAIL_REGEX, table));
  }

  /**
   * 组装ES数据模型
   *
   * @return
   */
  @Override
  protected List<OrgOrderModel> assemble() {
      List<OfflineOrder> canalOrderList = getData().getData();
      if (CollectionUtils.isEmpty(canalOrderList)) {
        return new ArrayList<>();
      }
     List<OfflineOrder> filterOrderList = canalOrderList.stream().filter(this::efficientData).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(filterOrderList)) {
      return new ArrayList<>();
    }
     //会员信息
      Map<String, String> memberMap = new HashMap<>();
      Map<String, String> organizationMap = new HashMap<>();
      List<OrgOrderModel> orgOrderModelList = new ArrayList<>();
      for (OfflineOrder orderSimple : filterOrderList) {
        if(orderSimple.migrateOrder()){
          continue;
        }

        OfflineOrderDO orderInfoDO = syncComponent.getOfflineOrderByOrderNo(orderSimple.getOrderNo(), orderSimple.getNeedRoute());
        if (ObjectUtil.isNull(orderInfoDO)) {
          continue;
        }
        LocalDateTime orderCreated = DateUtil.toLocalDateTime(orderInfoDO.getCreated());
        if(orderCreated.isBefore(LocalDateTime.now().minusDays(offlineOrderEsKeepDays))){
          continue;
        }
        OrgOrderModel orgOrderModel = new OrgOrderModel();
        orgOrderModel.setOrderNo(StrUtil.toStringOrNull(orderInfoDO.getOrderNo()));
        orgOrderModel.setThirdOrderNo(orderInfoDO.getThirdOrderNo());
        orgOrderModel.setCreated(orderInfoDO.getCreated());
        orgOrderModel.setCreateTime(orderInfoDO.getCreatedTime());
        orgOrderModel.setPayTime(ObjectUtil.isNull(orderInfoDO.getPayTime()) ? orderInfoDO.getCreated() : orderInfoDO.getPayTime());
        orgOrderModel.setPayDate(DateUtil.formatDate(orgOrderModel.getPayTime()));
        orgOrderModel.setStoreCode(orderInfoDO.getStoreCode());
        orgOrderModel.setOrgCode(orderInfoDO.getStoreCode());
        orgOrderModel.setSourceStoreCode(orderInfoDO.getStoreCode());
        orgOrderModel.setSourceOrgCode(orderInfoDO.getStoreCode());
        orgOrderModel.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        orgOrderModel.setOrderSource(OrderSource.OFFLINE.name());
        orgOrderModel.setPlatformCode(ThirdPlatformCodeEnum.fromString(orderInfoDO.getThirdPlatformCode()).name());
        orgOrderModel.setErpStatus(100);
        orgOrderModel.setErpTime(orderInfoDO.getBillTime());
        orgOrderModel.setErpSaleNo(orderInfoDO.getThirdOrderNo());
        orgOrderModel.setDeliveryType(DeliveryTypeEnum.BUYER_SELF.getCode());
        List<OfflineOrderDetailDO> detailList = syncComponent.getOfflineOrderDetailByOrderNo(orderSimple.getOrderNo(), orderSimple.getNeedRoute());
        if (CollUtil.isNotEmpty(detailList)) {
          List<OrgOrderDetailModel> detailModelList = detailList.stream().map(detail -> {
            OrgOrderDetailModel detailModel = new OrgOrderDetailModel();
            detailModel.setOrderDetailId(detail.getOrderDetailNo());
            detailModel.setErpCode(detail.getErpCode());
            detailModel.setItemName(detail.getErpName());
            return detailModel;
          }).collect(Collectors.toList());
          orgOrderModel.setDetailList(detailModelList);
          BigDecimal billAmount = detailList.stream().map(OfflineOrderDetailDO::getBillAmount)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
          orgOrderModel.setBillAmount(billAmount);
        }
        orgOrderModel.setUserId(orderInfoDO.getUserId());
        if (memberMap.containsKey(orderInfoDO.getUserId())) {
          orgOrderModel.setUserCardNo(memberMap.get(orderInfoDO.getUserId()));
        } else {
          String userCardNo = syncComponent.getUserCardNoByOrderNo(orderSimple.getOrderNo(), orderSimple.getNeedRoute());
          orgOrderModel.setUserCardNo(userCardNo);
          memberMap.put(orderInfoDO.getUserId(), userCardNo);
        }
        if (organizationMap.containsKey(orderInfoDO.getStoreCode())) {
          orgOrderModel.setStoreType(organizationMap.get(orderInfoDO.getStoreCode()));
        } else {
          OrganizationInfoDto organization = syncComponent.getOrganizationByOrderNo(orderSimple.getOrderNo(), orderSimple.getNeedRoute());
          if (organization != null) {
            orgOrderModel.setStoreType(organization.getStoreType());
            organizationMap.put(orderInfoDO.getStoreCode(), organization.getStoreType());
          }
          organizationMap.put(orderInfoDO.getStoreCode(), "");
        }
        orgOrderModel.setDeleted(0L);
        orgOrderModelList.add(orgOrderModel);
      }
      return orgOrderModelList;
  }

  public boolean efficientData(OfflineOrder offlineOrder) {
    //只筛选线下单平台
    if(!ThirdPlatformCodeEnum.isValid(offlineOrder.getThirdPlatformCode())){
      return false;
    }
    return true;
  }
}
