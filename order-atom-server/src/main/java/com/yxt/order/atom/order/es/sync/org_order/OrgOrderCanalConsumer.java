package com.yxt.order.atom.order.es.sync.org_order;

import static com.yxt.order.atom.common.configration.KafkaConfig.CANAL_MESSAGE_GROUP_ID_FOR_ORG_ORDER;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgOrderModel;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class OrgOrderCanalConsumer {

  @Resource
  private List<AbstractCanalHandler<? extends BaseCanalData<?>, OrgOrderModel>> abstractCanalHandlerList;
  @KafkaListener(topics = {"${canal.org-order}"}, groupId = CANAL_MESSAGE_GROUP_ID_FOR_ORG_ORDER, containerFactory = "canalKafkaListenerFactory")
  public void orgOrderConsumer(List<ConsumerRecord<String, String>> consumerRecordList, Acknowledgment ack) {
    try {
      if (CollectionUtils.isEmpty(consumerRecordList)) {
        return;
      }
      List<String> messageList = consumerRecordList.stream()
          .filter(s -> !StringUtils.isEmpty(s.value()))
          .filter(s -> !JsonUtils.toObject(s.value(), new TypeReference<BaseCanalData<?>>() {}).getIsDdl())
          .map(ConsumerRecord::value)
          .collect(Collectors.toList());
      if (CollectionUtils.isEmpty(messageList)) {
        return;
      }
      //根据操作类型和data去重
      messageList = CollUtil.distinct(messageList, data -> {
        BaseCanalData<?> baseCanalData = JsonUtils.toObject(data, new TypeReference<BaseCanalData<?>>() {});
        return StrUtil.join(StrUtil.DASHED, baseCanalData.getType(), JSON.toJSONString(baseCanalData.getData()));
      }, true);
      for (String message : messageList) {
        try {
          for (AbstractCanalHandler<? extends BaseCanalData<?>, ? extends BaseEsIndexModel> abstractCommonCanalHandler : abstractCanalHandlerList) {
            if (abstractCommonCanalHandler.execBusiness(message)) {
              break;
            }
          }
        } catch (Exception e) {
          log.warn("店铺正单消费记录条件索引,同步到ES异常:{},message: {}",e.getMessage(), message,e);
        }
      }

      ack.acknowledge();
    } catch (Exception e) {
      log.error("orgOrderConsumer consumer error,cause:{},data:{}", e.getMessage(),
          consumerRecordList, e);
    }
  }

}
