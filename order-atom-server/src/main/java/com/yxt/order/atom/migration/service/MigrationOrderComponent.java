package com.yxt.order.atom.migration.service;

import com.google.common.collect.Lists;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.dao.HanaOrderItem;
import com.yxt.order.atom.migration.dao.HanaOrderPay;
import com.yxt.order.atom.migration.dao.HanaStore;
import com.yxt.order.atom.migration.dao.HanaUser;
import com.yxt.order.atom.migration.service.HanaMigrationServiceImpl.StateEnum;
import com.yxt.order.atom.migration.service.dto.GoodsInfo;
import com.yxt.order.atom.migration.service.dto.MigrationExtend;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailPickDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPrescriptionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPromotionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderUserDTO;
import com.yxt.order.atom.sdk.offline_order.req.SaveOfflineOrderReqDto;
import com.yxt.order.common.exception.DetailNotExistsException;
import com.yxt.order.types.offline.DataDimensionType;
import com.yxt.order.types.offline.OfflineDetailGiftType;
import com.yxt.order.types.offline.OfflineDetailStatus;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineOrderState;
import com.yxt.order.types.offline.OfflineThirdPlatformCode;
import com.yxt.order.types.offline.OfflineUserId;
import com.yxt.order.types.offline.enums.DataDimensionTypeEnum;
import com.yxt.order.types.offline.enums.GiftTypeEnum;
import com.yxt.order.types.offline.enums.OfflineDetailStatusEnum;
import com.yxt.order.types.offline.enums.OfflineOrderStateEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月12日 9:44
 * @email: <EMAIL>
 */
@Component
public class MigrationOrderComponent {

  @Resource
  private MigrationPlatformCodeHandler migrationPlatformCodeHandler;
  @Resource
  private HanaMigrationMapper hanaMigrationMapper;
  @Resource
  private MigrationCommonComponent migrationCommonComponent;

  public SaveOfflineOrderReqDto buildOrder(HanaOrderInfo hanaOrderInfo, String schema) {
    // 1. 去hana库查会出会员信息
    HanaUser hanaUser = migrationCommonComponent.buildHanaUserInfo(hanaOrderInfo.getClientCode());
    String userId = Strings.EMPTY;
    if (Objects.nonNull(hanaUser) && Objects.nonNull(hanaUser.getMemberUserId())) {
      userId = String.valueOf(hanaUser.getMemberUserId());
    }

    SaveOfflineOrderReqDto reqDto = new SaveOfflineOrderReqDto();
    reqDto.setOfflineOrderDTO(buildOrderDTO(hanaOrderInfo, userId, schema));
    // 这里开始入参reqDto,注意编排顺序
    reqDto.setOfflineOrderOrganizationDTO(buildOrganization(reqDto));
    reqDto.setOfflineOrderCashierDeskDTO(buildOrderCashier(reqDto, hanaOrderInfo));
    if (Objects.nonNull(hanaUser)) {
      reqDto.setOfflineOrderUserDTO(buildUser(reqDto, hanaUser, userId));
    }
    reqDto.setOfflineOrderPrescriptionDTO(buildOrderPrescription(reqDto));
    reqDto.setOfflineOrderPayDTOList(buildOrderPay(reqDto, hanaOrderInfo, schema));
    reqDto.setOfflineOrderDetailDTOList(buildOrderDetail(reqDto, hanaOrderInfo, schema));
    reqDto.setOfflineOrderCouponDTOList(null); // 无
    reqDto.setOfflineOrderPromotionDTOList(buildOrderPromotionInfo(reqDto, hanaOrderInfo, schema));
    return reqDto;
  }


  private List<OfflineOrderDetailDTO> buildOrderDetail(SaveOfflineOrderReqDto reqDto,
      HanaOrderInfo hanaOrderInfo, String schema) {
    OfflineOrderDTO offlineOrderDTO = reqDto.getOfflineOrderDTO();

    List<HanaOrderItem> hanaOrderItemList = migrationCommonComponent.getHanaOrderItems(
        hanaOrderInfo, schema);

    if (CollectionUtils.isEmpty(hanaOrderItemList)) {
      throw new DetailNotExistsException(
          String.format("正单明细迁移库中不存在.店铺Code:%s, 三方单号:%s, schema:%s,txDate:%s,posCashierDeskNo:%s", hanaOrderInfo.getStoreCode(),
              hanaOrderInfo.getThirdOrderNo(),schema,hanaOrderInfo.getTxDate(),hanaOrderInfo.getPosCashierDeskNo()));
    }

    String orderNo = offlineOrderDTO.getOrderNo();

    Set<String> erpCodeSet = hanaOrderItemList.stream()
        .map(HanaOrderItem::getErpCode)
        .collect(Collectors.toSet());
    Map<String, GoodsInfo> goodsInfoMap = migrationCommonComponent.getGoodsInfo(erpCodeSet);

    return hanaOrderItemList.stream().map(hanaOrderItem -> {
      String orderDetailNo = migrationCommonComponent.generateId();

      OfflineOrderDetailDTO offlineOrderDetailDTO = new OfflineOrderDetailDTO();
      offlineOrderDetailDTO.setOrderNo(orderNo);
      offlineOrderDetailDTO.setOrderDetailNo(orderDetailNo);
      offlineOrderDetailDTO.setRowNo(hanaOrderItem.getRowNo());
      offlineOrderDetailDTO.setPlatformSkuId(null);
      offlineOrderDetailDTO.setErpCode(hanaOrderItem.getErpCode());
      String erpName = migrationCommonComponent.getErpNameByErpCode(hanaOrderItem.getErpCode());
      offlineOrderDetailDTO.setErpName(erpName);
      offlineOrderDetailDTO.setCommodityCount(hanaOrderItem.getCommodityCount());
      offlineOrderDetailDTO.setStatus(
          OfflineDetailStatus.status(OfflineDetailStatusEnum.NORMAL).toString());
      offlineOrderDetailDTO.setGiftType(
          OfflineDetailGiftType.giftType(GiftTypeEnum.NOT_GIFT).toString());
      offlineOrderDetailDTO.setOriginalPrice(hanaOrderItem.getOriginalPrice());
      // 商品总额/数量
      BigDecimal price = migrationCommonComponent.calcPrice(hanaOrderItem);
      offlineOrderDetailDTO.setPrice(price); // 自己计算的,实际价格
      offlineOrderDetailDTO.setTotalAmount(hanaOrderItem.getTotalAmount()); //已经乘以数量的
      // 总分摊
      BigDecimal totalDiscountShare = migrationCommonComponent.calcTotalDiscount(hanaOrderItem);
      offlineOrderDetailDTO.setDiscountShare(totalDiscountShare);
      offlineOrderDetailDTO.setDiscountAmount(totalDiscountShare); // 公式和总分摊一致
      offlineOrderDetailDTO.setBillPrice(price);// 公式和商品售价（实际）一致
      offlineOrderDetailDTO.setBillAmount(hanaOrderItem.getTotalAmount());
      offlineOrderDetailDTO.setCommodityCostPrice(BigDecimal.ZERO);
      offlineOrderDetailDTO.setCreatedBy("");
      offlineOrderDetailDTO.setUpdatedBy("");
      offlineOrderDetailDTO.setCreatedTime(new Date());
      offlineOrderDetailDTO.setUpdatedTime(new Date());
      offlineOrderDetailDTO.setVersion(1L);

      // 这个传入的是offlineOrderDetailDTO,注意编排顺序
      offlineOrderDetailDTO.setOfflineOrderDetailPickDTOList(
          buildOrderDetailPickInfo(hanaOrderItem, orderNo, orderDetailNo));

      // 对接明细是否参与了优惠
      BigDecimal promotionAmt = hanaOrderItem.getPromotionAmt();
      if (Objects.nonNull(promotionAmt) && promotionAmt.compareTo(BigDecimal.ZERO) > 0) {
        offlineOrderDetailDTO.setIsOnPromotion(String.valueOf(Boolean.TRUE));
        // 有一个商品参加了促销,就表示主单参加了促销
        offlineOrderDTO.setIsOnPromotion(String.valueOf(Boolean.TRUE));
      } else {
        offlineOrderDetailDTO.setIsOnPromotion(String.valueOf(Boolean.FALSE));
      }

      GoodsInfo goodsInfo = goodsInfoMap.get(offlineOrderDetailDTO.getErpCode());
      if (Objects.nonNull(goodsInfo)) {
        offlineOrderDetailDTO.setFiveClass(goodsInfo.getFiveClass());
        offlineOrderDetailDTO.setFiveClassName(goodsInfo.getFiveClassName());
        offlineOrderDetailDTO.setManufacture(goodsInfo.getManufacture());
        offlineOrderDetailDTO.setCommoditySpec(goodsInfo.getCommoditySpecValue());
        offlineOrderDetailDTO.setMainPic(goodsInfo.getMainPic());
      }
      offlineOrderDetailDTO.setSalerId(hanaOrderItem.getSalerId());
      offlineOrderDetailDTO.setSalerName(migrationCommonComponent.getHrmResourceName(hanaOrderItem.getSalerId()));
      return offlineOrderDetailDTO;
    }).collect(Collectors.toList());
  }


  private List<OfflineOrderPromotionDTO> buildOrderPromotionInfo(SaveOfflineOrderReqDto reqDto,
      HanaOrderInfo hanaOrderInfo, String schema) {

    List<HanaOrderItem> hanaOrderItemList = migrationCommonComponent.getHanaOrderItems(
        hanaOrderInfo, schema);
    if (CollectionUtils.isEmpty(hanaOrderItemList)) {
      return Lists.newArrayList();
    }

    OfflineOrderDTO offlineOrderDTO = reqDto.getOfflineOrderDTO();
    String orderNo = offlineOrderDTO.getOrderNo();
    String thirdOrderNo = offlineOrderDTO.getThirdOrderNo();

    // 基础
    OfflineOrderPromotionDTO base = new OfflineOrderPromotionDTO();
    base.setOrderNo(orderNo);
    base.setThirdOrderNo(thirdOrderNo);
    base.setType(DataDimensionType.dataDimensionType(DataDimensionTypeEnum.DETAIL).toString());
    base.setCreatedBy("");
    base.setUpdatedBy("迁移订单");
    base.setCreatedTime(new Date());
    base.setUpdatedTime(new Date());
    base.setVersion(0L);
    base.setPromotionType(null);

    List<OfflineOrderPromotionDTO> all = Lists.newArrayList();
    for (HanaOrderItem hanaOrderItem : hanaOrderItemList) {

      String promotionNo1 = hanaOrderItem.getPromotionNo1();
      String promotionNo2 = hanaOrderItem.getPromotionNo2();
      String promotionNo3 = hanaOrderItem.getPromotionNo3();
      String promotionNo4 = hanaOrderItem.getPromotionNo4();
      String promotionNo5 = hanaOrderItem.getPromotionNo5();

      BigDecimal promotionAmount1 = hanaOrderItem.getPromotionAmount1();
      BigDecimal promotionAmount2 = hanaOrderItem.getPromotionAmount2();
      BigDecimal promotionAmount3 = hanaOrderItem.getPromotionAmount3();
      BigDecimal promotionAmount4 = hanaOrderItem.getPromotionAmount4();
      BigDecimal promotionAmount5 = hanaOrderItem.getPromotionAmount5();

      OfflineOrderPromotionDTO p1 = SerializationUtils.clone(base);
      p1.setPromotionNo(promotionNo1);
      p1.setPromotionAmount(promotionAmount1);
      p1.setErpCode(hanaOrderItem.getErpCode());
      p1.setCommodityCount(hanaOrderItem.getCommodityCount());

      OfflineOrderPromotionDTO p2 = SerializationUtils.clone(base);
      p2.setPromotionNo(promotionNo2);
      p2.setPromotionAmount(promotionAmount2);
      p2.setErpCode(hanaOrderItem.getErpCode());
      p2.setCommodityCount(hanaOrderItem.getCommodityCount());

      OfflineOrderPromotionDTO p3 = SerializationUtils.clone(base);
      p3.setPromotionNo(promotionNo3);
      p3.setPromotionAmount(promotionAmount3);
      p3.setErpCode(hanaOrderItem.getErpCode());
      p3.setCommodityCount(hanaOrderItem.getCommodityCount());

      OfflineOrderPromotionDTO p4 = SerializationUtils.clone(base);
      p4.setPromotionNo(promotionNo4);
      p4.setPromotionAmount(promotionAmount4);
      p4.setErpCode(hanaOrderItem.getErpCode());
      p4.setCommodityCount(hanaOrderItem.getCommodityCount());

      OfflineOrderPromotionDTO p5 = SerializationUtils.clone(base);
      p5.setPromotionNo(promotionNo5);
      p5.setPromotionAmount(promotionAmount5);
      p5.setErpCode(hanaOrderItem.getErpCode());
      p5.setCommodityCount(hanaOrderItem.getCommodityCount());

      all.addAll(Lists.newArrayList(p1, p2, p3, p4, p5));
    }

    return all;
  }


  private List<OfflineOrderDetailPickDTO> buildOrderDetailPickInfo(HanaOrderItem hanaOrderItem,
      String orderNo, String orderDetailNo) {
    OfflineOrderDetailPickDTO offlineOrderDetailPickDTO = new OfflineOrderDetailPickDTO();
    offlineOrderDetailPickDTO.setOrderNo(orderNo);
    offlineOrderDetailPickDTO.setOrderDetailNo(orderDetailNo);
    offlineOrderDetailPickDTO.setErpCode(hanaOrderItem.getErpCode());
    offlineOrderDetailPickDTO.setMakeNo(hanaOrderItem.getMakeNo());
    offlineOrderDetailPickDTO.setCount(hanaOrderItem.getCommodityCount());
    offlineOrderDetailPickDTO.setCreatedBy("");
    offlineOrderDetailPickDTO.setUpdatedBy("");
    offlineOrderDetailPickDTO.setCreatedTime(new Date());
    offlineOrderDetailPickDTO.setUpdatedTime(new Date());
    offlineOrderDetailPickDTO.setVersion(1L);

    return Lists.newArrayList(offlineOrderDetailPickDTO);
  }


  private List<OfflineOrderPayDTO> buildOrderPay(SaveOfflineOrderReqDto reqDto,
      HanaOrderInfo hanaOrderInfo, String schema) {

    List<HanaOrderPay> hanaOrderPayList = migrationCommonComponent.getHanaOrderPays(
        hanaOrderInfo.getThirdOrderNo(), hanaOrderInfo.getStoreCode(), schema,hanaOrderInfo.orderIdList());

    OfflineOrderDTO offlineOrderDTO = reqDto.getOfflineOrderDTO();
    String orderNo = offlineOrderDTO.getOrderNo();

    return hanaOrderPayList.stream().map(hanaOrderPay -> {
      OfflineOrderPayDTO offlineOrderPayDTO = new OfflineOrderPayDTO();
      offlineOrderPayDTO.setOrderNo(orderNo);
      offlineOrderPayDTO.setPayType(hanaOrderPay.getPayType());
      offlineOrderPayDTO.setPayAmount(hanaOrderPay.getPayAmount());
      offlineOrderPayDTO.setPayName(hanaOrderPay.getPayName());
      offlineOrderPayDTO.setCreatedBy("");
      offlineOrderPayDTO.setUpdatedBy("");
      offlineOrderPayDTO.setCreatedTime(new Date());
      offlineOrderPayDTO.setUpdatedTime(new Date());
      offlineOrderPayDTO.setVersion(1L);
      return offlineOrderPayDTO;
    }).collect(Collectors.toList());

  }


  private OfflineOrderPrescriptionDTO buildOrderPrescription(SaveOfflineOrderReqDto reqDto) {
    return null; // 无
  }

  private OfflineOrderUserDTO buildUser(SaveOfflineOrderReqDto reqDto,
      HanaUser hanaUser, String userId) {
    OfflineOrderDTO offlineOrderDTO = reqDto.getOfflineOrderDTO();

    OfflineOrderUserDTO offlineOrderUserDTO = new OfflineOrderUserDTO();
    offlineOrderUserDTO.setOrderNo(offlineOrderDTO.getOrderNo());
    offlineOrderUserDTO.setUserId(userId);
    offlineOrderUserDTO.setUserName(hanaUser.getUserName());
    offlineOrderUserDTO.setUserCardNo(hanaUser.getUserCardNo());
    offlineOrderUserDTO.setUserMobile(hanaUser.getUserMobile());
    offlineOrderUserDTO.setCreatedBy("");
    offlineOrderUserDTO.setUpdatedBy("");
    offlineOrderUserDTO.setCreatedTime(new Date());
    offlineOrderUserDTO.setUpdatedTime(new Date());
    offlineOrderUserDTO.setVersion(1L);
    return offlineOrderUserDTO;
  }

  private OfflineOrderCashierDeskDTO buildOrderCashier(SaveOfflineOrderReqDto reqDto,
      HanaOrderInfo hanaOrderInfo) {
    OfflineOrderDTO offlineOrderDTO = reqDto.getOfflineOrderDTO();

    OfflineOrderCashierDeskDTO orderCashierDeskDTO = new OfflineOrderCashierDeskDTO();
    orderCashierDeskDTO.setOrderNo(offlineOrderDTO.getOrderNo());
    orderCashierDeskDTO.setPosCashierDeskNo(hanaOrderInfo.getPosCashierDeskNo());
    orderCashierDeskDTO.setCashier(hanaOrderInfo.getCashier());
    String cashierName = migrationCommonComponent.getHrmResourceName(hanaOrderInfo.getCashier());
    orderCashierDeskDTO.setCashierName(cashierName);
    orderCashierDeskDTO.setPicker(hanaOrderInfo.getPicker());
    String pickerName = migrationCommonComponent.getHrmResourceName(hanaOrderInfo.getPicker());
    orderCashierDeskDTO.setPickerName(pickerName);
    orderCashierDeskDTO.setShiftId(null);
    orderCashierDeskDTO.setShiftDate(null);
    orderCashierDeskDTO.setCreatedBy("");
    orderCashierDeskDTO.setUpdatedBy("");
    orderCashierDeskDTO.setCreatedTime(new Date());
    orderCashierDeskDTO.setUpdatedTime(new Date());
    orderCashierDeskDTO.setVersion(1L);
    return orderCashierDeskDTO;
  }

  private OfflineOrderOrganizationDTO buildOrganization(SaveOfflineOrderReqDto reqDto) {
    OfflineOrderDTO offlineOrderDTO = reqDto.getOfflineOrderDTO();
    String storeCode = offlineOrderDTO.getStoreCode();
    HanaStore hanaStore = migrationCommonComponent.queryStoreInfo(storeCode);
    OfflineOrderOrganizationDTO offlineOrderOrganizationDTO = new OfflineOrderOrganizationDTO();
    offlineOrderOrganizationDTO.setOrderNo(offlineOrderDTO.getOrderNo());
    offlineOrderOrganizationDTO.setStoreCode(storeCode);
    offlineOrderOrganizationDTO.setStoreName(hanaStore.getStoreName());
    offlineOrderOrganizationDTO.setCompanyCode(hanaStore.getCompanyCode());
    offlineOrderOrganizationDTO.setCompanyName(hanaStore.getCompanyName());
    offlineOrderOrganizationDTO.setStoreDirectJoinType(hanaStore.getStoreDirectJoinType());
    offlineOrderOrganizationDTO.setCreatedBy("");
    offlineOrderOrganizationDTO.setUpdatedBy("");
    offlineOrderOrganizationDTO.setCreatedTime(new Date());
    offlineOrderOrganizationDTO.setUpdatedTime(new Date());
    offlineOrderOrganizationDTO.setVersion(1L);
    return offlineOrderOrganizationDTO;
  }

  private OfflineOrderDTO buildOrderDTO(HanaOrderInfo hanaOrderInfo, String userId, String schema) {
    String orderNo = migrationCommonComponent.generateId();

//    ThirdPlatformCodeEnum platformCode = migrationPlatformCodeHandler.getPlatformCode(schema,
//        hanaOrderInfo.getCreateTime(), hanaOrderInfo.getStoreCode());
    ThirdPlatformCodeEnum platformCode = migrationPlatformCodeHandler.getPlatformCodeOtherOrderId(hanaOrderInfo);

    OfflineThirdPlatformCode thirdPlatformCode = OfflineThirdPlatformCode.thirdPlatformCode(
        platformCode);

    String storeCode = hanaOrderInfo.getStoreCode();

    Date txDateAndTime = migrationCommonComponent.getTxDateAndTime(hanaOrderInfo);

    // 生成 OfflineUserId
    OfflineUserId offlineUserId = StringUtils.isEmpty(userId) ? null : OfflineUserId.userId(userId);
    OfflineOrderNo offlineOrderNo = OfflineOrderNo.create(orderNo, offlineUserId, txDateAndTime);

    OfflineOrderDTO offlineOrderDTO = new OfflineOrderDTO();
    offlineOrderDTO.setOrderNo(offlineOrderNo.toString());
    offlineOrderDTO.setUserId(userId);
    offlineOrderDTO.setStoreCode(storeCode);
    offlineOrderDTO.setThirdPlatformCode(thirdPlatformCode.toString());
    offlineOrderDTO.setThirdOrderNo(hanaOrderInfo.getThirdOrderNo());
    String parentThirdOrderNo = migrationCommonComponent.fetchOutParentThirdOrderNo(
        hanaOrderInfo.getThirdOrderNo(), schema, hanaOrderInfo.getStoreCode());
    offlineOrderDTO.setParentThirdOrderNo(parentThirdOrderNo);
    offlineOrderDTO.setDayNum(null);
    offlineOrderDTO.setOrderState(
        OfflineOrderState.orderState(OfflineOrderStateEnum.DONE).toString());
    offlineOrderDTO.setCreated(txDateAndTime);
    offlineOrderDTO.setPayTime(txDateAndTime);
    offlineOrderDTO.setBillTime(txDateAndTime);
    offlineOrderDTO.setCompleteTime(txDateAndTime);
    offlineOrderDTO.setActualPayAmount(hanaOrderInfo.getActualPayAmount());
    offlineOrderDTO.setActualCollectAmount(hanaOrderInfo.getActualCollectAmount());
    offlineOrderDTO.setCouponCodes(JsonUtils.toJson(Lists.newArrayList()));
    offlineOrderDTO.setCreatedBy("");
    offlineOrderDTO.setUpdatedBy("");
    offlineOrderDTO.setCreatedTime(txDateAndTime); // 改为交易时间
    offlineOrderDTO.setUpdatedTime(new Date());
    offlineOrderDTO.setVersion(1L);
    offlineOrderDTO.setSerialNo(null);
    offlineOrderDTO.setMigration(String.valueOf(Boolean.TRUE));
    return offlineOrderDTO;
  }

  public void migrateResult(Long id,String schema,StateEnum state, MigrationExtend extend){
    if(Objects.isNull(id)){
      throw new RuntimeException("update, id can not be null");
    }
    hanaMigrationMapper.migrateResult(id,schema, state.getValue(),extend.json());
  }

}
