package com.yxt.order.atom.order_world.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.consistency.EfficientParam;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order_world.entity.OrderInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface NewOrderInfoMapper extends BaseMapper<OrderInfoDO> {

  Long selectMaxId(@Param("flashParam") FlashParam flashParam);

  Long selectMinId(@Param("flashParam") FlashParam flashParam);

  @Select("select max(id) from offline_order where created_time >= #{param.startDate} and created_time <= #{param.endDate}")
  Long selectEfficientCountMaxId(@Param("param")EfficientParam param);

  @Select("select min(id) from offline_order where created_time >= #{param.startDate} and created_time <= #{param.endDate}")
  Long selectEfficientCountMinId(@Param("param")EfficientParam param);
}
