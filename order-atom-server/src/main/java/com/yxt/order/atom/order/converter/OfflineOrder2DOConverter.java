package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OfflineOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineOrderCouponDO;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailTraceDO;
import com.yxt.order.atom.order.entity.OfflineOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineOrderPayDO;
import com.yxt.order.atom.order.entity.OfflineOrderPrescriptionDO;
import com.yxt.order.atom.order.entity.OfflineOrderPromotionDO;
import com.yxt.order.atom.order.entity.OfflineOrderUserDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCouponDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailPickDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailTraceDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderMedInsSettleDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPrescriptionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPromotionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderUserDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface OfflineOrder2DOConverter {

  OfflineOrder2DOConverter INSTANCE = Mappers.getMapper(OfflineOrder2DOConverter.class);

  OfflineOrderCashierDeskDO toDO(OfflineOrderCashierDeskDTO dto);
  OfflineOrderDetailDO toDO(OfflineOrderDetailDTO dto);

  OfflineOrderDetailPickDO toDO(OfflineOrderDetailPickDTO dto);

  OfflineOrderDO toDO(OfflineOrderDTO dto);

  OfflineOrderOrganizationDO toDO(OfflineOrderOrganizationDTO dto);


  List<OfflineOrderPayDO> toDO(List<OfflineOrderPayDTO> dtoList);

  OfflineOrderPrescriptionDO toDO(OfflineOrderPrescriptionDTO dto);

  OfflineOrderUserDO toDO(OfflineOrderUserDTO dto);

  OfflineRefundOrderDetailDO toDO(OfflineRefundOrderDetailDTO dto);

  OfflineOrderMedInsSettleDO toDO(OfflineOrderMedInsSettleDto dto);


  List<OfflineOrderCouponDO> toOrderCouponDO(List<OfflineOrderCouponDTO> offlineOrderCouponDTOList);

  List<OfflineOrderPromotionDO> toOrderPromotionDO(List<OfflineOrderPromotionDTO> offlineOrderPromotionDTOList);

  OfflineOrderDetailTraceDO toDO(OfflineOrderDetailTraceDTO offlineOrderDetailTraceDTO);
}
