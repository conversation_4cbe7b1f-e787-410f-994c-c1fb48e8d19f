package com.yxt.order.atom.common;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.ALERT_THREAD_POOL;

import java.util.concurrent.ThreadPoolExecutor;
import javax.annotation.Resource;

import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月19日 15:31
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class OfflineAlarmComponent {

  @Value("${offlineAlarmWebhook:}")
  private String offlineAlarmWebhook;

  @Value("${autoCreateTableNotifyWebhook:}")
  private String autoCreateTableNotifyWebhook;

  @Value("${esOrderMemberTransactionRecordAlertWebhook:}")
  private String esOrderMemberTransactionRecordAlertWebhook;

  @Value("${spring.profiles.active:}")
  private String env;


  @Qualifier(ALERT_THREAD_POOL)
  @Resource
  private ThreadPoolExecutor alertThreadPool;

  public void alarm(String content) {
    alertThreadPool.submit(() -> {
      try {
        if (StringUtils.isEmpty(offlineAlarmWebhook)) {
          log.warn("未配置线下单告警webhook url");
          return;
        }
        WxRobotOkHttpUtils.post(offlineAlarmWebhook, String.format("[%s]-%s", env, content));
      } catch (Exception e) {
        log.error("企微告警异常,{},{}", offlineAlarmWebhook, content);
      }
    });
  }


  public void autoCreateTableNotify(String content) {
    alertThreadPool.submit(() -> {
      try {
        if (StringUtils.isEmpty(autoCreateTableNotifyWebhook)) {
          log.warn("未配置线下单自动告警webhook url");
          return;
        }
        WxRobotOkHttpUtils.post(autoCreateTableNotifyWebhook,
            String.format("[%s]-%s", env, content));
      } catch (Exception e) {
        log.error("企微告警异常,{},{}", autoCreateTableNotifyWebhook, content);
      }
    });
  }



  public void esOrderMemberTransactionRecordAlert(String content,String domain) {
    alertThreadPool.submit(() -> {
      try {
        if (StringUtils.isEmpty(esOrderMemberTransactionRecordAlertWebhook)) {
          log.warn("未配置{}webhook url",domain);
          return;
        }
        WxRobotOkHttpUtils.post(esOrderMemberTransactionRecordAlertWebhook,
            String.format("[%s]-%s", env, content));
      } catch (Exception e) {
        log.error("{}企微告警异常,{},{}",domain, esOrderMemberTransactionRecordAlertWebhook, content);
      }
    });
  }
}
