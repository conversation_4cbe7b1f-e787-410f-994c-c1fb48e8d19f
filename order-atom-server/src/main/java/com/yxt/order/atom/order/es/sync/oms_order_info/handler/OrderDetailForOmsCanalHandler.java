package com.yxt.order.atom.order.es.sync.oms_order_info.handler;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.es.sync.data.CanalOrderDetail;
import com.yxt.order.atom.order.es.sync.data.CanalOrderDetail.OrderDetail;
import com.yxt.order.atom.order.es.sync.oms_order_info.EsOmsOrderInfoModel;
import com.yxt.order.atom.order.es.sync.oms_order_info.flash.OmsOrderInfoFlash;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/11/4 17:50
 */
@Component
@Slf4j
public class OrderDetailForOmsCanalHandler extends
    AbstractCanalHandler<CanalOrderDetail, EsOmsOrderInfoModel> {

  @Resource
  private OmsOrderInfoFlash omsOrderInfoFlash;


  public OrderDetailForOmsCanalHandler() {
    super(CanalOrderDetail.class);
  }


  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.ORDER_DETAIL);
  }

  @Override
  protected List<EsOmsOrderInfoModel> assemble() {
    List<OrderDetail> orderDetailList = getData().getData();
    if(CollectionUtils.isEmpty(orderDetailList)){
      return Lists.newArrayList();
    }

    FlashParam param = new FlashParam();
    param.setNoList(Lists.newArrayList(orderDetailList.get(0).getOmsOrderNo().toString()));
    omsOrderInfoFlash.startFlush(param);
    return Lists.newArrayList();
  }



}
