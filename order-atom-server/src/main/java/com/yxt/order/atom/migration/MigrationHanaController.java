package com.yxt.order.atom.migration;

import com.google.common.collect.Lists;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import com.yxt.order.atom.migration.req.SpecifyMigrationBatchReq;
import com.yxt.order.atom.migration.req.SpecifyMigrationId;
import com.yxt.order.atom.migration.service.MigrationService;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月25日 13:48
 * @email: <EMAIL>
 */
@Slf4j
@RestController
public class MigrationHanaController extends AbstractController {

  @Resource
  private MigrationService migrationService;


  @Data
  @Builder
  public static class MigrationRes {
    private String triggerResult;
    private String taskKey;
    private String configJson;
  }

  @PostMapping("/migration-hana")
  public ResponseBase<List<MigrationRes>> migration() {
    List<MigrationRes> migrationResList = Lists.newArrayList();

    List<HanaMigrationDO> enableMigrationList = migrationService.queryEnableMigrationConfigList();
    if (CollectionUtils.isEmpty(enableMigrationList)) {
      return ResponseBase.success(migrationResList);
    }
    log.info("本次启动迁移的所有迁移配置:{}", JsonUtils.toJson(enableMigrationList));

    for (HanaMigrationDO hanaMigrationDO : enableMigrationList) {
      // redis分布是锁
      String value = RedisStringUtil.getValue(hanaMigrationDO.taskKey());
      if (!StringUtils.isEmpty(value)) {
        log.info("{} 迁移已触发,请勿重新触发! {}", hanaMigrationDO.taskKey(),
            JsonUtils.toJson(hanaMigrationDO));
//        migrationResList.add(MigrationRes.builder().taskKey(hanaMigrationDO.taskKey())
//            .configJson(JsonUtils.toJson(hanaMigrationDO)).triggerResult("迁移已触发,请勿重新触发")
//            .build()); // 不输出
        continue;
      }

      RedisStringUtil.setValue(hanaMigrationDO.taskKey(), JsonUtils.toJson(hanaMigrationDO));

      // 触发迁移
      migrationService.migration(hanaMigrationDO);
      migrationResList.add(MigrationRes.builder().taskKey(hanaMigrationDO.taskKey())
          .configJson(JsonUtils.toJson(hanaMigrationDO)).triggerResult("触发成功").build());
      log.info("迁移hana数据脚本已经触发,{}", JsonUtils.toJson(hanaMigrationDO));
    }

    return ResponseBase.success(migrationResList);
  }



  /**
   * {
   * 	"hanaMigrationId": 889,
   * 	"id": 1,
   * 	"schema": "ynhx_data01"
   * }
   * @param specifyMigrationId
   * @return
   */
  @PostMapping("/specify-archive-id")
  public ResponseBase<Boolean> specifyArchiveId(@RequestBody @Valid SpecifyMigrationId specifyMigrationId) {
    Boolean success = migrationService.specifyArchiveId(specifyMigrationId);
    return ResponseBase.success(success);
  }
  @PostMapping("/specify-archive-batch")
  public ResponseBase<Boolean> specifyArchiveBatch(@RequestBody @Valid SpecifyMigrationBatchReq specifyMigrationBatchReq) {
    Boolean success = migrationService.specifyArchiveBatch(specifyMigrationBatchReq);
    return ResponseBase.success(success);
  }

  @Data
  public static class ReHandleErrorData {
    @NotNull
    private Long errorId;
  }
  @PostMapping("/reHandleError")
  public ResponseBase<Boolean> reHandleError(@RequestBody @Valid ReHandleErrorData reHandleErrorData) {
    Boolean success = migrationService.reHandleError(reHandleErrorData);
    return ResponseBase.success(success);
  }

}
