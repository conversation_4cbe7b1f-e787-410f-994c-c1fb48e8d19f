package com.yxt.order.atom.migration.fix.component;

import static com.yxt.order.atom.migration.constant.MigrationConstant.DELETED_PLATFORM_ERROR_REPEATED;
import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildOrderExistsQuery;
import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildRefundOrderExistsQuery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.dao.OfflineOrderPlatformErrorDO;
import com.yxt.order.atom.migration.dao.OfflineOrderPlatformErrorDOMapper;
import com.yxt.order.atom.migration.dao.OfflineOrderPlatformErrorDORepository;
import com.yxt.order.atom.migration.dao.enums.MigrationOrderType;
import com.yxt.order.atom.migration.req.OfflineOrderPlatformCheckReq;
import com.yxt.order.atom.migration.service.OrderDataRelationComponent;
import com.yxt.order.atom.migration.service.dto.MigrationExtend;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.repair.dto.StartEndId;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2025/3/14 19:32
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class OfflineOrderPlatformCheckDataCollect {

  @Value("${hanaMigrationServiceLogError:false}")
  private Boolean hanaMigrationServiceLogError;

  // sql里面没有根据金额来判断,所以会同时处理正单和退单
  private static final List<Integer> MIGRATION_SUCCESS_LIST = Lists.newArrayList(1,
      2); // 1	迁移成功 , 2-已存在 https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=57799101

  @Resource
  private OrderDataRelationComponent orderDataRelationComponent;

  @Resource
  private OfflineOrderPlatformErrorDORepository offlineOrderPlatformErrorDORepository;

  @Resource
  private OfflineOrderPlatformErrorDOMapper offlineOrderPlatformErrorDOMapper;


  @Resource
  private OfflineOrderRepository offlineOrderRepository;


  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private HanaMigrationMapper hanaMigrationMapper;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  public void offlineOrderPlatformCheck(OfflineOrderPlatformCheckReq req) {
    String targetSchema = req.getTargetSchema();

    StartEndId startEndId = new StartEndId();
    startEndId.setStartId(req.getStartId());
    startEndId.setEndId(req.getEndId());

    while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
      Long startId = startEndId.getStartId();
      Long endId = startEndId.getStartId() + keChuanTotalAmountDataGetLimit;

      List<HanaOrderInfo> hanaOrderInfoList = hanaMigrationMapper.queryNeedFixedHanaOrder(
          targetSchema, startId, endId, MIGRATION_SUCCESS_LIST/*, migrateSort*/);

      if (CollectionUtils.isEmpty(hanaOrderInfoList)) {
        refreshStartId(startEndId);
        continue;
      }

      for (HanaOrderInfo hanaOrderInfo : hanaOrderInfoList) {
        try {

          // 1. 先判断长度是否是16位,海典固定长度是16位
          String orderIdStr = String.valueOf(hanaOrderInfo.getOtherOrderId());
          if (orderIdStr.length() != 16) { // 海典的单号固定是16位
            continue;
          }

          // 2. 只处理指定状态的数据
          if (!MIGRATION_SUCCESS_LIST.contains(
              hanaOrderInfo.getMigration())) { // 再判断一遍,如果不是迁移成功的则不处理
            continue;
          }

          String extendJson = hanaOrderInfo.getExtendJson();
          if (StringUtils.isEmpty(extendJson)) {
            continue;
          }

          MigrationExtend extend = JsonUtils.toObject(extendJson,
              new TypeReference<MigrationExtend>() {
              });
          String orderNo = extend.getOrderNo();
          String refundNo = extend.getRefundNo();
          if (!StringUtils.isEmpty(orderNo)) {
            checkHanaOrderPlatform(hanaOrderInfo, orderNo, targetSchema);
          } else if (!StringUtils.isEmpty(refundNo)) {
            checkHanaRefundOrderPlatform(hanaOrderInfo, refundNo, targetSchema);
          }
        } catch (Exception e) {
          if (hanaMigrationServiceLogError) {
            log.warn("OfflineOrderPlatformCheckData warn message", e);
          }
        }
      }
      // 刷新起始Id
      refreshStartId(startEndId);
    }


  }


  private void refreshStartId(StartEndId startEndId) {
    long startId = startEndId.getStartId() + keChuanTotalAmountDataGetLimit;
    log.info("refreshStartId {}", startId);
    startEndId.setStartId(startId);
  }


  public void checkHanaOrderPlatform(HanaOrderInfo hanaOrderInfo, String orderNo,
      String targetSchema) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<>();
      query.eq(OfflineOrderDO::getOrderNo, orderNo);
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(query);
      if (Objects.isNull(offlineOrderDO)) {
        return; // 查不到,就是被清除了,不用处理
      }

      // 如果不是迁移订单,则不处理
      if (!Boolean.TRUE.toString().equals(offlineOrderDO.getMigration())) {
        return;
      }

      // XF_TXSERIAL是16位,但不是海典【在一开始就判断了】
      if (!ThirdPlatformCodeEnum.HAIDIAN.name().equals(offlineOrderDO.getThirdPlatformCode())) {
        // 记录关系
        orderDataRelationComponent.orderRelation(orderNo, targetSchema, hanaOrderInfo);

        // 入库
        OfflineOrderPlatformErrorDO entity = new OfflineOrderPlatformErrorDO();
        entity.setOrderType(MigrationOrderType.ORDER.name());
        entity.setBusinessNo(orderNo);
        entity.setStoreCode(offlineOrderDO.getStoreCode());
        entity.setThirdPlatformCode(offlineOrderDO.getThirdPlatformCode());
        entity.setThirdBusinessNo(offlineOrderDO.getThirdOrderNo());
        entity.setCreated(offlineOrderDO.getCreated());
        entity.setShardingNo(ShardingHelper.getTableIndexByNo(orderNo));
        entity.setNote(Strings.EMPTY);
        entity.setNeedDeleted(Boolean.FALSE.toString());
        entity.setDeleteSuccess(Strings.EMPTY);
        entity.setTargetSchema(targetSchema);
        entity.setHandId(hanaOrderInfo.getId());
        offlineOrderPlatformErrorDORepository.insert(entity);
      }


    }
  }

  public void checkHanaRefundOrderPlatform(HanaOrderInfo hanaOrderInfo, String refundNo,
      String targetSchema) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<>();
      query.eq(OfflineRefundOrderDO::getRefundNo, refundNo);
      OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(query);
      if (Objects.isNull(offlineRefundOrderDO)) {
        return; // 查不到,就是被清除了,不用处理
      }

      // 如果不是迁移订单,则不处理
      if (!Boolean.TRUE.toString().equals(offlineRefundOrderDO.getMigration())) {
        return;
      }

      // XF_TXSERIAL是16位,但不是海典【在一开始就判断了】
      if (!ThirdPlatformCodeEnum.HAIDIAN.name()
          .equals(offlineRefundOrderDO.getThirdPlatformCode())) {
        // 记录关系
        orderDataRelationComponent.refundRelation(refundNo, targetSchema, hanaOrderInfo);

        // 入库
        OfflineOrderPlatformErrorDO entity = new OfflineOrderPlatformErrorDO();
        entity.setOrderType(MigrationOrderType.REFUND.name());
        entity.setBusinessNo(refundNo);
        entity.setStoreCode(offlineRefundOrderDO.getStoreCode());
        entity.setThirdPlatformCode(offlineRefundOrderDO.getThirdPlatformCode());
        entity.setThirdBusinessNo(offlineRefundOrderDO.getThirdRefundNo());
        entity.setCreated(offlineRefundOrderDO.getCreated());
        entity.setShardingNo(ShardingHelper.getTableIndexByNo(refundNo));
        entity.setNote(Strings.EMPTY);
        entity.setNeedDeleted(Boolean.FALSE.toString());
        entity.setDeleteSuccess(Strings.EMPTY);
        entity.setTargetSchema(targetSchema);
        entity.setHandId(hanaOrderInfo.getId());
        offlineOrderPlatformErrorDORepository.insert(entity);

      }


    }
  }


  public void offlineOrderPlatformDataErrorHandle() {

    LambdaQueryWrapper<OfflineOrderPlatformErrorDO> all = new LambdaQueryWrapper<>();
    Integer count = offlineOrderPlatformErrorDOMapper.selectCount(all);
    if (count > 2000) {
      throw new RuntimeException("数量太多,请分页处理");
    }

    List<OfflineOrderPlatformErrorDO> list = offlineOrderPlatformErrorDOMapper.selectList(all);

    // 正确的平台编码
    String correctThirdPlatformCode = ThirdPlatformCodeEnum.HAIDIAN.name();

    for (OfflineOrderPlatformErrorDO entity : list) {
      if(Boolean.TRUE.toString().equals(entity.getDeleteSuccess())){
        continue;
      }

      HanaOrderInfo hanaOrderInfo = hanaMigrationMapper.queryMigrationOrderById(entity.getHandId(),
          entity.getTargetSchema());
      if (Objects.isNull(hanaOrderInfo)) {
        entity.setNote("查不到hana数据");
        offlineOrderPlatformErrorDORepository.updateById(entity);
        continue;
      }

      // 正确的三方业务单号
      String correctThirdBusinessNo = String.valueOf(hanaOrderInfo.getOtherOrderId());

      // 再判断下
      if (correctThirdBusinessNo.length() != 16) { // 海典的单号固定是16位
        entity.setNote(correctThirdBusinessNo+" 不是16位");
        offlineOrderPlatformErrorDORepository.updateById(entity);
        continue;
      }

      String orderType = entity.getOrderType();
      if (MigrationOrderType.ORDER.name().equals(orderType)) {
        handleOrderPlatformError(correctThirdPlatformCode, correctThirdBusinessNo, entity);
      } else if (MigrationOrderType.REFUND.name().equals(orderType)) {
        handleRefundOrderPlatformError(correctThirdPlatformCode, correctThirdBusinessNo, entity);
      }
    }
  }

  private void handleRefundOrderPlatformError(String correctThirdPlatformCode,
      String correctThirdBusinessNo, OfflineOrderPlatformErrorDO entity) {

    String refundNo = entity.getBusinessNo();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);


      OfflineRefundOrderExistsReqDto existsReq = new OfflineRefundOrderExistsReqDto();
      existsReq.setStoreCode(entity.getStoreCode());
      existsReq.setThirdCreated(entity.getCreated());
      existsReq.setThirdRefundNo(correctThirdBusinessNo); // 使用这个单号来处理
      existsReq.setThirdPlatformCode(correctThirdPlatformCode);
      LambdaQueryWrapper<OfflineRefundOrderDO> existsCount = buildRefundOrderExistsQuery(existsReq);
      if (offlineRefundOrderMapper.selectCount(existsCount) == 1) { // 说明重复了
        // 把entity对应订单删除
        LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<>();
        query.eq(OfflineRefundOrderDO::getRefundNo, refundNo);
        OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(query);
        if (Objects.isNull(offlineRefundOrderDO)) {
          return; // 查不到,就是被清除了,不用处理
        }

        // 如果不是迁移订单,则不处理
        if (!Boolean.TRUE.toString().equals(offlineRefundOrderDO.getMigration())) {
          return;
        }

        // 错误的数据是科传,如果不是科传,则不处理。 目的是要将科传处理为海典
        if (!ThirdPlatformCodeEnum.KE_CHUAN.name().equals(offlineRefundOrderDO.getThirdPlatformCode())) {
          return;
        }

        offlineOrderRepository.deletedOfflineRefundOrder(offlineRefundOrderDO, DELETED_PLATFORM_ERROR_REPEATED,
            Boolean.TRUE);

        entity.setNeedDeleted(Boolean.TRUE.toString());
        entity.setDeleteSuccess(Boolean.TRUE.toString());
        entity.setNote(Strings.EMPTY);
        offlineOrderPlatformErrorDORepository.updateById(entity);
      }
    }
  }


  private void handleOrderPlatformError(String correctThirdPlatformCode,
      String correctThirdBusinessNo, OfflineOrderPlatformErrorDO entity) {
    String orderNo = entity.getBusinessNo();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      OfflineOrderExistsReqDto existReq = new OfflineOrderExistsReqDto();
      existReq.setStoreCode(entity.getStoreCode());
      existReq.setThirdCreated(entity.getCreated());

      existReq.setThirdOrderNo(correctThirdBusinessNo);
      existReq.setThirdPlatformCode(correctThirdPlatformCode);
      LambdaQueryWrapper<OfflineOrderDO> existCount = buildOrderExistsQuery(existReq);
      if (offlineOrderMapper.selectCount(existCount) == 1) { // 说明重复了
        // 把entity对应订单删除
        LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<>();
        query.eq(OfflineOrderDO::getOrderNo, orderNo);
        OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(query);
        if (Objects.isNull(offlineOrderDO)) {
          return; // 查不到,就是被清除了,不用处理
        }

        // 如果不是迁移订单,则不处理
        if (!Boolean.TRUE.toString().equals(offlineOrderDO.getMigration())) {
          return;
        }

        // 错误的数据是科传,如果不是科传,则不处理。 目的是要将科传处理为海典
        if (!ThirdPlatformCodeEnum.KE_CHUAN.name().equals(offlineOrderDO.getThirdPlatformCode())) {
          return;
        }

        offlineOrderRepository.deletedOfflineOrder(offlineOrderDO, DELETED_PLATFORM_ERROR_REPEATED,
            Boolean.TRUE);

        entity.setNeedDeleted(Boolean.TRUE.toString());
        entity.setDeleteSuccess(Boolean.TRUE.toString());
        entity.setNote(Strings.EMPTY);
        offlineOrderPlatformErrorDORepository.updateById(entity);
      }
    }
  }
}
