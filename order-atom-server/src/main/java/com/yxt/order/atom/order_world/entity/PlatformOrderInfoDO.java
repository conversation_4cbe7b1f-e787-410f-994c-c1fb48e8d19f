package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName("platform_order_info")
public class PlatformOrderInfoDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 所属机构名称
   */
  private String organizationName;

  /**
   * 平台名称
   */
  private String thirdPlatformName;

  /**
   * 平台机构编码
   */
  private String thirdOrganizationCode;

  /**
   * 平台订单状态
   */
  private String thirdOrderStatus;

  /**
   * 第三方平台订单号(主)
   */
  private String parentThirdOrderNo;

  /**
   * 交易场景 online:代表线上交易 ,offline:代表线下交易
   */
  private String transactionChannel;

  /**
   * 业务类型 O2O、B2C、B2B
   */
  private String businessType;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  private String launchOrganizationCode;

  /**
   * 发起方所属机构名称,仅B2B场景有值
   */
  private String launchOrganizationName;

  /**
   * 发起人id，目前仅B2B有值
   */
  private String launchUserId;

  /**
   * 线上店铺编码
   */
  private String onlineStoreCode;

  /**
   * 线上店铺名称
   */
  private String onlineStoreName;

  /**
   * 来源业务线 如POS,ASSIST
   */
  private String sourceBizCode;

  /**
   * 来源场景
   */
  private String sourceScene;

  /**
   * 来源渠道,待产品定义
   */
  private String sourceChannel;

  /**
   * 来源端 如 PC,APP,POS
   */
  private String sourceDevice;

  /**
   * 支付时间
   */
  private Date payTime;

  /**
   * 完成时间
   */
  private Date completeTime;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 优惠券核销流水号(单级别)
   */
  private String serialNo;

  /**
   * 是否参加促销 TRUE、FALSE
   */
  private String isOnPromotion;

  /**
   * 是否参加预约单 TRUE、FALSE  
   */
  private String isOnBooking;

  /**
   * 预约单标记 TRUE、FALSE 
   */
  private String bookingFlag;

  /**
   * 仅预约单有值,预约送达开始时间
   */
  private Date bookingTimeStart;

  /**
   * 仅预约单有值,预约送达结束时间
   */
  private Date bookingTimeEnd;

  /**
   * 门店直营加盟类型 DIRECT_SALES-直营 JOIN - 加盟
   */
  private String storeDirectJoinType;

  /**
   * 是否起效 1-起效 -1-未起效  
   */
  private Long isValid;

  /**
   * 平台创建日
   */
  private String createdDay;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

}
