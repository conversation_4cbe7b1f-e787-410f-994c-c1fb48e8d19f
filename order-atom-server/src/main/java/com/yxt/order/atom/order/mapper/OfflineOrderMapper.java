package com.yxt.order.atom.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.consistency.EfficientParam;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface OfflineOrderMapper extends BaseMapper<OfflineOrderDO> {

  Long selectMaxId(@Param("flashParam") FlashParam flashParam);

  Long selectMinId(@Param("flashParam") FlashParam flashParam);

  @Select("select max(id) from offline_order where created_time >= #{param.startDate} and created_time <= #{param.endDate}")
  Long selectEfficientCountMaxId(@Param("param")EfficientParam param);

  @Select("select min(id) from offline_order where created_time >= #{param.startDate} and created_time <= #{param.endDate}")
  Long selectEfficientCountMinId(@Param("param")EfficientParam param);

  void deleteAll0(@Param("orderNo")String orderNo);
  void deleteAll1(@Param("orderNo")String orderNo);
  void deleteAll2(@Param("orderNo")String orderNo);
  void deleteAll3(@Param("orderNo")String orderNo);
  void deleteAll4(@Param("orderNo")String orderNo);
  void deleteAll5(@Param("orderNo")String orderNo);
  void deleteAll6(@Param("orderNo")String orderNo);
  void deleteAll7(@Param("orderNo")String orderNo);
  void deleteAll8(@Param("orderNo")String orderNo);
  void deleteAll9(@Param("orderNo")String orderNo);

}