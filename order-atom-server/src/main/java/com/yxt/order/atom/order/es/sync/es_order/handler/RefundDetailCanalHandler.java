package com.yxt.order.atom.order.es.sync.es_order.handler;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.es.sync.data.CanalRefundDetail;
import com.yxt.order.atom.order.es.sync.data.CanalRefundDetail.RefundDetail;
import com.yxt.order.atom.order.es.sync.es_order.flash.EsOrderFlashRefundOrder;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:21
 * @email: <EMAIL>
 */
@Component
public class RefundDetailCanalHandler extends
    AbstractCanalHandler<CanalRefundDetail,EsOrderIndexModel> {

  public RefundDetailCanalHandler() {
    super(CanalRefundDetail.class);
  }

  @Resource
  private EsOrderFlashRefundOrder esOrderFlashRefundOrder;

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.REFUND_ORDER_DETAIL);
  }

  @Override
  protected List<EsOrderIndexModel> assemble() {
    for (RefundDetail refundDetail : getData().getData()) {
      if (StringUtils.isEmpty(refundDetail.getRefundNo())) {
        continue;
      }

      FlashParam param = new FlashParam();
      param.setNoList(Lists.newArrayList(refundDetail.getRefundNo()));
      esOrderFlashRefundOrder.startFlush(param);
    }
    return Lists.newArrayList();
  }


}
