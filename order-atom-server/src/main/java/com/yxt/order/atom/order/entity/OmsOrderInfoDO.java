package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("oms_order_info")
public class OmsOrderInfoDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long orderNo;

    private Long omsOrderNo;

    private Long omsShipNo;

    private Integer orderStatus;

    private String warehouseId;

    private String warehouseName;

    private String expressName;

    private Integer expressId;

    private String expressNumber;

    private String remark;

    private Date createTime;

    private String creator;

    private Date modifyTime;

    private Integer orderType;

    private Integer splitStatus;

    private Date shipTime;

    private Date auditTime;

    private Integer isRefund;

    private String shipOperatorId;

    private String shipOperatorName;

    private String auditOperatorId;

    private String auditOperatorName;

    private String exOperatorId;

    private String exOperatorName;

    private String billOperatorId;

    private String billOperatorName;

    private Date billTime;

    private Date exOperatorTime;

    private Date completeTime;

    private Date cancelTime;

    private Integer exStatus;

    private Integer erpStatus;

    private String erpDeliverNo;

    private String erpSaleNo;

    private Integer interceptStatus;

    private String warehouseCode;

    private String exReason;

    private Integer isPostFeeOrder;

    private Integer joinWms;

    private Integer shipStatus;

    private String thirdPlatformCode;

    private String thirdOrderNo;

    private String merCode;

    private String clientCode;

    private String onlineStoreCode;

    private String onlineStoreName;

    private String organizationCode;

    private String organizationName;

    private Integer isPrescription;

    private Date payTime;

    private Date created;

    private String buyerName;

    private Long clientConfId;

    private String needInvoice;

    private String buyerMessage;

    private String sellerRemark;

    private String sheetStatus;

    private Integer warehouseType;

    private String supplierCode;

    private String o2oClientCode;

    private String o2oShopId;

    private Integer sendorderPrintNum;

    private Integer goodsQty;

    private Integer goodsCategoryQty;

    private Integer version;

    private String buyerId;

    private String erpCodeList;

    private Integer seq;

    private String erpErrorCode;

    private Long virtualMergeNo;

    private Boolean shippedStatus;

    private String spreadStoreCode;

    private Integer orderOwnerType;

    private String memberNo;

    private String stockState;

    private String platform;

    private String subBizType;

    private String vipLevel;

    private Integer erpAuditStatus;

    private Integer logisticsBackStatus;

    private Integer settlementStatus;

    private Long deleted;

    private Integer isProcurementErp;

    private Long procurementNo;

    private String tag;

    private String extendInfo;

}