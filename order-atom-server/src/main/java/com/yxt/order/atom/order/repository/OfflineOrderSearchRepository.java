package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.order.entity.OfflineOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineOrderPayDO;
import com.yxt.order.atom.order.entity.OfflineOrderPrescriptionDO;
import com.yxt.order.atom.order.entity.OfflineOrderUserDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderPayDO;
import java.util.List;

public interface OfflineOrderSearchRepository {

  OfflineOrderOrganizationDO getOfflineOrderOrganization(String orderNo);

  OfflineOrderCashierDeskDO getOfflineOrderCashierDesk(String orderNo);

  OfflineOrderUserDO getOfflineOrderUser(String orderNo);

  List<OfflineOrderDetailDO> getOfflineOrderDetail(String orderNo);

  List<OfflineOrderDetailPickDO> getOfflineOrderDetailPick(String orderNo, List<String> orderDetailNoList);

  OfflineOrderPrescriptionDO getOfflineOrderPrescription(String orderNo);

  List<OfflineOrderPayDO> getOfflineOrderPay(String orderNo);

  OfflineOrderMedInsSettleDO getOfflineOrderMedInsSettle(String orderNo);

  OfflineRefundOrderOrganizationDO getOfflineRefundOrderOrganization(String refundNo);

  OfflineRefundOrderCashierDeskDO getOfflineRefundOrderCashierDesk(String refundNo);

  List<OfflineRefundOrderDetailDO> getOfflineRefundOrderDetail(String refundNo);

  OfflineRefundOrderMedInsSettleDO getOfflineRefundOrderMedInsSettle(String refundNo);

  List<OfflineRefundOrderPayDO> getOfflineRefundOrderPay(String refundNo);
}
