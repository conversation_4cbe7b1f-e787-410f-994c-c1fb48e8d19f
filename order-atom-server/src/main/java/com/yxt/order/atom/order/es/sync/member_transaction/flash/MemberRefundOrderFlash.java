package com.yxt.order.atom.order.es.sync.member_transaction.flash;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.MemberTransaction;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;
import com.yxt.order.atom.order.es.sync.member_transaction.handler.MemberRefundOrderTransactionHandler;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/13 10:22
 */
@Component
public class MemberRefundOrderFlash extends AbstractFlash<RefundOrderDO, RefundOrder, MemberTransaction> {

  @Value("${memberTransactionFlashLimit:2000}")
  private Integer memberTransactionFlashLimit;

  @Resource
  private MemberRefundOrderTransactionHandler memberRefundOrderTransactionHandler;
  @Resource
  private RefundOrderMapper refundOrderMapper;

  @Override
  protected Long queryCursorStartId() {
    return refundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return refundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<RefundOrderDO> getSourceList() {
    LambdaQueryWrapper<RefundOrderDO> query = FlashQueryWrapper.refundOrderFlashQuery(getFlashParam(),defaultLimit());
    return refundOrderMapper.selectList(query);
  }

  @Override
  protected List<RefundOrder> assembleTargetData(List<RefundOrderDO> refundOrderDOS) {
    return refundOrderDOS.stream().map(DoToCanalDtoWrapper::getRefundOrder).collect(Collectors.toList());
  }



  @Override
  protected void flash(List<RefundOrder> refundOrders) {
    CanalRefundOrder canalRefundOrder = new CanalRefundOrder();
    canalRefundOrder.setData(refundOrders);
    memberRefundOrderTransactionHandler.manualFlash(canalRefundOrder);
  }

  @Override
  protected Integer defaultLimit() {
    return memberTransactionFlashLimit;
  }
}
