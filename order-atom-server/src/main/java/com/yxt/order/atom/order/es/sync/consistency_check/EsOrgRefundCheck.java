package com.yxt.order.atom.order.es.sync.consistency_check;

import cn.hutool.core.date.DateUtil;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.es.doc.EsOrgRefund;
import com.yxt.order.atom.order.es.mapper.EsOrgRefundMapper;
import com.yxt.order.atom.order.es.sync.AbstractOrderConsistencyCheck;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsOrgOfflineRefundEfficientCount;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsOrgOnlineRefundEfficientCount;
import com.yxt.order.atom.order.es.sync.org_order.flash.OrgOfflineRefundFlash;
import com.yxt.order.atom.order.es.sync.org_order.flash.OrgOnlineRefundFlash;
import javax.annotation.Resource;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

@Component
public class EsOrgRefundCheck extends AbstractOrderConsistencyCheck {

  @Resource
  private EsOrgOfflineRefundEfficientCount esOrgOfflineRefundEfficientCount;

  @Resource
  private EsOrgOnlineRefundEfficientCount esOrgOnlineRefundEfficientCount;

  @Resource
  private EsOrgRefundMapper esOrgRefundMapper;

  @Resource
  private OrgOfflineRefundFlash orgOfflineRefundFlash;

  @Resource
  private OrgOnlineRefundFlash orgOnlineRefundFlash;

  @Override
  protected Long dbDscloudCount() {
    return esOrgOnlineRefundEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
  }

  @Override
  protected Long dbDscloudOfflineCount() {
    return esOrgOfflineRefundEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
  }

  @Override
  protected Long esCount() {
    LambdaEsQueryWrapper<EsOrgRefund> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.ge(EsOrgRefund::getCreateTime, DateUtil.formatDateTime(getStartDate()));
    queryWrapper.le(EsOrgRefund::getCreateTime, DateUtil.formatDateTime(getEndDate()));
    return esOrgRefundMapper.selectCount(queryWrapper);
  }

  @Override
  protected ConsistencyNotify consistencyNotify() {
    return ConsistencyNotify.ORG_REFUND;
  }

  @Override
  protected void compensate() {
    FlashParam flashParam = getFlashParam();
    orgOfflineRefundFlash.startFlush(flashParam);
    orgOnlineRefundFlash.startFlush(flashParam);
  }


  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }
}
