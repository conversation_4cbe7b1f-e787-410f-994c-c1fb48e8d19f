package com.yxt.order.atom.order.repository.abstracts.refund.insert;

import cn.hutool.core.collection.CollUtil;
import com.yxt.order.atom.order.entity.RefundLogDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundInsert;
import com.yxt.order.atom.order.repository.batch.RefundBatchRepository;
import com.yxt.order.atom.order.repository.batch.RefundLogBatchRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RefundLogInsert extends AbstractRefundInsert<List<RefundLogDO>> {

  @Autowired
  private RefundLogBatchRepository refundLogBatchRepository;

  @Override
  protected Boolean canInsert() {
    return CollUtil.isNotEmpty(data());
  }

  @Override
  protected Integer insert(List<RefundLogDO> refundLogList) {
    return refundLogBatchRepository.saveBatch(refundLogList) ? refundLogList.size() : 0;
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<RefundLogDO> data() {
    return this.saveData.getRefundLogList();
  }
}
