package com.yxt.order.atom.order.es.doc;

import java.util.Date;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * 会员退单
 * @author: moatkon
 * @time: 2024/12/9 10:29
 */
@Data
@Settings(shardsNum = 12) // 退单按照正单的1/3评估,12个分片
@IndexName(value = "es_member_refund_order",keepGlobalPrefix = true,aliasName = "alias_es_member_refund_order")
public class EsMemberRefundOrder {


  @IndexId(type = IdType.CUSTOMIZE)
  private String id;

  /**
   * 系统单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundNo;

  /**
   * 会员ID (心云)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userId;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeType;

  /**
   * 门店
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeCode;

  /**
   * 退款类型, PART_REFUND-部分退款，REFUND_AMOUNT-全额退款
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundType;

  /**
   * 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer refundStatus;

  /**
   * 退款时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date created;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderSource;

  /**
   * 系统单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * 售后单类型 AFTER_SALE_AMOUNT-退款 、AFTER_SALE_GOODS-退货 、AFTER_SALE_AMOUNT_GOODS-退货退款
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String afterSaleType;

  /**
   * 创建时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  /**
   * 会员编码(唯一值)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userCardNo;



  /**
   * 三方退单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdRefundNo;

  /**
   * 三方订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String platformCode;

  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsMemberRefundOrderDetail.class)
  private List<EsMemberRefundOrderDetail> esMemberRefundOrderDetailList;


}
