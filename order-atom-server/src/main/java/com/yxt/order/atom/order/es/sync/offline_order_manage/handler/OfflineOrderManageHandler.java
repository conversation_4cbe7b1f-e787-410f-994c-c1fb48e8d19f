package com.yxt.order.atom.order.es.sync.offline_order_manage.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_ORDER_REGEX;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.offline_order_manage.model.OfflineOrderManageModel;
import com.yxt.order.atom.order.es.sync.offline_order_manage.model.OfflineOrderManageModel.OfflineOrderManageDetailModel;
import com.yxt.order.atom.order.mapper.dto.OrganizationInfoDto;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/4/1 10:54
 */
@Component
public class OfflineOrderManageHandler extends
    AbstractCanalHandler<CanalOfflineOrder, OfflineOrderManageModel> {

  @Resource
  protected SyncComponent syncComponent;

  public OfflineOrderManageHandler() {
    super(CanalOfflineOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();

    return Database.DSCLOUD_OFFLINE.equals(database) && Table.tableRegex(OFFLINE_ORDER_REGEX,
        table);
  }

  @Override
  protected List<OfflineOrderManageModel> assemble() {
    List<OfflineOrder> offlineOrderList = getData().getData();
    if (CollectionUtils.isEmpty(offlineOrderList)) {
      return Lists.newArrayList();
    }

    return offlineOrderList.stream()
        .filter(this::efficientData)
        .map(offlineOrder -> {
          OfflineOrderManageModel memberOrderModel = new OfflineOrderManageModel();
          memberOrderModel.setOrderNo(offlineOrder.getOrderNo());
          memberOrderModel.setThirdPlatformCode(offlineOrder.getThirdPlatformCode());
          memberOrderModel.setThirdOrderNo(offlineOrder.getThirdOrderNo());
          memberOrderModel.setStoreCode(offlineOrder.getStoreCode());
          memberOrderModel.setCreated(offlineOrder.getCreated());
          memberOrderModel.setActualPayAmount(offlineOrder.getActualPayAmount());

          List<OfflineOrderDetailDO> detailList = syncComponent.getOfflineOrderDetailByOrderNo(
              offlineOrder.getOrderNo(), offlineOrder.getNeedRoute());
          if (!CollectionUtils.isEmpty(detailList)) {
            memberOrderModel.setOfflineOrderManageDetailModelList(detailList.stream().map(item -> {
              OfflineOrderManageDetailModel detailModel = new OfflineOrderManageDetailModel();
              detailModel.setErpCode(item.getErpCode());
              return detailModel;
            }).collect(Collectors.toList()));
          }

          OrganizationInfoDto organization = syncComponent.getOrganizationByOrderNo(
              offlineOrder.getOrderNo(), offlineOrder.getNeedRoute());
          Optional.ofNullable(organization).ifPresent(organizationInfoDto -> {
            memberOrderModel.setCompanyCode(organization.getCompanyCode());
          });

          return memberOrderModel;
        }).collect(Collectors.toList());
  }

  public Boolean efficientData(OfflineOrder offlineOrder) {
    return
        !offlineOrder.migrateOrder()
            && ThirdPlatformCodeEnum.isValid(offlineOrder.getThirdPlatformCode())
        ;
  }
}