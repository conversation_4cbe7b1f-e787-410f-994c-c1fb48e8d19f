package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderDetailCommodityCostPriceDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OrderDetailCommodityCostPriceBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:58
 * @email: <EMAIL>
 */
@Component
public class OrderDetailCommodityCostPriceInsert extends
    AbstractInsert<List<OrderDetailCommodityCostPriceDO>> {

  @Resource
  private OrderDetailCommodityCostPriceBatchRepository orderDetailCommodityCostPriceBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderDetailCommodityCostPriceList());
  }

  @Override
  protected Integer insert(List<OrderDetailCommodityCostPriceDO> list) {
    return orderDetailCommodityCostPriceBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderDetailCommodityCostPriceDO> data() {
    return saveDataOptional.getOrderDetailCommodityCostPriceList();
  }
}
