package com.yxt.order.atom.order.es.sync.operate.offline_order_manage;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.order.atom.order.es.doc.EsOfflineRefundOrderManage;
import com.yxt.order.atom.order.es.mapper.EsOfflineRefundOrderManageMapper;
import com.yxt.order.atom.order.es.sync.offline_order_manage.model.OfflineRefundOrderManageModel;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/10 15:03
 */
@Component
@Slf4j
public class OfflineRefundOrderManageModelOperate extends
    AbstractEsOperate<OfflineRefundOrderManageModel> {

  @Resource
  private EsOfflineRefundOrderManageMapper esOfflineRefundOrderManageMapper;

  public OfflineRefundOrderManageModelOperate() {
    super(OfflineRefundOrderManageModel.class);
  }

  @Override
  protected Boolean exec() {
    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }
    return false;
  }


  private Boolean delete(OfflineRefundOrderManageModel memberRefundOrderModel) {
    return esOfflineRefundOrderManageMapper.deleteById(memberRefundOrderModel.defineId()) > 0;
  }

  private Boolean save(OfflineRefundOrderManageModel memberRefundOrderModel) {
    EsOfflineRefundOrderManage esMemberRefundOrder = memberRefundOrderModel.create();

    LambdaEsQueryWrapper<EsOfflineRefundOrderManage> memberRefundOrderQuery = new LambdaEsQueryWrapper<>();
    memberRefundOrderQuery.eq(EsOfflineRefundOrderManage::getId, memberRefundOrderModel.defineId());
    Long count = esOfflineRefundOrderManageMapper.selectCount(memberRefundOrderQuery);
    if (count > 0) {
      return esOfflineRefundOrderManageMapper.updateById(esMemberRefundOrder) > 0;
    } else {
      return esOfflineRefundOrderManageMapper.insert(esMemberRefundOrder) > 0;
    }
  }
}
