package com.yxt.order.atom.migration.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.order.common.utils.OrderJsonUtils;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: moatkon
 * @time: 2024/12/19 15:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MigrationExtend {
  private String orderNo;
  private String refundNo;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date thirdCreatedTime; // 迁移按照三方订单创建时间来

  private String remark; // 备注

  public String json(){
    return OrderJsonUtils.toJson(this);
  }
}
