package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.mapper.RefundDetailMapper;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import org.springframework.stereotype.Repository;


@Repository
public class RefundDetailBatchRepository extends
    ServiceImpl<RefundDetailMapper, RefundDetailDO> {

}

