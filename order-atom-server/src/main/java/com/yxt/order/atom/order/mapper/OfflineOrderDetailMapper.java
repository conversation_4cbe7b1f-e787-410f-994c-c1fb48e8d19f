package com.yxt.order.atom.order.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public interface OfflineOrderDetailMapper extends BaseMapper<OfflineOrderDetailDO> {

  @Select("select min(id) from offline_order_detail where created_time >= #{flashParam.start} and created_time <= #{flashParam.end} ")
  Long selectMinId(@Param("flashParam") FlashParam flashParam);

  @Select("select max(id) from offline_order_detail where created_time >= #{flashParam.start} and created_time <= #{flashParam.end} ")
  Long selectMaxId(@Param("flashParam") FlashParam flashParam);

}