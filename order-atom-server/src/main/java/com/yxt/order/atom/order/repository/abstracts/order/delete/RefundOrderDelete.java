package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class RefundOrderDelete extends AbstractDelete {

  @Resource
  private RefundOrderMapper refundOrderMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getRefundOrder();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<RefundOrderDO> query = new LambdaQueryWrapper<>();
    query.eq(RefundOrderDO::getOrderNo, orderNo);
    return refundOrderMapper.delete(query);
  }
}
