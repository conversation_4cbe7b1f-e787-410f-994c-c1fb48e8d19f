package com.yxt.order.atom.order;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.PickRepository;
import com.yxt.order.atom.sdk.online_order.pick.PickAtomCmdApi;
import com.yxt.order.atom.sdk.online_order.pick.PickAtomQryApi;
import com.yxt.order.atom.sdk.online_order.pick.dto.req.InsertPickReqDto;
import com.yxt.starter.controller.AbstractController;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月01日 14:44
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class PickController extends AbstractController implements PickAtomCmdApi,
    PickAtomQryApi {

  @Resource
  private PickRepository pickRepository;


  @Override
  public ResponseBase<Boolean> insertPick(InsertPickReqDto req) {
    return generateSuccess(pickRepository.insertPick(req));
  }
}
