package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 线下单促销
 */
@Data
@TableName("offline_order_promotion")
public class OrderPromotionDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 内部订单号,自己生成
   */
  private String orderNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 促销编码
   */
  private String promotionNo;

  /**
   * 子促销编码
   */
  private String subPromotionNo;

  /**
   * 促销类型
   */
  private String promotionType;

  /**
   * 促销金额
   */
  private BigDecimal promotionAmount;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 拓展字段
   */
  private String extendJson;

  /**
   * ORDER,DETAIL
   */
  private String type;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;
}
