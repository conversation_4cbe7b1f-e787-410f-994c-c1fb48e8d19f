package com.yxt.order.atom.dto;

import com.google.common.collect.Lists;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailTraceDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderOrganizationDO;
import java.util.List;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2024/12/11 15:23
 */
@Data
public class SimpleOfflineRefundOrderDetailDto {
  private OfflineRefundOrderDO offlineRefundOrderDO;
  private List<OfflineRefundOrderDetailDO> offlineRefundOrderDetailDOList;
  private List<OfflineRefundOrderDetailTraceDO> offlineRefundOrderDetailTraceDOList;
  private List<OfflineRefundOrderDetailPickDO> offlineRefundOrderDetailPickDOList;
  // 如果offlineRefundOrderDetailPickDOList为空且orderNo有值,则查正单的明细拣货信息
  private AppendOfflineOrderInfo appendOfflineOrderInfo = AppendOfflineOrderInfo.empty();

  private OfflineRefundOrderOrganizationDO offlineRefundOrderOrganizationDO;
  private OfflineRefundOrderCashierDeskDO offlineRefundOrderCashierDeskDO;


  @Data
  public static class AppendOfflineOrderInfo {
    private List<OfflineOrderDetailDO> offlineOrderDetailDOList;
    private List<OfflineOrderDetailPickEnhance> offlineOrderDetailPickEnhanceList;


    public AppendOfflineOrderInfo() {
      this.offlineOrderDetailDOList = Lists.newArrayList();
      this.offlineOrderDetailPickEnhanceList = Lists.newArrayList();
    }

    public static AppendOfflineOrderInfo empty(){
      return new AppendOfflineOrderInfo();
    }
  }

  @Data
  public static class OfflineOrderDetailPickEnhance extends OfflineOrderDetailPickDO {
    private String rowNo;

    public String rowNoErpCode(){
      return String.format("%s_%s",rowNo,getErpCode());
    }
  }


}
