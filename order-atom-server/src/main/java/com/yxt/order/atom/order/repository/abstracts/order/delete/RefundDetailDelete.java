//package com.yxt.order.atom.order.repository.abstracts.delete;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.yxt.order.atom.order.entity.RefundDetailDO;
//import com.yxt.order.atom.order.mapper.RefundDetailMapper;
//import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
//import javax.annotation.Resource;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR> (moatkon)
// * @date 2024年09月13日 11:52
// * @email: <EMAIL>
// */
//@Component
//public class RefundDetailDelete extends AbstractDelete {
//
//  @Resource
//  private RefundDetailMapper commodityExceptionOrderMapper;
//
//
//  @Override
//  protected Boolean canDelete() {
//    return dto.getRefundOrderDetail();
//  }
//
//  @Override
//protected Integer delete() {
//    LambdaQueryWrapper<RefundDetailDO> query = new LambdaQueryWrapper<>();
//    query.eq(RefundDetailDO::get, orderNo); // 没有单号,就不删
//    commodityExceptionOrderMapper.delete(query);
//  }
//}
