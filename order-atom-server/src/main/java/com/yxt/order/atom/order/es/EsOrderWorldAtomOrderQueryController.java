package com.yxt.order.atom.order.es;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.common.utils.ESSearchUtils;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrder;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldOrderMapper;
import com.yxt.order.atom.order.es.wrapper.EsQueryBuilder;
import com.yxt.order.atom.sdk.order_world.EsOrderWorldAtomOrderQueryApi;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderBaseReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderCountStatisticReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderCountWithConditionReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderPageQueryReq;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldCountStatisticRes;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldCountStatisticRes.EsOrderWorldCountResult;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldOrderInfoRes;
import com.yxt.order.common.es.EsPageDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.biz.PageSerializable;
import org.dromara.easyes.core.biz.SAPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class EsOrderWorldAtomOrderQueryController implements EsOrderWorldAtomOrderQueryApi {


  @Resource
  private EsOrderWorldOrderMapper esOrderWorldOrderMapper;


  @Override
  public ResponseBase<EsOrderWorldCountStatisticRes> orderCountStatistic(EsOrderWorldOrderCountStatisticReq request) {
    LambdaEsQueryWrapper<EsOrderWorldOrder> query = EsQueryBuilder.buildEsQueryForOrderCountStatic(request);
    SearchResponse response = esOrderWorldOrderMapper.search(query);
    //获取聚合结果
    List<EsOrderWorldCountResult> countList = new ArrayList<>();
    if (response.getAggregations() != null) {

      Terms aggregationResult = (Terms) response.getAggregations().asList().get(0);
      countList = aggregationResult.getBuckets().stream()
          .map(bucket -> new EsOrderWorldCountResult(bucket.getKey().toString(), String.valueOf(bucket.getDocCount())))
          .collect(Collectors.toList());
    }
    return ResponseBase.success(new EsOrderWorldCountStatisticRes(countList));
  }

  @Override
  public ResponseBase<EsPageDTO<EsOrderWorldOrderInfoRes>> orderPageQuery(EsOrderWorldOrderPageQueryReq request) {
    EsPageDTO<EsOrderWorldOrderInfoRes> resultPage = new EsPageDTO<>();
    resultPage.setCurrentPage(request.getCurrentPage());
    resultPage.setPageSize(request.getPageSize());
    LambdaEsQueryWrapper<EsOrderWorldOrder> wrapper = EsQueryBuilder.buildEsQueryForOrderPageQuery(request);
    PageSerializable<EsOrderWorldOrder> page = null;
    if (StrUtil.isNotBlank(request.getSearchAfter())) {
      List<Object> searchAfter = ESSearchUtils.parseSearchAfter(request.getSearchAfter());
      SAPageInfo<EsOrderWorldOrder> searchAfterPage = esOrderWorldOrderMapper.searchAfterPage(wrapper, searchAfter, request.getPageSize()
          .intValue());
      resultPage.setTotalPage((long) searchAfterPage.getPages());
      resultPage.setTotalCount(searchAfterPage.getTotal());
      if (CollUtil.isNotEmpty(searchAfterPage.getNextSearchAfter()) && searchAfterPage.getList()
          .size() >= request.getPageSize()) {
        resultPage.setSearchAfter(JSON.toJSONString(searchAfterPage.getNextSearchAfter()));
      }
      page = searchAfterPage;
    } else {
      EsPageInfo<EsOrderWorldOrder> esPageInfo = esOrderWorldOrderMapper.pageQuery(wrapper, request.getCurrentPage()
          .intValue(), request.getPageSize().intValue());
      resultPage.setTotalPage((long) esPageInfo.getPages());
      resultPage.setTotalCount(esPageInfo.getTotal());
      page = esPageInfo;
    }

    if (page.getTotal() <= 0) {
      resultPage.setData(new ArrayList<>(0));
      return ResponseBase.success(resultPage);
    }
    List<EsOrderWorldOrderInfoRes> resultList = page.getList().stream().map(data -> {
      EsOrderWorldOrderInfoRes orderResDTO = BeanUtil.toBean(data, EsOrderWorldOrderInfoRes.class);
      if (StrUtil.isNotBlank(data.getOrderFlags())) {
        orderResDTO.setOrderFlags(StrUtil.split(data.getOrderFlags(), " "));
      }
      if(StrUtil.isNotBlank(data.getOrderTypes())){
        orderResDTO.setOrderTypes(StrUtil.split(data.getOrderTypes(), " "));
      }
      if(StrUtil.isNotBlank(data.getAbnormalType())){
        orderResDTO.setAbnormalType(StrUtil.split(data.getAbnormalType(), " "));
      }
      if(StrUtil.isNotBlank(data.getPayTypes())){
        orderResDTO.setPayTypes(StrUtil.split(data.getPayTypes(), " "));
      }
      return orderResDTO;
    }).collect(Collectors.toList());
    resultPage.setData(resultList);
    return ResponseBase.success(resultPage);
  }

  @Override
  public ResponseBase<EsOrderWorldOrderInfoRes> orderDetailQuery(EsOrderWorldOrderBaseReq request) {
    LambdaEsQueryWrapper<EsOrderWorldOrder> wrapper = EsWrappers.lambdaQuery(EsOrderWorldOrder.class);
    wrapper.eq(EsOrderWorldOrder::getOrderNo, request.getOrderNo());
    EsOrderWorldOrder data = esOrderWorldOrderMapper.selectOne(wrapper);
    if(ObjectUtil.isNull(data)){
      return ResponseBase.success(null);
    }
    EsOrderWorldOrderInfoRes orderResDTO = BeanUtil.toBean(data, EsOrderWorldOrderInfoRes.class);
    if (StrUtil.isNotBlank(data.getOrderFlags())) {
      orderResDTO.setOrderFlags(StrUtil.split(data.getOrderFlags(), " "));
    }
    if(StrUtil.isNotBlank(data.getOrderTypes())){
      orderResDTO.setOrderTypes(StrUtil.split(data.getOrderTypes(), " "));
    }
    if(StrUtil.isNotBlank(data.getAbnormalType())){
      orderResDTO.setAbnormalType(StrUtil.split(data.getAbnormalType(), " "));
    }
    if(StrUtil.isNotBlank(data.getPayTypes())){
      orderResDTO.setPayTypes(StrUtil.split(data.getPayTypes(), " "));
    }
    return ResponseBase.success(orderResDTO);
  }

  @Override
  public ResponseBase<String> orderCountWithCondition(EsOrderWorldOrderCountWithConditionReq request) {
    LambdaEsQueryWrapper<EsOrderWorldOrder> query = EsQueryBuilder.buildEsQueryForOrderCountWithCondition(request);
    return ResponseBase.success(NumberUtil.toStr(esOrderWorldOrderMapper.selectCount(query)));
  }
}
