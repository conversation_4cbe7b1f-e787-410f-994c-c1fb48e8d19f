package com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.consistency.AbstractConsistencyCheckEfficientCount;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.order_world.handler.OrderWorldOrderHandler;
import com.yxt.order.atom.order_world.entity.OrderInfoDO;
import com.yxt.order.atom.order_world.mapper.NewOrderInfoMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class EsOrderWorldOrderEfficientCount extends AbstractConsistencyCheckEfficientCount<OrderInfoDO> {

  @Autowired
  private NewOrderInfoMapper orderInfoMapper;

  @Autowired
  private OrderWorldOrderHandler orderWorldOrderHandler;

  @Override
  protected Long queryCursorStartId() {
    return orderInfoMapper.selectEfficientCountMinId(getParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return orderInfoMapper.selectEfficientCountMaxId(getParam());
  }

  @Override
  protected List<OrderInfoDO> dataList() {
    LambdaQueryWrapper<OrderInfoDO> query = new LambdaQueryWrapper<>();
    query.ge(OrderInfoDO::getId, getParam().getCursorStartId());
    query.lt(OrderInfoDO::getId, getParam().currentCursorEndId(defaultLimit()));
    return orderInfoMapper.selectList(query);
  }


  /**
   * @param list
   * @return
   */
  @Override
  protected Long efficientCount(List<OrderInfoDO> list, Long maximumId) {
    return list.stream().filter(s -> s.getId() <= maximumId)
        .filter(s -> OrderDateUtils.isEfficientDate(getParam().getStartDate(), getParam().getEndDate(), s.getCreatedTime()))
        .map(DoToCanalDtoWrapper::getNewOrder)
        .filter(orderInfo -> orderWorldOrderHandler.efficientData(orderInfo)).count();
  }

}
