package com.yxt.order.atom.job.flash;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.sync.org_order.flash.OrgOrderFlashHandler;
import com.yxt.order.atom.sdk.org_order.req.OrgOrderFlashToEsJobProcessReqDTO;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class OrderToEsFlashHandler {

  @Resource
  private OrgOrderFlashHandler orgOrderFlashHandler;

  @XxlJob("orgOrderFlashDataToEsJob")
  public void orgOrderFlashDataToEsJob() {
    XxlJobHelper.log("orgOrderFlashDataToEsJob start-----");
    try {
      OrgOrderFlashToEsJobProcessReqDTO request = JsonUtils.toObject(XxlJobHelper.getJobParam(), OrgOrderFlashToEsJobProcessReqDTO.class);
      orgOrderFlashHandler.orgOrderFlashDataToEsWithJobProcess(request);
    } catch (Exception e) {
      XxlJobHelper.log(e);
      XxlJobHelper.handleFail("orgOrderFlashDataToEsJob 执行失败-----");
      return;
    }
    XxlJobHelper.log("orgOrderFlashDataToEsJob end-----");
    XxlJobHelper.handleSuccess();
  }

}
