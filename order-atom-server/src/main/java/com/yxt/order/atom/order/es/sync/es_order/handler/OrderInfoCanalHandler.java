package com.yxt.order.atom.order.es.sync.es_order.handler;
import com.google.common.collect.Lists;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.dto.OrderDetailDto;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.es.sync.clean.ExpireDaysConstant;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo.Order;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel.EsOrderItemModel;
import com.yxt.order.atom.order.mapper.SyncEsMapper;
import com.yxt.order.common.exception.DetailNotExistsException;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月20日 14:21
 * @email: <EMAIL>
 */
@Component
public class OrderInfoCanalHandler extends
    AbstractCanalHandler<CanalOrderInfo,EsOrderIndexModel> {

  @Resource
  protected SyncEsMapper syncEsMapper;

  @Resource
  private SyncComponent syncComponent;

  public OrderInfoCanalHandler() {
    super(CanalOrderInfo.class);
  }


  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.ORDER_INFO);
  }

  @Override
  protected List<EsOrderIndexModel> assemble() {

    List<Order> orderList = getData().getData();
    if (CollectionUtils.isEmpty(orderList)) {
      return Lists.newArrayList();
    }

    return orderList.stream()
        .filter(order -> !OrderDateUtils.isExpired(order.getCreated(), ExpireDaysConstant.EsOrderEfficientDays))
        .filter(order -> orderDoneStatus(order.getOrderState()))
        // 过滤userId
        .filter(order -> !StringUtils.isEmpty(syncComponent.queryUserId(order.getMemberNo())))
        .map(order -> {
          EsOrderIndexModel esOrderModel = new EsOrderIndexModel();
          esOrderModel.setOrderNumber(order.getOrderNo());
          esOrderModel.setOrderNo(order.getOrderNo());
          esOrderModel.setOnlineStoreCode(order.getOnlineStoreCode());
          esOrderModel.setOrganizationCode(order.getOrganizationCode());
          esOrderModel.setUserId(syncComponent.queryUserId(order.getMemberNo()));
          esOrderModel.setEsOrderType(EsOrderType.ORDER);
          esOrderModel.setServiceMode(syncComponent.mappingServiceMode(order.getServiceMode()));
          esOrderModel.setEsOrderStatus(mapppingEsOrderStatus(order.getOrderState()));
          esOrderModel.setPayTime(order.getPayTime());
          esOrderModel.setCompleteTime(order.getCompleteTime());
          esOrderModel.setThirdOrderNo(order.getThirdOrderNo());
          esOrderModel.setPlatformCode(order.getThirdPlatformCode());
          esOrderModel.setCreateTime(order.getCreated());
          esOrderModel.setDeleted(order.getDeleted());

          List<OrderDetailDto> orderDetailList = getOrderDetailDtoList(esOrderModel);

          esOrderModel.setEsOrderItemModelList(orderDetailList.stream()
              .filter(detail -> {
                if (EsServiceMode.O2O.equals(esOrderModel.getServiceMode())) {
                  return 10 != detail.getStatus();// REPLACE(10, "已换货")
                } else if (EsServiceMode.B2C.equals(esOrderModel.getServiceMode())) {
                  return 2 != detail.getStatus();// BE_REPLACED(2, "被换货的商品")
                } else {
                  throw new RuntimeException(
                      String.format("服务模式未知,%s", esOrderModel.getServiceMode()));
                }
              })
              .map(detail -> {
                EsOrderItemModel itemModel = new EsOrderItemModel();
                itemModel.setCommodityCode(detail.getCommodityCode());
                itemModel.setCommodityName(detail.getCommodityName());
                itemModel.setCommodityCount(detail.getCommodityCount());
                itemModel.setFiveClass(detail.getFiveClass());
                return itemModel;
              }).collect(Collectors.toList()));

          return esOrderModel;
        }).collect(Collectors.toList());
  }

  @Retryable(value = DetailNotExistsException.class,maxAttempts = 8,backoff = @Backoff(delay = 2000,multiplier = 1.5))
  public List<OrderDetailDto> getOrderDetailDtoList(EsOrderIndexModel esOrderModel) {
    List<OrderDetailDto> orderDetailList = syncEsMapper.selectDetailByOrderNo(Long.valueOf(
        esOrderModel.getOrderNumber()));
    if(CollectionUtils.isEmpty(orderDetailList)){
      String error = String.format("线上单-正单明细不存在,%s", esOrderModel.getOrderNumber());
      throw new DetailNotExistsException(error);
    }

    return orderDetailList;
  }

  private EsOrderStatus mapppingEsOrderStatus(String orderStatus) {
    if (orderDoneStatus(orderStatus)) {
      return EsOrderStatus.DONE;
    }
    return null;
  }

  private boolean orderDoneStatus(String orderStatus) {
    // COMPLETED(100, "已完成",3),
    return "100".equals(orderStatus);

  }


}
