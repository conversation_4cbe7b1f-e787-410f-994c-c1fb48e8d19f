package com.yxt.order.atom.order.repository.abstracts.order;


/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:46
 * @email: <EMAIL>
 */
public abstract class AbstractInsert<T> {

  protected SaveOrderOptionalDO saveDataOptional;

  protected abstract Boolean canInsert();

  protected abstract Integer insert(T t);

  /**
   * 转换成目标对象T
   *
   * @return
   */
  protected abstract T data();


  public Integer exec(SaveOrderOptionalDO req) {

    init(req);

    if (!canInsert()) {
      return 0;
    }

    return insert(data());
  }

  private void init(SaveOrderOptionalDO req) {
    this.saveDataOptional = req;
  }


}
