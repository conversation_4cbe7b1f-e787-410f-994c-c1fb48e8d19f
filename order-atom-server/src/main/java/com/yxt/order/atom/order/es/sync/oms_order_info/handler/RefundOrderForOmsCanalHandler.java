package com.yxt.order.atom.order.es.sync.oms_order_info.handler;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;
import com.yxt.order.atom.order.es.sync.oms_order_info.EsOmsOrderInfoModel;
import com.yxt.order.atom.order.es.sync.oms_order_info.flash.OmsOrderInfoFlash;
import com.yxt.order.atom.order.mapper.OmsOrderInfoMapper;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/11/4 17:50
 */
@Component
@Slf4j
public class RefundOrderForOmsCanalHandler extends
    AbstractCanalHandler<CanalRefundOrder, EsOmsOrderInfoModel> {

  @Resource
  private OmsOrderInfoFlash omsOrderInfoFlash;

  @Resource
  private OmsOrderInfoMapper omsOrderInfoMapper;


  public RefundOrderForOmsCanalHandler() {
    super(CanalRefundOrder.class);
  }


  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.REFUND_ORDER);
  }

  @Override
  protected List<EsOmsOrderInfoModel> assemble() {
    List<RefundOrder> refundOrderList = getData().getData();
    if(CollectionUtils.isEmpty(refundOrderList)){
      return Lists.newArrayList();
    }

    //去掉邮费退款单
    OmsOrderInfoDO omsOrderInfoDO = omsOrderInfoMapper.selectByOmsOrderNo(refundOrderList.get(0).getOmsOrderNo());
    if(omsOrderInfoDO.getIsPostFeeOrder().equals(0)){
      return Lists.newArrayList();
    }

    FlashParam param = new FlashParam();
    param.setNoList(Lists.newArrayList(refundOrderList.get(0).getOmsOrderNo().toString()));
    omsOrderInfoFlash.startFlush(param);
    return Lists.newArrayList();
  }



}
