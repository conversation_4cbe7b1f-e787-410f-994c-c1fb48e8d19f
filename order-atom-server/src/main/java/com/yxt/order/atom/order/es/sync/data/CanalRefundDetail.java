package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalRefundDetail.RefundDetail;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月22日 16:22
 * @email: <EMAIL>
 */
public class CanalRefundDetail extends BaseCanalData<RefundDetail> {

  @Data
  public static class RefundDetail {

    @JsonProperty("refund_no")
    private String refundNo;


  }
}