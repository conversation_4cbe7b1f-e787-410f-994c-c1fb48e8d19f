package com.yxt.order.atom.order.es.doc;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.FieldType;

/**
 * @author: moatkon
 * @time: 2024/11/5 16:51
 */
@Data
public class EsOmsOrderEx {

  /**
   * 异常状态:  0.无异常 1.异常,具体异常查看枚举类
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer exType;

  /**
   * 异常处理结果 0-未处理 1-已处理
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer operateStatus;

}
