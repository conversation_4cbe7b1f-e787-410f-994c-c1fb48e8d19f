package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 转单调拨表
 * @TableName route_allot
 */
@TableName(value ="route_allot")
@Data
public class RouteAllotDO implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 调出门店编码
     */
    private String outStoreCode;

    /**
     * 调出门店名称
     */
    private String outStoreName;

    /**
     * 调入门店编码
     */
    private String inStoreCode;

    /**
     * 调入门店名称
     */
    private String inStoreName;

    /**
     * 发货子公司全路径
     */
    private String orgIdPath;

    /**
     * 下单子公司全路径
     */
    private String sourceOrgIdPath;

    /**
     * 三方订单号
     */
    private String thirdOrderNo;

    /**
     * 三方退款单号
     */
    private String thirdRefundNo;

    /**
     * 系统订单号 区分销售单和退款单
     */
    private Long omsNo;

    /**
     * 关联订单类型 ORDER-销售单 REFUND-退款单
     */
    private String orderType;

    /**
     * 心云拨单号
     */
    private Long omsAllotNo;

    /**
     * pos拨单号
     */
    private String posAllotNo;

    /**
     * 调拨单状态 WAIT-待发送到POS  PROCEED-处理中 FAIL-失败  SUCCESS-成功
     */
    private String allotStatus;

    /**
     * 调拨成功时间
     */
    private Date allotTime;

    /**
     * 失败类型 BATCH_NO_ERROR -批次号错误
     */
    private String failType;

    /**
     * 失败原因
     */
    private String failMessage;

    /**
     * 调出门店创建人
     */
    private String createUser;

    /**
     * 调入门店审批人
     */
    private String auditUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *  数据版本，每次update+1
     */
    private Long version;

}