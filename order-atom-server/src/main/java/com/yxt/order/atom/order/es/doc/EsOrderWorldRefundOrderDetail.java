package com.yxt.order.atom.order.es.doc;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldType;


@Data
public class EsOrderWorldRefundOrderDetail {

  /**
   * 详情id
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundDetailNo;

  /**
   * 商品编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String erpCode;

  /**
   * 商品名称
   */
  @IndexField(fieldType = FieldType.KEYWORD_TEXT, analyzer = Analyzer.IK_MAX_WORD)
  private String erpName;
}
