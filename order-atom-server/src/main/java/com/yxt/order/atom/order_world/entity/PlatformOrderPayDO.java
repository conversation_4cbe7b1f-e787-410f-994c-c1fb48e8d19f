package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("platform_order_pay")
public class PlatformOrderPayDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 支付唯一号
   */
  private String orderPayNo;

  /**
   * 支付类型
   */
  private String payType;

  /**
   * 支付名称
   */
  private String payName;

  /**
   * 支付金额
   */
  private BigDecimal payAmount;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  @Data
  @TableName("platform_order_organization")
  public static class PlatformOrderOrganizationDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台编码
     */
    private String thirdPlatformCode;

    /**
     * 平台订单号
     */
    private String thirdOrderNo;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 所属机构编码
     */
    private String organizationCode;

    /**
     * 所属机构名称
     */
    private String organizationName;

    /**
     * 组织机构父路径id链路 1000-1100-1110-
     */
    private String orgParentPath;

    /**
     * 门店直营加盟类型 DIRECT_SALES-直营 JOIN - 加盟
     */
    private String storeDirectJoinType;

    /**
     * 平台创建时间
     */
    private Date created;

    /**
     * 平台更新时间
     */
    private Date updated;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 系统创建时间
     */
    private Date sysCreateTime;

    /**
     * 系统更新时间
     */
    private Date sysUpdateTime;

    /**
     * 数据版本，每次update+1
     */
    private Long version;

  }
}
