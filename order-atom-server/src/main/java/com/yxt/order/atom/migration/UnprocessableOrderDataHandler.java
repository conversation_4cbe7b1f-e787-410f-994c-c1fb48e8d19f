package com.yxt.order.atom.migration;

import static com.yxt.order.atom.migration.constant.MigrationConstant.DONT_NEED_OPERATE;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.migration.constant.MigrationConstant;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDO;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDOMapper;
import com.yxt.order.atom.migration.dao.enums.UnprocessableSceneEnum;
import com.yxt.order.atom.migration.dao.enums.UnprocessableStatusEnum;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.repair.dto.StartEndId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 无法处理的订单数据处理
 *
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月25日 10:51
 * @email: <EMAIL>
 */

@Component
@Slf4j
public class UnprocessableOrderDataHandler {


  @Resource
  private UnprocessableOrderDOMapper unprocessableOrderDOMapper;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;


  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Value("${migrationBatchSize:2000}")
  private Long batchSize;

  @XxlJob("unprocessableOrderDataHandler")
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void execute() {
    try {
      String param = XxlJobHelper.getJobParam();
      if (StringUtils.isEmpty(param)) {
        throw new RuntimeException("请配置参数");
      }
      StartEndId startEndId = JsonUtils.toObject(param, StartEndId.class);
      XxlJobHelper.log("param:{}", JsonUtils.toJson(startEndId));
//    StartEndId startEndId = unprocessableOrderDOMapper.selectMaxMinId(); // 采用配置,不查库了
      while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
        LambdaQueryWrapper<UnprocessableOrderDO> query = new LambdaQueryWrapper<>();
        query.ge(UnprocessableOrderDO::getId, startEndId.getStartId());
        query.lt(UnprocessableOrderDO::getId, startEndId.getStartId() + batchSize);
        List<UnprocessableOrderDO> unprocessableOrderDOList = unprocessableOrderDOMapper.selectList(
            query);
        if (CollectionUtils.isEmpty(unprocessableOrderDOList)) {
          refreshStartId(startEndId);
          continue;
        }

        for (UnprocessableOrderDO unprocessableOrderDO : unprocessableOrderDOList) {
          if (Objects.isNull(unprocessableOrderDO.getId()) || Boolean.FALSE.toString()
              .equals(unprocessableOrderDO.getAllowOperate())
              || UnprocessableStatusEnum.HANDLED.name().equals(unprocessableOrderDO.getStatus())
              || !Boolean.TRUE.toString().equals(unprocessableOrderDO.getMigration())
              || DONT_NEED_OPERATE.equals(unprocessableOrderDO.getAllowOperate())
          ) {
            continue;
          }

          String scene = unprocessableOrderDO.getScene();
          String businessNo = unprocessableOrderDO.getBusinessNo();

          if (UnprocessableSceneEnum.REPEATED_OFFLINE_ORDER.name().equals(scene)) {
            try (HintManager hintManager = HintManager.getInstance()) {
              OfflineOrderHit hit = new OfflineOrderHit();
              hit.setDefineNo(businessNo);
              OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

              OfflineOrderDO offlineOrderDO = getOfflineOrderDO(businessNo);
              if (Objects.isNull(offlineOrderDO)) {
                continue;
              }
              offlineOrderRepository.deletedOfflineOrder(offlineOrderDO,
                  MigrationConstant.DELETED_FROM_UNPROCESSABLE, Boolean.TRUE);
            }
          } else if (UnprocessableSceneEnum.REPEATED_OFFLINE_REFUND_ORDER.name().equals(scene)) {
            try (HintManager hintManager = HintManager.getInstance()) {
              OfflineOrderHit hit = new OfflineOrderHit();
              hit.setDefineNo(businessNo);
              OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

              OfflineRefundOrderDO offlineRefundOrderDO = getOfflineRefundOrderDO(businessNo);
              if (Objects.isNull(offlineRefundOrderDO)) {
                continue;
              }
              offlineOrderRepository.deletedOfflineRefundOrder(offlineRefundOrderDO,
                  MigrationConstant.DELETED_FROM_UNPROCESSABLE, Boolean.TRUE);
            }
          } else {
            throw new RuntimeException("未匹配到处理场景");
          }

          unprocessableOrderDO.setStatus(UnprocessableStatusEnum.HANDLED.name());
          unprocessableOrderDO.setNote(new Date().toString());
          unprocessableOrderDOMapper.updateById(unprocessableOrderDO);
        }
        // 刷新起始Id
        refreshStartId(startEndId);
      }

      XxlJobHelper.handleSuccess();
    } catch (Exception e) {
      XxlJobHelper.handleFail("任务执行失败:" + e.getMessage());
    }
  }

  private OfflineRefundOrderDO getOfflineRefundOrderDO(String businessNo) {
    LambdaQueryWrapper<OfflineRefundOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OfflineRefundOrderDO::getRefundNo, businessNo);
    return offlineRefundOrderMapper.selectOne(queryWrapper);
  }

  private OfflineOrderDO getOfflineOrderDO(String businessNo) {
    LambdaQueryWrapper<OfflineOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OfflineOrderDO::getOrderNo, businessNo);
    return offlineOrderMapper.selectOne(queryWrapper);
  }


  private void refreshStartId(StartEndId startEndId) {
    startEndId.setStartId(startEndId.getStartId() + batchSize);
  }

}
