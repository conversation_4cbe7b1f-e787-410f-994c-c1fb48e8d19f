package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.OrderCommodityDetailCostPriceDO;
import com.yxt.order.atom.order.mapper.OrderCommodityDetailCostPriceMapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 15:41
 * @email: <EMAIL>
 */
@Repository
public class OrderCommodityDetailCostPriceBatchRepository extends
    ServiceImpl<OrderCommodityDetailCostPriceMapper, OrderCommodityDetailCostPriceDO> {

}

