package com.yxt.order.atom.order_world.controller;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order_world.service.OrderService;
import com.yxt.order.atom.sdk.order_world.OrderWorldOrderAtomCmdApi;
import com.yxt.order.atom.sdk.order_world.OrderWorldOrderAtomQueryApi;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldOrderBatchQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldOrderQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.SaveOrderWorldOrderOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderMainStatusOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderPayStatusOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderWorldOrderOptionalReq;
import com.yxt.order.atom.sdk.order_world.res.OrderRelatedInfoRes;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OrderCmdController implements OrderWorldOrderAtomCmdApi, OrderWorldOrderAtomQueryApi {

  @Resource
  private OrderService orderService;

  @Override
  public ResponseBase<Void> saveOptional(SaveOrderWorldOrderOptionalReq req) {
    orderService.saveOrderOptional(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<Void> updateMainStatusOptional(UpdateOrderMainStatusOptionalReq req) {
    orderService.updateMainStatusOptional(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<Void> updatePayStatusOptional(UpdateOrderPayStatusOptionalReq req) {
    orderService.updatePayStatusOptional(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<Void> updateOptional(UpdateOrderWorldOrderOptionalReq req) {
    orderService.updateOptional(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<OrderRelatedInfoRes> getOrderInfoByScale(OrderWorldOrderQueryByScaleReq request) {
    return ResponseBase.success(orderService.getOrderInfoByScale(request));
  }

  @Override
  public ResponseBase<List<OrderRelatedInfoRes>> getOrderInfoBatchByScale(OrderWorldOrderBatchQueryByScaleReq request) {
    return ResponseBase.success(orderService.getOrderInfoBatchByScale(request));
  }

}
