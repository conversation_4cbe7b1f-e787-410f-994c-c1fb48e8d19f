package com.yxt.order.atom.order.repository;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.atom.order.entity.ImportExportTaskDO;
import com.yxt.order.atom.sdk.imextask.ImportExportTaskRes;
import com.yxt.order.atom.sdk.imextask.QueryImportExportTaskReq;

/**
 * @author: moatkon
 * @time: 2025/4/3 10:55
 */

public interface ImportExportTaskRepository {

  ImportExportTaskDO queryTask(String taskNo);


  Boolean insert(ImportExportTaskDO create);

  Boolean update(ImportExportTaskDO update);

  PageDTO<ImportExportTaskRes> list(QueryImportExportTaskReq queryImportExportTaskReq);
}
