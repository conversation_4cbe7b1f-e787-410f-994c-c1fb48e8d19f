package com.yxt.order.atom.order.repository.impl;

import com.yxt.order.atom.order.mapper.OrderDataRepairMapper;
import com.yxt.order.atom.order.repository.RepairOrderRepository;
import com.yxt.order.types.offline.enums.PreCheckEnum;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * @author: moatkon
 * @time: 2025/1/16 15:40
 */
@Repository
public class RepairOrderRepositoryImpl implements RepairOrderRepository {

  @Resource
  private OrderDataRepairMapper orderDataRepairMapper;

  @Override
  public void updatePreCheck(Long id, PreCheckEnum preCheckEnum, String orderNo) {
    orderDataRepairMapper.updatePreCheck(id,preCheckEnum,orderNo);
  }
}
