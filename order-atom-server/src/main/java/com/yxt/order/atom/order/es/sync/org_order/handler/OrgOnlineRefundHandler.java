package com.yxt.order.atom.order.es.sync.org_order.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;
import com.yxt.order.atom.order.es.sync.member_transaction.utils.OnlineOrderStoreTypeUtils;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgRefundDetailModel;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgRefundModel;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.order.enums.OrderServiceModeEnum;
import com.yxt.order.types.order.enums.OrderSource;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import com.yxt.order.types.order.enums.RefundAfterSaleTypeEnum;
import com.yxt.order.types.order.enums.RefundTypeEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class OrgOnlineRefundHandler extends AbstractCanalHandler<CanalRefundOrder, OrgRefundModel> {

  @Resource
  private SyncComponent syncComponent;

  @Value("${org-order.online-order-es-keep-day:365}")
  private Integer onlineOrderEsKeepDays;

  public OrgOnlineRefundHandler() {
    super(CanalRefundOrder.class);
  }

  /**
   * 检查
   *
   * @return
   */
  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    List<String> refundRelateTableList = Lists.newArrayList(Table.REFUND_ORDER, Table.REFUND_ORDER_DETAIL, Table.ERP_REFUND_INFO);
    return Database.DSCLOUD.equals(database) && refundRelateTableList.contains(table);
  }

  /**
   * 组装ES数据模型
   *
   * @return
   */
  @Override
  protected List<OrgRefundModel> assemble() {
    List<RefundOrder> canalRefundList = getData().getData();
    if (CollectionUtils.isEmpty(canalRefundList)) {
      return Lists.newArrayList();
    }

    List<String> canalRefundNoList = canalRefundList.stream().map(RefundOrder::getRefundNo).distinct().collect(Collectors.toList());
    List<RefundOrderDO> refundList = syncComponent.getRefundListByRefundNo(canalRefundNoList)
        .stream()
        //排除B2C和机构为空的
        .filter(refund-> StrUtil.equalsIgnoreCase(OrderServiceModeEnum.O2O.getCode(),refund.getServiceMode()))
        .filter(refund-> StrUtil.isNotBlank(refund.getOrganizationCode()))
        //排除时间范围
        .filter(refund-> !DateUtil.toLocalDateTime(refund.getCreateTime()).isBefore(LocalDateTime.now().minusDays(onlineOrderEsKeepDays)))
        .collect(Collectors.toList());
    if(CollUtil.isEmpty(refundList)){
      return Lists.newArrayList();
    }
    List<String> orderNoList = refundList.stream().map(refund->StrUtil.toStringOrNull(refund.getOrderNo())).distinct().collect(Collectors.toList());
    List<Long> refundNoList = refundList.stream().map(RefundOrderDO::getRefundNo).collect(Collectors.toList());
    List<OrderInfoDO> orderList = syncComponent.getOrderListByOrderNo(orderNoList);
    Map<Long, OrderInfoDO> orderMap = new HashMap<>();
    if(CollUtil.isNotEmpty(orderList)){
      orderMap = orderList.stream().collect(Collectors.toMap(OrderInfoDO::getOrderNo, Function.identity()));
    }
    List<RefundDetailDO> refundDetailList = syncComponent.getRefundDetailListByRefundNo(refundNoList);
    Map<Long, List<RefundDetailDO>> refundDetailMap = new HashMap<>();
    if(CollUtil.isNotEmpty(refundDetailList)){
      refundDetailMap = refundDetailList.stream().collect(Collectors.groupingBy(RefundDetailDO::getRefundNo));
    }
    List<ErpRefundInfoDO> refundErpList = syncComponent.getRefundErpInfoListByRefundNo(refundNoList);
    Map<Long, ErpRefundInfoDO> refundErpMap = new HashMap<>();
    if(CollUtil.isNotEmpty(refundErpList)){
      refundErpMap = refundErpList.stream().collect(Collectors.toMap(ErpRefundInfoDO::getRefundNo, Function.identity()));
    }

    List<OrgRefundModel> refundModelList = new ArrayList<>();
    for (RefundOrderDO refund : refundList) {
      if(StrUtil.isBlank(refund.getOrganizationCode())){
        continue;
      }
      OrgRefundModel refundModel = new OrgRefundModel();
      refundModel.setOrderNo(StrUtil.toStringOrNull(refund.getOrderNo()));
      refundModel.setThirdOrderNo(refund.getThirdOrderNo());
      refundModel.setRefundNo(StrUtil.toStringOrNull(refund.getRefundNo()));
      refundModel.setThirdRefundNo(refund.getThirdRefundNo());
      refundModel.setCreated(ObjectUtil.isNull(refund.getRefundApplicationTime()) ? refund.getCreateTime() : refund.getRefundApplicationTime());
      refundModel.setCreateTime(refund.getCreateTime());
      refundModel.setStoreCode(refund.getOnlineStoreCode());
      refundModel.setOrgCode(refund.getOrganizationCode());
      refundModel.setSourceStoreCode(refund.getSourceOnlineStoreCode());
      refundModel.setSourceOrgCode(refund.getSourceOrganizationCode());
      refundModel.setRefundStatus(refund.getState());
      refundModel.setErpStatus(StrUtil.toStringOrNull(refund.getErpState()));
      refundModel.setErpTime(refund.getBillTime());
      refundModel.setErpRefundNo(refund.getErpRefundNo());
      refundModel.setOrderSource(OrderSource.ONLINE.name());
      refundModel.setPlatformCode(PlatformCodeEnum.getByCode(refund.getThirdPlatformCode()).name());
      refundModel.setRefundType(RefundTypeEnum.getByCode(refund.getType()).name());
      RefundAfterSaleTypeEnum afterSaleTypeEnum = RefundAfterSaleTypeEnum.getByCode(refund.getAfterSaleType());
      refundModel.setAfterSaleType(afterSaleTypeEnum == null ? RefundAfterSaleTypeEnum.REFUND.name() : afterSaleTypeEnum.name());
      refundModel.setRefundFlags(null);
      refundModel.setRefundBillAmount(BigDecimal.ZERO);
      if(refundErpMap.containsKey(refund.getRefundNo())){
        ErpRefundInfoDO erpRefundInfoDO = refundErpMap.get(refund.getRefundNo());
        refundModel.setRefundBillAmount(erpRefundInfoDO.getRefundMerchantTotal());
      }
      if(refundDetailMap.containsKey(refund.getRefundNo())){
        List<RefundDetailDO> refundDetailDOS = refundDetailMap.get(refund.getRefundNo());
        List<OrgRefundDetailModel> detailModels = refundDetailDOS.stream().map(detail -> {
          OrgRefundDetailModel detailModel = new OrgRefundDetailModel();
          detailModel.setOrderDetailId(StrUtil.toStringOrNull(detail.getId()));
          detailModel.setErpCode(detail.getErpCode());
          detailModel.setItemName(detail.getCommodityName());
          return detailModel;
        }).collect(Collectors.toList());
        refundModel.setDetailList(detailModels);
      }
      refundModel.setOrderCreated(refund.getCreateTime());
      if(orderMap.containsKey(refund.getOrderNo())){
        OrderInfoDO orderInfoDO = orderMap.get(refund.getOrderNo());
        refundModel.setOrderCreated(orderInfoDO.getCreateTime());
        refundModel.setUserCardNo(orderInfoDO.getMemberNo());
        refundModel.setUserId(syncComponent.queryUserId(orderInfoDO.getMemberNo()));
      }
      refundModel.setStoreType(OnlineOrderStoreTypeUtils.storeType(refund.getOrganizationCode()).name());
      refundModel.setServiceMode(refund.getServiceMode());
      refundModel.setDeleted(0L);
      refundModelList.add(refundModel);
    }
    return refundModelList;
  }

  public boolean efficientData(RefundOrder refundOrder) {
    return StrUtil.equalsIgnoreCase(refundOrder.getServiceMode(), OrderServiceModeEnum.O2O.getCode()) && StrUtil.isNotBlank(refundOrder.getOrganizationCode());
  }
}
