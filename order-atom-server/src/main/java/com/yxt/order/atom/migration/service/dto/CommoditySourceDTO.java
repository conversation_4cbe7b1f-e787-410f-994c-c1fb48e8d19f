package com.yxt.order.atom.migration.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommoditySourceDTO {

    @ApiModelProperty("商品编码")
    private String erpCode;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "通用名")
    private String commonName;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "剂型")
    private String dosageForm;

    @ApiModelProperty(value = "生产厂家")
    private String manufacture;

}