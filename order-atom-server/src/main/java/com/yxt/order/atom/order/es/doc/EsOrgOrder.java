package com.yxt.order.atom.order.es.doc;

import cn.hutool.core.util.StrUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

@Data
@Settings(shardsNum = 36)
@IndexName(value = "es_order_shard_by_org", keepGlobalPrefix = true)
public class EsOrgOrder {

  @IndexId(type = IdType.CUSTOMIZE)
  private String id;

  /**
   * 系统单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * 三方订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * 下单时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date created;

  /**
   * 创建时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  /**
   * 支付时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date payTime;

  /**
   * 支付日期（冗余）
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String payDate;

  /**
   * 线上门店编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeCode;

  /**
   * 机构编码（线下实际发货门店）
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orgCode;

  /**
   * 下单线上门店编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String sourceStoreCode;

  /**
   * 下单线下机构编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String sourceOrgCode;

  /**
   * 订单状态 5待处理,10待接单,20待拣货,30待配送,
   * 40待收货,100已完成,102已取消,101已关闭
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer orderStatus;

  /**
   * 下账状态 20, "待锁定" 30, "待下账" 99, "下账失败" 100, "已下账" 110, "已取消" 120,"已退款"
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer erpStatus;

  /**
   * 下账时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date erpTime;

  /**
   * 零售流水
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String erpSaleNo;

  /**
   * 配送方式
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String deliveryType;

  /**
   * 订单来源 ONLINE-线上订单 OFFLINE-线下订单
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderSource;

  /**
   * HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String platformCode;

  /**
   * 订单标记，通过空格分割，使用match匹配
   */
  @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.WHITESPACE, strategy = FieldStrategy.IGNORED)
  private String orderFlags;

  /**
   * 锁定标志
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String lockFlag;

  /**
   * 下账金额
   */
  @IndexField(fieldType = FieldType.SCALED_FLOAT)
  private BigDecimal billAmount;

  /**
   * 订单明细
   */
  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOrgOrderDetail.class)
  private List<EsOrgOrderDetail> detailList;

  /**
   * 会员编码(唯一值)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userCardNo;

  /**
   * 会员ID (心云)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userId;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeType;

  /**
   * 服务模式 O2O B2C B2B
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String serviceMode;

  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  @IndexField(fieldType = FieldType.KEYWORD)
  private Long deleted;

  public void setOrderFlags(List<String> orderFlags) {
    this.orderFlags = StrUtil.join(" ", orderFlags);
  }
}
