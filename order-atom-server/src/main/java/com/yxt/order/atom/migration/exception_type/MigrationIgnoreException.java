package com.yxt.order.atom.migration.exception_type;

public class MigrationIgnoreException extends RuntimeException {

  private static final long serialVersionUID = -8398600407698355389L;

  public MigrationIgnoreException() {
  }

  public MigrationIgnoreException(String message) {
    super(message);
  }

  public MigrationIgnoreException(String message, Throwable cause) {
    super(message, cause);
  }

  public MigrationIgnoreException(Throwable cause) {
    super(cause);
  }

  protected MigrationIgnoreException(String message, Throwable cause, boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }
}
