package com.yxt.order.atom.order_world.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.exception.YxtParamException;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.migration.config.ThreadPoolConfig;
import com.yxt.order.atom.order_world.entity.RefundOrderAmountDO;
import com.yxt.order.atom.order_world.entity.RefundOrderDO;
import com.yxt.order.atom.order_world.entity.RefundOrderDetailDO;
import com.yxt.order.atom.order_world.entity.RefundOrderPayDO;
import com.yxt.order.atom.order_world.entity.RefundOrderUserDO;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderAmountMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderUserMapper;
import com.yxt.order.atom.order_world.repository.ExtendDataBatchRepository;
import com.yxt.order.atom.order_world.repository.NewRefundOrderDetailBatchRepository;
import com.yxt.order.atom.order_world.repository.NewRefundPayBatchRepository;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderAmountDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderPayDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderUserDTO;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldRefundBatchQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldRefundQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.SaveOrderWorldRefundOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderWorldRefundOptionalReq;
import com.yxt.order.atom.sdk.order_world.res.RefundRelatedInfoRes;
import com.yxt.order.common.utils.CompletableFutureUtils;
import com.yxt.order.types.order_world.RefundQueryScaleEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Service
public class RefundService {

  @Autowired
  private NewRefundOrderAmountMapper refundOrderAmountMapper;
  @Autowired
  private NewRefundOrderDetailBatchRepository refundOrderDetailBatchRepository;
  @Autowired
  private NewRefundOrderMapper refundOrderMapper;
  @Autowired
  private NewRefundOrderUserMapper refundOrderUserMapper;
  @Autowired
  private NewRefundPayBatchRepository refundPayBatchRepository;
  @Autowired
  private RefundSearchService refundSearchService;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_POOL)
  private Executor orderSearchPool;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_SUB_POOL)
  private Executor orderSearchSubPool;

  @Transactional
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void saveRefundOptional(SaveOrderWorldRefundOptionalReq request) {
    RefundOrderDTO refundOrder = request.getRefundOrder();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundOrder.getRefundNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      // 保存退款单
      refundOrderMapper.insert(BeanUtil.toBean(refundOrder, RefundOrderDO.class));

      //退款单明细
      if (CollUtil.isNotEmpty(request.getRefundOrderDetailList())) {
        List<RefundOrderDetailDO> refundOrderDetailDOList = BeanUtil.copyToList(request.getRefundOrderDetailList(), RefundOrderDetailDO.class);
        refundOrderDetailBatchRepository.saveBatch(refundOrderDetailDOList);

      }
      //退款单支付方式
      if (CollUtil.isNotEmpty(request.getRefundOrderPayList())) {
        refundPayBatchRepository.saveBatch(BeanUtil.copyToList(request.getRefundOrderPayList(), RefundOrderPayDO.class));
      }

      //退款单金额
      if (ObjectUtil.isNotNull(request.getRefundOrderAmount())) {
        refundOrderAmountMapper.insert(BeanUtil.toBean(request.getRefundOrderAmount(), RefundOrderAmountDO.class));
      }

      //保存退款单用户信息
      if (ObjectUtil.isNotNull(request.getRefundOrderUser())) {
        refundOrderUserMapper.insert(BeanUtil.toBean(request.getRefundOrderUser(), RefundOrderUserDO.class));
      }
    }

  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public RefundRelatedInfoRes getRefundInfoByScale(OrderWorldRefundQueryByScaleReq req) {
    String refundNo = req.getRefundNo();
    AtomicReference<RefundRelatedInfoRes> resDto = new AtomicReference<>(new RefundRelatedInfoRes());
    RefundOrderDO refundOrderDO = null;
    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      // 查询主单
      refundOrderDO = refundOrderMapper.selectOne(new LambdaQueryWrapper<RefundOrderDO>().eq(RefundOrderDO::getRefundNo, refundNo));

      Assert.isTrue(Objects.nonNull(refundOrderDO), String.format("退款单%s不存在,req:%s", refundNo, JsonUtils.toJson(req)));
    }
    for (RefundQueryScaleEnum qryScaleEnum : req.getQryScaleList()) {
      switch (qryScaleEnum) {
        case MAIN:
          RefundOrderDTO refundOrderDTO = BeanUtil.toBean(refundOrderDO, RefundOrderDTO.class);
          resDto.get().setRefundOrder(refundOrderDTO);
          break;
        case DETAIL:
          CompletableFuture<Void> detailFuture = CompletableFuture.runAsync(() -> {
            List<RefundOrderDetailDO> refundDetailList = refundSearchService.getRefundDetailList(refundNo);
            List<RefundOrderDetailDTO> refundOrderDetailDTOS = BeanUtil.copyToList(refundDetailList, RefundOrderDetailDTO.class);
            resDto.get().setRefundOrderDetailList(refundOrderDetailDTOS);
          }, orderSearchSubPool);
          futureList.add(detailFuture);
          break;
        case PAY:
          // 查询支付方式信息
          CompletableFuture<Void> payFuture = CompletableFuture.runAsync(() -> {
            List<RefundOrderPayDO> payDOList = refundSearchService.getOrderPayList(refundNo);
            resDto.get().setRefundOrderPayList(BeanUtil.copyToList(payDOList, RefundOrderPayDTO.class));
          }, orderSearchSubPool);
          futureList.add(payFuture);

          // 查询支付金额信息
          CompletableFuture<Void> amountFuture = CompletableFuture.runAsync(() -> {
            RefundOrderAmountDO amountDO = refundSearchService.getRefundAmount(refundNo);
            resDto.get().setRefundOrderAmount(BeanUtil.toBean(amountDO, RefundOrderAmountDTO.class));
          }, orderSearchSubPool);
          futureList.add(amountFuture);
          break;
        case USER:
          // 查询用户信息
          CompletableFuture<Void> userFuture = CompletableFuture.runAsync(() -> {
            RefundOrderUserDO userDO = refundSearchService.getRefundUserInfo(refundNo);
            resDto.get().setRefundOrderUser(BeanUtil.toBean(userDO, RefundOrderUserDTO.class));
          }, orderSearchSubPool);
          futureList.add(userFuture);
          break;
        default:
          break;
      }
    }
    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    return resDto.get();
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<RefundRelatedInfoRes> getRefundInfoBatchByScale(OrderWorldRefundBatchQueryByScaleReq req) {
    Function<String, Supplier<RefundRelatedInfoRes>> function = (refundNo) -> () -> {
      OrderWorldRefundQueryByScaleReq tempRequest = new OrderWorldRefundQueryByScaleReq(req.getQryScaleList(), refundNo);
      return SpringUtil.getBean(RefundService.class).getRefundInfoByScale(tempRequest);
    };
    return CompletableFutureUtils.supplyAsync(function, req.getRefundNoList(), 10, orderSearchPool).stream().filter(Objects::nonNull).collect(Collectors.toList());
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void updateOptional(UpdateOrderWorldRefundOptionalReq req) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(req.getRefundNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      if(ObjectUtil.isNotNull(req.getRefundOrder())){
        RefundOrderDO originRefund = refundOrderMapper.selectOne(new LambdaQueryWrapper<RefundOrderDO>().eq(RefundOrderDO::getRefundNo, req.getRefundNo()));
        if(ObjectUtil.isNull(originRefund)){
          throw new YxtParamException("退款单不存在！");
        }
        RefundOrderDO refundOrderDO = BeanUtil.toBean(req.getRefundOrder(), RefundOrderDO.class);
        refundOrderDO.setId(originRefund.getId());
        refundOrderMapper.updateById(refundOrderDO);
      }
    }
  }

  public List<RefundRelatedInfoRes> queryDBByAfterSaleNo(OrderWorldRefundBatchQueryByScaleReq req) {
    return null;
  }
}
