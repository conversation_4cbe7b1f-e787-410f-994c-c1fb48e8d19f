package com.yxt.order.atom.migration.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:40
 * @email: <EMAIL>
 */
@DS(DATA_SOURCE.ORDER_OFFLINE)
@Mapper
public interface HanaMigrationDOMapper extends BaseMapper<HanaMigrationDO> {

  @Update("update hana_migration set migration_total_count_success = #{count} where id=#{id}")
  Integer updateHanaOrderSuccessHandledCount(@Param("id") Long id, @Param("count") Long hanaOrderSuccessHandledCount);

  @Update("update hana_migration set migration_result = 'true' where id=#{id}")
  void migrationResultSuccess(@Param("id")Long id);
}
