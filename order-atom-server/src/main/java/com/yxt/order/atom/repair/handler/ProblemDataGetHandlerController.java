package com.yxt.order.atom.repair.handler;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.COMMON_BUSINESS_POOL;

import com.yxt.common.logic.flash.FlashParam;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.AbstractFlash.CustomData;
import com.yxt.order.atom.order.es.sync.FixKeChuanAmountError;
import com.yxt.order.atom.sdk.order_info.req.FlashDataToEsReq;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 问题数据入表
 *
 * @author: moatkon
 * @time: 2025/1/8 18:04
 */
@RestController
@Slf4j
public class ProblemDataGetHandlerController extends AbstractController {

  @Resource
  private List<AbstractFlash<?, ?, FixKeChuanAmountError>> abstractFlashList;

  @Qualifier(COMMON_BUSINESS_POOL)
  @Resource
  private ThreadPoolExecutor commonBusinessPool;

  @Value("${fixKeChuanAmountErrorOnOff:false}")
  private Boolean fixKeChuanAmountErrorOnOff;


  @PostMapping("/fix/ke-chuan/detail-amount-error")
  ResponseBase<Boolean> fixKeChuanAmountError(
      @RequestBody @Valid FlashDataToEsReq flashDataToEsReq) {

    if(!fixKeChuanAmountErrorOnOff){
      log.info("未开启,防止误触;请在apollo开启fixKeChuanAmountErrorOnOff");
      return generateSuccess(Boolean.FALSE);
    }


    commonBusinessPool.submit(() -> {
      try {
        FlashParam flashParam = new FlashParam();
        flashParam.setStart(flashDataToEsReq.getStartDate());
        flashParam.setEnd(flashDataToEsReq.getEndDate());
        flashParam.setNoList(flashDataToEsReq.getNoList());
        flashParam.setMonitorKey(flashDataToEsReq.getMonitorKey());

        CustomData customData = new CustomData();
        customData.setShardingValueList(flashDataToEsReq.getShardingValueList());

        for (AbstractFlash<?, ?, FixKeChuanAmountError> fixKeChuanAmountErrorAbstractFlash : abstractFlashList) {
          try {
            fixKeChuanAmountErrorAbstractFlash.customData(customData);
            fixKeChuanAmountErrorAbstractFlash.startFlush(flashParam);
          } catch (Exception e) {
            log.error("FixKeChuanAmountError修复失败,请人工介入排查。", e);
          }
        }

      } catch (Exception e) {
        log.error("fixKeChuanAmountError error,{}", e.getMessage(), e);
      }
    });

    return generateSuccess(Boolean.TRUE);

  }

}
