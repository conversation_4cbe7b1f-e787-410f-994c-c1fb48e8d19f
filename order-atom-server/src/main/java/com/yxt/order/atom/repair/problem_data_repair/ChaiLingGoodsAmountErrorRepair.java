package com.yxt.order.atom.repair.problem_data_repair;

import static com.yxt.order.atom.repair.RepairUtils.validateUniqueBusinessUk;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OrderDataRepairDO;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.repair.AbstractOrderRepair;
import com.yxt.order.atom.repair.dto.PreCheckResult;
import com.yxt.order.atom.repair.dto.RepairResult;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.repair.RepairOfflineOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.common.utils.OrderJsonUtils;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.repair.RepairScene;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/1/9 10:03
 */
@Component
public class ChaiLingGoodsAmountErrorRepair extends
    AbstractOrderRepair<RepairOfflineOrderReqDto> {

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Override
  public RepairScene scene() {
    return RepairScene.CHAI_LING_GOODS_AMOUNT_ERROR_ORDER;
  }

  @Override
  protected RepairOfflineOrderReqDto parse(OrderDataRepairDO orderDataRepairDO) {
    return OrderJsonUtils.toObject(
        orderDataRepairDO.getInput(), RepairOfflineOrderReqDto.class);
  }

  @Override
  protected String shardingNo(OrderDataRepairDO orderDataRepairDO) {
    return parse(orderDataRepairDO).getOfflineOrderDTO().getOrderNo();
  }

  @Override
  protected PreCheckResult repairPreCheck(OrderDataRepairDO orderDataRepairDO) {
    RepairOfflineOrderReqDto repairReqDto = parse(orderDataRepairDO);

    OfflineOrderDTO needRepairOrder = repairReqDto.getOfflineOrderDTO();

    OfflineOrderExistsReqDto reqDto = new OfflineOrderExistsReqDto();
    reqDto.setStoreCode(needRepairOrder.getStoreCode());
    reqDto.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    reqDto.setThirdOrderNo(needRepairOrder.getThirdOrderNo());
    reqDto.setThirdCreated(needRepairOrder.getCreated());
    OfflineOrderDO offlineOrderDO = offlineOrderRepository.offlineOrderInfoForRepair(reqDto);
    if (Objects.isNull(offlineOrderDO)) {
      return PreCheckResult.create()
          .failed(String.format("%s 查不到数据,可能是重复MQ消息,已经处理被删了,或者就是找不到", OrderJsonUtils.toJson(reqDto)));
    } else {
      return PreCheckResult.create()
          .passAndRecordBeforeImage(OrderJsonUtils.toJson(offlineOrderDO), offlineOrderDO.getOrderNo());
    }
  }

  @Override
  protected RepairResult orderRepair(OrderDataRepairDO orderDataRepair) {
    OfflineOrderDO dbOfflineOrder = OrderJsonUtils.toObject(orderDataRepair.getBeforeImage(),OfflineOrderDO.class);
    List<OfflineOrderDetailDO> dbDetailDOList = dbOfflineOrder.getDetailDOList();

    RepairOfflineOrderReqDto reqDto = parse(orderDataRepair);
    OfflineOrderDTO reqOfflineOrderDTO = reqDto.getOfflineOrderDTO();
    List<OfflineOrderDetailDTO> reqDetailDTOList = reqDto.getOfflineOrderDetailDTOList();
    List<OfflineOrderDetailDO> reqDetailList = BeanUtil.copyToList(reqDetailDTOList,
        OfflineOrderDetailDO.class);

    // 统一校验唯一性
    validateUniqueBusinessUk(dbDetailDOList.stream().map(OfflineOrderDetailDO::businessUk).collect(
        Collectors.toList()));
    validateUniqueBusinessUk(reqDetailList.stream().map(OfflineOrderDetailDO::businessUk).collect(
        Collectors.toList()));

    // 使用新过来的值
    dbOfflineOrder.setActualCollectAmount(reqOfflineOrderDTO.getActualCollectAmount());
    dbOfflineOrder.setActualPayAmount(reqOfflineOrderDTO.getActualPayAmount());
    dbOfflineOrder.markUpdateBy("海典修复CLO"); // ChaiLingOrder

    // 处理明细
    for (OfflineOrderDetailDO dbDetail : dbDetailDOList) {
      for (OfflineOrderDetailDO reqDetail : reqDetailList) {
        if (dbDetail.businessUk().equals(reqDetail.businessUk())) {
          String orderNo = dbDetail.getOrderNo();
          String orderDetailNo = dbDetail.getOrderDetailNo();
          Date createdTime = dbDetail.getCreatedTime();
          Long id = dbDetail.getId();

          BeanUtil.copyProperties(reqDetail, dbDetail);
          dbDetail.setId(id);
          dbDetail.setOrderNo(orderNo);
          dbDetail.setOrderDetailNo(orderDetailNo);
          dbDetail.setCreatedTime(createdTime);
          dbDetail.markUpdateBy("海典修复CLO");
        }
      }
    }

    Boolean result = offlineOrderRepository.chaiLingOfflineOrderUpdate(dbOfflineOrder);

    return RepairResult.builder()
        .result(result)
        .businessNo(dbOfflineOrder.getOrderNo())
        .afterImage(OrderJsonUtils.toJson(dbOfflineOrder))
        .build();
  }





}
