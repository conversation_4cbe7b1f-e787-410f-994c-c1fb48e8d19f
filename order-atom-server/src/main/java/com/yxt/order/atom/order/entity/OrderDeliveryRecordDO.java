package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单配送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-07
 */
@Data
@TableName("order_delivery_record")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderDeliveryRecordDO implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 订单号
   */
  private Long orderNo;

  /**
   * 快递单当前状态，包括0待呼叫 1待接单，2待取货，3配送中，4已签收, 5已取消，6已过期，7异常
   */
  @ApiModelProperty(value = "快递单当前状态，包括0待呼叫 1待接单，2待取货，3配送中，4已签收, 5已取消，6已过期，7异常")
  private Integer state;

  /**
   * 骑手订单号
   */
  @ApiModelProperty(value = "骑手订单号")
  private String riderOrderNo;

  /**
   * 骑手平台
   */
  @ApiModelProperty(value = "骑手平台")
  @TableField(exist = false)
  private String riderPlatform;

  /**
   * 配送方式 1平台配送 2平台合作方配送 3自配送 4到店自取 0未知
   */
  @ApiModelProperty(value = "配送方式 1平台配送 2平台合作方配送 3自配送 4到店自取 0未知")
  private String deliveryType;

  /**
   * 第三方配送有配送平台
   */
  @ApiModelProperty(value = "第三方配送有配送平台")
  private String deliveryPlatName;

  /**
   * 第三方配送有配送网店
   */
  @ApiModelProperty(value = "第三方配送有配送网店")
  private String deliveryClientCode;

  /**
   * 第三方配送有配送门店
   */
  @ApiModelProperty(value = "第三方配送有配送门店")
  private String deliveryStoreCode;

  /**
   * 配送小费
   */
  @ApiModelProperty(value = "配送小费")
  private BigDecimal deliveryTip;

  /**
   * 骑手名称
   */
  @ApiModelProperty(value = "骑手名称")
  private String riderName;

  /**
   * 配送员手机
   */
  @ApiModelProperty(value = "骑手手机")
  private String riderPhone;

  /**
   * 员工自配送时，保存员工编号
   */
  @ApiModelProperty(value = "员工编号")
  private String riderStaffCode;


  /**
   * 配送地址
   */
  @ApiModelProperty(value = "配送地址")
  private String riderAddress;

  /**
   * 收货人名称
   */
  @ApiModelProperty(value = "收货人")
  @TableField(exist = false)
  private String receiverName;

  /**
   * 纬度
   */
  @ApiModelProperty(value = "纬度")
  private String latitude;

  /**
   * 经度
   */
  @ApiModelProperty(value = "经度")
  private String longitude;

  /**
   * 呼叫时间
   */
  @ApiModelProperty(value = "呼叫时间")
  private Date callTime;

  /**
   * 接单时间
   */
  @ApiModelProperty(value = "接单时间")
  private Date acceptTime;

  /**
   * 取货时间
   */
  @ApiModelProperty(value = "取货时间")
  private Date pickTime;

  /**
   * 取消来源
   */
  @ApiModelProperty(value = "取消来源")
  private String cancelFrom;

  /**
   * 取消原因
   */
  @ApiModelProperty(value = "取消原因")
  private String cancelReason;

  /**
   * 取消描述
   */
  @ApiModelProperty(value = "取消描述")
  private String cancelDetail;

  /**
   * 异常原因等
   */
  @ApiModelProperty(value = "异常原因等")
  private String exceptionReason;

  /**
   * 实际运费
   */
  @ApiModelProperty(value = "实际运费")
  private BigDecimal actualDeliveryFee;

  /**
   * 总运费
   */
  @ApiModelProperty(value = "总运费")
  private BigDecimal deliveryFeeTotal;

  @ApiModelProperty(value = "取消标志, 0未取消，1已取消")
  private Integer cancelFlag;
  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;

  /**
   * 物流编码
   */
  private String logisticsCompany;

  /**
   * 物流名称
   */
  private String logisticsName;

  /**
   * 物流单号
   */
  private String logisticsNo;

  /**
   * 额外信息
   */
  private String extraInfo;

  @ApiModelProperty(value = "延迟呼叫标识 0-不需延迟呼叫 1-等待延迟呼叫,默认 0")
  private int delayState;

  @ApiModelProperty(value = "是否已预呼叫: 1-是")
  private Integer preCallFlag;

  @ApiModelProperty(value = "是否展示转自配送按钮: 1-是")
  @TableField(exist = false)
  private Integer changeFlag;

  /**
   * 是否上传坐标信息
   */
  private String uploadLocationFlag;

}
