package com.yxt.order.atom.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.consistency.EfficientParam;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface OmsOrderInfoMapper extends BaseMapper<OmsOrderInfoDO> {

  OmsOrderInfoDO selectByOmsOrderNo(Long omsOrderNo);

  List<OmsOrderInfoDO> selectByOmsOrderNoList(@Param("list") List<Long> omsOrderNoList);

  @Select("select (bill_commodity_amount+delivery_fee) as billAmount from account_order where order_no = #{omsOrderNo} and deleted = 0")
  BigDecimal sumBillAmount(@Param("omsOrderNo") Long omsOrderNo);

  @Select("select sum(bill_price*goods_count) as erpCodeBillAmount from account_order_detail where order_no = #{omsOrderNo} and deleted = 0 and erp_code=#{erpCode}")
  BigDecimal b2cGoodsBillAmount(@Param("omsOrderNo")Long omsOrderNo, @Param("erpCode")String erpCode);

  @Select("select (refund_goods_total + refund_post_fee) as refundBillAmount from account_refund where order_no = #{omsOrderNo} and deleted = 0 ")
  BigDecimal sumRefundBillAmount(Long omsOrderNo);

  @Select("select sum(refund_goods_amount) as erpCodeBillAmount from account_refund_detail where refund_no = #{refundNo} and deleted = 0 and erp_code=#{erpCode}")
  BigDecimal selectDetailBillAmount(Long refundNo, String erpCode);

  Long selectMaxId(@Param("flashParam") FlashParam flashParam);

  Long selectMinId(@Param("flashParam") FlashParam flashParam);

  @Select("select max(id) from oms_order_info where created >= #{param.startDate} and created <= #{param.endDate}")
  Long selectEfficientCountMaxId(@Param("param")EfficientParam param);

  @Select("select min(id) from oms_order_info where created >= #{param.startDate} and created <= #{param.endDate}")
  Long selectEfficientCountMinId(@Param("param")EfficientParam param);
}