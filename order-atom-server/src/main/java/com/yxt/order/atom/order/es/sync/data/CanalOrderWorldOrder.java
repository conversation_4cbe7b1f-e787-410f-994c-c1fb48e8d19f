package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOrderWorldOrder.OrderInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CanalOrderWorldOrder extends BaseCanalData<OrderInfo> {

  @Data
  public static class OrderInfo {

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("third_platform_code")
    private String thirdPlatformCode;

    @JsonProperty("business_type")
    private String businessType;

    @JsonProperty("transaction_channel")
    private String transactionChannel;

    private Boolean needRoute = true;
  }

}
