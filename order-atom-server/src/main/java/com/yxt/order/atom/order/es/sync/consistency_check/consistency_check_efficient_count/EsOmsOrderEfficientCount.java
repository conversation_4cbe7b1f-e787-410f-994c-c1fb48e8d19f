package com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.consistency.AbstractConsistencyCheckEfficientCount;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.mapper.OmsOrderInfoMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/21
 * @since 1.0
 */
@Component
public class EsOmsOrderEfficientCount extends
    AbstractConsistencyCheckEfficientCount<OmsOrderInfoDO> {

  @Resource
  private OmsOrderInfoMapper omsOrderInfoMapper;

  @Override
  protected Long queryCursorStartId() {
    return omsOrderInfoMapper.selectEfficientCountMinId(getParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return omsOrderInfoMapper.selectEfficientCountMaxId(getParam());
  }

  @Override
  protected List<OmsOrderInfoDO> dataList() {
    LambdaQueryWrapper<OmsOrderInfoDO> query = new LambdaQueryWrapper<>();
    query.ge(OmsOrderInfoDO::getId, getParam().getCursorStartId());
    query.lt(OmsOrderInfoDO::getId, getParam().currentCursorEndId(defaultLimit()));
    query.eq(OmsOrderInfoDO::getDeleted, 0);
    return omsOrderInfoMapper.selectList(query);
  }


  @Override
  protected Long efficientCount(List<OmsOrderInfoDO> list, Long maximumId) {
    return list.stream().filter(s -> s.getId() <= maximumId)
        .filter(s -> OrderDateUtils.isEfficientDate(getParam().getStartDate(), getParam().getEndDate(), s.getCreated()))
        .map(DoToCanalDtoWrapper::getOmsOrderInfo)
        .count();
  }
}
