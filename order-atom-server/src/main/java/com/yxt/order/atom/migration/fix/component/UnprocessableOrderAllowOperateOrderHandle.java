package com.yxt.order.atom.migration.fix.component;

import static com.yxt.order.common.constants.Constant.OFFLINE_SHARDING_NUM;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDO;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDOMapper;
import com.yxt.order.atom.migration.dao.enums.UnprocessableSceneEnum;
import com.yxt.order.atom.migration.dao.enums.UnprocessableStatusEnum;
import com.yxt.order.atom.migration.fix.dto.HandleAllowOperateFieldReq;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.repair.dto.StartEndId;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/3/17 10:16
 */
@Component
@Slf4j
public class UnprocessableOrderAllowOperateOrderHandle {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private UnprocessableOrderDOMapper unprocessableOrderDOMapper;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void handleAllowOperateField(@Valid HandleAllowOperateFieldReq req) {

    StartEndId startEndId = new StartEndId();
    startEndId.setStartId(req.getStartId());
    startEndId.setEndId(req.getEndId());

    while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
      Long startId = startEndId.getStartId();
      Long endId = startEndId.getStartId() + keChuanTotalAmountDataGetLimit;

      LambdaQueryWrapper<UnprocessableOrderDO> query = new LambdaQueryWrapper<>();
      query.ge(UnprocessableOrderDO::getId, startId);
      query.le(UnprocessableOrderDO::getId, endId);

      List<UnprocessableOrderDO> unprocessableOrderDOList = unprocessableOrderDOMapper.selectList(
          query);

      if (CollectionUtils.isEmpty(unprocessableOrderDOList)) {
        refreshStartId(startEndId);
        continue;
      }

      for (UnprocessableOrderDO unprocessableOrderDO : unprocessableOrderDOList) {
        if (Objects.isNull(unprocessableOrderDO.getId())) {
          continue;
        }

        if (Boolean.TRUE.toString().equals(unprocessableOrderDO.getAllowOperate())) {
          continue;
        }

        if (UnprocessableStatusEnum.HANDLED.name().equals(unprocessableOrderDO.getStatus())) {
          continue;
        }

        try {
          String businessNo = unprocessableOrderDO.getBusinessNo();
          try (HintManager hintManager = HintManager.getInstance()) {
            OfflineOrderHit hit = new OfflineOrderHit();
            hit.setDefineNo(businessNo);
            OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

            String thirdPlatformCode = unprocessableOrderDO.getThirdPlatformCode();
            String thirdBusinessNo = unprocessableOrderDO.getThirdBusinessNo();
            String storeCode = unprocessableOrderDO.getStoreCode();

            if (UnprocessableSceneEnum.REPEATED_OFFLINE_ORDER.name()
                .equals(unprocessableOrderDO.getScene())) {
              LambdaQueryWrapper<OfflineOrderDO> queryWrapper = new LambdaQueryWrapper<>();
              queryWrapper.eq(OfflineOrderDO::getThirdPlatformCode, thirdPlatformCode);
              queryWrapper.eq(OfflineOrderDO::getThirdOrderNo, thirdBusinessNo);
              queryWrapper.eq(OfflineOrderDO::getStoreCode, storeCode);
              List<OfflineOrderDO> offlineOrderDOS = offlineOrderMapper.selectList(queryWrapper);
              if (CollectionUtils.isEmpty(offlineOrderDOS)) {
                continue;
              }

              List<String> distinctUuserIdList = offlineOrderDOS.stream().map(OfflineOrderDO::getUserId)
                  .distinct().collect(Collectors.toList());
              // 会员表需要校验userId是否是同一个
              if (checkOneUserId(unprocessableOrderDO, distinctUuserIdList)) {
                continue;
              }

              if (checkMigrateOkIfEqual(offlineOrderDOS)) {
                unprocessableOrderDO.setAllowOperate(Boolean.TRUE.toString());
                unprocessableOrderDO.setNote(Strings.EMPTY);
                unprocessableOrderDOMapper.updateById(unprocessableOrderDO);
              } else {
                unprocessableOrderDO.setNote("程序无法处理,请查看原因,并补充程序处理场景");
                unprocessableOrderDOMapper.updateById(unprocessableOrderDO);
              }

            } else if (UnprocessableSceneEnum.REPEATED_OFFLINE_REFUND_ORDER.name()
                .equals(unprocessableOrderDO.getScene())) {
              LambdaQueryWrapper<OfflineRefundOrderDO> queryWrapper = new LambdaQueryWrapper<>();
              queryWrapper.eq(OfflineRefundOrderDO::getThirdPlatformCode, thirdPlatformCode);
              queryWrapper.eq(OfflineRefundOrderDO::getThirdRefundNo, thirdBusinessNo);
              queryWrapper.eq(OfflineRefundOrderDO::getStoreCode, storeCode);
              List<OfflineRefundOrderDO> offlineRefundOrderDOS = offlineRefundOrderMapper.selectList(
                  queryWrapper);
              if (CollectionUtils.isEmpty(offlineRefundOrderDOS)) {
                continue;
              }

              List<String> distinctUserIdList = offlineRefundOrderDOS.stream()
                  .map(OfflineRefundOrderDO::getUserId).distinct().collect(Collectors.toList());
              // 会员表需要校验userId是否是同一个
              if (checkOneUserId(unprocessableOrderDO, distinctUserIdList)) {
                continue;
              }

              if (checkRefundMigrateOkIfEqual(offlineRefundOrderDOS)) {
                unprocessableOrderDO.setAllowOperate(Boolean.TRUE.toString());
                unprocessableOrderDO.setNote(Strings.EMPTY);
                unprocessableOrderDOMapper.updateById(unprocessableOrderDO);
              } else {
                unprocessableOrderDO.setNote("程序无法处理,请查看原因,并补充程序处理场景");
                unprocessableOrderDOMapper.updateById(unprocessableOrderDO);
              }

            }
          }
        } catch (Exception e) {
          log.warn("handleAllowOperateField warn message", e);
          unprocessableOrderDO.setNote("异常"+e.getMessage());
          unprocessableOrderDOMapper.updateById(unprocessableOrderDO);
        }
      }
      // 刷新起始Id
      refreshStartId(startEndId);
    }

  }

  private boolean checkOneUserId(UnprocessableOrderDO unprocessableOrderDO,
      List<String> distinctUuserIdList) {
    if (Integer.parseInt(unprocessableOrderDO.getShardingNo()) <= OFFLINE_SHARDING_NUM) {
      // 校验是否是同一个会员
      if (distinctUuserIdList.size() > 1) {
        unprocessableOrderDO.setNote("会员不同");
        unprocessableOrderDOMapper.updateById(unprocessableOrderDO);
        return true;
      }
    }
    return false;
  }

  private static Boolean checkMigrateOkIfEqual(List<OfflineOrderDO> offlineOrderDOS) {
    // 迁移的
    OfflineOrderDO migration = offlineOrderDOS.stream()
        .filter(i -> Boolean.TRUE.toString().equals(i.getMigration())).collect(Collectors.toList())
        .get(0);

    // 非迁移的
    List<OfflineOrderDO> toCompareList = offlineOrderDOS.stream()
        .filter(i -> !Boolean.TRUE.toString().equals(i.getMigration()))
        .collect(Collectors.toList());

    for (OfflineOrderDO compare : toCompareList) {
      if (compare.getActualCollectAmount().compareTo(migration.getActualCollectAmount()) == 0) {
        return Boolean.TRUE;
      }
    }
    return Boolean.FALSE;
  }

  private static Boolean checkRefundMigrateOkIfEqual(
      List<OfflineRefundOrderDO> offlineRefundOrderDOList) {
    // 迁移的
    OfflineRefundOrderDO migration = offlineRefundOrderDOList.stream()
        .filter(i -> Boolean.TRUE.toString().equals(i.getMigration())).collect(Collectors.toList())
        .get(0);

    // 非迁移的
    List<OfflineRefundOrderDO> toCompareList = offlineRefundOrderDOList.stream()
        .filter(i -> !Boolean.TRUE.toString().equals(i.getMigration()))
        .collect(Collectors.toList());

    for (OfflineRefundOrderDO compare : toCompareList) {
      if (compare.getTotalAmount().compareTo(migration.getTotalAmount()) == 0) {
        return Boolean.TRUE;
      }
    }
    return Boolean.FALSE;
  }

  private void refreshStartId(StartEndId startEndId) {
    startEndId.setStartId(startEndId.getStartId() + keChuanTotalAmountDataGetLimit);
  }

}
