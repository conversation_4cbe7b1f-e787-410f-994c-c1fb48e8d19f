package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.order.atom.order.entity.OrderPrescriptionDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.order.repository.batch.OrderPrescriptionBatchRepository;
import com.yxt.order.atom.sdk.common.data.OrderPrescriptionDTO;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 16:00
 * @email: <EMAIL>
 */
@Component
public class OrderPrescriptionUpdate extends AbstractUpdate<List<OrderPrescriptionDO>> {

  @Resource
  private OrderPrescriptionBatchRepository orderPrescriptionBatchRepository;

  @Override
  protected Boolean canUpdate() {
    List<OrderPrescriptionDTO> orderPrescriptionDtoList = req.getOrderPrescriptionDtoList();
    if (CollectionUtils.isEmpty(orderPrescriptionDtoList)) {
      return false;
    }
    // 强制校验Id
    orderPrescriptionDtoList.forEach(
        dto -> Assert.isTrue(!StringUtils.isEmpty(dto.getId()), "id can not null"));
    return Boolean.TRUE;
  }

  @Override
  protected Integer update(List<OrderPrescriptionDO> list) {
    return orderPrescriptionBatchRepository.updateBatchById(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderPrescriptionDO> convert() {
    return BeanUtil.copyToList(req.getOrderPrescriptionDtoList(),OrderPrescriptionDO.class);
  }
}
