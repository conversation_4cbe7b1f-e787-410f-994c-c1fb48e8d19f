package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OrderDeliveryRecordDO;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto.CancelInfo;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto.OtherInfo;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto.Rider;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto.RiderAmountInfo;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto.TargetInfo;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto;
import com.yxt.order.types.order.DataVersionTime;
import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.RiderOrderNo;
import com.yxt.order.types.order.enums.DeliveryPlatformCodeEnum;
import com.yxt.order.types.order.enums.DeliveryStateEnum;
import com.yxt.order.types.order.enums.DeliveryTypeEnum;
import java.util.Optional;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年03月01日 16:24
 * @email: <EMAIL>
 */

public class OrderDeliveryRecordConverter {


  private OrderDeliveryRecordConverter() {
    throw new UnsupportedOperationException(
        "OrderDeliveryRecordDto is a utility class and cannot be instantiated.");
  }

  public static OrderDeliveryRecordDO toDO(OrderDeliveryRecordResDto dto) {
    OrderDeliveryRecordDO resDO = new OrderDeliveryRecordDO();
//    resDO.setId(dto.getId());
    resDO.setOrderNo(dto.getOrderNo().getOrderNo());

    Integer state = Optional.ofNullable(dto.getDeliveryStateEnum()).map(DeliveryStateEnum::getCode)
        .orElse(null);
    resDO.setState(state);

    String deliveryPlatName = Optional.ofNullable(dto.getDeliveryPlatformCodeEnum())
        .map(DeliveryPlatformCodeEnum::getCode).orElse(null);
    resDO.setDeliveryPlatName(deliveryPlatName);

    resDO.setRiderOrderNo(dto.getRiderOrderNo().getRiderOrderNo());

    String deliveryType = Optional.ofNullable(dto.getDeliveryTypeEnum())
        .map(DeliveryTypeEnum::getCode).orElse(null);
    resDO.setDeliveryType(deliveryType);

    if (dto.getRider() != null) {
      resDO.setRiderName(dto.getRider().getRiderName());
      resDO.setRiderPhone(dto.getRider().getRiderPhone());
      resDO.setRiderStaffCode(dto.getRider().getRiderStaffCode());
      resDO.setCallTime(dto.getRider().getCallTime());
      resDO.setAcceptTime(dto.getRider().getAcceptTime());
      resDO.setPickTime(dto.getRider().getPickTime());
      resDO.setLogisticsCompany(dto.getRider().getLogisticsCompany());
      resDO.setLogisticsName(dto.getRider().getLogisticsName());
      resDO.setLogisticsNo(dto.getRider().getLogisticsNo());
    }
    if (dto.getRiderAmountInfo() != null) {
      resDO.setDeliveryTip(dto.getRiderAmountInfo().getDeliveryTip());
      resDO.setActualDeliveryFee(dto.getRiderAmountInfo().getActualDeliveryFee());
      resDO.setDeliveryFeeTotal(dto.getRiderAmountInfo().getDeliveryFeeTotal());
    }
    if (dto.getTargetInfo() != null) {
      resDO.setRiderAddress(dto.getTargetInfo().getRiderAddress());
      resDO.setLatitude(dto.getTargetInfo().getLatitude());
      resDO.setLongitude(dto.getTargetInfo().getLongitude());
    }
    if (dto.getCancelInfo() != null) {
      resDO.setCancelFlag(dto.getCancelInfo().getCancelFlag());
      resDO.setCancelFrom(dto.getCancelInfo().getCancelFrom());
      resDO.setCancelReason(dto.getCancelInfo().getCancelReason());
      resDO.setCancelDetail(dto.getCancelInfo().getCancelDetail());
    }

    if (dto.getOtherInfo() != null) {
      resDO.setDeliveryClientCode(dto.getOtherInfo().getDeliveryClientCode());
      resDO.setDeliveryStoreCode(dto.getOtherInfo().getDeliveryStoreCode());
      resDO.setExceptionReason(dto.getOtherInfo().getExceptionReason());
      resDO.setExtraInfo(dto.getOtherInfo().getExtraInfo());
      resDO.setDelayState(dto.getOtherInfo().getDelayState());
      resDO.setPreCallFlag(dto.getOtherInfo().getPreCallFlag());
      resDO.setUploadLocationFlag(dto.getOtherInfo().getUploadLocationFlag());
//      resDO.setCreateTime(dto.getOtherInfo().getCreateTime());
//      resDO.setModifyTime(dto.getOtherInfo().getModifyTime());
    }
    return resDO;

  }

  public static OrderDeliveryRecordResDto toDto(OrderDeliveryRecordDO deliveryRecordDO) {
    if (deliveryRecordDO == null) {
      return null;
    }
    OrderDeliveryRecordResDto resDto = new OrderDeliveryRecordResDto();
    resDto.setId(deliveryRecordDO.getId());
    resDto.setOrderNo(OrderNo.orderNo(deliveryRecordDO.getOrderNo()));
    resDto.setDeliveryStateEnum(DeliveryStateEnum.getByCode(deliveryRecordDO.getState()));
    resDto.setDeliveryPlatformCodeEnum(
        DeliveryPlatformCodeEnum.getByName(deliveryRecordDO.getDeliveryPlatName()));
    resDto.setRiderOrderNo(RiderOrderNo.riderOrderNo(deliveryRecordDO.getRiderOrderNo()));
    resDto.setDeliveryTypeEnum(DeliveryTypeEnum.getByCode(deliveryRecordDO.getDeliveryType()));
    resDto.setRider(toRider(deliveryRecordDO));
    resDto.setRiderAmountInfo(toRiderAmountInfo(deliveryRecordDO));
    resDto.setTargetInfo(toTargetInfo(deliveryRecordDO));
    resDto.setCancelInfo(toCancelInfo(deliveryRecordDO));
    resDto.setOtherInfo(toOtherInfo(deliveryRecordDO));
    resDto.setDataVersion(DataVersionTime.dataVersionTime(deliveryRecordDO.getModifyTime()));
    return resDto;
  }

  private static OtherInfo toOtherInfo(OrderDeliveryRecordDO deliveryRecordDO) {
    OtherInfo otherInfo = new OtherInfo();
    otherInfo.setDeliveryClientCode(deliveryRecordDO.getDeliveryClientCode());
    otherInfo.setDeliveryStoreCode(deliveryRecordDO.getDeliveryStoreCode());
    otherInfo.setExceptionReason(deliveryRecordDO.getExceptionReason());
    otherInfo.setExtraInfo(deliveryRecordDO.getExtraInfo());
    otherInfo.setDelayState(deliveryRecordDO.getDelayState());
    otherInfo.setPreCallFlag(deliveryRecordDO.getPreCallFlag());
    otherInfo.setUploadLocationFlag(deliveryRecordDO.getUploadLocationFlag());
    otherInfo.setCreateTime(deliveryRecordDO.getCreateTime());
    otherInfo.setModifyTime(deliveryRecordDO.getModifyTime());
    return otherInfo;
  }

  private static CancelInfo toCancelInfo(OrderDeliveryRecordDO deliveryRecordDO) {
    CancelInfo cancelInfo = new CancelInfo();
    cancelInfo.setCancelFlag(deliveryRecordDO.getCancelFlag());
    cancelInfo.setCancelFrom(deliveryRecordDO.getCancelFrom());
    cancelInfo.setCancelReason(deliveryRecordDO.getCancelReason());
    cancelInfo.setCancelDetail(deliveryRecordDO.getCancelDetail());
    return cancelInfo;

  }

  private static TargetInfo toTargetInfo(OrderDeliveryRecordDO deliveryRecordDO) {
    TargetInfo targetInfo = new TargetInfo();
    targetInfo.setRiderAddress(deliveryRecordDO.getRiderAddress());
    targetInfo.setLatitude(deliveryRecordDO.getLatitude());
    targetInfo.setLongitude(deliveryRecordDO.getLongitude());
    return targetInfo;


  }

  private static RiderAmountInfo toRiderAmountInfo(OrderDeliveryRecordDO deliveryRecordDO) {
    RiderAmountInfo riderAmountInfo = new RiderAmountInfo();
    riderAmountInfo.setDeliveryTip(deliveryRecordDO.getDeliveryTip());
    riderAmountInfo.setActualDeliveryFee(deliveryRecordDO.getActualDeliveryFee());
    riderAmountInfo.setDeliveryFeeTotal(deliveryRecordDO.getDeliveryFeeTotal());
    return riderAmountInfo;
  }

  private static Rider toRider(OrderDeliveryRecordDO deliveryRecordDO) {
    Rider rider = new Rider();
    rider.setRiderName(deliveryRecordDO.getRiderName());
    rider.setRiderPhone(deliveryRecordDO.getRiderPhone());
    rider.setRiderStaffCode(deliveryRecordDO.getRiderStaffCode());
    rider.setCallTime(deliveryRecordDO.getCallTime());
    rider.setAcceptTime(deliveryRecordDO.getAcceptTime());
    rider.setPickTime(deliveryRecordDO.getPickTime());
    rider.setLogisticsCompany(deliveryRecordDO.getLogisticsCompany());
    rider.setLogisticsNo(deliveryRecordDO.getLogisticsNo());
    rider.setLogisticsName(deliveryRecordDO.getLogisticsName());
    return rider;
  }

}
