package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.order.entity.AfterSaleOrderDO;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.OrderPayInfoDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.sdk.common.data.OrderBusinessConsumerMessageDTO;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleBatchReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderBusinessConsumerMessageReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderDetailByThirdReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderDetailReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QuerySimpleOrderReqByThirdDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QuerySimpleOrderReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.SaveOrderOptionalReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.UpdateOrderOptionalReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.FullOrderDtoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.SimpleOrderInfoResDto;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月07日 16:28
 * @email: <EMAIL>
 * <p>
 */
public interface OrderRepository {


  OrderInfoResDto getOrderInfoByOrderNo(OrderInfoQryReqDto orderNoDto);


  SimpleOrderInfoResDto querySimpleOrderInfo(QuerySimpleOrderReqByThirdDto dto);

  SimpleOrderInfoResDto querySimpleOrderInfo(QuerySimpleOrderReqDto dto);
  List<OrderInfoDO> querySimpleOrderInfoList(List<Long> orderNoList);

  Boolean saveOptional(SaveOrderOptionalReq req);

  Boolean updateOptional(UpdateOrderOptionalReq req);


  FullOrderDtoResDto queryFullOrder(QueryOrderDetailReqDto dto);

  FullOrderDtoResDto queryFullOrderByThirdReq(QueryOrderDetailByThirdReqDto dto);

  List<OrderBusinessConsumerMessageDTO> queryOrderBusinessConsumerMessageList(
      QueryOrderBusinessConsumerMessageReq dto);

  FullOrderDtoResDto getOrderInfoByScale(OrderInfoQryByScaleReqDto request);

  List<FullOrderDtoResDto> getOrderInfoBatchByScale(OrderInfoQryByScaleBatchReqDto request);
//  List<OrderDetailSimpleResDto> queryOrderDetailList(List<Long> orderNoList);
  List<OrderDetailDO> queryOrderDetailList(List<Long> orderNoList);
  List<OrderPayInfoDO>  queryPayInfoList(List<Long> orderNoList);

  Map<Long, Boolean> hasRefundOrder(List<Long> orderNoList);

  List<ErpBillInfoDO> queryBillInfoList(List<Long> orderNoList);

  /**
   *
   * @param refundNoList
   * @return
   */
  List<RefundOrderDO> queryRefundOrderList(List<String> refundNoList);


  List<RefundDetailDO> queryRefundDetailList(List<String> refundNoList);

  List<ErpRefundInfoDO> queryErpRefundInfoList( List<String> refundNoList);


  List<AfterSaleOrderDO> selectEfficientErpRefundInfo(List<String> refundNo);
}
