package com.yxt.order.atom.order.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:40
 * @email: <EMAIL>
 */

@Mapper
public interface AutoCreateMapper {

  String showCreateTable(@Param("tableName") String sql);

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  void createTable(@Param("sql") String sql);

  Integer checkTableExists(@Param("tableSchema") String tableSchema,
      @Param("tableName") String tableName);
}
