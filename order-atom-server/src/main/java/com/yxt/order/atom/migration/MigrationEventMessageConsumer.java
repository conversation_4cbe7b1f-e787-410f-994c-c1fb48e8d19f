package com.yxt.order.atom.migration;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.MIGRATION_MQ_Consumer_POOL;
import static com.yxt.order.atom.migration.service.MigrationServiceImpl.MIGRATION_TAG;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.AbstractRocketMQListenerEnhance;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.dao.HanaOrderPay;
import com.yxt.order.atom.migration.dao.constant.MigrateError;
import com.yxt.order.atom.migration.dao.mongo.MigrationDelayMapping;
import com.yxt.order.atom.migration.message.MigrationEventMessage;
import com.yxt.order.atom.migration.service.HanaMigrationService;
import com.yxt.order.atom.migration.service.HanaMigrationServiceImpl.StateEnum;
import com.yxt.order.atom.migration.service.HanaMigrationServiceImpl.SubStateEnum;
import com.yxt.order.atom.migration.service.HdOrderExistCheckComponent;
import com.yxt.order.atom.migration.service.MigrationCommonComponent;
import com.yxt.order.atom.migration.service.MigrationDelayOrderService;
import com.yxt.order.atom.migration.service.MigrationOrderComponent;
import com.yxt.order.atom.migration.service.MigrationRefundOrderComponent;
import com.yxt.order.atom.migration.service.dto.MigrationExtend;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomApi;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.SaveOfflineOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.SaveOfflineRefundOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.res.ExistOrderInfo;
import com.yxt.order.atom.sdk.offline_order.res.ExistRefundOrderInfo;
import com.yxt.order.common.Utils;
import com.yxt.order.common.exception.DetailNotExistsException;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月26日 11:32
 * @email: <EMAIL>
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "${mq.topic.producer.migrationHanaData}_ORDER", topic = "${mq.topic.producer.migrationHanaData}", selectorExpression = MIGRATION_TAG, consumeMode = ConsumeMode.CONCURRENTLY)
public class MigrationEventMessageConsumer extends AbstractRocketMQListenerEnhance {

  @Resource
  private MigrationCommonComponent migrationCommonComponent;

  @Resource
  private HdOrderExistCheckComponent hdOrderExistCheckComponent;

  @Resource
  private MigrationOrderComponent migrationOrderComponent;

  @Resource
  private MigrationRefundOrderComponent migrationRefundOrderComponent;

  @Resource
  private OfflineOrderAtomApi offlineOrderAtomApi;


  @Resource
  private MigrationDelayOrderService migrationDelayOrderService;

  @Qualifier(MIGRATION_MQ_Consumer_POOL)
  @Resource
  private ThreadPoolExecutor migrationMqConsumerPool;

  @Resource
  private HanaMigrationService hanaMigrationService;

  @Value("${migrationHanaDataMinConsumeNum:32}")
  private Integer migrationHanaDataMinConsumeNum;

  @Value("${migrationHanaDataMaxConsumeNum:32}")
  private Integer migrationHanaDataMaxConsumeNum;

  @Value("${onlineOrderFlagStr:YL,Q8,Q7,EM,EN,BA,JG,JE,JD,EO,EL,MU,MT,EZ,EY,ES,EW,EU,EG,EA,DU,DA,GU,DY,CB,BD,BF,GY,GP,GO,GN,GZ,MH,E6,DQ,SP,KA}")
  private String onlineOrderFlagStr;

  @Value("${migrateTimeLimitOnOff:true}")
  private Boolean migrateTimeLimitOnOff;



  @Override
  public Integer minConsumeThreadNum() {
    return migrationHanaDataMinConsumeNum;
  }

  @Override
  public Integer maxConsumeThreadNum() {
    return migrationHanaDataMaxConsumeNum;
  }

  @Value("${migrationHanaClose:false}")
  private Boolean migrationHanaClose;

  @Override
  public void handleMsg(String messageStr) {
    if(migrationHanaClose){
      log.warn("MigrationEventMessageConsumer 暂停消费");
      return;
    }

    log.info("MigrationEventMessageConsumer ,{}", messageStr);
    MigrationEventMessage message = JsonUtils.toObject(messageStr, MigrationEventMessage.class);
    migrationMqConsumerPool.submit(() -> loadToDb(message));
  }

  public void loadToDb(MigrationEventMessage migrationEventMessage) {
    HanaOrderInfo hanaOrderInfo = migrationEventMessage.getHanaOrderInfo();
    HanaMigrationDO itemConfig = migrationEventMessage.getItemConfig();
    String schema = itemConfig.getTargetSchema();
    String taskKey = migrationEventMessage.getTaskKey();

    try {
      // 一条一条处理
      one2one(hanaOrderInfo, schema, taskKey, Boolean.TRUE,itemConfig);
    } catch (Exception e) {
      log.info("MQ消息处理-订单处理失败:{},info日志", JsonUtils.toJson(migrationEventMessage), e);
      hanaMigrationService.processingError(itemConfig, hanaOrderInfo, e.getMessage(), MigrateError.FROM_ARCHIVE_TO_OFFLINE_ORDER);
    }
  }


  public void one2one(HanaOrderInfo hanaOrderInfo, String schema, String taskKey,
      Boolean stageOrder,HanaMigrationDO hanaMigrationDO) {
    try{
      // 过滤线上订单
      if(isOnlineOrder(hanaOrderInfo,schema)){
        migrationOrderComponent.migrateResult(hanaOrderInfo.getId(),schema,StateEnum.ONLINE_ORDER,
            MigrationExtend.builder().build());
        return;
      }


      if (hanaOrderInfo.getActualCollectAmount().compareTo(BigDecimal.ZERO) >= 0) {
        // 正单
        SaveOfflineOrderReqDto reqDto = migrationOrderComponent.buildOrder(hanaOrderInfo,
            schema.toLowerCase());

  //      // 迁移订单,找不到会员Id,暂存
  //      if (stageOrder && stageForMigrationOrder(hanaOrderInfo, schema, taskKey, reqDto)) {
  //        return;
  //      }

        OfflineOrderDTO offlineOrderDTO = reqDto.getOfflineOrderDTO();
        if (businessCreatedTimeFilter(hanaOrderInfo, schema, hanaMigrationDO, offlineOrderDTO.getCreatedTime())) {
          return; // 这个是要忽略掉的,不属于需要处理的数据，不做任何处理; 使用游标的缘故
        }

        // 校验是否重复,重复则直接不做后续操作
        OfflineOrderExistsReqDto existsReqDto = new OfflineOrderExistsReqDto();
        existsReqDto.setStoreCode(offlineOrderDTO.getStoreCode());
        existsReqDto.setThirdOrderNo(offlineOrderDTO.getThirdOrderNo());
        existsReqDto.setThirdPlatformCode(offlineOrderDTO.getThirdPlatformCode());
        existsReqDto.setThirdCreated(offlineOrderDTO.getCreated());

        existsReqDto.setDefineNo(offlineOrderDTO.getOrderNo());
        ResponseBase<ExistOrderInfo> exists = offlineOrderAtomApi.offlineOrderExistsInfo(
            existsReqDto);
        Utils.checkRespSuccess(exists);
        ExistOrderInfo data = exists.getData();
        if (Objects.nonNull(data)) {
          migrationOrderComponent.migrateResult(hanaOrderInfo.getId(), schema,StateEnum.EXISTS,
              MigrationExtend.builder()
                  .orderNo(data.getOrderNo())
                  .thirdCreatedTime(offlineOrderDTO.getCreated()).build());
          return;
        }

        // 二次检查海典订单是否重复
        if(ThirdPlatformCodeEnum.HAIDIAN.name().equals(offlineOrderDTO.getThirdPlatformCode())){
          OfflineOrderDO offlineOrderDO = hdOrderExistCheckComponent.hdOfflineOrderDtoIsExistsWarp(
              hanaOrderInfo, offlineOrderDTO);
          if(Objects.nonNull(offlineOrderDO)){
            migrationOrderComponent.migrateResult(hanaOrderInfo.getId(), schema,StateEnum.EXISTS,
                MigrationExtend.builder()
                    .orderNo(offlineOrderDO.getOrderNo())
                    .thirdCreatedTime(offlineOrderDTO.getCreated()).build());
            return;
          }
        }


        offlineOrderAtomApi.saveOfflineOrder(reqDto); //这里一次会入库多张表,复用接口,未做批量插入
        migrationOrderComponent.migrateResult(hanaOrderInfo.getId(),schema,
            StateEnum.SUCCESS,MigrationExtend.builder().orderNo(offlineOrderDTO.getOrderNo()).build());

        // 如果迁移有父单,做入库,后续等到下一个迁移数据过来使用
        String thirdOrderNo = offlineOrderDTO.getThirdOrderNo();
        String parentThirdOrderNo = offlineOrderDTO.getParentThirdOrderNo();
        if (!StringUtils.isEmpty(parentThirdOrderNo)
            //parentThirdOrderNo不为空说明是拆单,parentThirdOrderNo与thirdOrderNo不等,说明是子单
            && !parentThirdOrderNo.equals(thirdOrderNo)) {
          MigrationDelayMapping migrationDelayMapping = new MigrationDelayMapping();
          migrationDelayMapping.setParentThirdOrderNo(parentThirdOrderNo);
          migrationDelayMapping.setChildOrderNo(offlineOrderDTO.getOrderNo());// 子单号

          migrationDelayMapping.setThirdPlatformCode(offlineOrderDTO.getThirdPlatformCode());
          migrationDelayMapping.setStoreCode(offlineOrderDTO.getStoreCode());
          migrationDelayMapping.setCreateDateTime(new Date());
          migrationDelayOrderService.create(migrationDelayMapping);
        }
      } else {
        // 退单
        SaveOfflineRefundOrderReqDto refundReqDto = migrationRefundOrderComponent.buildRefundOrder(
            hanaOrderInfo, schema.toLowerCase());
        OfflineRefundOrderDTO offlineRefundOrderDTO = refundReqDto.getOfflineRefundOrderDTO();

        if(businessCreatedTimeFilter(hanaOrderInfo, schema, hanaMigrationDO, offlineRefundOrderDTO.getCreatedTime())){
          return;
        }

  //      if (stageOrder && stageUser404ForRefundOrder(hanaOrderInfo, schema, taskKey,
  //          offlineRefundOrderDTO)) {
  //        return;
  //      }

  //      if (stageOrder && stageOrder404ForRefundOrder(hanaOrderInfo, schema, taskKey,
  //          offlineRefundOrderDTO)) {
  //        return;
  //      }

        // 校验是否重复,重复则直接不做后续操作
        OfflineRefundOrderExistsReqDto existsReqDto = new OfflineRefundOrderExistsReqDto();
        existsReqDto.setStoreCode(offlineRefundOrderDTO.getStoreCode());
        existsReqDto.setThirdRefundNo(offlineRefundOrderDTO.getThirdRefundNo());
        existsReqDto.setThirdPlatformCode(offlineRefundOrderDTO.getThirdPlatformCode());
        existsReqDto.setThirdCreated(offlineRefundOrderDTO.getCreated());
        existsReqDto.setDefineNo(offlineRefundOrderDTO.getRefundNo());
        ResponseBase<ExistRefundOrderInfo> exists = offlineOrderAtomApi.offlineRefundOrderExistsInfo(
            existsReqDto);
        Utils.checkRespSuccess(exists);
        ExistRefundOrderInfo data = exists.getData();
        if (Objects.nonNull(data)) {
          migrationOrderComponent.migrateResult(hanaOrderInfo.getId(), schema,StateEnum.EXISTS,
              MigrationExtend.builder()
                  .refundNo(data.getRefundNo())
                  .thirdCreatedTime(offlineRefundOrderDTO.getCreated())
                  .build());
          return;
        }

        // 二次检查海典订单是否重复
        if(ThirdPlatformCodeEnum.HAIDIAN.name().equals(offlineRefundOrderDTO.getThirdPlatformCode())){
          OfflineRefundOrderDO offlineRefundOrderDO = hdOrderExistCheckComponent.hdOfflineRefundOrderDtoIsExistsWarp(
              hanaOrderInfo, offlineRefundOrderDTO);
          if(Objects.nonNull(offlineRefundOrderDO)){
            migrationOrderComponent.migrateResult(hanaOrderInfo.getId(), schema,StateEnum.EXISTS,
                MigrationExtend.builder()
                    .orderNo(offlineRefundOrderDO.getRefundNo())
                    .thirdCreatedTime(offlineRefundOrderDTO.getCreated()).build());
            return;
          }
        }

        offlineOrderAtomApi.saveOfflineRefundOrder(refundReqDto);//这里一次会入库多张表,复用接口,未做批量插入
        migrationOrderComponent.migrateResult(hanaOrderInfo.getId(),schema,StateEnum.SUCCESS,MigrationExtend.builder().refundNo(offlineRefundOrderDTO.getRefundNo()).build());
      }

    }catch (Exception e){
      if(e instanceof DetailNotExistsException){
        migrationOrderComponent.migrateResult(hanaOrderInfo.getId(),schema,StateEnum.WITHOUT_DETAIL,MigrationExtend.builder().build());
      }else {
        throw e;
      }
    }
  }

  private boolean businessCreatedTimeFilter(HanaOrderInfo hanaOrderInfo, String schema,
      HanaMigrationDO hanaMigrationDO, Date createdTime) {
    if(migrateTimeLimitOnOff && !OrderDateUtils.isEfficientDate(hanaMigrationDO.getMigrationStartTime(), hanaMigrationDO.getMigrationEndTime(),createdTime)){
      migrationOrderComponent.migrateResult(hanaOrderInfo.getId(), schema,StateEnum.KEEP,
          MigrationExtend.builder()
              .remark(SubStateEnum.KEEP_NEXT_BATCH_TO_MIGRATION.name())
           .build());
      return true;
    }
    return false;
  }

  private boolean isOnlineOrder(HanaOrderInfo hanaOrderInfo, String schema) {
    List<HanaOrderPay> hanaOrderPayList = migrationCommonComponent.getHanaOrderPays(
        hanaOrderInfo.getThirdOrderNo(), hanaOrderInfo.getStoreCode(), schema,hanaOrderInfo.orderIdList());
    Set<String> payCodeSet = hanaOrderPayList.stream().map(HanaOrderPay::getPayType)
        .collect(Collectors.toSet());
    if (CollectionUtils.isEmpty(payCodeSet)) {
      return Boolean.FALSE;
    }
    Set<String> configOnlinePayCodeSet = Arrays.stream(onlineOrderFlagStr.split(","))
        .collect(Collectors.toSet());
    for (String dbPayCode : payCodeSet) {
      if (configOnlinePayCodeSet.contains(dbPayCode)) {
        return Boolean.TRUE;
      }
    }
    return Boolean.FALSE;
  }

//  private boolean stageUser404ForRefundOrder(HanaOrderInfo hanaOrderInfo, String schema,
//      String taskKey, OfflineRefundOrderDTO offlineRefundOrderDTO) {
//    if (StringUtils.isEmpty(hanaOrderInfo.getClientCode())) {
//      return false;
//    }
//
//    // 1. 查不到会员暂存
//    if (Objects.isNull(offlineRefundOrderDTO) || StringUtils.isEmpty(
//        offlineRefundOrderDTO.getUserId())) {
//      OfflineOrderStagingReqDto stageReq = new OfflineOrderStagingReqDto();
//      stageReq.setMigration(Boolean.TRUE);
//      stageReq.setNumberType(NumberType.REFUND);
//      stageReq.setStagingType(StagingType.QUERY_MEMBER_404_FROM_API);
//
//      StageMigrateData data = new StageMigrateData();
//      data.setHanaOrderInfo(hanaOrderInfo);
//      data.setSchema(schema);
//      data.setTaskKey(taskKey);
//      stageReq.setData(JsonUtils.toJson(data));
//
//      stageReq.setStoreCode(offlineRefundOrderDTO.getStoreCode());
//      stageReq.setThirdPlatformCode(offlineRefundOrderDTO.getThirdPlatformCode());
////      stageReq.setThirdOrderNo(); 退单不设置该字段
//      stageReq.setThirdRefundNo(offlineRefundOrderDTO.getThirdRefundNo());
//      stageReq.setDefineNo(offlineRefundOrderDTO.getRefundNo());
//      stageReq.setUserCardNo(hanaOrderInfo.getClientCode());
//      ResponseBase<Boolean> res = offlineOrderAtomApi.stageOfflineOrder(stageReq);
//      Utils.checkRespSuccess(res);
//      if (res.getData()) {
//        log.warn("迁移订单,[{}]找不到会员Id,暂存,{}", hanaOrderInfo.getClientCode(),
//            JsonUtils.toJson(stageReq));
//        RedisStringUtil.incr(buildSend2MqKey(taskKey, StateEnum.STAGE_FOR_MEMBER_404));
//        return true;
//      }
//    }
//
//    return false;
//  }

//  private boolean stageOrder404ForRefundOrder(HanaOrderInfo hanaOrderInfo, String schema,
//      String taskKey, OfflineRefundOrderDTO offlineRefundOrderDTO) {
//    // 如果本来就没有对应的正单就不用兜底
//    String refundThirdOrderNo = getRefundThirdOrderNo(hanaOrderInfo);
//    if (StringUtils.isEmpty(refundThirdOrderNo)) {
//      return false;
//    }
//
//    // 2. 查不到正单暂存
//    if (Objects.isNull(offlineRefundOrderDTO) || StringUtils.isEmpty(
//        offlineRefundOrderDTO.getOrderNo())) {
//      OfflineOrderStagingReqDto stageReq = new OfflineOrderStagingReqDto();
//      stageReq.setMigration(Boolean.TRUE);
//      stageReq.setNumberType(NumberType.REFUND);
//      stageReq.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
//
//      StageMigrateData data = new StageMigrateData();
//      data.setHanaOrderInfo(hanaOrderInfo);
//      data.setSchema(schema);
//      data.setTaskKey(taskKey);
//      stageReq.setData(JsonUtils.toJson(data));
//
//      stageReq.setStoreCode(offlineRefundOrderDTO.getStoreCode());
//      stageReq.setThirdPlatformCode(offlineRefundOrderDTO.getThirdPlatformCode());
//      stageReq.setThirdOrderNo(refundThirdOrderNo);
//      stageReq.setThirdRefundNo(offlineRefundOrderDTO.getThirdRefundNo());
//      stageReq.setDefineNo(offlineRefundOrderDTO.getRefundNo());
//      ResponseBase<Boolean> res = offlineOrderAtomApi.stageOfflineOrder(stageReq);
//      Utils.checkRespSuccess(res);
//      if (res.getData()) {
//        log.warn("迁移订单,退单[{}]找不到正单,暂存,{}", offlineRefundOrderDTO.getThirdRefundNo(),
//            JsonUtils.toJson(stageReq));
//        RedisStringUtil.incr(buildSend2MqKey(taskKey, StateEnum.STAGE_FOR_ORDER_404));
//        return true;
//      }
//    }
//    return false;
//  }

//  private boolean stageForMigrationOrder(HanaOrderInfo hanaOrderInfo, String schema, String taskKey,
//      SaveOfflineOrderReqDto reqDto) {
//    if (StringUtils.isEmpty(hanaOrderInfo.getClientCode())) {
//      return false;
//    }
//
//    OfflineOrderDTO offlineOrderDTO = reqDto.getOfflineOrderDTO();
//    if (Objects.isNull(offlineOrderDTO) || StringUtils.isEmpty(offlineOrderDTO.getUserId())) {
//      OfflineOrderStagingReqDto stageReq = new OfflineOrderStagingReqDto();
//      stageReq.setMigration(Boolean.TRUE);
//      stageReq.setNumberType(NumberType.ORDER);
//      stageReq.setStagingType(StagingType.QUERY_MEMBER_404_FROM_API);
//
//      StageMigrateData data = new StageMigrateData();
//      data.setHanaOrderInfo(hanaOrderInfo);
//      data.setSchema(schema);
//      data.setTaskKey(taskKey);
//      stageReq.setData(JsonUtils.toJson(data));
//
//      stageReq.setStoreCode(offlineOrderDTO.getStoreCode());
//      stageReq.setThirdPlatformCode(offlineOrderDTO.getThirdPlatformCode());
//      stageReq.setThirdOrderNo(offlineOrderDTO.getThirdOrderNo());
////      stageReq.setThirdRefundNo(offlineOrderDTO.getThirdRefundNo()); //正单不设置该字段
//      stageReq.setDefineNo(offlineOrderDTO.getOrderNo());
//      stageReq.setUserCardNo(hanaOrderInfo.getClientCode());
//      ResponseBase<Boolean> res = offlineOrderAtomApi.stageOfflineOrder(stageReq);
//      Utils.checkRespSuccess(res);
//      if (res.getData()) {
//        log.warn("迁移订单,有卡号[{}]找不到会员Id,暂存,{}", hanaOrderInfo.getClientCode(),
//            JsonUtils.toJson(stageReq));
//        RedisStringUtil.incr(buildSend2MqKey(taskKey, StateEnum.STAGE_FOR_MEMBER_404));
//        return true;
//      }
//    }
//    return false;
//  }
}
