package com.yxt.order.atom.order.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.consistency.EfficientParam;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 基本订单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
@Repository
public interface OrderInfoMapper extends BaseMapper<OrderInfoDO> {

    @Delete({"delete from order_info where order_no = #{orderNo}"})
    Integer deleteByOrderNo(Long orderNo);

    Long selectMaxId(@Param("flashParam") FlashParam flashParam);

    Long selectMinId(@Param("flashParam") FlashParam flashParam);

  @Select("select max(id) from order_info where create_time >= #{param.startDate} and create_time <= #{param.endDate}")
  Long selectEfficientCountMaxId(@Param("param")EfficientParam param);

  @Select("select min(id) from order_info where create_time >= #{param.startDate} and create_time <= #{param.endDate}")
  Long selectEfficientCountMinId(@Param("param")EfficientParam param);
}
