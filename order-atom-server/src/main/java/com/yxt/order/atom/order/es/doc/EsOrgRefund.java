package com.yxt.order.atom.order.es.doc;

import cn.hutool.core.util.StrUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

@Data
@Settings(shardsNum = 12)
@IndexName(value = "es_refund_shard_by_org", keepGlobalPrefix = true)
public class EsOrgRefund {

  @IndexId(type = IdType.CUSTOMIZE)
  private String id;

  /**
   * 系统订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * 三方订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * 系统退单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundNo;

  /**
   * 三方退单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdRefundNo;

  /**
   * 下单时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date created;

  /**
   * 创建时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  /**
   * 订单支付时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date orderCreated;

  /**
   * 线上门店编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeCode;

  /**
   * 机构编码（线下实际发货门店）
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orgCode;

  /**
   * 下单线上门店编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String sourceStoreCode;

  /**
   * 下单线下机构编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String sourceOrgCode;

  /**
   * 0, "待退款",20, "待退货",100, "已完成",102, "已拒绝",103, "已取消"
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer refundStatus;

  /**
   * 下账状态：20, "待下账" 99, "下账失败"  100, "已下账" 102, "已取消"
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String erpStatus;

  /**
   * 下账时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date erpTime;

  /**
   * 退款单零售流水
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String erpRefundNo;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderSource;

  /**
   * HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String platformCode;


  /**
   * 退款类型, PART-部分退款，ALL-全额退款,UNKNOWN-未知
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundType;

  /**
   * 售后单类型 AFTER_SALE_AMOUNT-退款 、AFTER_SALE_GOODS-退货 、AFTER_SALE_AMOUNT_GOODS-退货退款
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String afterSaleType;

  /**
   * 退单标记，通过空格分割，使用match匹配
   */
  @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.WHITESPACE, strategy = FieldStrategy.IGNORED)
  private String refundFlags;

  /**
   * 下账金额
   */
  @IndexField(fieldType = FieldType.SCALED_FLOAT)
  private BigDecimal refundBillAmount;

  /**
   * 退单明细
   */
  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOrgRefundDetail.class)
  private List<EsOrgRefundDetail> detailList;

  /**
   * 会员编码(唯一值)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userCardNo;

  /**
   * 会员ID (心云)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userId;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeType;

  /**
   * 服务模式 O2O B2C B2B
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String serviceMode;

  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  @IndexField(fieldType = FieldType.KEYWORD)
  private Long deleted;

  public void setRefundFlags(List<String> refundFlags) {
    this.refundFlags = StrUtil.join(" ", refundFlags);
  }
}
