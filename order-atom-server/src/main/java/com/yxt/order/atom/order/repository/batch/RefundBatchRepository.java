package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.mapper.OrderDetailMapper;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import org.springframework.stereotype.Repository;


@Repository
public class RefundBatchRepository extends
    ServiceImpl<RefundOrderMapper, RefundOrderDO> {

}

