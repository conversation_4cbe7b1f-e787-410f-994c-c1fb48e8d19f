package com.yxt.order.atom.migration.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 15:41
 * @email: <EMAIL>
 */
@Repository
public class OfflineOrderPlatformErrorDORepository extends
    ServiceImpl<OfflineOrderPlatformErrorDOMapper, OfflineOrderPlatformErrorDO> {

  @Resource
  private OfflineOrderPlatformErrorDOMapper offlineOrderPlatformErrorDOMapper;

  public void insert(OfflineOrderPlatformErrorDO entity) {

    LambdaQueryWrapper<OfflineOrderPlatformErrorDO> query = new LambdaQueryWrapper<>();
    query.eq(OfflineOrderPlatformErrorDO::getOrderType, entity.getOrderType());
    query.eq(OfflineOrderPlatformErrorDO::getBusinessNo, entity.getBusinessNo());
    if (offlineOrderPlatformErrorDOMapper.selectCount(query) >= 1) {
      return;
    }

    offlineOrderPlatformErrorDOMapper.insert(entity);
  }
}

