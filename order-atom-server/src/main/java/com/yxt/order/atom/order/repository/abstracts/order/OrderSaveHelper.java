package com.yxt.order.atom.order.repository.abstracts.order;

import com.yxt.order.atom.order.entity.CommodityStockDO;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OrderSaveHelper {

  // UNIQUE KEY `u_order_erp_details_id` (`order_no`,`erp_code`,`third_detail_id`) USING BTREE,
  // UK 和 PRIMARY KEY本质上是同一个
  private static String orderDetailUk(OrderDetailDO orderDetailDO) {
    return String.format("%s%s%s", orderDetailDO.getOrderNo(), orderDetailDO.getErpCode(),
        orderDetailDO.getThirdDetailId());
  }

  private static String commodityStockVirtualUKForOrderDetail(CommodityStockDO commodityStockDO) {
    return String.format("%s%s%s", commodityStockDO.getOrderNo(), commodityStockDO.getErpCode(),
        commodityStockDO.getThirdDetailId());
  }


  public static Long findOrderDetailId(List<OrderDetailDO> list,
      CommodityStockDO commodityStockDO) {
    String target = commodityStockVirtualUKForOrderDetail(commodityStockDO);
    for (OrderDetailDO orderDetailDO : list) {
      String orderDetailUk = orderDetailUk(orderDetailDO);
      if (target.equals(orderDetailUk)) {
        return orderDetailDO.getId();
      }
    }
    return null;
  }


}
