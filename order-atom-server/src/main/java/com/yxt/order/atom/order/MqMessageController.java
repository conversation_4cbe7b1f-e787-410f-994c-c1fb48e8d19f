package com.yxt.order.atom.order;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.converter.MqMessageConverter;
import com.yxt.order.atom.order.entity.MqMessageDO;
import com.yxt.order.atom.order.repository.MqMessageRepository;
import com.yxt.order.atom.sdk.mqmessage.MqMessageAtomApi;
import com.yxt.order.atom.sdk.mqmessage.MqMessageAtomQueryApi;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageDeleteReqDto;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageQueryReqDto;
import com.yxt.order.atom.sdk.mqmessage.req.SaveMqMessageReqDto;
import com.yxt.order.atom.sdk.mqmessage.res.MqMessageResDto;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月15日 16:34
 * @email: <EMAIL>
 */
@RestController
public class MqMessageController extends AbstractController implements MqMessageAtomApi,
    MqMessageAtomQueryApi {

  @Resource
  private MqMessageRepository mqMessageRepository;

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  @Override
  public ResponseBase<Boolean> save(SaveMqMessageReqDto saveOfflineOrderReqDto) {
    Assert.isTrue(!StringUtils.isEmpty(saveOfflineOrderReqDto.getMsgId()), "msgId不能为空");
    MqMessageDO mqMessageDO = MqMessageConverter.INSTANCE.toDO(saveOfflineOrderReqDto);

    mqMessageRepository.save(mqMessageDO);

    return generateSuccess(Boolean.TRUE);
  }

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  @Override
  public ResponseBase<List<MqMessageResDto>> list(MqMessageQueryReqDto mqMessageQueryReqDto) {
    return generateSuccess(mqMessageRepository.list(mqMessageQueryReqDto));
  }

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  @Override
  public ResponseBase<Boolean> delete(MqMessageDeleteReqDto deleteReqDto) {
    return generateSuccess(mqMessageRepository.delete(deleteReqDto));
  }
}
