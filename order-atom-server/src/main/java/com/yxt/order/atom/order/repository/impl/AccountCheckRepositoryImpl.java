package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.atom.order.entity.AccountCheckChannelDO;
import com.yxt.order.atom.order.entity.AccountCheckPullJobDO;
import com.yxt.order.atom.order.repository.AccountCheckRepository;
import com.yxt.order.atom.order.repository.batch.AccountCheckChannelBatchRepository;
import com.yxt.order.atom.order.repository.batch.AccountCheckPullJobBatchRepository;
import com.yxt.order.atom.sdk.common.data.AccountCheckChannelDTO;
import com.yxt.order.atom.sdk.common.data.AccountCheckPullJobDTO;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckCleanReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckPullJobPageQueryReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckPullJobSaveReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckSaveReqDto;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

@Repository
public class AccountCheckRepositoryImpl implements AccountCheckRepository {

  @Resource
  private AccountCheckChannelBatchRepository accountCheckChannelBatchRepository;

  @Resource
  private AccountCheckPullJobBatchRepository accountCheckPullJobBatchRepository;

  @Value("${account-check-delete-row:50000}")
  private Integer deleteMaxRow;

  @Override
  public void saveAccountCheck(AccountCheckSaveReqDto req) {
    List<AccountCheckChannelDTO> dataList = req.getDataList();
    // 去重
    dataList = dataList.stream().distinct().collect(Collectors.toList());
    List<String> queryList = dataList.stream().map(AccountCheckChannelDTO::getMd5Code).collect(Collectors.toList());
    queryList = queryList.stream().distinct().collect(Collectors.toList());
    QueryWrapper<AccountCheckChannelDO> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().in(AccountCheckChannelDO::getMd5Code, queryList);
    List<AccountCheckChannelDO> existDataList = accountCheckChannelBatchRepository.list(queryWrapper);
    if (CollectionUtils.isEmpty(existDataList)) {
      existDataList = Collections.emptyList();
    }
    Map<String, AccountCheckChannelDO> dataMap = existDataList.stream().collect(Collectors.toMap(AccountCheckChannelDO::getMd5Code, a -> a, (v1, v2) -> v1));
    List<AccountCheckChannelDTO> updateDataList = dataList.stream().filter(data -> dataMap.containsKey(data.getMd5Code())).collect(Collectors.toList());
    List<AccountCheckChannelDTO> insertDataList = dataList.stream().filter(data -> !dataMap.containsKey(data.getMd5Code())).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(updateDataList)) {
      updateDataList.forEach(data -> {
        data.setId(dataMap.get(data.getMd5Code()).getId());
        data.setModifyTime(new Date());
      });
      accountCheckChannelBatchRepository.updateBatchById(BeanUtil.copyToList(updateDataList, AccountCheckChannelDO.class));
    }
    if (!CollectionUtils.isEmpty(insertDataList)) {
      insertDataList.forEach(data -> {
        data.setCreateTime(new Date());
      });
      accountCheckChannelBatchRepository.saveBatch(BeanUtil.copyToList(insertDataList, AccountCheckChannelDO.class));
    }
  }

  @Override
  public void saveAccountCheckPullJob(AccountCheckPullJobSaveReqDto req) {
    if(CollUtil.isEmpty(req.getDataList())){
      return;
    }
    List<AccountCheckPullJobDO> accountCheckJobList = BeanUtil.copyToList(req.getDataList(), AccountCheckPullJobDO.class);
    accountCheckPullJobBatchRepository.saveOrUpdateBatch(accountCheckJobList);
  }

  @DS(DsConstants.DB_ORDER_SLAVE)
  @Override
  public PageDTO<AccountCheckPullJobDTO> queryAccountCheckPullJobPage(AccountCheckPullJobPageQueryReqDto req) {

    IPage<AccountCheckPullJobDO> page = new Page<>(req.getCurrentPage(), req.getPageSize());
    Wrapper<AccountCheckPullJobDO> wrapper = Wrappers.<AccountCheckPullJobDO>lambdaQuery()
        .eq(AccountCheckPullJobDO::getMerCode, req.getMerCode().getMerCode())
        .eq(AccountCheckPullJobDO::getPlatformCode, req.getPlatformCode().getCode())
        .eq(StrUtil.isNotBlank(req.getContextId()), AccountCheckPullJobDO::getContextId, req.getContextId())
        .eq(ObjectUtil.isNotNull(req.getJobStatus()), AccountCheckPullJobDO::getStatus, req.getJobStatus())
        .in(CollUtil.isNotEmpty(req.getClientCodeList()), AccountCheckPullJobDO::getClientCode, req.getClientCodeList())
        .orderByAsc(AccountCheckPullJobDO::getId);
    IPage<AccountCheckPullJobDO> accountCheckPullJobPage = accountCheckPullJobBatchRepository.page(page, wrapper);
    PageDTO<AccountCheckPullJobDTO> resultPage = new PageDTO<>(accountCheckPullJobPage.getCurrent(), req.getPageSize());
    resultPage.setTotalPage(accountCheckPullJobPage.getPages());
    resultPage.setTotalCount(accountCheckPullJobPage.getTotal());
    if (CollUtil.isNotEmpty(accountCheckPullJobPage.getRecords())) {
      resultPage.setData(BeanUtil.copyToList(accountCheckPullJobPage.getRecords(), AccountCheckPullJobDTO.class));
    }
    return resultPage;
  }

  @Override
  public void cleanAccountCheck(AccountCheckCleanReqDto req) {
    LocalDateTime cleanDate = LocalDateTime.now().minusDays(req.getCleanDays());
    List<String> platformCodeList = new ArrayList<>();
    //根据时间获取最大id
    Long maxId = accountCheckChannelBatchRepository.getMaxIdByAccountTime(req.getMerCode(), req.getPlatformCodeList(), cleanDate);
    if(maxId == null){
      return;
    }

    if(CollUtil.isNotEmpty(req.getPlatformCodeList())){
      platformCodeList = req.getPlatformCodeList().stream().map(PlatformCodeEnum::getCode).collect(Collectors.toList());
    }
    LambdaQueryWrapper<AccountCheckChannelDO> wrapper = Wrappers.<AccountCheckChannelDO>lambdaQuery()
        .in(CollUtil.isNotEmpty(req.getOnlineStoreCodeList()), AccountCheckChannelDO::getOnlineStoreCode, req.getOnlineStoreCodeList())
        .le(AccountCheckChannelDO::getId, maxId);
    if(CollUtil.isNotEmpty(platformCodeList)){
      wrapper.eq(AccountCheckChannelDO::getMerCode, req.getMerCode().getMerCode())
          .in(CollUtil.isNotEmpty(platformCodeList), AccountCheckChannelDO::getThirdPlatformCode, platformCodeList);
    }
    wrapper.last(" limit " + deleteMaxRow);
    while (true) {
      int deleteRows = accountCheckChannelBatchRepository.getBaseMapper().delete(wrapper);
      if (deleteRows <= 0) {
        break;
      }
    }
  }
}
