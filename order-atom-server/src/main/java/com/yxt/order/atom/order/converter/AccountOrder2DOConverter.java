package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.AccountOrderDO;
import com.yxt.order.atom.sdk.common.data.AccountOrderDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface AccountOrder2DOConverter {

  AccountOrder2DOConverter INSTANCE = Mappers.getMapper(AccountOrder2DOConverter.class);

  AccountOrderDO toDO(AccountOrderDTO obj);

}
