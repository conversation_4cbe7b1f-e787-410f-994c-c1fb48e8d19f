package com.yxt.order.atom.order.es.sync.order_world.flash;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.AbstractFlashEnhance;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.OrgOrderScene;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOrderWorldOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOrderWorldOrder.OrderInfo;
import com.yxt.order.atom.order.es.sync.order_world.handler.OrderWorldOrderHandler;
import com.yxt.order.atom.order.es.sync.order_world.scene.OrderWorldOrderScene;
import com.yxt.order.atom.order.es.sync.org_order.handler.OrgOfflineOrderHandler;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order_world.entity.OrderInfoDO;
import com.yxt.order.atom.order_world.mapper.NewOrderInfoMapper;
import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.order.enums.OrderSource;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class OrderWorldOrderFlash extends AbstractFlash<OrderInfoDO, OrderInfo, OrderWorldOrderScene> {

  @Autowired
  private OrderWorldOrderHandler orderWorldOrderHandler;

  @Autowired
  private NewOrderInfoMapper orderInfoMapper;

  @Override
  protected Long queryCursorStartId() {
    return orderInfoMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return orderInfoMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OrderInfoDO> getSourceList() {
    LambdaQueryWrapper<OrderInfoDO> query = FlashQueryWrapper.newOrderFlashQuery(getFlashParam(),defaultLimit());
    return orderInfoMapper.selectList(query);
  }


  @Override
  protected List<OrderInfo> assembleTargetData(List<OrderInfoDO> sourceList) {
    return sourceList.stream().map(DoToCanalDtoWrapper::getNewOrder).collect(Collectors.toList());
  }


  @Override
  protected void flash(List<OrderInfo> orderList) {
    CanalOrderWorldOrder canalOrder = new CanalOrderWorldOrder();
    canalOrder.setData(orderList);
    orderWorldOrderHandler.manualFlash(canalOrder);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }
}
