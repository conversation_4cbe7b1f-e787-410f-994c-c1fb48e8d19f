package com.yxt.order.atom.order_world.controller;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order_world.service.OrderWorldCommodityService;
import com.yxt.order.atom.sdk.order_world.OrderWorldCommodityAtomCmdApi;
import com.yxt.order.atom.sdk.order_world.OrderWorldCommodityAtomQueryApi;
import com.yxt.order.atom.sdk.order_world.req.CommodityStockChangeRecordSaveReq;
import com.yxt.order.atom.sdk.order_world.req.CommodityStockChangeRecordSearchReq;
import com.yxt.order.atom.sdk.order_world.res.CommodityStockChangeRecordRes;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OrderWorldCommodityController implements OrderWorldCommodityAtomQueryApi, OrderWorldCommodityAtomCmdApi {

  @Resource
  private OrderWorldCommodityService orderWorldCommodityService;

  @Override
  public ResponseBase<List<CommodityStockChangeRecordRes>> queryCommodityStockStockChangeRecord(CommodityStockChangeRecordSearchReq req) {
    return ResponseBase.success(orderWorldCommodityService.queryCommodityStockStockChangeRecord(req));
  }

  @Override
  public ResponseBase<Void> saveCommodityStockStockChangeRecord(CommodityStockChangeRecordSaveReq req) {
    orderWorldCommodityService.saveCommodityStockStockChangeRecord(req);
    return ResponseBase.success();
  }
}
