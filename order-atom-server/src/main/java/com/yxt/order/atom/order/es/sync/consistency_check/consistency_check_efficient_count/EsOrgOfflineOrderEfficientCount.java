package com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.consistency.AbstractConsistencyCheckEfficientCount;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.org_order.handler.OrgOfflineOrderHandler;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class EsOrgOfflineOrderEfficientCount extends
    AbstractConsistencyCheckEfficientCount<OfflineOrderDO> {

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OrgOfflineOrderHandler orgOfflineOrderHandler;

  @Override
  protected Long queryCursorStartId() {
    return offlineOrderMapper.selectEfficientCountMinId(getParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineOrderMapper.selectEfficientCountMaxId(getParam());
  }

  @Override
  protected List<OfflineOrderDO> dataList() {
    LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<>();
    query.ge(OfflineOrderDO::getId, getParam().getCursorStartId());
    query.lt(OfflineOrderDO::getId, getParam().currentCursorEndId(defaultLimit()));
    return offlineOrderMapper.selectList(query);
  }


  /**
   * @param list
   * @return
   */
  @Override
  protected Long efficientCount(List<OfflineOrderDO> list, Long maximumId) {
    return list.stream().filter(s -> s.getId() <= maximumId)
        .filter(s -> OrderDateUtils.isEfficientDate(getParam().getStartDate(), getParam().getEndDate(), s.getCreatedTime()))
        .map(DoToCanalDtoWrapper::getOfflineOrder)
        .filter(offlineOrder -> orgOfflineOrderHandler.efficientData(offlineOrder))
        .count();
  }
}
