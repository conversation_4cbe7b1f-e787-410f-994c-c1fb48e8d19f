package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderPayInfoDO;
import com.yxt.order.atom.order.mapper.OrderPayInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class OrderPayInfoDelete extends AbstractDelete {

  @Resource
  private OrderPayInfoMapper orderPayInfoMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getOrderPayInfo();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<OrderPayInfoDO> query = new LambdaQueryWrapper<>();
    query.eq(OrderPayInfoDO::getOrderNo, orderNo);
    return orderPayInfoMapper.delete(query);
  }
}
