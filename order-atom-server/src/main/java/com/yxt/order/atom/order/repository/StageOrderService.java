package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.order.mongo.StagingOrder;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderStagingReqDto;
import java.util.List;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月13日 16:24
 * @email: <EMAIL>
 */
public interface StageOrderService {

  Boolean exist(OfflineOrderStagingReqDto dto);

  Boolean create(OfflineOrderStagingReqDto dto);


  List<StagingOrder> queryRefundOrderFind404Order(Integer pageNo, Integer pageSize);

  Long countRefundOrderFind404Order();

  void delete(StagingOrder stagingRefundOrder);

  Long countUser404();

  List<StagingOrder> queryUser404(Integer pageNo, Integer pageSize);
  
}
