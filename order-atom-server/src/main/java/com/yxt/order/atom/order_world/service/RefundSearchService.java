package com.yxt.order.atom.order_world.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.order_world.entity.RefundOrderAmountDO;
import com.yxt.order.atom.order_world.entity.RefundOrderDetailDO;
import com.yxt.order.atom.order_world.entity.RefundOrderPayDO;
import com.yxt.order.atom.order_world.entity.RefundOrderUserDO;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderAmountMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderUserMapper;
import com.yxt.order.atom.order_world.repository.NewRefundOrderDetailBatchRepository;
import com.yxt.order.atom.order_world.repository.NewRefundPayBatchRepository;
import java.util.List;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class RefundSearchService {

  @Autowired
  private NewRefundOrderAmountMapper refundOrderAmountMapper;
  @Autowired
  private NewRefundOrderDetailBatchRepository refundOrderDetailBatchRepository;
  @Autowired
  private NewRefundPayBatchRepository refundPayBatchRepository;
  @Autowired
  private NewRefundOrderUserMapper refundOrderUserMapper;

  public RefundOrderUserDO getRefundUserInfo(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return refundOrderUserMapper.selectOne(Wrappers.<RefundOrderUserDO>lambdaQuery()
          .eq(RefundOrderUserDO::getRefundNo, refundNo).last(" limit 1 "));
    }
  }


  public RefundOrderAmountDO getRefundAmount(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return refundOrderAmountMapper.selectOne(Wrappers.<RefundOrderAmountDO>lambdaQuery()
          .eq(RefundOrderAmountDO::getRefundNo, refundNo).last(" limit 1 "));
    }
  }

  public List<RefundOrderPayDO> getOrderPayList(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return refundPayBatchRepository.list(Wrappers.<RefundOrderPayDO>lambdaQuery()
          .eq(RefundOrderPayDO::getRefundNo, refundNo));
    }
  }

  public List<RefundOrderDetailDO> getRefundDetailList(String refundNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return refundOrderDetailBatchRepository.list(Wrappers.<RefundOrderDetailDO>lambdaQuery()
          .eq(RefundOrderDetailDO::getRefundNo, refundNo));
    }
  }

}
