员工单个的和list分批查询：
@PostMapping("/1.0/listQueryEmployee")
@ApiOperation(value = "列表查询员工", notes = "列表查询员工")
ResponseBase<List<EmployeeResDTO>> listQueryEmployee(@RequestBody @Valid EmployeeListQueryReqDTO reqDTO);

@PostMapping("/1.0/getEmployeeSimple")
@ApiOperation(value = "查询员工基础信息（只有员工主表数据）", notes = "查询员工基础信息")
ResponseBase<EmployeeSimpleResDTO> getEmployeeSimple(@RequestBody @Valid EmployeeGetReqDTO reqDTO);

组织单个和分批查询：

/**
* 根据条件查询组织信息-（查数据库）
*
* @param openReqDTO 请求参数
* @return List<OrgTreeOpenResDTO>
*/
@PostMapping(value = "/1.0/listOrgByCondition")
@ApiOperation(value = "根据条件查询组织信息", notes = "根据条件查询组织信息")
ResponseBase<List<OrgInfoQueryOpenResDTO>> listOrgByCondition(
@RequestBody @Valid OrganizationConditionOpenReqDTO openReqDTO);

/**
 * 根据条件查询组织信息-（查本地缓存）
 *
 * @param openReqDTO 请求参数
 * @return List<OrgTreeOpenResDTO>
 */
@PostMapping(value = "/1.0/listOrgByCache")
@ApiOperation(value = "根据条件查询缓存的组织信息", notes = "根据条件查询缓存的组织信息")
ResponseBase<List<OrgInfoQueryOpenResDTO>> listOrgByCache(
    @RequestBody @Valid OrganizationCacheOpenReqDTO openReqDTO);