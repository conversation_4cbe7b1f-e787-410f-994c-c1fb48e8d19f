package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.abstracts.order.InsertOrder;
import com.yxt.order.atom.order.repository.batch.OrderDetailBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:57
 * @email: <EMAIL>
 */
@Component
@Order(InsertOrder.OrderDetailInsert)
public class OrderDetailInsert extends AbstractInsert<List<OrderDetailDO>> {

  @Resource
  private OrderDetailBatchRepository orderDetailBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderDetailList());
  }

  @Override
  protected Integer insert(List<OrderDetailDO> list) {
    return orderDetailBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderDetailDO> data() {
    return saveDataOptional.getOrderDetailList();
  }

}
