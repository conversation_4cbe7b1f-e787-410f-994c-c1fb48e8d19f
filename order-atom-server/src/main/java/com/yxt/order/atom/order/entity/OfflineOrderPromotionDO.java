package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("offline_order_promotion")
public class OfflineOrderPromotionDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String orderNo;

    private String erpCode;

    private BigDecimal commodityCount;

    private String thirdOrderNo;

    private String promotionNo;

    private String subPromotionNo;

    private String promotionType;

    private BigDecimal promotionAmount;

    private String createdBy;

    private String updatedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    private Long version;

    private String type;

    private String extendJson;

}