package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.SimpleOmsOrderInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface OmsOrderInfo2DtoConverter {

  OmsOrderInfo2DtoConverter INSTANCE = Mappers.getMapper(OmsOrderInfo2DtoConverter.class);

  SimpleOmsOrderInfoDTO toDto(OmsOrderInfoDO obj);

}
