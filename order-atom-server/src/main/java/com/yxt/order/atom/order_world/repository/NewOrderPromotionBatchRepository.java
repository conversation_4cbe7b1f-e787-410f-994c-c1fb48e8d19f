package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.OrderDetailPromotionDO;
import com.yxt.order.atom.order_world.entity.OrderPromotionDO;
import com.yxt.order.atom.order_world.mapper.NewOrderDetailPromotionMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderPromotionMapper;
import org.springframework.stereotype.Repository;

@Repository
public class NewOrderPromotionBatchRepository extends ServiceImpl<NewOrderPromotionMapper, OrderPromotionDO> {

}
