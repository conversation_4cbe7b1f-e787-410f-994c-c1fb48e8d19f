package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.OrderDetailCouponDO;
import com.yxt.order.atom.order_world.entity.OrderDetailPromotionDO;
import com.yxt.order.atom.order_world.mapper.NewOrderDetailCouponMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderDetailPromotionMapper;
import org.springframework.stereotype.Repository;

@Repository
public class NewOrderDetailCouponBatchRepository extends ServiceImpl<NewOrderDetailCouponMapper, OrderDetailCouponDO> {

}
