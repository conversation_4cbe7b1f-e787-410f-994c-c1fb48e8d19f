package com.yxt.order.atom.migration.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.order.atom.migration.dao.enums.UnprocessableSceneEnum;
import com.yxt.order.atom.migration.dao.enums.UnprocessableStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 无法处理的订单
 *
 * @author: moatkon
 * @time: 2025/3/13 18:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("unprocessable_order")
public class UnprocessableOrderDO {



  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 处理场景
   *
   * @see UnprocessableSceneEnum
   */
  private String scene;


  /**
   * 业务主键
   */
  private String businessId;


  /**
   * 业务单号
   */
  private String businessNo;

  /**
   * 三方平台编码
   *
   * @see com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum
   */
  private String thirdPlatformCode;

  private String thirdBusinessNo;

  private String storeCode;

  private String shardingNo;

  private Integer count;

  private String amountJson;

  /**
   * 是否允许操作 false,true
   */
  private String allowOperate;

  /**
   * 处理状态
   * @see UnprocessableStatusEnum
   */
  private String status;

  /**
   * 笔记
   */
  private String note;

  /**
   * 是否是迁移订单 true,false
   */
  private String migration;

}
