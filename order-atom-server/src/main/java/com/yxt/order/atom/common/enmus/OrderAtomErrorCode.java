package com.yxt.order.atom.common.enmus;

import com.yxt.lang.constants.response.ResponseCodeType;
import com.yxt.order.common.OrderErrorCode;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/8/7
 */
public enum OrderAtomErrorCode implements OrderErrorCode {

  DB_FOR_UPDATE_FAIL(ResponseCodeType.BIZ_EXCEPTION, "DB", "100",
      "DB更新失败"),

  ORDER_NOT_EXISTS(ResponseCodeType.BIZ_EXCEPTION, "DB", "101",
      "订单不存在"),

  ;


  private final ResponseCodeType errorCodeEnum;
  private final String subDomain;
  private final String subErrorCode;
  private final String subErrorMessageTemplate;

  OrderAtomErrorCode(ResponseCodeType errorCodeEnum, String subDomain, String subErrorCode,
      String subErrorMessageTemplate) {
    this.errorCodeEnum = errorCodeEnum;
    this.subDomain = subDomain;
    this.subErrorCode = subErrorCode;
    this.subErrorMessageTemplate = subErrorMessageTemplate;
  }

  @Override
  public ResponseCodeType getErrorCodeEnum() {
    return this.errorCodeEnum;
  }

  @Override
  public String getApplicationCode() {
    return "orderAtom";
  }

  @Override
  public String getSubErrorCode() {
    return String.format("%s_%s", this.subDomain, this.subErrorCode);
  }

  @Override
  public String getSubErrorMessageTemplate() {
    return this.subErrorMessageTemplate;
  }
}
