package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOrderDeliveryAddress.OrderDeliveryAddress;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2024/11/9 10:55
 */
public class CanalOrderDeliveryAddress extends BaseCanalData<OrderDeliveryAddress> {
  @Data
  public static class OrderDeliveryAddress {
    @JsonProperty("oms_order_no")
    private Long omsOrderNo;
  }
}
