package com.yxt.order.atom.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.order.entity.OfflineOrderPromotionDO;
import com.yxt.order.atom.order.mapper.dto.IdOrderNo;
import java.util.List;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface OfflineOrderPromotionMapper extends BaseMapper<OfflineOrderPromotionDO> {

  @Select({"select id,order_no as orderNo from offline_order_promotion where id > #{startId} order by id asc limit 100"})
  List<IdOrderNo> selectPromotionOrderNoList(Long startId);

}