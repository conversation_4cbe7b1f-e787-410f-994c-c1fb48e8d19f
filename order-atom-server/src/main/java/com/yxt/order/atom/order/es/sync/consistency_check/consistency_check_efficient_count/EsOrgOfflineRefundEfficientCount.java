package com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.consistency.AbstractConsistencyCheckEfficientCount;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.org_order.handler.OrgOfflineRefundHandler;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/25 15:30
 */
@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class EsOrgOfflineRefundEfficientCount extends
    AbstractConsistencyCheckEfficientCount<OfflineRefundOrderDO> {

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private OrgOfflineRefundHandler orgOfflineRefundHandler;


  @Override
  protected Long queryCursorStartId() {
    return offlineRefundOrderMapper.selectEfficientCountMinId(getParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineRefundOrderMapper.selectEfficientCountMaxId(getParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> dataList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<>();
    query.ge(OfflineRefundOrderDO::getId, getParam().getCursorStartId());
    query.lt(OfflineRefundOrderDO::getId, getParam().currentCursorEndId(defaultLimit()));
    return offlineRefundOrderMapper.selectList(query);
  }


  /**
   * @param list
   * @return
   */
  @Override
  protected Long efficientCount(List<OfflineRefundOrderDO> list, Long maximumId) {
    return list.stream().filter(s -> s.getId() <= maximumId)
        .filter(s -> OrderDateUtils.isEfficientDate(getParam().getStartDate(), getParam().getEndDate(), s.getCreatedTime()))
        .map(DoToCanalDtoWrapper::getOfflineRefundOrder)
        .filter(offlineRefundOrder -> orgOfflineRefundHandler.efficientData(offlineRefundOrder))
        .count();
  }
}
