package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderCouponInfoDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OrderCouponInfoBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:58
 * @email: <EMAIL>
 */
@Component
public class OrderCouponInfoInsert extends AbstractInsert<List<OrderCouponInfoDO>> {

  @Resource
  private OrderCouponInfoBatchRepository orderCouponInfoBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderCouponInfoList());
  }

  @Override
  protected Integer insert(List<OrderCouponInfoDO> list) {
    return orderCouponInfoBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderCouponInfoDO> data() {
    return saveDataOptional.getOrderCouponInfoList();
  }
}
