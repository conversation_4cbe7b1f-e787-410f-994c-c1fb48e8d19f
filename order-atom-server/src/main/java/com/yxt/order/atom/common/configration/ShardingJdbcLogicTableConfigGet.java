package com.yxt.order.atom.common.configration;

import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @author: moatkon
 * @time: 2024/12/27 9:40
 */
@Configuration
@ConfigurationProperties(prefix = "spring.shardingsphere.sharding")
@Data
public class ShardingJdbcLogicTableConfigGet {
  private Map<String, Object> tables;
}
