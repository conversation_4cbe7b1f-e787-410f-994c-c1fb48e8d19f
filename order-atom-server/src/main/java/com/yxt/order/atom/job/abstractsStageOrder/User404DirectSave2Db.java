package com.yxt.order.atom.job.abstractsStageOrder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月16日 17:56
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class User404DirectSave2Db extends AbstractStageOrderHandler {


  @Override
  protected String tag() {
    return buildUser404Tag("directSave");
  }


  @Override
  protected Boolean isStageOrder() {
    return Boolean.FALSE;
  }


}
