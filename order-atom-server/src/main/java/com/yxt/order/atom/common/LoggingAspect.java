package com.yxt.order.atom.common;

import com.yxt.lang.util.JsonUtils;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月29日 11:18
 * @email: <EMAIL>
 */

@Aspect
@Component
public class LoggingAspect {

  private static final Logger logger = LoggerFactory.getLogger(LoggingAspect.class);

  @Value("${orderAtomServiceIgnoreError:退单对应的正单未推到订单库,找不到订单||||退单对应的正单未推到订单库,找不到订单2}")
  private String orderAtomServiceIgnoreError;

  @Value("${migrationIgnoreError:uk_storeCode_third_platform_code_no}")
  private String migrationIgnoreError;

  @Pointcut("@within(org.springframework.web.bind.annotation.RestController)")
  public void restControllerPointcut() {
  }

  @Around("restControllerPointcut()")
  public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
    long start = System.currentTimeMillis();

    // 尝试获取HTTP请求信息
    Optional<HttpServletRequest> requestOptional = Optional.ofNullable(
            RequestContextHolder.getRequestAttributes())
        .filter(ServletRequestAttributes.class::isInstance)
        .map(ServletRequestAttributes.class::cast)
        .map(ServletRequestAttributes::getRequest);

    // 记录请求日志
    String argsJson = JsonUtils.toJson(joinPoint.getArgs());
    String methodName = joinPoint.getSignature().getName();

    if (requestOptional.isPresent()) {
      HttpServletRequest request = requestOptional.get();
      logger.info("Request: {} {} - Method: {} - Args: {}",
          request.getMethod(),
          request.getRequestURI(),
          methodName,
          argsJson);
    } else {
      logger.info("Method called: {} - Args: {}",
          methodName,
          argsJson);
    }

    Object result;
    try {
      result = joinPoint.proceed();
    } catch (Exception e) {
      // 忽略迁移告警
      migrationIgnore( e,  argsJson);

      if (Objects.nonNull(e.getMessage()) && ignore(e.getMessage())) {
        logger.info("Exception in {}.{} errorMessage:{}, req:{}",
            joinPoint.getSignature().getDeclaringTypeName(),
            methodName, e.getMessage(), argsJson);
      } else {
        logger.error("Exception in {}.{} errorMessage:{}, req:{}",
            joinPoint.getSignature().getDeclaringTypeName(),
            methodName, e.getMessage(), argsJson);
      }
      throw e;
    }

    long executionTime = System.currentTimeMillis() - start;

    // 记录响应日志
    logger.info("Response: {} - Execution Time: {}ms",
        JsonUtils.toJson(result),
        executionTime);

    return result;
  }

  private void migrationIgnore( Exception e,String argsJson) {
    try {
      if(e instanceof DuplicateKeyException){
        if(Thread.currentThread().getName().contains("migrationMqConsumerPool") && migrationIgnore(e.getMessage())){
          logger.info("迁移重复,忽略:{}", argsJson);
        }
      }

    } catch (Exception ignore) {

    }
  }

  private Boolean ignore(String errorMsg) {
    List<String> collect = Arrays.stream(orderAtomServiceIgnoreError.split("\\|\\|\\|\\|"))
        .collect(Collectors.toList());
    for (String ignore : collect) {
      if(errorMsg.contains(ignore)){
        return true;
      }
    }
    return false;
  }

  private Boolean migrationIgnore(String errorMsg) {
    List<String> collect = Arrays.stream(migrationIgnoreError.split("\\|\\|\\|\\|"))
        .collect(Collectors.toList());
    for (String ignore : collect) {
      if(errorMsg.contains(ignore)){
        return true;
      }
    }
    return false;
  }
}