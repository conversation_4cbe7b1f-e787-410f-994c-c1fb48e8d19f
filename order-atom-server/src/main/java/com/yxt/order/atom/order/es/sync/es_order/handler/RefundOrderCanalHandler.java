package com.yxt.order.atom.order.es.sync.es_order.handler;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.dto.OrderDetailDto;
import com.yxt.order.atom.order.es.dto.RefundDetailDto;
import com.yxt.order.atom.order.es.sync.clean.ExpireDaysConstant;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel.EsOrderItemModel;
import com.yxt.order.atom.order.mapper.SyncEsMapper;
import com.yxt.order.common.exception.DetailNotExistsException;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月20日 14:21
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class RefundOrderCanalHandler extends
    AbstractCanalHandler<CanalRefundOrder,EsOrderIndexModel> {


  @Resource
  protected SyncEsMapper syncEsMapper;

  @Resource
  protected SyncComponent syncComponent;

  public RefundOrderCanalHandler() {
    super(CanalRefundOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.REFUND_ORDER);
  }

  @Override
  protected List<EsOrderIndexModel> assemble() {

    List<RefundOrder> refundOrderList = getData().getData();
    if (CollectionUtils.isEmpty(refundOrderList)) {
      return Lists.newArrayList();
    }

    return refundOrderList.stream()
        .filter(refundOrder -> !OrderDateUtils.isExpired(refundOrder.getCreateTime(), ExpireDaysConstant.EsOrderEfficientDays))
        // 需要已完成的状态
        .filter(refundOrder -> refundOrderDoneStatus(refundOrder.getState()))
        // 需要有userId的数据
        .filter(refundOrder -> !StringUtils.isEmpty(syncComponent.queryRefundOrderNo(refundOrder.getOrderNo())))
        // 需要非邮费单的退单数据
        .filter(this::ignorePostOrder)
        .map(refundOrder -> {
          EsOrderIndexModel esOrderModel = new EsOrderIndexModel();
          esOrderModel.setOrderNumber(refundOrder.getRefundNo());
          esOrderModel.setRefundNo(refundOrder.getRefundNo());
          esOrderModel.setOrderNo(refundOrder.getOrderNo());
          esOrderModel.setOnlineStoreCode(refundOrder.getOnlineStoreCode());
          esOrderModel.setOrganizationCode(refundOrder.getOrganizationCode());
          esOrderModel.setUserId(syncComponent.queryRefundOrderNo(refundOrder.getOrderNo()));
          esOrderModel.setEsOrderType(EsOrderType.REFUND);
          esOrderModel.setServiceMode(
              syncComponent.mappingServiceMode(refundOrder.getServiceMode()));
          esOrderModel.setEsOrderStatus(mapppingEsOrderStatus(refundOrder.getState()));
//          esOrderModel.setPayTime();
          esOrderModel.setCompleteTime(refundOrder.getCompleteTime());
          esOrderModel.setThirdOrderNo(refundOrder.getThirdOrderNo());
          esOrderModel.setThirdRefundNo(refundOrder.getThirdRefundNo());
          esOrderModel.setPlatformCode(refundOrder.getThirdPlatformCode());
          esOrderModel.setCreateTime(refundOrder.getCreateTime());

          List<RefundDetailDto> refundDetailList = getRefundDetailDtoList(esOrderModel);

          esOrderModel.setEsOrderItemModelList(refundDetailList.stream().map(detail -> {
            EsOrderItemModel itemModel = new EsOrderItemModel();
            itemModel.setCommodityCode(detail.getCommodityCode());
            itemModel.setCommodityName(detail.getCommodityName());
            itemModel.setCommodityCount(detail.getCommodityCount());
            itemModel.setFiveClass(detail.getFiveClass());
            return itemModel;
          }).collect(Collectors.toList()));

          return esOrderModel;
        }).collect(Collectors.toList());
  }

  private boolean ignorePostOrder(RefundOrder refundOrder) {
    Long omsOrderNo = refundOrder.getOmsOrderNo();
    if(0L == omsOrderNo){ // 0 是DB默认值
      return true;
    }

    if(DsConstants.B2C.equals(refundOrder.getServiceMode())){
      Integer postOrder = syncEsMapper.isPostOrder(omsOrderNo);
      // `is_post_fee_order` tinyint(1) DEFAULT '0' COMMENT '是否为邮费单，0否（默认），1是',
      if(postOrder == 1){
        log.info("该退单是邮费单,不推送,{}",refundOrder.getRefundNo());
        return false;
      }
    }

    return true;
  }

  @Retryable(value = DetailNotExistsException.class,maxAttempts = 8,backoff = @Backoff(delay = 2000,multiplier = 1.5))
  public List<RefundDetailDto> getRefundDetailDtoList(EsOrderIndexModel esOrderModel) {
    List<RefundDetailDto> refundDetailList = syncEsMapper.selectDetailByRefundNo(
        Long.valueOf(esOrderModel.getOrderNumber()));
    if(CollectionUtils.isEmpty(refundDetailList)){
      String error = String.format("线上单-退单明细不存在,%s", esOrderModel.getOrderNumber());
      throw new DetailNotExistsException(error);
    }

    // 处理退款明细五级分类
    List<OrderDetailDto> orderDetailList = syncEsMapper.selectDetailByOrderNo(
        Long.valueOf(esOrderModel.getOrderNo()));
    if (!CollectionUtils.isEmpty(orderDetailList)) {
      Map<String, OrderDetailDto> orderDetailDtoMap = orderDetailList.stream()
          .collect(Collectors.toMap(OrderDetailDto::getCommodityCode, v -> v, (v1, v2) -> v1));
      refundDetailList.forEach(refundDetailDto -> {
        OrderDetailDto orderDetailDto = orderDetailDtoMap.get(refundDetailDto.getCommodityCode());
        if (Objects.nonNull(orderDetailDto)) {
          refundDetailDto.setFiveClass(orderDetailDto.getFiveClass());
        }
      });
    }

    return refundDetailList;
  }

  private EsOrderStatus mapppingEsOrderStatus(String refundStatus) {
    if (refundOrderDoneStatus(refundStatus)) {
      return EsOrderStatus.DONE;
    }
    return null;
  }



  private boolean refundOrderDoneStatus(String refundStatus) {
//    SUCCESS(100, "已完成"),
    return "100".equals(refundStatus);
  }

}
