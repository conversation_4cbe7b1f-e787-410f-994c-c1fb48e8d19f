package com.yxt.order.atom.order.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.DeletedDataDO;
import com.yxt.order.atom.order.mapper.DeletedDataMapper;
import com.yxt.order.atom.order.repository.DeleteDataRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * @author: moatkon
 * @time: 2025/1/23 16:50
 */
@Repository
public class DeleteDataRepositoryImpl implements DeleteDataRepository {

  @Resource
  private DeletedDataMapper deletedDataMapper;

  @Override
  public void toDeleteData(DeletedDataDO deletedDataDO) {
    // 校验重复数据
    LambdaQueryWrapper<DeletedDataDO> distinctQuery = new LambdaQueryWrapper<>();
    distinctQuery.eq(DeletedDataDO::getBusinessNo,deletedDataDO.getBusinessNo());
    distinctQuery.eq(DeletedDataDO::getDatabaseName,deletedDataDO.getDatabaseName());
    distinctQuery.eq(DeletedDataDO::getTableName,deletedDataDO.getTableName());
    distinctQuery.eq(DeletedDataDO::getReason,deletedDataDO.getReason());
    if(deletedDataMapper.selectCount(distinctQuery) >=1){
      return;
    }

    deletedDataMapper.insert(deletedDataDO);
  }

}
