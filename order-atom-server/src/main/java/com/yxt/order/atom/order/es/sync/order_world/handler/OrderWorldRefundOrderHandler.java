package com.yxt.order.atom.order.es.sync.order_world.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_REFUND_ORDER_REGEX;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.es.components.OrderWorldSyncComponent;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrderWorldOrder;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrderWorldOrder.RefundInfo;
import com.yxt.order.atom.order.es.sync.order_world.model.EsOrderWorldRefundOrderDetailModel;
import com.yxt.order.atom.order.es.sync.order_world.model.EsOrderWorldRefundOrderModel;
import com.yxt.order.atom.order_world.entity.RefundOrderAmountDO;
import com.yxt.order.atom.order_world.entity.RefundOrderDO;
import com.yxt.order.atom.order_world.entity.RefundOrderDetailDO;
import com.yxt.order.atom.order_world.entity.RefundOrderUserDO;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.order_world.BusinessType;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class OrderWorldRefundOrderHandler extends AbstractCanalHandler<CanalRefundOrderWorldOrder, EsOrderWorldRefundOrderModel> {

  @Resource
  private OrderWorldSyncComponent orderWorldSyncComponent;

  protected OrderWorldRefundOrderHandler() {
    super(CanalRefundOrderWorldOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD_OFFLINE.equals(database) && (Table.tableRegex(OFFLINE_REFUND_ORDER_REGEX, table));
  }

  @Override
  protected List<EsOrderWorldRefundOrderModel> assemble() {

    List<RefundInfo> canalRefundList = getData().getData();
    if (CollUtil.isEmpty(canalRefundList)) {
      return new ArrayList<>(0);
    }
    List<RefundInfo> filterRefundList = canalRefundList.stream().filter(this::efficientData)
        .collect(Collectors.toList());
    if(CollUtil.isEmpty(filterRefundList)){
      return new ArrayList<>(0);
    }
    List<EsOrderWorldRefundOrderModel> esRefundList = new ArrayList<>();
    for (RefundInfo canalRefundInfo : filterRefundList) {
      //根据orderNo查询订单主信息
      RefundOrderDO refundInfo = orderWorldSyncComponent.getRefundInfoByRefundNo(canalRefundInfo.getRefundNo(), canalRefundInfo.getNeedRoute());
      if (ObjectUtil.isNull(refundInfo)) {
        continue;
      }
      //根据orderNo查询订单明细信息
      List<RefundOrderDetailDO> refundDetailList = orderWorldSyncComponent.getRefundDetailByRefundNo(canalRefundInfo.getRefundNo(), canalRefundInfo.getNeedRoute());
      //根据 orderNo 查询订单金额信息
      RefundOrderAmountDO refundAmount = orderWorldSyncComponent.getRefundAmountByOrderNo(canalRefundInfo.getRefundNo(), canalRefundInfo.getNeedRoute());
      EsOrderWorldRefundOrderModel esRefund = new EsOrderWorldRefundOrderModel();
      esRefund.setOrderNo(refundInfo.getOrderNo());
      esRefund.setRefundNo(refundInfo.getRefundNo());
      esRefund.setUserId(refundInfo.getUserId());
      if(StrUtil.isNotBlank(refundInfo.getUserId())){
        RefundOrderUserDO refundUserInfo = orderWorldSyncComponent.getRefundUserInfoByRefundNo(refundInfo.getRefundNo(), canalRefundInfo.getNeedRoute());
        if(ObjectUtil.isNotNull(refundUserInfo)){
          esRefund.setUserCardNo(refundUserInfo.getUserCardNo());
        }
      }
      esRefund.setThirdPlatformCode(refundInfo.getThirdPlatformCode());
      esRefund.setThirdRefundNo(refundInfo.getThirdRefundNo());
      esRefund.setThirdOrderNo(refundInfo.getThirdOrderNo());
      esRefund.setAfterSaleScope(refundInfo.getRefundType());
      esRefund.setAfterSaleType(refundInfo.getAfterSaleType());
      esRefund.setRefundStatus(refundInfo.getRefundStatus());
      esRefund.setCreated(LocalDateTimeUtil.of(refundInfo.getCreated()));
      esRefund.setApplyTime(LocalDateTimeUtil.of(refundInfo.getApplyTime()));
      esRefund.setRefundAmount(refundAmount.getTotalRefund());
      esRefund.setRefundFlags(null);
      esRefund.setAfterSaleNo(refundInfo.getAfterSaleNo());
      esRefund.setTransactionChannel(refundInfo.getTransactionChannel());
      esRefund.setBusinessType(refundInfo.getBusinessType());
      esRefund.setLaunchOrganizationCode(refundInfo.getLaunchOrganizationCode());
      esRefund.setLaunchUserId(refundInfo.getLaunchUserId());
      esRefund.setMerCode(refundInfo.getMerCode());
      esRefund.setCompanyCode(refundInfo.getCompanyCode());
      esRefund.setOrganizationCode(refundInfo.getOrganizationCode());
      esRefund.setValid(refundInfo.getIsValid());
      esRefund.setCreatedDay(refundInfo.getCreatedDay());
      esRefund.setSysCreateTime(LocalDateTimeUtil.of(refundInfo.getCreatedTime()));
      if (CollUtil.isNotEmpty(refundDetailList)) {
        List<EsOrderWorldRefundOrderDetailModel> refundDetailModels = refundDetailList.stream()
            .map(detail -> {
              EsOrderWorldRefundOrderDetailModel refundDetailModel = new EsOrderWorldRefundOrderDetailModel();
              refundDetailModel.setRefundDetailNo(detail.getRefundDetailNo());
              refundDetailModel.setErpCode(detail.getErpCode());
              refundDetailModel.setErpName(detail.getErpName());
              return refundDetailModel;
            }).collect(Collectors.toList());
        esRefund.setDetailList(refundDetailModels);
      }
      esRefundList.add(esRefund);
    }
    return esRefundList;
  }

  public boolean efficientData(RefundInfo refundInfo) {
    //暂时只筛选B2B
    if(BusinessType.JOIN_B2B.getType().equalsIgnoreCase(refundInfo.getBusinessType())){
      return true;
    }
    return false;
  }
}