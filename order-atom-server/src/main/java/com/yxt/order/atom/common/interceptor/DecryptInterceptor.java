package com.yxt.order.atom.common.interceptor;


import com.yxt.order.atom.common.interceptor.config.SensitiveConfig;
import com.yxt.order.common.annotations.SensitiveModel;
import com.yxt.order.common.cryptography.AESDecrypt;
import java.lang.reflect.Field;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * mybatis 返回接果拦截器
 */
@Slf4j
@Component
@Intercepts({
    @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
})
public class DecryptInterceptor implements Interceptor {

  private AESDecrypt aesDecrypt;
  private SensitiveConfig sensitiveConfig;

  public DecryptInterceptor(AESDecrypt aesDecrypt, SensitiveConfig sensitiveConfig) {
    this.aesDecrypt = aesDecrypt;
    this.sensitiveConfig = sensitiveConfig;
  }


  @Override
  public Object intercept(Invocation invocation) throws Throwable {
    ResultSetHandler resultSetHandler = (ResultSetHandler) invocation.getTarget();
    Field mappedStatementField = resultSetHandler.getClass().getDeclaredField("mappedStatement");
    mappedStatementField.setAccessible(true);
    MappedStatement mappedStatement = (MappedStatement) mappedStatementField.get(
        resultSetHandler);
    //statement id 为空，不处理
    if (StringUtils.isEmpty(mappedStatement.getId())) {
      return invocation.proceed();
    }
    //不在查询脱敏范围内，不处理
    if (!sensitiveConfig.getSelects().contains(mappedStatement.getId())) {
      return invocation.proceed();
    }
    try {
      Object resultObject = invocation.proceed();
      if (Objects.isNull(resultObject)) {
        return invocation.proceed();
      }
      if (resultObject instanceof ArrayList) {
        ArrayList resultList = (ArrayList) resultObject;
        if (!CollectionUtils.isEmpty(resultList) && needDecrypt(resultList.get(0))) {
          for (Object result : resultList) {
            aesDecrypt.decrypt(result);
          }
        }
      } else {
        if (needDecrypt(resultObject)) {
          aesDecrypt.decrypt(resultObject);
        }
      }
      return resultObject;
    } catch (Exception e) {
      log.info("数据加密解码时发生异常：", e);
      return invocation.proceed();
    }

  }

  @Override
  public Object plugin(Object o) {
    return Plugin.wrap(o, this);
  }

  @Override
  public void setProperties(Properties properties) {

  }

  private boolean needDecrypt(Object object) {
    Class<?> objectClass = object.getClass();
    SensitiveModel sensitiveModel = AnnotationUtils.findAnnotation(objectClass,
        SensitiveModel.class);
    return Objects.nonNull(sensitiveModel);
  }
}
