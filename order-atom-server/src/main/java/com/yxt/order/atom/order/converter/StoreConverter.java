package com.yxt.order.atom.order.converter;

import com.yxt.order.atom.order.entity.DsOnlineClientDO;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineClientResDto;
import java.util.Objects;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月11日 17:54
 * @email: <EMAIL>
 */
public class StoreConverter {

  public static DsOnlineClientResDto convert2Client(DsOnlineClientDO data) {
    if (Objects.isNull(data)) {
      return null;
    }

    DsOnlineClientResDto dsOnlineClientResDto = new DsOnlineClientResDto();
    dsOnlineClientResDto.setId(data.getId());
    dsOnlineClientResDto.setMerCode(data.getMerCode());
    dsOnlineClientResDto.setPlatformCode(data.getPlatformCode());
    dsOnlineClientResDto.setPlatformName(data.getPlatformName());
    dsOnlineClientResDto.setAppid(data.getAppid());
    dsOnlineClientResDto.setAppSecret(data.getAppSecret());
    dsOnlineClientResDto.setOnlineClientCode(data.getOnlineClientCode());
    dsOnlineClientResDto.setOnlineClientName(data.getOnlineClientName());
    dsOnlineClientResDto.setAuthDeadline(data.getAuthDeadline());
    dsOnlineClientResDto.setAuthTime(data.getAuthTime());
    dsOnlineClientResDto.setCreateTime(data.getCreateTime());
    dsOnlineClientResDto.setModifyTime(data.getModifyTime());
    dsOnlineClientResDto.setSellerId(data.getSellerId());
    dsOnlineClientResDto.setAccessToken(data.getAccessToken());
    dsOnlineClientResDto.setRefreshToken(data.getRefreshToken());
    dsOnlineClientResDto.setServerUrl(data.getServerUrl());
    dsOnlineClientResDto.setServiceMode(data.getServiceMode());
    dsOnlineClientResDto.setPartnerId(data.getPartnerId());
    dsOnlineClientResDto.setOnlineClientOutCode(data.getOnlineClientOutCode());
    dsOnlineClientResDto.setServiceMask(data.getServiceMask());
    dsOnlineClientResDto.setSyncCreate(data.getSyncCreate());
    dsOnlineClientResDto.setPullOrder(data.getPullOrder());
    dsOnlineClientResDto.setSecretKey(data.getSecretKey());
    dsOnlineClientResDto.setAccessType(data.getAccessType());
    dsOnlineClientResDto.setPlatformShopId(data.getPlatformShopId());
    return dsOnlineClientResDto;
  }
}
