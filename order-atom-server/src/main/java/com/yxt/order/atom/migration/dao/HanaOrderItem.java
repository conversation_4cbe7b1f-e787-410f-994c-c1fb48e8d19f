package com.yxt.order.atom.migration.dao;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月06日 16:56
 * @email: <EMAIL>
 */
@Data
public class HanaOrderItem {

  private String rowNo;
  private String erpCode;
  private BigDecimal commodityCount;
  private BigDecimal originalPrice;
  private BigDecimal totalAmount;
  private BigDecimal markdownAmt;
  private BigDecimal discountAmt;
  private BigDecimal promotionAmt;
  private String makeNo;
  private String promotionNo1;
  private String promotionNo2;
  private String promotionNo3;
  private String promotionNo4;
  private String promotionNo5;
  private BigDecimal promotionAmount1;
  private BigDecimal promotionAmount2;
  private BigDecimal promotionAmount3;
  private BigDecimal promotionAmount4;
  private BigDecimal promotionAmount5;

  private String salerId; // 售货员ID
}
