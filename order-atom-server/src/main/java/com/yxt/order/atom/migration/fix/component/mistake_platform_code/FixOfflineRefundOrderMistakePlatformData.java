package com.yxt.order.atom.migration.fix.component.mistake_platform_code;

import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildRefundOrderExistsQuery;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.dao.ModifyPlatformExistsDO;
import com.yxt.order.atom.migration.dao.ModifyPlatformExistsDOMapper;
import com.yxt.order.atom.migration.dao.enums.ModifyPlatformExistSceneEnum;
import com.yxt.order.atom.migration.dao.enums.ModifyPlatformExistStatusEnum;
import com.yxt.order.atom.migration.fix.FixOfflineRefundOrderMistakeScene;
import com.yxt.order.atom.migration.fix.component.es.EsDataOperateComponent;
import com.yxt.order.atom.migration.fix.dto.RepeatedEsDataOperate;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 修复科传的单子,平台标识缺标识为海典 原因: 是因为之前的平台标识取值是根据 inner_store_dictionary 来获取导致的
 *
 * @author: moatkon
 * @time: 2024/12/13 10:21
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class FixOfflineRefundOrderMistakePlatformData extends
    AbstractFlash<OfflineRefundOrderDO, OfflineRefundOrderDO, FixOfflineRefundOrderMistakeScene> {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private EsDataOperateComponent esDataOperateComponent;

  @Resource
  private ModifyPlatformExistsDOMapper modifyPlatformExistsDOMapper;



  @Override
  protected Long queryCursorStartId() {
    CustomData customData = getCustomData();
    Long startId = customData.getStartId();
    return Objects.nonNull(startId) ? startId
        : offlineRefundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    CustomData customData = getCustomData();
    Long endId = customData.getEndId();
    return Objects.nonNull(endId) ? endId : offlineRefundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(
        getFlashParam(), defaultLimit());
    return offlineRefundOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrderDO> assembleTargetData(
      List<OfflineRefundOrderDO> offlineOrderDOList) {
    return offlineOrderDOList;
  }

  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param offlineRefundOrderDOList
   */
  @Override
  protected void flash(List<OfflineRefundOrderDO> offlineRefundOrderDOList) {
    for (OfflineRefundOrderDO offlineRefundOrderDO : offlineRefundOrderDOList) {
      handle(offlineRefundOrderDO);

    }

  }

  private void handle(OfflineRefundOrderDO offlineRefundOrderDO) {
    String migration = offlineRefundOrderDO.getMigration();
    if (StringUtils.isEmpty(migration)) {
      return;
    }
    if (!Boolean.TRUE.toString().equals(migration)) {
      return;
    }

    String refundNo = offlineRefundOrderDO.getRefundNo();
    if (StringUtils.isEmpty(refundNo)) {
      return;
    }

    String thirdRefundNo = offlineRefundOrderDO.getThirdRefundNo();
    if (StringUtils.isEmpty(thirdRefundNo)) {
      return;
    }

    String thirdPlatformCode = offlineRefundOrderDO.getThirdPlatformCode();

    // 三方单号不是16位,但是平台却是海典的,需要将平台变更为科传
    if (thirdRefundNo.length() != 16 && ThirdPlatformCodeEnum.HAIDIAN.name()
        .equals(thirdPlatformCode)) {

      // 先检查下科传的订单存不存在
      String needCheckPlatform = ThirdPlatformCodeEnum.KE_CHUAN.name();
      // 校验是否已经存在
      OfflineRefundOrderExistsReqDto dto = new OfflineRefundOrderExistsReqDto();
      dto.setStoreCode(offlineRefundOrderDO.getStoreCode());
      dto.setThirdRefundNo(offlineRefundOrderDO.getThirdRefundNo());
      dto.setThirdPlatformCode(needCheckPlatform);
      dto.setThirdCreated(offlineRefundOrderDO.getCreated());
      LambdaQueryWrapper<OfflineRefundOrderDO> query = buildRefundOrderExistsQuery(dto);
      Integer i = offlineRefundOrderMapper.selectCount(query);
      if(i>=1){
        // 说明已经存在
        ModifyPlatformExistsDO existsDO = new ModifyPlatformExistsDO();
        existsDO.setScene(ModifyPlatformExistSceneEnum.REFUND.name());
        existsDO.setBusinessId(offlineRefundOrderDO.getId().toString());
        existsDO.setBusinessNo(offlineRefundOrderDO.getRefundNo());
        existsDO.setThirdPlatformCode(offlineRefundOrderDO.getThirdPlatformCode());
        existsDO.setAllowOperate("false");
        existsDO.setStatus(ModifyPlatformExistStatusEnum.UN_HANDLE.name());
        existsDO.setNote("存在"+needCheckPlatform+"的订单,请介入排查");
        existsDO.setMigration(offlineRefundOrderDO.getMigration());
        existsDO.setThirdBusinessNo(offlineRefundOrderDO.getThirdRefundNo());
        existsDO.setStoreCode(offlineRefundOrderDO.getStoreCode());
        existsDO.setCreated(offlineRefundOrderDO.getCreated());
        existsDO.setShardingNo(ShardingHelper.getTableIndexByNo(offlineRefundOrderDO.getRefundNo()));
        modifyPlatformExistsDOMapper.insert(existsDO);
        return;
      }




      offlineRefundOrderDO.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());

      LambdaQueryWrapper<OfflineRefundOrderDO> where = new LambdaQueryWrapper<>();
      where.eq(OfflineRefundOrderDO::getRefundNo, refundNo);
      offlineRefundOrderMapper.update(offlineRefundOrderDO, where);

      // 将错误的ES索引删除
      OfflineRefundOrderDO deleteOfflineRefundOrderEs = BeanUtil.toBean(offlineRefundOrderDO, OfflineRefundOrderDO.class);
      deleteOfflineRefundOrderEs.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
      RepeatedEsDataOperate repeatedEsDataOperate = new RepeatedEsDataOperate(deleteOfflineRefundOrderEs);
      esDataOperateComponent.removeEsEsUnhandled(repeatedEsDataOperate);
    }
  }


  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}