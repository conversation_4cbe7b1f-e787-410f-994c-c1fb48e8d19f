package com.yxt.order.atom.order.es.doc;

import java.time.LocalDateTime;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

@Data
@Settings(shardsNum = 36)
@IndexName(value = "es_order_world_biz_log", keepGlobalPrefix = true)
public class EsBizLog {

  // 什么人，在什么时间、什么环境，对什么东西，做了什么操作，产生了什么结果
  @IndexId(type = IdType.NONE)
  private String id;

  /**
   * 操作人id
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String operatorId;

  /**
   * 操作人名
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String operatorName;

  /**
   * 操作时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime operateTime;

  /**
   * 操作时间戳
   */
  @IndexField(fieldType = FieldType.LONG)
  private Long operateTimeStamp;

  /**
   * 操作服务
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String operateService;

  /**
   * 操作产生的traceId
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String traceId;

  /**
   * 被操作的业务对象标识id，如订单号，售后单号等
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String bizNo;

  /**
   * 业务场景，如订单、售后单等
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String bizScene;

  /**
   * 具体执行的动作，如创建、修改、删除等
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String bizAction;

  /**
   * 操作结果
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Boolean bizResult;

  /**
   * 操作结果描述
   */
  @IndexField(fieldType = FieldType.TEXT)
  private String bizResultDesc;

  /**
   * 扩展字段1
   */
  @IndexField(fieldType = FieldType.TEXT)
  private String extensionNum1;

  /**
   * 扩展字段2
   */
  @IndexField(fieldType = FieldType.TEXT)
  private String extensionNum2;

  /**
   * 扩展字段3
   */
  @IndexField(fieldType = FieldType.TEXT)
  private String extensionNum3;

  /**
   * 扩展字段，存储一些自定义的扩展信息，以JSON String格式存储
   */
  @IndexField(fieldType = FieldType.TEXT)
  private String extJson;
}
