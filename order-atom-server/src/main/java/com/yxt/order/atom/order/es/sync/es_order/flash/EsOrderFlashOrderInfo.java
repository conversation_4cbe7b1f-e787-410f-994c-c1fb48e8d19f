package com.yxt.order.atom.order.es.sync.es_order.flash;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.SupportChronicDisease;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo.Order;
import com.yxt.order.atom.order.es.sync.es_order.handler.OrderInfoCanalHandler;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/20 17:31
 */@Component
public class EsOrderFlashOrderInfo extends
    AbstractFlash<OrderInfoDO, Order, SupportChronicDisease> {
  @Resource
  private OrderInfoMapper orderInfoMapper;

  @Resource
  private OrderInfoCanalHandler orderInfoCanalHandler;

  @Override
  protected Long queryCursorStartId() {
    return orderInfoMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return orderInfoMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OrderInfoDO> getSourceList() {
    LambdaQueryWrapper<OrderInfoDO> query = FlashQueryWrapper.orderInfoFlashQuery(getFlashParam(),defaultLimit());
    return orderInfoMapper.selectList(query);
  }

  @Override
  protected List<Order> assembleTargetData(List<OrderInfoDO> orderInfoDOList) {
    return orderInfoDOList.stream().map(DoToCanalDtoWrapper::getOrder).collect(Collectors.toList());
  }



  @Override
  protected void flash(List<Order> orders) {
    CanalOrderInfo canalOrderInfo = new CanalOrderInfo();
    canalOrderInfo.setData(orders);
    orderInfoCanalHandler.manualFlash(canalOrderInfo);
  }
}
