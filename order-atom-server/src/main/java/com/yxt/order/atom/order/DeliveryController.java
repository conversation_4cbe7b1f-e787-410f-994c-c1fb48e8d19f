package com.yxt.order.atom.order;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.DeliveryRepository;
import com.yxt.order.atom.sdk.online_order.delivery.DeliveryAtomCmdApi;
import com.yxt.order.atom.sdk.online_order.delivery.DeliveryAtomQryApi;
import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto;
import com.yxt.order.atom.sdk.online_order.delivery.dto.req.UpdateOrderDeliveryRecordReqDto;
import com.yxt.order.types.order.OrderNo;
import com.yxt.starter.controller.AbstractController;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月01日 14:44
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class DeliveryController extends AbstractController implements DeliveryAtomCmdApi,
    DeliveryAtomQryApi {

  @Resource
  private DeliveryRepository deliveryRepository;


  /**
   * 动作+实体+后缀（可选）
   */
  @Override
  public ResponseBase<OrderDeliveryRecordResDto> getOrderDeliveryRecord(OrderNo orderNo) {
    return generateSuccess(deliveryRepository.getOrderDeliveryRecord(orderNo));
  }

  @Override
  public ResponseBase<Boolean> updateOrderDeliveryRecordByOrderNo(
      UpdateOrderDeliveryRecordReqDto updateDto) {

    return generateSuccess(deliveryRepository.updateOrderDeliveryRecord(updateDto));
  }
}
