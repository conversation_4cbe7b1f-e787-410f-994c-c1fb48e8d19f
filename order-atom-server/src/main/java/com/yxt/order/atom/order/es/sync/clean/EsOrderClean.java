package com.yxt.order.atom.order.es.sync.clean;

import static com.yxt.order.atom.common.utils.OrderDateUtils.formatYYMMDD;

import com.yxt.order.atom.order.es.doc.EsOrder;
import com.yxt.order.atom.order.es.mapper.EsOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractClean;
import java.util.Date;
import javax.annotation.Resource;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/12/13 15:42
 */
@Component
public class EsOrderClean extends AbstractClean {

  @Resource
  private EsOrderMapper esOrderMapper;

  @Override
  protected Integer expireDays() {
    return ExpireDaysConstant.EsOrderEfficientDays;
  }

  @Override
  protected void clean(String startDate, String endDate) {
    LambdaEsQueryWrapper<EsOrder> query = new LambdaEsQueryWrapper<>();
    query.gt(EsOrder::getCreateTime, startDate);
    query.le(EsOrder::getCreateTime, endDate);
    esOrderMapper.delete(query);
  }

  @Override
  protected Boolean checkHasData(String endDate) {
    LambdaEsQueryWrapper<EsOrder> query = new LambdaEsQueryWrapper<>();
    query.le(EsOrder::getCreateTime, endDate);
    Long count = esOrderMapper.selectCount(query);
    return count > 0;
  }

  @Override
  protected Date getLatestdEndDate(Date endDate) {
    LambdaEsQueryWrapper<EsOrder> query = new LambdaEsQueryWrapper<>();
    query.le(EsOrder::getCreateTime, formatYYMMDD(endDate));
    query.orderByDesc(EsOrder::getCreateTime);
    EsPageInfo<EsOrder> esPageInfo = esOrderMapper.pageQuery(query, 1, 1);
    if(CollectionUtils.isEmpty(esPageInfo.getList())){
      return endDate;
    }
    return esPageInfo.getList().get(0).getCreateTime();
  }
}
