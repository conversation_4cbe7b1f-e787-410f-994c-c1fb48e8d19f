package com.yxt.order.atom.order_world.service;

import static com.yxt.order.atom.common.LocalConst.DATA_SOURCE.ORDER_OFFLINE;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order_world.entity.CommodityStockChangeRecordDO;
import com.yxt.order.atom.order_world.mapper.CommodityStockChangeRecordMapper;
import com.yxt.order.atom.order_world.repository.CommodityStockChangeRecordRepository;
import com.yxt.order.atom.sdk.order_world.req.CommodityStockChangeRecordSaveReq;
import com.yxt.order.atom.sdk.order_world.req.CommodityStockChangeRecordSearchReq;
import com.yxt.order.atom.sdk.order_world.res.CommodityStockChangeRecordRes;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrderWorldCommodityService {

  @Autowired
  private CommodityStockChangeRecordMapper commodityStockChangeRecordMapper;

  @Autowired
  private CommodityStockChangeRecordRepository commodityStockChangeRecordRepository;

  @DS(ORDER_OFFLINE)
  public List<CommodityStockChangeRecordRes> queryCommodityStockStockChangeRecord(CommodityStockChangeRecordSearchReq req) {
    List<CommodityStockChangeRecordDO> changeRecordDOList = commodityStockChangeRecordMapper.selectList(Wrappers.<CommodityStockChangeRecordDO>lambdaQuery()
        .eq(CommodityStockChangeRecordDO::getOrderNo, req.getOrderNo()));
    return BeanUtil.copyToList(changeRecordDOList, CommodityStockChangeRecordRes.class);
  }

  @DS(ORDER_OFFLINE)
  public void saveCommodityStockStockChangeRecord(CommodityStockChangeRecordSaveReq req) {
    commodityStockChangeRecordRepository.saveBatch(BeanUtil.copyToList(req.getStockChangeRecordRecordList(), CommodityStockChangeRecordDO.class));
  }
}
