package com.yxt.order.atom.common.sharding;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 订单Sharding
 *
 * @author: moatkon
 * @time: 2025/1/23 17:08
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(value = {ElementType.METHOD})
public @interface YxtOrderSharding {

  String shardingNo();
}
