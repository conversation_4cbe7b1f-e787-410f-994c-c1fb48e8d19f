package com.yxt.order.atom.job.abstractsStageOrder;

import com.yxt.order.atom.migration.MigrationEventMessageConsumer;
import com.yxt.order.atom.order.mongo.StagingOrder;
import com.yxt.order.atom.order.repository.StageOrderService;
import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月16日 15:58
 * @email: <EMAIL>
 */
@Slf4j
public abstract class AbstractStageOrderHandler {

  @Value("${mq.topic.producer.hdOfflineOrder}")
  private String hdOfflineOrder;

  @Value("${mq.topic.producer.offlineOrderSyncKc}")
  private String offlineOrderSyncKc;

  @Resource
  private RocketMQTemplate template;

  @Resource
  private StageOrderService stageOrderService;

  @Resource
  private MigrationEventMessageConsumer migrationEventMessageConsumer;

  protected void handlerNormalStageOrder() {
    String data = stagingOrder.getData();
    log.info("stageOrderHandler,topic:{},tag：{},data:{}", topic, tag(), data);

    SendResult sendResult = template.syncSend(topic + ":" + tag(), data, 6000);
    if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
      log.warn("发送消息失败,stageOrderHandler,topic:{},tag：{},data:{}", topic, tag(), data);
      return;
    }

    stageOrderService.delete(stagingOrder);
  }

  /**
   * 迁移的场景简单,直接调用方法就可以处理科传、海典的正单和退单
   */
//  protected void handlerMigrationStageOrder() {
//    String data = stagingOrder.getData();
//    // 不用分正单和退单
//    StageMigrateData migrateData = JsonUtils.toObject(data, StageMigrateData.class);
//    HanaOrderInfo hanaOrderInfo = migrateData.getHanaOrderInfo();
//    String schema = migrateData.getSchema();
//    String taskKey = migrateData.getTaskKey();
//
//    String doNotImpactMigrationCount = taskKey;
//    String noCountMigrationFlag = "noCountMigration";
//    // 迁移数据补偿逻辑,使用重建Key,不影响原有的迁移记录
//    if (!taskKey.contains(noCountMigrationFlag)) { // 同一条消息会多次补偿,这个key只处理一次
//      doNotImpactMigrationCount = String.format("%s-%s", taskKey, noCountMigrationFlag);
//    }
//
//    migrationEventMessageConsumer.one2one(hanaOrderInfo, schema, doNotImpactMigrationCount,
//        isStageOrder());
//
//    stageOrderService.delete(stagingOrder);
//  }

  protected String topic;
  protected StagingOrder stagingOrder;

  /**
   * 初始化
   *
   * @param stagingRefundOrder
   */
  private void init(StagingOrder stagingRefundOrder) {
    this.stagingOrder = stagingRefundOrder;
    this.topic = Strings.EMPTY;
  }

  protected abstract String tag();

  private void normalStageOrderSetTopic() {
    String thirdPlatformCode = stagingOrder.getThirdPlatformCode();
    if (ThirdPlatformCodeEnum.HAIDIAN.name().equals(thirdPlatformCode)) {
      this.topic = hdOfflineOrder;
    } else if (ThirdPlatformCodeEnum.KE_CHUAN.name().equals(thirdPlatformCode)) {
      this.topic = offlineOrderSyncKc;
    }
  }

  protected String buildFoundOrderTag(String appendTag) {
    return String.format("TAG_STAGE_refundMissOrder_%s", appendTag);
  }

  protected String buildUser404Tag(String appendTag) {
    String tag = String.format("TAG_STAGE_user404_%s", appendTag);

    // 科传正退单是同topic,不同的tag,消息不一样,所以这里需要区分，
    // 而海典,虽然是同topic,不同的tag,但是消息结构是一样的,所以不需要区分
    String thirdPlatformCode = stagingOrder.getThirdPlatformCode();
    if (ThirdPlatformCodeEnum.KE_CHUAN.name().equals(thirdPlatformCode)) {
      NumberType numberType = stagingOrder.getNumberType();
      tag = tag + "_" + numberType.name();
    }
    return tag;
  }


  protected Boolean check() {
    return Boolean.TRUE;
  }


  /**
   * 是否拦截订单
   *
   * @return
   */
  protected abstract Boolean isStageOrder();


  public void handle(StagingOrder stagingRefundOrder) {
    // init
    init(stagingRefundOrder);

    if (!check()) {
      return;
    }

    Boolean migration = stagingRefundOrder.getMigration();
    if (!migration) { // 非迁移订单

      normalStageOrderSetTopic();
      if (StringUtils.isEmpty(topic)) {
        log.error("StageOrder-topic未配置");
        return;
      }

      handlerNormalStageOrder();
    }/* else {// 迁移订单

      handlerMigrationStageOrder();
    }*/
  }


}
