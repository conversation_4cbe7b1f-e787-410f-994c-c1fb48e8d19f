package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("offline_order_organization")
public class OfflineOrderOrganizationDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  private String orderNo;

  private String storeCode;

  private String storeName;

  private String companyCode;

  private String companyName;

  private String storeDirectJoinType;

  private String createdBy;

  private String updatedBy;

  private Date createdTime;

  private Date updatedTime;

  private Long version;

}