package com.yxt.order.atom.migration;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.MIGRATION_MQ_Consumer_POOL;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDO;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDOMapper;
import com.yxt.order.atom.migration.dao.enums.UnprocessableSceneEnum;
import com.yxt.order.atom.migration.fix.FixOfflineOrderMistakeScene;
import com.yxt.order.atom.migration.fix.FixOfflineOrderMistakeSceneUnhandleEs;
import com.yxt.order.atom.migration.fix.FixOfflineRefundOrderMistakeScene;
import com.yxt.order.atom.migration.fix.FixOfflineRefundOrderMistakeSceneUnhandleEs;
import com.yxt.order.atom.migration.fix.RemoveEsOrderRepeatedDataScene;
import com.yxt.order.atom.migration.fix.RemoveMigrationOrderRepeatedData;
import com.yxt.order.atom.migration.fix.RemoveMigrationRefundOrderRepeatedData;
import com.yxt.order.atom.migration.fix.UnprocessableOfflineOrderCollectScene;
import com.yxt.order.atom.migration.fix.UnprocessableOfflineRefundOrderCollectScene;
import com.yxt.order.atom.migration.fix.component.FlushAmtAmountData;
import com.yxt.order.atom.migration.fix.component.OfflineOrderPlatformCheckDataCollect;
import com.yxt.order.atom.migration.fix.component.RemoveHdRepeatedOrderData;
import com.yxt.order.atom.migration.fix.component.UnprocessableOrderAllowOperateOrderHandle;
import com.yxt.order.atom.migration.fix.dto.FixPlatformCodeMistakeReq;
import com.yxt.order.atom.migration.fix.dto.HandleAllowOperateFieldReq;
import com.yxt.order.atom.migration.fix.dto.RemoveRepeatedDataReq;
import com.yxt.order.atom.migration.fix.dto.RemoveRepeatedEsDataReq;
import com.yxt.order.atom.migration.fix.dto.UnprocessableOrderReq;
import com.yxt.order.atom.migration.req.FlushAmtAmountReq;
import com.yxt.order.atom.migration.req.OfflineOrderPlatformCheckReq;
import com.yxt.order.atom.migration.req.RemoveHdRepeatedOrderReq;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.AbstractFlash.CustomData;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.types.utils.ShardingHelper;
import com.yxt.starter.controller.AbstractController;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 删除迁移重复的订单数据
 *
 * @author: moatkon
 * @time: 2025/3/10 12:09
 */
@RestController
@Slf4j
public class MigrationDataFixController extends AbstractController {

  @Resource
  private UnprocessableOrderAllowOperateOrderHandle unprocessableOrderAllowOperateOrderHandle;

  @Resource
  private UnprocessableOrderDOMapper unprocessableOrderDOMapper;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private List<AbstractFlash<?, ?, RemoveMigrationOrderRepeatedData>> removeMigrationOrderRepeatedDataList;

  @Resource
  private List<AbstractFlash<?, ?, RemoveMigrationRefundOrderRepeatedData>> removeMigrationRefundOrderRepeatedDataList;

  @Resource
  private List<AbstractFlash<?, ?, RemoveEsOrderRepeatedDataScene>> removeRepeatedDataEsList;

//  @Resource
//  private List<AbstractFlash<?, ?, FlushOfflineOrderCreatedScene>> flushOfflineOrderCreatedSceneList;
//
//  @Resource
//  private List<AbstractFlash<?, ?, FlushOfflineRefundOrderCreatedScene>> flushOfflineRefundOrderCreatedSceneList;

  @Resource
  private List<AbstractFlash<?, ?, UnprocessableOfflineOrderCollectScene>> unprocessableOfflineOrderCollectSceneList;

  @Resource
  private List<AbstractFlash<?, ?, UnprocessableOfflineRefundOrderCollectScene>> unprocessableOfflineRefundOrderCollectSceneList;

  @Resource
  private FlushAmtAmountData flushAmtAmountData;

  @Resource
  private RemoveHdRepeatedOrderData removeHdRepeatedOrderData;

  @Resource
  private OfflineOrderPlatformCheckDataCollect offlineOrderPlatformCheckDataCollect;


  @Value("${deletedDataTableSuffix:_6}")
  private String deletedDataTableSuffix;

  @Qualifier(MIGRATION_MQ_Consumer_POOL)
  @Resource
  private ThreadPoolExecutor migrationMqConsumerPool;

  @Value("${completeUnprocessableOrderDataOnOff:true}")
  private Boolean completeUnprocessableOrderDataOnOff;


  @Value("${flushAmtAmountOnOff:true}")
  private Boolean flushAmtAmountOnOff;

  @Value("${removeHdRepeatedOrderOnOff:true}")
  private Boolean removeHdRepeatedOrderOnOff;

  @Value("${offlineOrderPlatformCheckDataCollectOnOff:false}")
  private Boolean offlineOrderPlatformCheckDataCollectOnOff;


  @Resource
  private List<AbstractFlash<?, ?, FixOfflineOrderMistakeScene>> fixOfflineOrderMistakeSceneList;
  @Resource
  private List<AbstractFlash<?, ?, FixOfflineOrderMistakeSceneUnhandleEs>> fixOfflineOrderMistakeSceneUnhandleEsList;

  @Resource
  private List<AbstractFlash<?, ?, FixOfflineRefundOrderMistakeScene>> fixOfflineRefundOrderMistakeSceneList;

  @Resource
  private List<AbstractFlash<?, ?, FixOfflineRefundOrderMistakeSceneUnhandleEs>> fixOfflineRefundOrderMistakeSceneUnhandleEsList;


  /**
   * 删除正单迁移重复的数据
   *
   * @param req
   * @return
   */
  @PostMapping("/removeRepeatedOrderData")
  public ResponseBase<Boolean> removeRepeatedOrderData(
      @RequestBody @Valid RemoveRepeatedDataReq req) {
    String scene = req.getScene();
    Boolean onlyHandleKeChuan = req.getOnlyHandleKeChuan();
    Long startId = req.getStartId();
    Long endId = req.getEndId();

    for (String shardingValue : req.getShardingValueList()) {
      for (AbstractFlash<?, ?, RemoveMigrationOrderRepeatedData> flash : removeMigrationOrderRepeatedDataList) {
        migrationMqConsumerPool.submit(() -> {
          FlashParam flashParam = new FlashParam();
          flashParam.setMonitorKey(
              String.format("removeRepeatedOrderData_%s_%s", shardingValue, scene));

          CustomData customData = new CustomData();
          customData.setShardingValueList(Lists.newArrayList(shardingValue));
          customData.setOnlyHandleKeChuan(onlyHandleKeChuan);
          customData.setStartId(startId);
          customData.setEndId(endId);
          flash.customData(customData);
          flash.startFlush(flashParam);
        });
      }
    }

    return generateSuccess(Boolean.TRUE);
  }

  /**
   * 删除退单迁移重复的数据
   *
   * @param req
   * @return
   */
  @PostMapping("/removeRepeatedRefundOrderData")
  public ResponseBase<Boolean> removeRepeatedRefundOrderData(
      @RequestBody @Valid RemoveRepeatedDataReq req) {
    String scene = req.getScene();
    Boolean onlyHandleKeChuan = req.getOnlyHandleKeChuan();
    Long startId = req.getStartId();
    Long endId = req.getEndId();

    for (String shardingValue : req.getShardingValueList()) {
      for (AbstractFlash<?, ?, RemoveMigrationRefundOrderRepeatedData> flash : removeMigrationRefundOrderRepeatedDataList) {
        migrationMqConsumerPool.submit(() -> {
          FlashParam flashParam = new FlashParam();
          flashParam.setMonitorKey(
              String.format("removeRepeatedRefundOrderData_%s_%s", shardingValue, scene));

          CustomData customData = new CustomData();
          customData.setShardingValueList(Lists.newArrayList(shardingValue));
          customData.setOnlyHandleKeChuan(onlyHandleKeChuan);
          customData.setStartId(startId);
          customData.setEndId(endId);
          flash.customData(customData);
          flash.startFlush(flashParam);
        });
      }
    }
    return generateSuccess(Boolean.TRUE);
  }


  /**
   * 移除ES中被删除的
   *
   * @param req
   * @return
   */
  @PostMapping("/removeEsOrderRepeatedData")
  public ResponseBase<Boolean> removeEsOrderRepeatedData(
      @RequestBody @Valid RemoveRepeatedEsDataReq req) {

    String suffix = req.getSuffix();
    Long startId = req.getStartId();
    Long endId = req.getEndId();
    String scene = req.getScene();

    if (!Sets.newHashSet(deletedDataTableSuffix.split(",")).contains(suffix)) {
      throw new RuntimeException("不允许的后缀,请检查");
    }

    for (AbstractFlash<?, ?, RemoveEsOrderRepeatedDataScene> flash : removeRepeatedDataEsList) {
      migrationMqConsumerPool.submit(() -> {

        CustomData customData = new CustomData();
        customData.setSuffix(suffix);
        customData.setStartId(startId);
        customData.setEndId(endId);

        FlashParam flashParam = new FlashParam();
        flashParam.setMonitorKey(String.format("removeEsOrderRepeatedDataScene_%s_%s_",
            customData.removeEsDataMonitorKey(), scene));
        flash.customData(customData);
        flash.startFlush(flashParam);
      });
    }
    return generateSuccess(Boolean.TRUE);
  }
//
//
//  @PostMapping("/fixOfflineOrderCreated")
//  public ResponseBase<Boolean> fixOfflineOrderCreated(@RequestBody @Valid FixCreatedReq req) {
//    String scene = req.getScene();
//    Long startId = req.getStartId();
//    Long endId = req.getEndId();
//    for (String shardingValue : req.getShardingValueList()) {
//      for (AbstractFlash<?, ?, FlushOfflineOrderCreatedScene> flash : flushOfflineOrderCreatedSceneList) {
//        migrationMqConsumerPool.submit(() -> {
//          FlashParam flashParam = new FlashParam();
//          flashParam.setMonitorKey(
//              String.format("fixOfflineOrderCreated_%s_%s", shardingValue, scene));
//
//          CustomData customData = new CustomData();
//          customData.setShardingValueList(Lists.newArrayList(shardingValue));
//          customData.setStartId(startId);
//          customData.setEndId(endId);
//
//          flash.customData(customData);
//          flash.startFlush(flashParam);
//        });
//      }
//    }
//    return generateSuccess(Boolean.TRUE);
//  }

//
//  @PostMapping("/fixOfflineRefundOrderCreated")
//  @Deprecated
//  public ResponseBase<Boolean> fixOfflineRefundOrderCreated(@RequestBody @Valid FixCreatedReq req) {
//    String scene = req.getScene();
//    Long startId = req.getStartId();
//    Long endId = req.getEndId();
//    for (String shardingValue : req.getShardingValueList()) {
//      for (AbstractFlash<?, ?, FlushOfflineRefundOrderCreatedScene> flash : flushOfflineRefundOrderCreatedSceneList) {
//        migrationMqConsumerPool.submit(() -> {
//          FlashParam flashParam = new FlashParam();
//          flashParam.setMonitorKey(
//              String.format("fixOfflineRefundOrderCreated_%s_%s", shardingValue, scene));
//
//          CustomData customData = new CustomData();
//          customData.setShardingValueList(Lists.newArrayList(shardingValue));
//          customData.setStartId(startId);
//          customData.setEndId(endId);
//
//          flash.customData(customData);
//          flash.startFlush(flashParam);
//        });
//      }
//    }
//    return generateSuccess(Boolean.TRUE);
//  }


  /**
   * 删除正单迁移重复的数据
   *
   * @param req
   * @return
   */
  @PostMapping("/fixUnprocessableOfflineOrder")
  public ResponseBase<Boolean> fixUnprocessableOfflineOrder(
      @RequestBody @Valid UnprocessableOrderReq req) {
    String scene = req.getScene();
    Boolean onlyHandleKeChuan = req.getOnlyHandleKeChuan();
    Long startId = req.getStartId();
    Long endId = req.getEndId();

    for (String shardingValue : req.getShardingValueList()) {
      for (AbstractFlash<?, ?, UnprocessableOfflineOrderCollectScene> flash : unprocessableOfflineOrderCollectSceneList) {
        migrationMqConsumerPool.submit(() -> {
          FlashParam flashParam = new FlashParam();
          flashParam.setMonitorKey(
              String.format("fixUnprocessableOfflineOrder_%s_%s", shardingValue, scene));

          CustomData customData = new CustomData();
          customData.setShardingValueList(Lists.newArrayList(shardingValue));
          customData.setOnlyHandleKeChuan(onlyHandleKeChuan);
          customData.setStartId(startId);
          customData.setEndId(endId);
          flash.customData(customData);
          flash.startFlush(flashParam);
        });
      }
    }

    return generateSuccess(Boolean.TRUE);
  }

  /**
   * 删除退单迁移重复的数据
   *
   * @param req
   * @return
   */
  @PostMapping("/fixUnprocessableOfflineRefundOrder")
  public ResponseBase<Boolean> fixUnprocessableOfflineRefundOrder(
      @RequestBody @Valid UnprocessableOrderReq req) {
    String scene = req.getScene();
    Boolean onlyHandleKeChuan = req.getOnlyHandleKeChuan();
    Long startId = req.getStartId();
    Long endId = req.getEndId();

    for (String shardingValue : req.getShardingValueList()) {
      for (AbstractFlash<?, ?, UnprocessableOfflineRefundOrderCollectScene> flash : unprocessableOfflineRefundOrderCollectSceneList) {
        migrationMqConsumerPool.submit(() -> {
          FlashParam flashParam = new FlashParam();
          flashParam.setMonitorKey(
              String.format("fixUnprocessableOfflineRefundOrder_%s_%s", shardingValue, scene));

          CustomData customData = new CustomData();
          customData.setShardingValueList(Lists.newArrayList(shardingValue));
          customData.setOnlyHandleKeChuan(onlyHandleKeChuan);
          customData.setStartId(startId);
          customData.setEndId(endId);
          flash.customData(customData);
          flash.startFlush(flashParam);
        });
      }
    }
    return generateSuccess(Boolean.TRUE);
  }


  @PostMapping("/complete-unprocessable-order-data")
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @Deprecated // 除非需要补数据,否则不用再触发
  public ResponseBase<Boolean> completeUnprocessableOrderData() {
    if(completeUnprocessableOrderDataOnOff){
      throw new RuntimeException("completeUnprocessableOrderDataOnOff closed");
    }

    Integer count = unprocessableOrderDOMapper.selectCount(new LambdaQueryWrapper<>());
    if(count > 2000){
      throw new RuntimeException("数据量过大");
    }

    List<UnprocessableOrderDO> unprocessableOrderDOList = unprocessableOrderDOMapper.selectList(
        new LambdaQueryWrapper<>());

    for (UnprocessableOrderDO unprocessableOrderDO : unprocessableOrderDOList) {
      // 1
      String shardingNo = ShardingHelper.getTableIndexByNo(unprocessableOrderDO.getBusinessNo());
      unprocessableOrderDO.setShardingNo(shardingNo);

      String businessId = unprocessableOrderDO.getBusinessId();
      String businessNo = unprocessableOrderDO.getBusinessNo();

      if (UnprocessableSceneEnum.REPEATED_OFFLINE_ORDER.name()
          .equals(unprocessableOrderDO.getScene())) {

        try (HintManager hintManager = HintManager.getInstance()) {
          OfflineOrderHit hit = new OfflineOrderHit();
          hit.setDefineNo(businessNo);
          OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

          LambdaQueryWrapper<OfflineOrderDO> queryById = new LambdaQueryWrapper<>();
          queryById.eq(OfflineOrderDO::getId,businessId);
          OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(queryById);


          LambdaQueryWrapper<OfflineOrderDO> queryList = new LambdaQueryWrapper<>();
          queryList.eq(OfflineOrderDO::getThirdPlatformCode,offlineOrderDO.getThirdPlatformCode());
          queryList.eq(OfflineOrderDO::getThirdOrderNo,offlineOrderDO.getThirdOrderNo());
          queryList.eq(OfflineOrderDO::getStoreCode,offlineOrderDO.getStoreCode());
          List<OfflineOrderDO> offlineOrderDOS = offlineOrderMapper.selectList(queryList);
          String amountJson = offlineOrderDOS.stream()
              .map(OfflineOrderDO::getActualCollectAmount)
              .map(BigDecimal::toPlainString)
              .collect(Collectors.joining(","));

          // 2
          unprocessableOrderDO.setStoreCode(offlineOrderDO.getStoreCode());
          unprocessableOrderDO.setThirdBusinessNo(offlineOrderDO.getThirdOrderNo());
          unprocessableOrderDO.setCount(offlineOrderDOS.size());
          unprocessableOrderDO.setAmountJson(amountJson);
        }


      } else if (UnprocessableSceneEnum.REPEATED_OFFLINE_REFUND_ORDER.name()
          .equals(unprocessableOrderDO.getScene())) {
        try (HintManager hintManager = HintManager.getInstance()) {
          OfflineOrderHit hit = new OfflineOrderHit();
          hit.setDefineNo(businessNo);
          OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

          LambdaQueryWrapper<OfflineRefundOrderDO> queryById = new LambdaQueryWrapper<>();
          queryById.eq(OfflineRefundOrderDO::getId,businessId);
          OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(queryById);

          LambdaQueryWrapper<OfflineRefundOrderDO> queryList = new LambdaQueryWrapper<>();
          queryList.eq(OfflineRefundOrderDO::getThirdPlatformCode,offlineRefundOrderDO.getThirdPlatformCode());
          queryList.eq(OfflineRefundOrderDO::getThirdRefundNo,offlineRefundOrderDO.getThirdRefundNo());
          queryList.eq(OfflineRefundOrderDO::getStoreCode,offlineRefundOrderDO.getStoreCode());
          List<OfflineRefundOrderDO> offlineRefundOrderDOS = offlineRefundOrderMapper.selectList(queryList);

          String amountJson = offlineRefundOrderDOS.stream()
              .map(OfflineRefundOrderDO::getTotalAmount)
              .map(BigDecimal::toPlainString)
              .collect(Collectors.joining(","));

          // 2
          unprocessableOrderDO.setStoreCode(offlineRefundOrderDO.getStoreCode());
          unprocessableOrderDO.setThirdBusinessNo(offlineRefundOrderDO.getThirdRefundNo());
          unprocessableOrderDO.setCount(offlineRefundOrderDOS.size());
          unprocessableOrderDO.setAmountJson(amountJson);
        }
      }
      unprocessableOrderDOMapper.updateById(unprocessableOrderDO);
    }

    return generateSuccess(Boolean.TRUE);
  }


  /**
   * 刷新订单主表实付和是否金额
   * @param req
   * @return
   */
  @PostMapping("/flushAmtAmount")
  public ResponseBase<Boolean> flushAmtAmount(@RequestBody @Valid FlushAmtAmountReq req) {
    if(flushAmtAmountOnOff){
      throw new RuntimeException("flushAmtAmountOnOff closed");
    }

    String monitorKey = req.monitorKey();
    migrationMqConsumerPool.submit(() -> {
      RedisStringUtil.setValue(monitorKey, "刷数中", 30L, TimeUnit.DAYS);
      flushAmtAmountData.fixAmount(req);
      RedisStringUtil.setValue(monitorKey, "刷数结束", 30L, TimeUnit.DAYS);
    });
    return generateSuccess(Boolean.TRUE);
  }

  /**
   * 触发前已经按照以下sql处理了一遍重复数据了
   * select business_id,sharding_no,count(1) c from unprocessable_order GROUP BY business_id,sharding_no having c > 1;
   *
   * DELETE t1 FROM unprocessable_order t1,unprocessable_order t2 WHERE t1.business_id = t2.business_id and t1.sharding_no = t2.sharding_no AND t1.id > t2.id;
   * @return
   */
  @PostMapping("/handleUnprocessableOrderAllowOperateField")
  public ResponseBase<Boolean> handleUnprocessableOrderAllowOperateField(
      @RequestBody @Valid HandleAllowOperateFieldReq req) {
    unprocessableOrderAllowOperateOrderHandle.handleAllowOperateField(req);
    return generateSuccess(Boolean.TRUE);
  }


  /**
   * 删除海典重复的订单
   *
   * cf: https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=65480961
   * @param req
   * @return
   */
  @PostMapping("/removeHdRepeatedOrder")
  public ResponseBase<Boolean> removeHdRepeatedOrder(@RequestBody @Valid RemoveHdRepeatedOrderReq req) {
    if(removeHdRepeatedOrderOnOff){
      throw new RuntimeException("removeHdRepeatedOrder closed,please open");
    }

    String monitorKey = req.monitorKey();
    migrationMqConsumerPool.submit(() -> {
      StopWatch stopWatch = new StopWatch();
      if (!stopWatch.isRunning()) {
        stopWatch.start();
      }

      RedisStringUtil.setValue(monitorKey, "刷数中" + new Date(), 30L, TimeUnit.DAYS);
      removeHdRepeatedOrderData.removeHdRepeatedOrder(req);

      stopWatch.stop();
      RedisStringUtil.setValue(monitorKey,
          "刷数结束" + new Date() + ",耗时" + stopWatch.getTotalTimeSeconds(), 30L, TimeUnit.DAYS);
    });
    return generateSuccess(Boolean.TRUE);
  }


  /**
   * 收集平台可能错误的订单 cf: https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=65483076
   *
   * @param req
   * @return
   */
  @PostMapping("/offlineOrderPlatformCheckDataCollect")
  public ResponseBase<Boolean> offlineOrderPlatformCheckDataCollect(
      @RequestBody @Valid OfflineOrderPlatformCheckReq req) {
    if (offlineOrderPlatformCheckDataCollectOnOff) {
      throw new RuntimeException("offlineOrderPlatformCheckDataCollectOnOff closed,please open");
    }

    String monitorKey = req.monitorKey();
    migrationMqConsumerPool.submit(() -> {
      StopWatch stopWatch = new StopWatch();
      if (!stopWatch.isRunning()) {
        stopWatch.start();
      }

      RedisStringUtil.setValue(monitorKey, "刷数中" + new Date(), 30L, TimeUnit.DAYS);
      offlineOrderPlatformCheckDataCollect.offlineOrderPlatformCheck(req);

      stopWatch.stop();
      RedisStringUtil.setValue(monitorKey,
          "刷数结束" + new Date() + ",耗时" + stopWatch.getTotalTimeSeconds(), 30L, TimeUnit.DAYS);
    });
    return generateSuccess(Boolean.TRUE);
  }


  @PostMapping("/offlineOrderPlatformDataErrorHandle")
  public ResponseBase<Boolean> offlineOrderPlatformDataErrorHandle() {
    if (offlineOrderPlatformCheckDataCollectOnOff) {
      throw new RuntimeException("offlineOrderPlatformCheckDataCollectOnOff closed,please open");
    }

    String monitorKey = "offlineOrderPlatformDataErrorHandle";
    StopWatch stopWatch = new StopWatch();
    if (!stopWatch.isRunning()) {
      stopWatch.start();
    }

    RedisStringUtil.setValue(monitorKey, "处理中" + new Date(), 30L, TimeUnit.DAYS);
    offlineOrderPlatformCheckDataCollect.offlineOrderPlatformDataErrorHandle();

    stopWatch.stop();
    RedisStringUtil.setValue(monitorKey,
        "处理结束" + new Date() + ",耗时" + stopWatch.getTotalTimeSeconds(), 30L, TimeUnit.DAYS);
    return generateSuccess(Boolean.TRUE);
  }


  @PostMapping("/fixOfflineOrderMistakePlatformData")
  public ResponseBase<Boolean> FixOfflineOrderMistakePlatformData(
      @RequestBody @Valid FixPlatformCodeMistakeReq req) {
    String scene = req.getScene();
    Long startId = req.getStartId();
    Long endId = req.getEndId();
    List<String> shardingValueList = req.getShardingValueList();
    for (AbstractFlash<?, ?, FixOfflineOrderMistakeScene> flash : fixOfflineOrderMistakeSceneList) {
      migrationMqConsumerPool.submit(() -> {
        FlashParam flashParam = new FlashParam();
        flashParam.setMonitorKey(
            String.format("V2FixOfflineOrderMistakeScene_%s",  scene));

        CustomData customData = new CustomData();
        customData.setShardingValueList(shardingValueList);
        customData.setStartId(startId);
        customData.setEndId(endId);

        flash.customData(customData);
        flash.startFlush(flashParam);
      });
    }
    return generateSuccess(Boolean.TRUE);
  }


  @PostMapping("/fixOfflineRefundOrderMistakePlatformData")
  public ResponseBase<Boolean> FixOfflineRefundOrderMistakePlatformData(
      @RequestBody @Valid FixPlatformCodeMistakeReq req) {
    String scene = req.getScene();
    Long startId = req.getStartId();
    Long endId = req.getEndId();
    List<String> shardingValueList = req.getShardingValueList();
    for (AbstractFlash<?, ?, FixOfflineRefundOrderMistakeScene> flash : fixOfflineRefundOrderMistakeSceneList) {
      migrationMqConsumerPool.submit(() -> {
        FlashParam flashParam = new FlashParam();
        flashParam.setMonitorKey(
            String.format("V2FixOfflineRefundOrderMistakeScene_%s", scene));

        CustomData customData = new CustomData();
        customData.setShardingValueList(shardingValueList);
        customData.setStartId(startId);
        customData.setEndId(endId);

        flash.customData(customData);
        flash.startFlush(flashParam);
      });
    }
    return generateSuccess(Boolean.TRUE);
  }



  @PostMapping("/fixOfflineOrderMistakePlatformDataEsUnhandle")
  public ResponseBase<Boolean> FixOfflineOrderMistakePlatformDataEsUnhandle(
      @RequestBody @Valid FixPlatformCodeMistakeReq req) {
    String scene = req.getScene();
    Long startId = req.getStartId();
    Long endId = req.getEndId();
    List<String> shardingValueList = req.getShardingValueList();
    for (AbstractFlash<?, ?, FixOfflineOrderMistakeSceneUnhandleEs> flash : fixOfflineOrderMistakeSceneUnhandleEsList) {
      migrationMqConsumerPool.submit(() -> {
        FlashParam flashParam = new FlashParam();
        flashParam.setMonitorKey(
            String.format("V2FixOfflineOrderMistakeSceneEsUnhandle_%s",  scene));

        CustomData customData = new CustomData();
        customData.setShardingValueList(shardingValueList);
        customData.setStartId(startId);
        customData.setEndId(endId);

        flash.customData(customData);
        flash.startFlush(flashParam);
      });
    }
    return generateSuccess(Boolean.TRUE);
  }


  @PostMapping("/fixOfflineRefundOrderMistakePlatformDataEsUnhandle")
  public ResponseBase<Boolean> FixOfflineRefundOrderMistakePlatformDataEsUnhandle(
      @RequestBody @Valid FixPlatformCodeMistakeReq req) {
    String scene = req.getScene();
    Long startId = req.getStartId();
    Long endId = req.getEndId();
    List<String> shardingValueList = req.getShardingValueList();
    for (AbstractFlash<?, ?, FixOfflineRefundOrderMistakeSceneUnhandleEs> flash : fixOfflineRefundOrderMistakeSceneUnhandleEsList) {
      migrationMqConsumerPool.submit(() -> {
        FlashParam flashParam = new FlashParam();
        flashParam.setMonitorKey(
            String.format("V2FixOfflineRefundOrderMistakeSceneEsUnhandle_%s", scene));

        CustomData customData = new CustomData();
        customData.setShardingValueList(shardingValueList);
        customData.setStartId(startId);
        customData.setEndId(endId);

        flash.customData(customData);
        flash.startFlush(flashParam);
      });
    }
    return generateSuccess(Boolean.TRUE);
  }
}
