package com.yxt.order.atom.order.es.sync.oms_order_info.handler;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.es.sync.data.CanalOmsOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOmsOrderInfo.OmsOrderInfo;
import com.yxt.order.atom.order.es.sync.oms_order_info.EsOmsOrderInfoModel;
import com.yxt.order.atom.order.es.sync.oms_order_info.EsOmsOrderInfoModel.OmsOrderExModel;
import com.yxt.order.atom.order.es.sync.oms_order_info.EsOmsOrderInfoModel.OmsOrderItemModel;
import com.yxt.order.atom.order.mapper.OmsOrderInfoMapper;
import com.yxt.order.atom.order.mapper.SyncEsMapper;
import com.yxt.order.atom.order.mapper.dto.OmsAddressDto;
import com.yxt.order.atom.order.mapper.dto.OmsLogisticOrderDto;
import com.yxt.order.atom.order.mapper.dto.OmsOrderDetailDto;
import com.yxt.order.atom.order.mapper.dto.OmsOrderExDto;
import com.yxt.order.atom.order.mapper.dto.OmsOrderPayInfoDto;
import com.yxt.order.atom.order.mapper.dto.OmsPlatformOrderDto;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2024/11/4 17:50
 */
@Component
@Slf4j
public class OmsOrderInfoCanalHandler extends
    AbstractCanalHandler<CanalOmsOrderInfo, EsOmsOrderInfoModel> {

  @Resource
  private OmsOrderInfoMapper omsOrderInfoMapper;
  @Resource
  private SyncEsMapper syncEsMapper;

  public OmsOrderInfoCanalHandler() {
    super(CanalOmsOrderInfo.class);
  }


  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.OMS_ORDER_INFO);
  }

  @Override
  protected List<EsOmsOrderInfoModel> assemble() {
    List<OmsOrderInfo> omsOrderInfoList = getData().getData();
    if (CollectionUtils.isEmpty(omsOrderInfoList)) {
      return Lists.newArrayList();
    }

    return omsOrderInfoList.stream().map(
        item -> {
          OmsOrderInfoDO omsOrderInfoDO = omsOrderInfoMapper.selectByOmsOrderNo(
              item.getOmsOrderNo());
          return buildEsOmsOrderInfo(omsOrderInfoDO);
        }
    ).collect(Collectors.toList());
  }


  private EsOmsOrderInfoModel buildEsOmsOrderInfo(OmsOrderInfoDO omsOrderInfoDO) {
    EsOmsOrderInfoModel esOmsOrderInfoModel = new EsOmsOrderInfoModel();
    Long omsOrderNo = omsOrderInfoDO.getOmsOrderNo();
    String thirdPlatformCode = omsOrderInfoDO.getThirdPlatformCode();
    String thirdOrderNo = omsOrderInfoDO.getThirdOrderNo();
    esOmsOrderInfoModel.setOmsOrderNo(omsOrderNo);
    esOmsOrderInfoModel.setClientCode(omsOrderInfoDO.getClientCode());
    esOmsOrderInfoModel.setDeleted(omsOrderInfoDO.getDeleted());
    esOmsOrderInfoModel.setMerCode(omsOrderInfoDO.getMerCode());
    esOmsOrderInfoModel.setOrderOwnerType(omsOrderInfoDO.getOrderOwnerType());
    esOmsOrderInfoModel.setSupplierCode(omsOrderInfoDO.getSupplierCode());
    esOmsOrderInfoModel.setOrderStatus(omsOrderInfoDO.getOrderStatus());
    esOmsOrderInfoModel.setErpStatus(omsOrderInfoDO.getErpStatus());

    OmsLogisticOrderDto omsLogisticOrderDto = syncEsMapper.selectByOmsOrderNo(
        omsOrderNo);
    if(Objects.nonNull(omsLogisticOrderDto)){
      esOmsOrderInfoModel.setLogisticOrderOfPlatformCode(omsLogisticOrderDto.getPlatformCode());
      esOmsOrderInfoModel.setLogisticOrderOfStatus(omsLogisticOrderDto.getStatus());
      esOmsOrderInfoModel.setLogisticOrderOfLogisticConfigId(omsLogisticOrderDto.getLogisticConfigId());
      esOmsOrderInfoModel.setLogisticConfigInfoOfStandardTemplateId(omsLogisticOrderDto.getStandardTemplateId());
    }

    esOmsOrderInfoModel.setCreated(omsOrderInfoDO.getCreated());
    esOmsOrderInfoModel.setPayTime(omsOrderInfoDO.getPayTime());
    esOmsOrderInfoModel.setAuditTime(omsOrderInfoDO.getAuditTime());
    esOmsOrderInfoModel.setShipTime(omsOrderInfoDO.getShipTime());
    esOmsOrderInfoModel.setShipStatus(omsOrderInfoDO.getShipStatus());
    esOmsOrderInfoModel.setCancelTime(omsOrderInfoDO.getCancelTime());
    esOmsOrderInfoModel.setCompleteTime(omsOrderInfoDO.getCompleteTime());
    esOmsOrderInfoModel.setBillTime(omsOrderInfoDO.getBillTime());
    esOmsOrderInfoModel.setSpreadStoreCode(omsOrderInfoDO.getSpreadStoreCode());
    esOmsOrderInfoModel.setIsPostFeeOrder(omsOrderInfoDO.getIsPostFeeOrder());
    esOmsOrderInfoModel.setExStatus(omsOrderInfoDO.getExStatus());
    esOmsOrderInfoModel.setCreateTime(omsOrderInfoDO.getCreateTime());
    esOmsOrderInfoModel.setExpressId(omsOrderInfoDO.getExpressId());
    esOmsOrderInfoModel.setWarehouseId(omsOrderInfoDO.getWarehouseId());
    esOmsOrderInfoModel.setIsPrescription(omsOrderInfoDO.getIsPrescription());
    esOmsOrderInfoModel.setThirdPlatformCode(thirdPlatformCode);
    esOmsOrderInfoModel.setThirdOrderNo(thirdOrderNo);
    esOmsOrderInfoModel.setOmsShipNo(omsOrderInfoDO.getOmsShipNo());
    esOmsOrderInfoModel.setExpressNumber(omsOrderInfoDO.getExpressNumber());
    esOmsOrderInfoModel.setBuyerName(omsOrderInfoDO.getBuyerName());
    esOmsOrderInfoModel.setBuyerMessage(omsOrderInfoDO.getBuyerMessage());
    esOmsOrderInfoModel.setSellerRemark(omsOrderInfoDO.getSellerRemark());
    esOmsOrderInfoModel.setOrderType(omsOrderInfoDO.getOrderType());
    esOmsOrderInfoModel.setSplitStatus(omsOrderInfoDO.getSplitStatus());
    esOmsOrderInfoModel.setSheetStatus(omsOrderInfoDO.getSheetStatus());
    esOmsOrderInfoModel.setRemark(omsOrderInfoDO.getRemark());
    esOmsOrderInfoModel.setOnlineStoreCode(omsOrderInfoDO.getOnlineStoreCode());
    String onlineOrderStoreType = syncEsMapper.onlineStoreType(omsOrderInfoDO.getMerCode(),omsOrderInfoDO.getOnlineStoreCode());
    esOmsOrderInfoModel.setOnlineStoreType(onlineOrderStoreType);
    esOmsOrderInfoModel.setGoodsQty(omsOrderInfoDO.getGoodsQty());
    esOmsOrderInfoModel.setGoodsCategoryQty(omsOrderInfoDO.getGoodsCategoryQty());
    String tag = omsOrderInfoDO.getTag();
    if (!StringUtils.isEmpty(tag) && !"{}".equals(tag)) {
      OrderTag orderTag = JsonUtils.toObject(tag, OrderTag.class);
      if (tag.contains("emptyOrder")) {
        esOmsOrderInfoModel.setTagEmptyOrderStatus(orderTag.getEmptyOrder().getStatus());
      }
      if (tag.contains("mergeOrder")) {
        esOmsOrderInfoModel.setTagMergeOrderStatus(orderTag.getMergeOrder().getStatus());
      }
      if (tag.contains("preSellOrder")) {
        esOmsOrderInfoModel.setTagPreSellOrderStatus(orderTag.getPreSellOrder().getStatus());
      }
      if (tag.contains("modifyAddressTag")) {
        esOmsOrderInfoModel.setTagModifyAddressTagStatus(orderTag.getModifyAddressTag().getStatus());
      }
      if (tag.contains("logisticsInterceptTag")) {
        List<EsOmsOrderInfoModel.LogisticsInterceptOrder> tagLogisticsIntercepts = new ArrayList<>();
        if(!CollectionUtils.isEmpty(orderTag.getLogisticsInterceptOrder())){
          tagLogisticsIntercepts = orderTag.getLogisticsInterceptOrder().stream().map(item -> {
            EsOmsOrderInfoModel.LogisticsInterceptOrder logisticsInterceptOrder = new EsOmsOrderInfoModel.LogisticsInterceptOrder();
            logisticsInterceptOrder.setStatus(item.getStatus());
            logisticsInterceptOrder.setLogisticsNo(item.getLogisticsNo());
            return logisticsInterceptOrder;
          }).collect(Collectors.toList());
        }
        esOmsOrderInfoModel.setTagLogisticsIntercept(tagLogisticsIntercepts);
      }
      if (tag.contains("logisticsUpAddressesTag")) {
        List<EsOmsOrderInfoModel.LogisticsUpAddressOrder> tagLogisticsUpAddress = new ArrayList<>();
        if(!CollectionUtils.isEmpty(orderTag.getLogisticsUpAddressOrder())){
          tagLogisticsUpAddress = orderTag.getLogisticsUpAddressOrder().stream().map(item -> {
            EsOmsOrderInfoModel.LogisticsUpAddressOrder logisticsUpAddressOrder = new EsOmsOrderInfoModel.LogisticsUpAddressOrder();
            logisticsUpAddressOrder.setStatus(item.getStatus());
            logisticsUpAddressOrder.setLogisticsNo(item.getLogisticsNo());
            return logisticsUpAddressOrder;
          }).collect(Collectors.toList());
        }
        esOmsOrderInfoModel.setTagLogisticsUpAddress(tagLogisticsUpAddress);
      }
      if (tag.contains("agentDeliveryOrder")) {
        esOmsOrderInfoModel.setTagAgentDeliveryOrder(orderTag.getAgentDeliveryOrder().getStatus());
      }
    }

    esOmsOrderInfoModel.setStockState(omsOrderInfoDO.getStockState());
    esOmsOrderInfoModel.setSendOrderPrintNum(omsOrderInfoDO.getSendorderPrintNum());

    Integer refundCount = syncEsMapper.omsOrderRefundCount(omsOrderNo);
    esOmsOrderInfoModel.setRefundCount(refundCount);
    esOmsOrderInfoModel.setErpAuditStatus(omsOrderInfoDO.getErpAuditStatus());
    esOmsOrderInfoModel.setErpCodeList(omsOrderInfoDO.getErpCodeList());

    OmsPlatformOrderDto cfyAndAuditStatusDto = syncEsMapper.selectPlatformOrder(
        thirdOrderNo, thirdPlatformCode);
    if(Objects.nonNull(cfyAndAuditStatusDto)){
      esOmsOrderInfoModel.setHavecfy(cfyAndAuditStatusDto.getHaveCfy());
      esOmsOrderInfoModel.setRxAuditStatus(cfyAndAuditStatusDto.getRxAuditStatus());
    }

    OmsOrderPayInfoDto omsOrderPayInfoDto = syncEsMapper.selectOmsOrderPayInfo(omsOrderNo);
    if(Objects.nonNull(omsOrderPayInfoDto)){
      esOmsOrderInfoModel.setPayType(omsOrderPayInfoDto.getPayType());
      esOmsOrderInfoModel.setBuyerActualAmount(omsOrderPayInfoDto.getBuyerActualAmount());
    }

    OmsAddressDto omsAddressDto = syncEsMapper.selectOmsOrderDeliveryAddress(omsOrderNo);
    if(Objects.nonNull(omsAddressDto)){
      esOmsOrderInfoModel.setReceiverMobile(omsAddressDto.getReceiverMobile());
      esOmsOrderInfoModel.setReceiverTelephone(omsAddressDto.getReceiverTelephone());
      esOmsOrderInfoModel.setReceiverName(omsAddressDto.getReceiverName());
      esOmsOrderInfoModel.setFullAddress(omsAddressDto.getFullAddress());
    }
    // 查询订单异常信息（1对多）：订单异常类型、订单异常状态（会出现相同异常类型既有已处理也有未处理的，如果出现，则ES里面设置为未处理）
    // 过滤出异常类型为未处理的，或者异常类型为已处理但是不存在于未处理的异常类型之中
    // 0：未处理，1：已处理
    List<OmsOrderExDto> omsOrderExDtoList = syncEsMapper.selectOmsOrderExList(omsOrderNo);
    if(!CollectionUtils.isEmpty(omsOrderExDtoList)){
      List<Integer> noHandleExTypes = omsOrderExDtoList.stream().filter(exception -> 0 == exception.getOperateStatus()).map(OmsOrderExDto::getExType).collect(Collectors.toList());
      List<OmsOrderExDto> filterExDtoList = omsOrderExDtoList.stream().distinct().filter(exception -> 0 == exception.getOperateStatus() ||
              (1 == exception.getOperateStatus() && !noHandleExTypes.contains(exception.getExType()))).collect(Collectors.toList());
      if(!CollectionUtils.isEmpty(filterExDtoList)){
        esOmsOrderInfoModel.setOmsOrderExModelList(filterExDtoList.stream().map(item->{
          OmsOrderExModel omsOrderExModel = new OmsOrderExModel();
          omsOrderExModel.setExType(item.getExType());
          omsOrderExModel.setOperateStatus(item.getOperateStatus());
          return omsOrderExModel;
        }).collect(Collectors.toList()));
      }
    }

    List<OmsOrderDetailDto> omsOrderDetailDtoList = syncEsMapper.selectOmsOrderDetailList(omsOrderNo);
    if(!CollectionUtils.isEmpty(omsOrderDetailDtoList)){
      esOmsOrderInfoModel.setOmsOrderItemModelList(omsOrderDetailDtoList.stream().map(item -> {
        OmsOrderItemModel omsOrderItemModel = new OmsOrderItemModel();
        omsOrderItemModel.setOmsOrderNo(item.getOmsOrderNo());
        omsOrderItemModel.setStatus(item.getStatus());
        omsOrderItemModel.setErpCode(item.getErpCode());
        omsOrderItemModel.setCommodityName(item.getCommodityName());
        return omsOrderItemModel;
      }).collect(Collectors.toList()));
    }
    return esOmsOrderInfoModel;
  }


//  {"mergeOrder": {"status": 1, "mergedOmsOrderNo": 1794118919036736518}}
//  {"preSellOrder": {"status": 1}, "modifyAddressTag": {"status": 1}}

  @Data
  public static class Tag {
    private Integer status;
  }

  public static class EmptyOrder extends Tag {}
  public static class MergeOrder extends Tag {}
  public static class PreSellOrder extends Tag {}
  public static class ModifyAddressTagOrder extends Tag {}

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class LogisticsInterceptOrder extends Tag {
    private String logisticsNo;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class LogisticsUpAddressOrder extends Tag {
    private String logisticsNo;
  }

  public static class AgentDeliveryOrder extends Tag {}

  @Data
  public static class OrderTag {
    @JsonProperty("emptyOrder")
    private EmptyOrder emptyOrder;

    @JsonProperty("mergeOrder")
    private MergeOrder mergeOrder;

    @JsonProperty("preSellOrder")
    private PreSellOrder preSellOrder;

    @JsonProperty("modifyAddressTag")
    private ModifyAddressTagOrder modifyAddressTag;

    @JsonProperty("logisticsInterceptTag")
    private List<LogisticsInterceptOrder> logisticsInterceptOrder;

    @JsonProperty("logisticsUpAddressesTag")
    private List<LogisticsUpAddressOrder> logisticsUpAddressOrder;

    @JsonProperty("agentDeliveryOrder")
    private AgentDeliveryOrder agentDeliveryOrder;
  }


}
