package com.yxt.order.atom.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.order.entity.DeletedDataDO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DeletedDataMapper extends BaseMapper<DeletedDataDO> {

  Long selectMinId(@Param("suffix")String suffix);

  Long selectMaxId(@Param("suffix")String suffix);

  List<DeletedDataDO> selectListByMinMaxId(@Param("suffix")String suffix,@Param("startId")Long startId, @Param("endId")Long endId);

}
