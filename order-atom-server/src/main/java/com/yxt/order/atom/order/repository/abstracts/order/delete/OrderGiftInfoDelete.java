package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderGiftInfoDO;
import com.yxt.order.atom.order.mapper.OrderGiftInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class OrderGiftInfoDelete extends AbstractDelete {

  @Resource
  private OrderGiftInfoMapper orderGiftInfoMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getOrderGiftInfo();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<OrderGiftInfoDO> query = new LambdaQueryWrapper<>();
    query.eq(OrderGiftInfoDO::getOrderNo, orderNo);
    return orderGiftInfoMapper.delete(query);
  }
}
