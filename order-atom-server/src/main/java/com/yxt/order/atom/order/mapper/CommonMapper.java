package com.yxt.order.atom.order.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.mapper.dto.HdMissPromotionAndCouponDataDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月15日 14:31
 * @email: <EMAIL>
 */
@Repository
public interface CommonMapper {

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  @Select("select id,json from business_compensate where id > #{id} and result='UN_HANDLE' and data_type = 'HD_PRO_COU' order by id asc limit 500")
  List<HdMissPromotionAndCouponDataDto> listHdMissPromotionAndCouponDataList(@Param("id") Long id);

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  @Update("update business_compensate set result = #{result} where id=#{id}")
  Integer updateCompensateResult(@Param("id") Long id, @Param("result") String result);

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  @Update("update business_compensate set result = #{result},reason = #{reason} where id=#{id}")
  Integer compensateError(@Param("id") Long id, @Param("result") String result,
      @Param("reason") String reason);

}
