package com.yxt.order.atom.order.es.sync.operate;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.order.atom.order.es.doc.EsOrgOrder;
import com.yxt.order.atom.order.es.mapper.EsOrgOrderMapper;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgOrderModel;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrgOrderModelOperate extends AbstractEsOperate<OrgOrderModel> {

  @Resource
  private EsOrgOrderMapper esOrgOrderMapper;

  public OrgOrderModelOperate() {
    super(OrgOrderModel.class);
  }

  @Override
  protected Boolean exec() {
    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;
  }

  private Boolean save(OrgOrderModel model) {
    // 适配逻辑删除
    if (Objects.nonNull(model.getDeleted()) && model.getDeleted() != 0L) {// 不为0,表示删除
      return delete(model);
    }

    EsOrgOrder esOrgOrder = model.create();

    LambdaEsQueryWrapper<EsOrgOrder> wrapper = new LambdaEsQueryWrapper<>();
    wrapper.eq(EsOrgOrder::getId, model.defineId());
    wrapper.eq(EsOrgOrder::getDeleted, 0L);
    //先添加路由查找,如果能查找到，直接更新
    wrapper.routing(model.routeKey());
    Long count = esOrgOrderMapper.selectCount(wrapper);
    if(count > 0){
      esOrgOrderMapper.updateById(model.routeKey(), esOrgOrder);
      return true;
    }
    //查不到则需要根据id查一次
    EsOrgOrder existEsOrder = esOrgOrderMapper.selectById(model.defineId());
    if(ObjectUtil.isNull(existEsOrder)){
      esOrgOrderMapper.insert(model.routeKey(), esOrgOrder);
      return true;
    }
    //判断orgCode是否发生了变更
    if(StrUtil.equalsIgnoreCase(existEsOrder.getOrgCode(), model.getOrgCode())){
      esOrgOrderMapper.updateById(model.routeKey(), esOrgOrder);
      return true;
    }
    //如果orgCode发生了变更，此时插入会导致ES中存在两条订单，所以需要删除原来的数据
    esOrgOrderMapper.deleteById(existEsOrder.getOrgCode(), model.defineId());
    //重新插入
    esOrgOrderMapper.insert(model.routeKey(), esOrgOrder);
    return true;
  }

  private Boolean delete(OrgOrderModel model) {
    return esOrgOrderMapper.deleteById(model.routeKey(), model.defineId()) > 0;
  }
}
