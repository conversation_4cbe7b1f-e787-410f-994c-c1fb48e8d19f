package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.AccountCheckChannelDO;
import com.yxt.order.atom.order.entity.AccountCheckPullJobDO;
import com.yxt.order.atom.order.mapper.AccountCheckChannelMapper;
import com.yxt.order.atom.order.mapper.AccountCheckPullJobMapper;
import org.springframework.stereotype.Repository;

@Repository
public class AccountCheckPullJobBatchRepository extends ServiceImpl<AccountCheckPullJobMapper, AccountCheckPullJobDO> {

}
