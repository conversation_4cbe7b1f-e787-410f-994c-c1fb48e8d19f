package com.yxt.order.atom.order.es.sync.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.doc.EsOmsOrderInfo;
import com.yxt.order.atom.order.es.mapper.EsOmsOrderInfoMapper;
import com.yxt.order.atom.order.es.sync.oms_order_info.EsOmsOrderInfoModel;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/11/5 11:18
 */
@Component
@Slf4j
public class EsOmsOrderInfoModelOperate extends AbstractEsOperate<EsOmsOrderInfoModel> {

  @Resource
  private EsOmsOrderInfoMapper esOmsOrderInfoMapper;

  public EsOmsOrderInfoModelOperate() {
    super(EsOmsOrderInfoModel.class);
  }

  @Override
  protected Boolean exec() {

    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;

  }


  private Boolean delete(EsOmsOrderInfoModel esOmsOrderInfoModel) {
    return esOmsOrderInfoMapper.deleteById(esOmsOrderInfoModel.getOmsOrderNo()) > 0;
  }

  private Boolean save(EsOmsOrderInfoModel esOmsOrderInfoModel) {
    // 适配逻辑删除
    if(Objects.nonNull(esOmsOrderInfoModel.getDeleted()) && esOmsOrderInfoModel.getDeleted() != 0L){// 不为0,表示删除
      return delete(esOmsOrderInfoModel);
    }

    EsOmsOrderInfo esOmsOrderInfo = esOmsOrderInfoModel.create();

    LambdaEsQueryWrapper<EsOmsOrderInfo> query = new LambdaEsQueryWrapper<>();
    query.eq(EsOmsOrderInfo::getId, esOmsOrderInfoModel.getOmsOrderNo());
    query.eq(EsOmsOrderInfo::getDeleted, 0);
    Long count = esOmsOrderInfoMapper.selectCount(query);
    if (count > 0) {
      boolean update = esOmsOrderInfoMapper.updateById(esOmsOrderInfo) > 0;
      if (!update) {
        log.warn("更新索引数据失败,{}", JsonUtils.toJson(esOmsOrderInfo));
      }
      return update;

    } else {
      boolean create = esOmsOrderInfoMapper.insert(esOmsOrderInfo) > 0;
      if (create) {
        log.warn("创建索引数据成功,{}", JsonUtils.toJson(esOmsOrderInfo));
      }
      if (!create) {
        log.warn("创建索引数据失败,{}", JsonUtils.toJson(esOmsOrderInfo));
      }
      return create;
    }
  }
}
