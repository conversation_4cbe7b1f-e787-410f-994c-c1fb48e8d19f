package com.yxt.order.atom.repair.handler;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.COMMON_BUSINESS_POOL;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import com.yxt.order.atom.repair.AbstractOrderRepair;
import com.yxt.order.atom.repair.dto.RepairReq;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: moatkon
 * @time: 2025/1/8 18:05
 */
@RestController
@Slf4j
public class ProblemDataRepairController extends AbstractController {

  @Resource
  private List<AbstractOrderRepair<?>> abstractOrderRepairList;

  @Qualifier(COMMON_BUSINESS_POOL)
  @Resource
  private ThreadPoolExecutor commonBusinessPool;

  @PostMapping("/abstractOrderRepairData")
  public ResponseBase<Boolean> abstractOrderRepairData(@RequestBody @Valid RepairReq repairReq) {
    commonBusinessPool.submit(() -> {
      for (AbstractOrderRepair<?> abstractRepair : abstractOrderRepairList) {
        if (repairReq.getRepairScene().equals(abstractRepair.scene())) {
          try {
            String monitoryKey = repairReq.getMonitoryKey();
            RedisStringUtil.setValue(monitoryKey, "刷数中", 30L, TimeUnit.DAYS);
            Long startId = repairReq.getStartId();
            Long endId = repairReq.getEndId();
            abstractRepair.repair(startId, endId);
            RedisStringUtil.setValue(monitoryKey, "刷数结束", 30L, TimeUnit.DAYS);
          } catch (Exception e) {
            log.warn("[step3][数据修复]数据修复失败,请人工介入排查。修复场景:{}",
                abstractRepair.scene(), e);
          }
        }
      }
    });

    return generateSuccess(Boolean.TRUE);
  }

//  @XxlJob("ProblemDataRepairHandler")
//  public void execute() {
//    for (AbstractOrderRepair<?> abstractRepair : abstractOrderRepairList) {
//      try {
//        abstractRepair.repair();
//      } catch (Exception e) {
//        log.error("[step3][数据修复]数据修复失败,请人工介入排查。修复场景:{}",
//            abstractRepair.scene(), e);
//      }
//    }
//  }

}
