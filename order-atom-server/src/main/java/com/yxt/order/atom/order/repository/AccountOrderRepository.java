package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.sdk.common.data.AccountOrderDTO;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountOrderReqDto;
import com.yxt.order.types.order.OrderNo;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:28
 * @email: <EMAIL>
 */
public interface AccountOrderRepository {

  Boolean insertAccountOrder(ApplyAccountOrderReqDto req);


  AccountOrderDTO getAccountOrderByOrderNo(OrderNo orderNo);
}
