package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单支付方式信息
 */
@Data
@TableName("offline_order_pay")
public class OrderPayDO {


  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 内部订单号，自己生成
   */
  private String orderNo;

  /**
   * 支付唯一号
   */
  private String orderPayNo;

  /**
   * 支付方式
   */
  private String payType;

  /**
   * 支付方式名称
   */
  private String payName;

  /**
   * 支付金额
   */
  private BigDecimal payAmount;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;
}
