package com.yxt.order.atom.order.es.sync.es_order.flash;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.SupportChronicDisease;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.es_order.handler.OfflineOrderCanalHandler;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/20 17:30
 */
@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class EsOrderFlashOfflineOrder extends
    AbstractFlash<OfflineOrderDO, OfflineOrder, SupportChronicDisease> {

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OfflineOrderCanalHandler offlineOrderCanalHandler;


  @Override
  protected Long queryCursorStartId() {
    return offlineOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineOrderDO> query = FlashQueryWrapper.offlineOrderFlashQuery(getFlashParam(),defaultLimit());
    return offlineOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineOrder> assembleTargetData(List<OfflineOrderDO> sourceList) {
    return sourceList.stream().map(DoToCanalDtoWrapper::getOfflineOrder).collect(Collectors.toList());
  }



  @Override
  protected void flash(List<OfflineOrder> offlineOrderList) {
    CanalOfflineOrder canalOfflineOrder = new CanalOfflineOrder();
    canalOfflineOrder.setData(offlineOrderList);

    offlineOrderCanalHandler.manualFlash(canalOfflineOrder);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }
}
