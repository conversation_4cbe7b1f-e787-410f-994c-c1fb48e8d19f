package com.yxt.order.atom.order.es;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.es.mapper.EsOrgOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsOrgRefundMapper;
import com.yxt.order.atom.order.es.sync.org_order.flash.OrgOrderFlashHandler;
import com.yxt.order.atom.sdk.order_info.req.FlashDataToEsReq;
import com.yxt.order.atom.sdk.org_order.EsOrgOrderCmdApi;
import com.yxt.order.atom.sdk.org_order.req.OrgOrderFlashToEsJobProcessReqDTO;
import com.yxt.order.atom.sdk.org_order.req.OrgOrderFlashToEsWithJobReqDTO;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class EsOrgOrderCmdController implements EsOrgOrderCmdApi {

  @Resource
  private OrgOrderFlashHandler orgOrderFlashHandler;

  @Resource
  private EsOrgOrderMapper esOrgOrderMapper;

  @Resource
  private EsOrgRefundMapper esOrgRefundMapper;
  /**
   * job执行
   */
  @Override
  public ResponseBase<Void> orgOrderFlashDataToEsWithJobProcess(OrgOrderFlashToEsJobProcessReqDTO request) {
    orgOrderFlashHandler.orgOrderFlashDataToEsWithJobProcess(request);
    return ResponseBase.success();
  }

  /**
   * 通过job方式刷数
   */
  @Override
  public ResponseBase<Void> orgOrderFlashDataToEsWithJob(OrgOrderFlashToEsWithJobReqDTO request) {
    orgOrderFlashHandler.orgOrderFlashDataToEsWithJob(request);
    return ResponseBase.success();
  }

  /**
   * 创建门店正单索引
   */
  @Override
  public ResponseBase<Boolean> createOrgOrderIndex() {
    return ResponseBase.success(esOrgOrderMapper.createIndex());
  }

  /**
   * 创建门店退单索引
   */
  @Override
  public ResponseBase<Boolean> createOrgRefundIndex() {
    return ResponseBase.success(esOrgRefundMapper.createIndex());
  }

  /**
   * 通过订单号刷数
   */
  @Override
  public ResponseBase<Void> orgOrderFlashDataToEs(FlashDataToEsReq flashDataToEsReq) {
    orgOrderFlashHandler.orgOrderFlashDataToEs(flashDataToEsReq);
    return ResponseBase.success();
  }
}
