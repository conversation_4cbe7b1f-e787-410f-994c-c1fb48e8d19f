package com.yxt.order.atom.job.compensate;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import com.yxt.order.atom.order.mapper.CommonMapper;
import com.yxt.order.atom.order.mapper.dto.HdMissPromotionAndCouponDataDto;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 补充信息中心漏传的海典线下单
 *
 * @author: moatkon
 * @time: 2024/12/7 11:46
 */
@Component
@Slf4j
public class CompensateHdOfflineOrderMissPromotionCouponInfoHandler {

  @Resource
  private CommonMapper commonMapper;

  @Value("${mq.topic.producer.hdOfflineOrder}")
  private String hdOfflineOrder;

  @Resource
  private RocketMQTemplate template;

  @XxlJob("compensateHdOfflineOrderMissPromotionCouponInfoHandler")
  public void execute() {
    String key = "compensateHdOfflineOrderMissPromotionCouponHit";
    String value = RedisStringUtil.getValue(key);
    XxlJobHelper.log("key:{}, value:{}",key, value);
    if (!StringUtils.isEmpty(value)) {
      XxlJobHelper.log("已经触发," + value); // 只允许出发一次
      XxlJobHelper.handleSuccess();
      return;
    }
    RedisStringUtil.setValue(key, "hited," + new Date());

    Long startId = 0L;
    while (true) {
      List<HdMissPromotionAndCouponDataDto> dataList = commonMapper.listHdMissPromotionAndCouponDataList(
          startId);
      if (CollectionUtils.isEmpty(dataList)) {
        break;
      }

      handleHdMissData(dataList);

      startId = dataList.get(dataList.size() - 1).getId();
    }

    XxlJobHelper.handleSuccess();
  }

  private void handleHdMissData(List<HdMissPromotionAndCouponDataDto> dataList) {
    for (HdMissPromotionAndCouponDataDto hdMissPromotionAndCouponDataDto : dataList) {
      String tag = "TAG_HD_MISS_PROMOTION_COUPON_DATA";

      Long id = hdMissPromotionAndCouponDataDto.getId();
      String json = hdMissPromotionAndCouponDataDto.getJson();

      JSONObject message = new JSONObject();
      message.put("id", id);
      message.put("json", json);

      SendResult sendResult = template.syncSend(hdOfflineOrder + ":" + tag, message, 6000);
      if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
        commonMapper.compensateError(id, "ERROR", "信息中心漏传促销和券信息补充,消息发送失败");
      } else {
        commonMapper.updateCompensateResult(id, "HANDLING");
      }
    }

  }


}
