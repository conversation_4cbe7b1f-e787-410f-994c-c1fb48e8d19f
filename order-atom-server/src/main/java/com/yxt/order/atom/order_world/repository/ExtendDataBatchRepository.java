package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.ExtendDataDO;
import com.yxt.order.atom.order_world.entity.PlatformOrderPayDO;
import com.yxt.order.atom.order_world.mapper.ExtendDataMapper;
import com.yxt.order.atom.order_world.mapper.PlatformOrderPayMapper;
import org.springframework.stereotype.Repository;

@Repository
public class ExtendDataBatchRepository extends ServiceImpl<ExtendDataMapper, ExtendDataDO> {

}
