package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderMultiPayInfoDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OrderMultiPayInfoDOBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:57
 * @email: <EMAIL>
 */
@Component
public class OrderMultiPayInfoInsert extends AbstractInsert<List<OrderMultiPayInfoDO>> {

  @Resource
  private OrderMultiPayInfoDOBatchRepository orderMultiPayInfoDOBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderMultiPayInfoList());
  }

  @Override
  protected Integer insert(List<OrderMultiPayInfoDO> list) {
    return orderMultiPayInfoDOBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderMultiPayInfoDO> data() {
    return saveDataOptional.getOrderMultiPayInfoList();
  }
}
