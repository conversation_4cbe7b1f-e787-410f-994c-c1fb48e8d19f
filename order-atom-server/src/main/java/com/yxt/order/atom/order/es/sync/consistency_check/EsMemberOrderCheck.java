package com.yxt.order.atom.order.es.sync.consistency_check;

import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.es.doc.EsMemberOrder;
import com.yxt.order.atom.order.es.mapper.EsMemberOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractOrderConsistencyCheck;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsMemberOfflineOrderEfficientCount;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsMemberOrderEfficientCount;
import com.yxt.order.atom.order.es.sync.member_transaction.flash.MemberOfflineOrderInfoFlash;
import com.yxt.order.atom.order.es.sync.member_transaction.flash.MemberOrderInfoFlash;
import javax.annotation.Resource;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/24 15:53
 */
@Component
public class EsMemberOrderCheck extends AbstractOrderConsistencyCheck {

  @Resource
  private EsMemberOrderMapper esMemberOrderMapper;

  @Resource
  private EsMemberOrderEfficientCount esMemberOrderEfficientCount;

  @Resource
  private EsMemberOfflineOrderEfficientCount esMemberOfflineOrderEfficientCount;

  @Resource
  private MemberOfflineOrderInfoFlash memberOfflineOrderInfoFlash;

  @Resource
  private MemberOrderInfoFlash memberOrderInfoFlash;


  @Override
  protected Long dbDscloudCount() {
    return esMemberOrderEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
  }

  @Override
  protected Long dbDscloudOfflineCount() {
    return esMemberOfflineOrderEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
  }

  @Override
  protected Long esCount() {
    LambdaEsQueryWrapper<EsMemberOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.ge(EsMemberOrder::getCreateTime, OrderDateUtils.formatYYMMDD(getStartDate()));
    queryWrapper.le(EsMemberOrder::getCreateTime,OrderDateUtils.formatYYMMDD(getEndDate()));
    return esMemberOrderMapper.selectCount(queryWrapper);
  }

  @Override
  protected ConsistencyNotify consistencyNotify() {
    return ConsistencyNotify.MEMBER_ORDER;
  }

  @Override
  protected void compensate() {
    FlashParam flashParam = getFlashParam();
    memberOfflineOrderInfoFlash.startFlush(flashParam);
    memberOrderInfoFlash.startFlush(flashParam);
  }
}
