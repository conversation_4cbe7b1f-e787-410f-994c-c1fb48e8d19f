package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:06
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalOfflineRefundOrder extends BaseCanalData<OfflineRefundOrder> {

  @Data
  public static class OfflineRefundOrder {

    @JsonProperty("refund_no")
    private String refundNo;

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("store_code")
    private String storeCode;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("refund_state")
    private String refundState;

    @JsonProperty("third_refund_no")
    private String thirdRefundNo;

    @JsonProperty("third_order_no")
    private String thirdOrderNo;

    @JsonProperty("refund_type")
    private String refundType;

    @JsonProperty("after_sale_type")
    private String afterSaleType;

    @JsonProperty("third_platform_code")
    private String thirdPlatformCode;

    @JsonProperty("created")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date created;

    @JsonProperty("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    @JsonProperty("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    private Boolean needRoute = true;

    @JsonProperty("migration")
    private String migration;

    @JsonProperty("consumer_refund")
    private BigDecimal consumerRefund;

    /**
     * 返回是否是迁移订单
     * @return
     */
    public Boolean migrateRefundOrder(){
//      return "true".equalsIgnoreCase(this.migration);
      return false; // 关闭判断,处理迁移订单
    }
  }
}
