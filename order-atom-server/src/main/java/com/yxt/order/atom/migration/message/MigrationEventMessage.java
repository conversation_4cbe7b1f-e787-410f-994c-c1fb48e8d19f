package com.yxt.order.atom.migration.message;

import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月26日 11:29
 * @email: <EMAIL>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MigrationEventMessage {

  private HanaOrderInfo hanaOrderInfo;

  private String taskKey;

  private HanaMigrationDO itemConfig;

  private String schema;

}
