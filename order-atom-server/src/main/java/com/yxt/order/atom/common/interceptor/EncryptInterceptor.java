package com.yxt.order.atom.common.interceptor;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.interceptor.config.SensitiveConfig;
import com.yxt.order.common.annotations.SensitiveModel;
import com.yxt.order.common.cryptography.EncryptUtil;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/***
 * mybatis 设置参数拦截器
 */
@Slf4j
@Component
@Intercepts({
    @Signature(type = ParameterHandler.class, method = "setParameters", args = PreparedStatement.class),
})
public class EncryptInterceptor implements Interceptor {

  /***
   * 加密解密工具
   */
  private final EncryptUtil encryptUtil;
  private SensitiveConfig sensitiveConfig;

  @Autowired
  public EncryptInterceptor(EncryptUtil encryptUtil, SensitiveConfig sensitiveConfig) {
    this.encryptUtil = encryptUtil;
    this.sensitiveConfig = sensitiveConfig;
  }

  @Override
  public Object intercept(Invocation invocation) throws Throwable {
    try {
      ParameterHandler parameterHandler = (ParameterHandler) invocation.getTarget();
      Field mappedStatementField = parameterHandler.getClass().getDeclaredField("mappedStatement");
      mappedStatementField.setAccessible(true);
      MappedStatement mappedStatement = (MappedStatement) mappedStatementField.get(
          parameterHandler);
      //statement id 为空，不处理
      if (StringUtils.isEmpty(mappedStatement.getId())) {
        return invocation.proceed();
      }
      //不在增加修改脱敏范围内，不处理
      if (!sensitiveConfig.getAddUpdates().contains(mappedStatement.getId())) {
        return invocation.proceed();
      }

      Field parameterField = parameterHandler.getClass().getDeclaredField("parameterObject");
      parameterField.setAccessible(true);
      Object parameterObject = parameterField.get(parameterHandler);
      if (parameterObject != null) {
        //参数为@Param注解参数时候
        if (parameterObject instanceof MapperMethod.ParamMap) {
          MapperMethod.ParamMap<?> parameterMap = ((MapperMethod.ParamMap<?>) parameterObject);
          Set<String> keys = parameterMap.keySet();
          if (Objects.nonNull(keys) && keys.size() > 0) {
            for (String key : keys) {
              parameterObject = parameterMap.get(key);
              if (parameterObject == null) {
                continue;
              }
              if (parameterObject instanceof LambdaQueryWrapper) {
                continue;
              }
              break;
            }
          }
          this.handler(parameterObject, invocation);
          return invocation.proceed();
        }
        //其它形式的参数
        this.handler(parameterObject, invocation);
      }
      return invocation.proceed();
    } catch (Exception e) {
      log.info("数据加密时发生异常：", e);
      return invocation.proceed();
    }
  }

  /**
   * 处理不同类型字段参数的加码
   *
   * @param parameterObject
   * @param invocation
   * @return
   * @throws IllegalAccessException
   * @throws InvocationTargetException
   */
  private Object handler(Object parameterObject, Invocation invocation)
      throws IllegalAccessException, InvocationTargetException {
    //参数为数组List形式，循环处理每个数据项
    if (parameterObject != null && (parameterObject instanceof ArrayList)) {
      ArrayList parameterArray = (ArrayList) parameterObject;
      if (!CollectionUtils.isEmpty(parameterArray)) {
        for (Object parameter : parameterArray) {
          if (parameter.getClass() == null) {
            continue;
          }
          SensitiveModel sensitiveModel = AnnotationUtils.findAnnotation(parameter.getClass(),
              SensitiveModel.class);
          if (Objects.nonNull(sensitiveModel)) {
            Field[] declaredFields = parameter.getClass().getDeclaredFields();
            encryptUtil.encrypt(declaredFields, parameter);
          }
        }
      }
      return invocation.proceed();
    }

    if (parameterObject != null) {
      //参数为对象格式
      Class<?> parameterObjectClass = parameterObject.getClass();
      if (parameterObjectClass != null) {
        SensitiveModel sensitiveModel = AnnotationUtils.findAnnotation(parameterObjectClass,
            SensitiveModel.class);
        if (Objects.nonNull(sensitiveModel)) {
          Field[] declaredFields = parameterObjectClass.getDeclaredFields();
          encryptUtil.encrypt(declaredFields, parameterObject);
        }
      }
    }
    return invocation.proceed();
  }

  @Override
  public Object plugin(Object o) {
    return Plugin.wrap(o, this);
  }

  @Override
  public void setProperties(Properties properties) {

  }
}
