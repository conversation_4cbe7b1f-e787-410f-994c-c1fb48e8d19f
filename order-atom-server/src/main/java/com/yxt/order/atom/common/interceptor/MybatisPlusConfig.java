package com.yxt.order.atom.common.interceptor;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisPlusConfig {
  @Bean
  public PaginationInterceptor mybatisPlusInterceptor() {
    return new PaginationInterceptor()
        .setDialectType(DbType.MYSQL.getDb())
        .setLimit(1000L);
  }
}
