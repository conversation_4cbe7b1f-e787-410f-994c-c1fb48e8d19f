package com.yxt.order.atom.order.es.sync.es_order.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_ORDER_REGEX;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.es.sync.clean.ExpireDaysConstant;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel.EsOrderItemModel;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailMapper;
import com.yxt.order.common.exception.DetailNotExistsException;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import com.yxt.order.types.offline.enums.OfflineOrderStateEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月20日 14:21
 * @email: <EMAIL>
 */
@Component
public class OfflineOrderCanalHandler extends
    AbstractCanalHandler<CanalOfflineOrder, EsOrderIndexModel> {

  @Resource
  private OfflineOrderDetailMapper offlineOrderDetailMapper;

  public OfflineOrderCanalHandler() {
    super(CanalOfflineOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();

    return Database.DSCLOUD_OFFLINE.equals(database) && Table.tableRegex(OFFLINE_ORDER_REGEX,
        table);
  }

  @Override
  protected List<EsOrderIndexModel> assemble() {
    List<OfflineOrder> offlineOrderList = getData().getData();
    if (CollectionUtils.isEmpty(offlineOrderList)) {
      return Lists.newArrayList();
    }

    return offlineOrderList.stream()
        .filter(offlineOrder -> !offlineOrder.migrateOrder())
        .filter(offlineOrder -> !OrderDateUtils.isExpired(offlineOrder.getCreated(),
            ExpireDaysConstant.EsOrderEfficientDays))
        .filter(
            offlineOrder -> offlineOrderDoneStatus(offlineOrder.getOrderState()))
        .filter(
            offlineOrder -> !StringUtils.isEmpty(offlineOrder.getUserId()))
        .filter(offlineOrder -> ThirdPlatformCodeEnum.isValid(offlineOrder.getThirdPlatformCode()))
        .map(offlineOrder -> {

          EsOrderIndexModel esOrderModel = new EsOrderIndexModel();
          esOrderModel.setOrderNumber(offlineOrder.getOrderNo());
          esOrderModel.setOrderNo(offlineOrder.getOrderNo());
          esOrderModel.setOrganizationCode(
              offlineOrder.getStoreCode());// 这里只设置OrganizationCode,不设置onlineStore,线下单没有线上门店

          esOrderModel.setUserId(offlineOrder.getUserId());
          esOrderModel.setEsOrderType(EsOrderType.ORDER);
          esOrderModel.setServiceMode(EsServiceMode.POS);
          esOrderModel.setEsOrderStatus(mappingEsOrderStatus(offlineOrder.getOrderState()));
          esOrderModel.setPayTime(offlineOrder.getPayTime());
          esOrderModel.setThirdOrderNo(offlineOrder.getThirdOrderNo());
          esOrderModel.setPlatformCode(offlineOrder.getThirdPlatformCode());
          esOrderModel.setCreateTime(offlineOrder.getCreated());

          if (offlineOrder.getNeedRoute()) {
            try (HintManager hintManager = HintManager.getInstance()) {
              OfflineOrderHit hit = new OfflineOrderHit();
              hit.setDefineNo(esOrderModel.getOrderNumber());
              OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
              bindOfflineOrderDetail(esOrderModel);
            }
          } else {
            bindOfflineOrderDetail(esOrderModel); // 手动刷数,已经在抽象层路由了,不需要在执行路由
          }
          return esOrderModel;
        }).collect(Collectors.toList());
  }

  private void bindOfflineOrderDetail(EsOrderIndexModel esOrderModel) {
    List<OfflineOrderDetailDO> offlineDetailList = getDetailDOList(esOrderModel);

    esOrderModel.setEsOrderItemModelList(
        offlineDetailList.stream().map(offlineDetail -> {
          EsOrderItemModel itemModel = new EsOrderItemModel();
          itemModel.setCommodityCode(offlineDetail.getErpCode());
          itemModel.setCommodityName(offlineDetail.getErpName());
          itemModel.setCommodityCount(offlineDetail.getCommodityCount());
          itemModel.setFiveClass(offlineDetail.getFiveClass());
          return itemModel;
        }).collect(Collectors.toList()));
  }

  @Retryable(value = DetailNotExistsException.class, maxAttempts = 8, backoff = @Backoff(delay = 2000, multiplier = 1.5))
  public List<OfflineOrderDetailDO> getDetailDOList(EsOrderIndexModel esOrderModel) {
    LambdaQueryWrapper<OfflineOrderDetailDO> detailQuery = new LambdaQueryWrapper<>();
    detailQuery.eq(OfflineOrderDetailDO::getOrderNo, esOrderModel.getOrderNumber());
    List<OfflineOrderDetailDO> offlineDetailList = offlineOrderDetailMapper.selectList(
        detailQuery);

    if (CollectionUtils.isEmpty(offlineDetailList)) {
      String error = String.format("线下单-正单明细不存在,%s", esOrderModel.getOrderNumber());
      throw new DetailNotExistsException(error);
    }
    return offlineDetailList;
  }

  private static boolean offlineOrderDoneStatus(String orderStatus) {
    return OfflineOrderStateEnum.DONE.name().equals(orderStatus);
  }

  private EsOrderStatus mappingEsOrderStatus(String orderStatus) {
    if (offlineOrderDoneStatus(orderStatus)) {
      return EsOrderStatus.DONE;
    }
    return null;
  }
}
