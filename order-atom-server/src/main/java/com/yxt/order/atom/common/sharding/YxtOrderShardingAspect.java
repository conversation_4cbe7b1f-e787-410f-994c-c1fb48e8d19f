package com.yxt.order.atom.common.sharding;


import java.lang.reflect.Method;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

/**
 * 请求限流器切面
 *
 * <AUTHOR> (moatkon)
 * @date 2024年01月17日 9:33
 * @email: <EMAIL>
 */
@Aspect
@Slf4j
@Component
public class YxtOrderShardingAspect {

  @Around(value = "@annotation(com.yxt.order.atom.common.sharding.YxtOrderSharding)")
  public Object sharding(ProceedingJoinPoint pjp) throws Throwable {

    MethodSignature signature = (MethodSignature) pjp.getSignature();
    Method method = signature.getMethod();

    YxtOrderSharding yxtOrderSharding = method.getAnnotation(YxtOrderSharding.class);
    String shardingAnno = yxtOrderSharding.shardingNo();

    // spel
    ExpressionParser parser = new SpelExpressionParser();
    LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
    String[] params = discoverer.getParameterNames(method);
    Object[] args = pjp.getArgs();

    EvaluationContext context = new StandardEvaluationContext();
    for (int len = 0; len < params.length; len++) {
      context.setVariable(params[len], args[len]);
    }

    // shardingNo
    Expression expression = parser.parseExpression(shardingAnno);
    String shardingNo = expression.getValue(context, String.class); // token
    if (StringUtils.isEmpty(shardingNo)) {
      throw new RuntimeException("分表键不能为空");
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(shardingNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      return pjp.proceed();
    }


  }
}
