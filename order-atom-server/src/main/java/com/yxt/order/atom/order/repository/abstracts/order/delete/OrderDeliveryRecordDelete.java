package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderDeliveryRecordDO;
import com.yxt.order.atom.order.mapper.OrderDeliveryRecordMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class OrderDeliveryRecordDelete extends AbstractDelete {

  @Resource
  private OrderDeliveryRecordMapper orderDeliveryRecordMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getOrderDeliveryRecord();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<OrderDeliveryRecordDO> query = new LambdaQueryWrapper<>();
    query.eq(OrderDeliveryRecordDO::getOrderNo, orderNo);
    return orderDeliveryRecordMapper.delete(query);
  }
}
