package com.yxt.order.atom.order;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.MerchantConfigRepository;
import com.yxt.order.atom.sdk.online_order.merchant.MerchantAtomQryApi;
import com.yxt.order.atom.sdk.online_order.merchant.req.MerchantConfigReq;
import com.yxt.order.atom.sdk.online_order.merchant.res.MerchantConfigRes;
import com.yxt.starter.controller.AbstractController;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月01日 14:44
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class MerchantAtomConfigController extends AbstractController implements MerchantAtomQryApi {

  @Resource
  private MerchantConfigRepository merchantConfigRepository;


  @Override
  public ResponseBase<MerchantConfigRes> queryMerchantConfig(MerchantConfigReq req) {
    return generateSuccess(merchantConfigRepository.queryMerchantConfig(req));
  }
}
