package com.yxt.order.atom.order.es.sync.org_order.flash;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlashEnhance;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.OrgOrderScene;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;
import com.yxt.order.atom.order.es.sync.org_order.handler.OrgOnlineRefundHandler;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.order.enums.OrderSource;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class OrgOnlineRefundFlash extends AbstractFlashEnhance<RefundOrderDO, RefundOrder, OrgOrderScene> {

  @Resource
  private RefundOrderMapper refundOrderMapper;

  @Resource
  private OrgOnlineRefundHandler orgOnlineRefundHandler;

  @Override
  protected Long queryCursorStartId() {
    return refundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return refundOrderMapper.selectMaxId(getFlashParam());
  }

  /**
   * 获取数据源
   *
   * @return
   */
  @Override
  protected List<RefundOrderDO> getSourceList() {
    LambdaQueryWrapper<RefundOrderDO> query = FlashQueryWrapper.refundOrderFlashQuery(getFlashParam(),defaultLimit());
    return refundOrderMapper.selectList(query);
  }

  /**
   * 组装Canal目标数据
   *
   * @param refundOrderDOS DB数据,需要转成目标CanalData
   * @return canalData
   */
  @Override
  protected List<RefundOrder> assembleTargetData(List<RefundOrderDO> refundOrderDOS) {
    return refundOrderDOS.stream().map(DoToCanalDtoWrapper::getRefundOrder).collect(Collectors.toList());
  }

  /**
   * 刷数
   *
   * @param refundOrders
   */
  @Override
  protected void flash(List<RefundOrder> refundOrders) {
    CanalRefundOrder canalRefundOrder = new CanalRefundOrder();
    canalRefundOrder.setData(refundOrders);
    orgOnlineRefundHandler.manualFlash(canalRefundOrder);
  }

  @Override
  public OrderSource getOrderSource() {
    return OrderSource.ONLINE;
  }

  @Override
  public NumberType getOrderType() {
    return NumberType.REFUND;
  }
}
