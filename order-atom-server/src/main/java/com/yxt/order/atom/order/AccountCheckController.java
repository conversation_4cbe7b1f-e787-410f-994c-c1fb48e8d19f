package com.yxt.order.atom.order;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.AccountCheckRepository;
import com.yxt.order.atom.sdk.common.data.AccountCheckPullJobDTO;
import com.yxt.order.atom.sdk.online_order.account_check.AccountCheckAtomCmdApi;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckCleanReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckPullJobPageQueryReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckPullJobSaveReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckSaveReqDto;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AccountCheckController implements AccountCheckAtomCmdApi {

  @Resource
  private AccountCheckRepository accountCheckRepository;

  @Override
  public ResponseBase<Void> saveAccountCheck(AccountCheckSaveReqDto req) {
    accountCheckRepository.saveAccountCheck(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<Void> saveAccountCheckPullJob(AccountCheckPullJobSaveReqDto req) {
    accountCheckRepository.saveAccountCheckPullJob(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<PageDTO<AccountCheckPullJobDTO>> queryAccountCheckPullJobPage(AccountCheckPullJobPageQueryReqDto req) {
    return ResponseBase.success(accountCheckRepository.queryAccountCheckPullJobPage(req));
  }

  @Override
  public ResponseBase<Void> cleanAccountCheck(AccountCheckCleanReqDto req) {
    accountCheckRepository.cleanAccountCheck(req);
    return ResponseBase.success();
  }
}
