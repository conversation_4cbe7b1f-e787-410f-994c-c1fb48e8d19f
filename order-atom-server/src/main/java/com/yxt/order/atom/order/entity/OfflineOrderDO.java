package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("offline_order")
public class OfflineOrderDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  private String orderNo;
  private String parentOrderNo;

  private String userId;

  private String storeCode;

  private String thirdPlatformCode;

  private String thirdOrderNo;
  private String parentThirdOrderNo;

  private String dayNum;

  private String orderState;

  private Date created;

  private Date payTime;

  private Date billTime;

  private Date completeTime;

  private BigDecimal actualPayAmount;

  private BigDecimal actualCollectAmount;

  private String couponCodes;

  private String createdBy;

  private String updatedBy;

  private Date createdTime;

  private Date updatedTime;

  private Long version;

  // 优惠券核销流水号
  private String serialNo;

  @TableField(exist = false)
  private OfflineOrderOrganizationDO organizationDO;

  @TableField(exist = false)
  private OfflineOrderCashierDeskDO cashierDeskDO;

  @TableField(exist = false)
  private OfflineOrderUserDO userDO;

  @TableField(exist = false)
  private OfflineOrderPrescriptionDO prescriptionDO;

  @TableField(exist = false)
  private List<OfflineOrderPayDO> payDOList;

  @TableField(exist = false)
  private List<OfflineOrderDetailDO> detailDOList;

  @TableField(exist = false)
  private OfflineOrderMedInsSettleDO orderMedInsSettleDO;

  @TableField(exist = false)
  private List<OfflineOrderCouponDO> offlineOrderCouponDOList;

  @TableField(exist = false)
  private List<OfflineOrderPromotionDO> offlineOrderPromotionDOList;

  private String migration;

  // 是否参加促销的标识, true,false
  private String isOnPromotion;

  // 版本号递增
  public void increaseVersion() {
    this.version = this.version + 1;
  }
  public void markUpdateBy(String updatedBy){
    if(StringUtils.isEmpty(this.updatedBy)){
      this.updatedBy = updatedBy;
    }else {
      this.updatedBy = String.format("%s,%s",this.updatedBy,updatedBy);
    }
  }
}