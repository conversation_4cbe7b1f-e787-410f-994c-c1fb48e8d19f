package com.yxt.order.atom.common.utils;

import static com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm.yyMMDatabaseStartValue;
import static com.yxt.order.atom.common.utils.OrderDateUtils.yyMMList;
import static com.yxt.order.common.constants.Constant.OFFLINE_SHARDING_NUM;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/1/15 11:27
 */
public class ShardingUtils {
  public static List<String> shardingValueList(Boolean isHandleNonVipData) {
    List<String> shardingValueList = Lists.newArrayList();
    // 会员分表
    for (int shardingValue = 0; shardingValue <= OFFLINE_SHARDING_NUM; shardingValue++) {
      shardingValueList.add(String.valueOf(shardingValue));
    }

    // 非会员年月分表
    if (isHandleNonVipData) {
      List<String> nonVipYYmmList = nonVipShardingValueList();
      if (!CollectionUtils.isEmpty(nonVipYYmmList)) {
        shardingValueList.addAll(nonVipYYmmList);
      }
    }

    return shardingValueList;
  }

  public static List<String> nonVipShardingValueList(){
    Integer yyMMStart = yyMMDatabaseStartValue();
    return yyMMList(yyMMStart);
  }

  public static Set<String> shardingIndexSet(List<String> noList) {
    Set<String> shardingIndexSet = Sets.newHashSet();
    if (CollectionUtils.isEmpty(noList)) {
      return shardingIndexSet;
    }

    return noList.stream().map(ShardingHelper::getTableIndexByNo).collect(Collectors.toSet());
  }
}
