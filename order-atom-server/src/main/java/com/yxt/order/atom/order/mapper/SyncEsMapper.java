package com.yxt.order.atom.order.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.es.dto.OrderDetailDto;
import com.yxt.order.atom.order.es.dto.RefundDetailDto;
import com.yxt.order.atom.order.mapper.dto.OmsAddressDto;
import com.yxt.order.atom.order.mapper.dto.OmsLogisticOrderDto;
import com.yxt.order.atom.order.mapper.dto.OmsOrderDetailDto;
import com.yxt.order.atom.order.mapper.dto.OmsOrderExDto;
import com.yxt.order.atom.order.mapper.dto.OmsOrderPayInfoDto;
import com.yxt.order.atom.order.mapper.dto.OmsPlatformOrderDto;
import com.yxt.order.atom.order.mapper.dto.OrganizationInfoDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月15日 14:31
 * @email: <EMAIL>
 */
@Repository
public interface SyncEsMapper {

  @DS(DATA_SOURCE.ORDER_MASTER)
  List<OrderDetailDto> selectDetailByOrderNo(@Param("orderNo") Long orderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  List<OrderDetailDto> selectDetailByOmsOrderNo(@Param("omsOrderNo") Long omsOrderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  List<OrderDetailDto> selectRefundDetailByRefundNo(@Param("refundNo") Long refundNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  List<RefundDetailDto> selectDetailByRefundNo(@Param("refundNo") Long refundNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  String selectMemberCardNo(@Param("orderNo") Long orderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  void refreshOmsOrderInfoDataVersion(@Param("omsOrderNo") Long orderNo);
  @DS(DATA_SOURCE.ORDER_MASTER)
  void refreshAccountInfoDataVersion(@Param("orderNo") Long orderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  void refreshOmsOrderInfoDataVersionByThirdOrderNoAndPlatformCode(@Param("thirdPlatformCode") String thirdPlatformCode,
      @Param("thirdOrderNo") String thirdOrderNo);
  @DS(DATA_SOURCE.ORDER_MASTER)
  Integer isPostOrder(@Param("omsOrderNo") Long omsOrderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  OmsLogisticOrderDto selectByOmsOrderNo(@Param("omsOrderNo") Long omsOrderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  Integer omsOrderRefundCount(@Param("omsOrderNo") Long omsOrderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  OmsPlatformOrderDto selectPlatformOrder(@Param("thirdOrderNo") String thirdOrderNo
      , @Param("thirdPlatformCode") String thirdPlatformCode);

  @DS(DATA_SOURCE.ORDER_MASTER)
  OmsOrderPayInfoDto selectOmsOrderPayInfo(@Param("omsOrderNo") Long omsOrderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  OmsAddressDto selectOmsOrderDeliveryAddress(@Param("omsOrderNo") Long omsOrderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  List<OmsOrderExDto> selectOmsOrderExList(@Param("omsOrderNo") Long omsOrderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  List<OmsOrderDetailDto> selectOmsOrderDetailList(@Param("omsOrderNo") Long omsOrderNo);

  @DS(DATA_SOURCE.ORDER_MASTER)
  String onlineStoreType(@Param("merCode") String merCode, @Param("onlineStoreCode")String onlineStoreCode);

  @Select("select user_card_no from offline_refund_order_user where refund_no=#{refundNo}")
  String getUserCardNoByRefundNo(@Param("refundNo")String refundNo);

  @Select("select user_card_no from offline_order_user where order_no=#{orderNo}")
  String getUserCardNoByOrderNo(@Param("orderNo")String orderNo);
  @Select("select company_code as companyCode,store_direct_join_type as storeType from offline_refund_order_organization where refund_no=#{refundNo}")
  OrganizationInfoDto getOrganizationByRefundNo(@Param("refundNo")String refundNo);

  @Select("select company_code as companyCode,store_direct_join_type as storeType from offline_order_organization where order_no=#{orderNo}")
  OrganizationInfoDto getOrganizationByOrderNo(@Param("orderNo")String orderNo);


}
