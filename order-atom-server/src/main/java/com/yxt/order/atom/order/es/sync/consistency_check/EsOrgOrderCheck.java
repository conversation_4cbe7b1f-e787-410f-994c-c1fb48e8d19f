package com.yxt.order.atom.order.es.sync.consistency_check;

import cn.hutool.core.date.DateUtil;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.es.doc.EsOrgOrder;
import com.yxt.order.atom.order.es.mapper.EsOrgOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractOrderConsistencyCheck;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsOrgOfflineOrderEfficientCount;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsOrgOnlineOrderEfficientCount;
import com.yxt.order.atom.order.es.sync.org_order.flash.OrgOfflineOrderFlash;
import com.yxt.order.atom.order.es.sync.org_order.flash.OrgOnlineOrderFlash;
import javax.annotation.Resource;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

@Component
public class EsOrgOrderCheck extends AbstractOrderConsistencyCheck {

  @Resource
  private EsOrgOnlineOrderEfficientCount esOrgOnlineOrderEfficientCount;

  @Resource
  private EsOrgOfflineOrderEfficientCount esOrgOfflineOrderEfficientCount;

  @Resource
  private EsOrgOrderMapper esOrgOrderMapper;

  @Resource
  private OrgOfflineOrderFlash orgOfflineOrderFlash;

  @Resource
  private OrgOnlineOrderFlash orgOnlineOrderFlash;

  @Override
  protected Long dbDscloudCount() {
    return esOrgOnlineOrderEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
  }

  @Override
  protected Long dbDscloudOfflineCount() {
    return esOrgOfflineOrderEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
  }

  @Override
  protected Long esCount() {
    LambdaEsQueryWrapper<EsOrgOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.ge(EsOrgOrder::getCreateTime, DateUtil.formatDateTime(getStartDate()));
    queryWrapper.le(EsOrgOrder::getCreateTime, DateUtil.formatDateTime(getEndDate()));
    return esOrgOrderMapper.selectCount(queryWrapper);
  }

  @Override
  protected ConsistencyNotify consistencyNotify() {
    return ConsistencyNotify.ORG_ORDER;
  }

  @Override
  protected void compensate() {
    FlashParam flashParam = getFlashParam();
    orgOfflineOrderFlash.startFlush(flashParam);
    orgOnlineOrderFlash.startFlush(flashParam);
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }
}
