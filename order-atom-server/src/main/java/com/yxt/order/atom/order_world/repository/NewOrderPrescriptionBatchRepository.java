package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.OrderPrescriptionDO;
import com.yxt.order.atom.order_world.entity.RefundOrderPayDO;
import com.yxt.order.atom.order_world.mapper.NewOrderPrescriptionMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderPayMapper;
import org.springframework.stereotype.Repository;

@Repository
public class NewOrderPrescriptionBatchRepository extends ServiceImpl<NewOrderPrescriptionMapper, OrderPrescriptionDO> {

}
