package com.yxt.order.atom.order.es.doc;


import cn.hutool.core.util.StrUtil;
import com.yxt.order.types.offline.enums.StoreDirectJoinTypeEnum;
import com.yxt.order.types.order_world.OrderType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

@Data
@Settings(shardsNum = 36)
@IndexName(value = "es_order_world_order", keepGlobalPrefix = true)
public class EsOrderWorldOrder {


  @IndexId(type = IdType.CUSTOMIZE)
  private String id;

  /**
   * 系统单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * 父系统单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String parentOrderNo;

  /**
   * 三方订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * 父三方订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String parentThirdOrderNo;

  /**
   * 平台下单时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime created;

  /**
   * 平台下单日期
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String createdDay;

  /**
   * 创建时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime sysCreateTime;

  /**
   * 支付时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime payTime;

  /**
   * 商户编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String merCode;

  /**
   * 线上门店编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String onlineStoreCode;

  /**
   * 下单线下机构编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String organizationCode;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String launchOrganizationCode;

  /**
   * 子公司编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String companyCode;

  /**
   * 订单状态
   * @see com.yxt.order.types.order_world.OrderMainStatus
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderMainStatus;

  /**
   * 支付状态
   * @see com.yxt.order.types.order_world.OrderPaymentStatus
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String paymentStatus;

  /**
   * 交易场景 ONLINE-线上订单 OFFLINE-线下订单
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String transChannel;

  /**
   * 三方平台编码
   * @see com.yxt.order.types.order.enums.PlatformCodeEnum
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdPlatformCode;

  /**
   * 服务模式 O2O B2C JOIN_B2B
   * @see com.yxt.order.types.order_world.BusinessType
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String businessType;

  /**
   * 订单标记，通过空格分割，使用match匹配
   * @see com.yxt.order.types.order.enums.OrderFlagEnum
   */
  @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.WHITESPACE, strategy = FieldStrategy.IGNORED)
  private String orderFlags;

  /**
   * 订单类型，通过空格分割，使用match匹配
   *
   * @see OrderType
   */
  @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.WHITESPACE, strategy = FieldStrategy.IGNORED)
  private String orderTypes;

  /**
   * 异常类型
   */
  @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.WHITESPACE, strategy = FieldStrategy.IGNORED)
  private String abnormalType;

  /**
   * 订单金额
   */
  @IndexField(fieldType = FieldType.SCALED_FLOAT)
  private BigDecimal orderAmount;

  /**
   * 订单明细
   */
  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOrderWorldOrderDetail.class)
  private List<EsOrderWorldOrderDetail> detailList;

  /**
   * 会员编码(唯一值)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userCardNo;

  /**
   * 会员ID (心云)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userId;

  /**
   * 发起人id，目前仅B2B有值
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String launchUserId;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   * @see StoreDirectJoinTypeEnum
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeDirectJoinType;

  /**
   * 支付方式,多个使用空格分割，使用match匹配
   */
  @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.WHITESPACE, strategy = FieldStrategy.IGNORED)
  private String payTypes;

  /**
   * 是否起效 1-起效 -1-未起效
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Long valid;

  /**
   * 全局拦截锁,0正常  ,非0都需要拦截
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer lockForWorld;

  public void fillOrderFlags(List<String> orderFlags) {
    this.orderFlags = StrUtil.join(" ", orderFlags);
  }

  public void fillOrderTypes(List<String> orderTypes) {
    this.orderTypes = StrUtil.join(" ", orderTypes);
  }

  public void fillPayTypes(List<String> payTypes) {
    this.payTypes = StrUtil.join(" ", payTypes);
  }

  public void fillAbnormalType(List<String> abnormalTypes) {
    this.abnormalType = StrUtil.join(" ", abnormalTypes);
  }
}
