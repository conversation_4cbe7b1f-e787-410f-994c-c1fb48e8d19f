package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:06
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CanalRefundOrder extends BaseCanalData<RefundOrder> {

  @Data
  public static class RefundOrder {

    @JsonProperty("refund_no")
    private String refundNo;

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("online_store_code")
    private String onlineStoreCode;

    @JsonProperty("organization_code")
    private String organizationCode;

//    @JsonProperty("user_id")
//    private String userId;

    @JsonProperty("state")
    private String state;

    @JsonProperty("type")
    private String type;

    @JsonProperty("refund_type")
    private String refundType;

    @JsonProperty("third_refund_no")
    private String thirdRefundNo;

    @JsonProperty("third_order_no")
    private String thirdOrderNo;

    @JsonProperty("third_platform_code")
    private String thirdPlatformCode;

    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonProperty("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    @JsonProperty("service_mode")
    private String serviceMode;


    @JsonProperty("oms_order_no") // DB默认值为0
    private Long omsOrderNo;


  }
}

