package com.yxt.order.atom.order;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.RefundRepository;
import com.yxt.order.atom.sdk.common.data.ErpRefundInfoDTO;
import com.yxt.order.atom.sdk.common.data.RefundDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import com.yxt.order.atom.sdk.online_order.refund_info.RefundAtomCmdApi;
import com.yxt.order.atom.sdk.online_order.refund_info.RefundAtomQryApi;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.ErpRefundQueryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundAuditOptionalReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundDetailInsertReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryBatchReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundLogOptionalReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundWithOrderQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.res.RefundInfoQryResDto;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RefundController implements RefundAtomQryApi, RefundAtomCmdApi {

  @Autowired
  private RefundRepository refundRepository;

  @Override
  public ResponseBase<RefundInfoQryResDto> queryRefundInfo(RefundInfoQryReqDto request) {
    return ResponseBase.success(refundRepository.queryRefundInfo(request));
  }


  @Override
  public ResponseBase<List<RefundInfoQryResDto>> queryRefundInfoBatch(RefundInfoQryBatchReqDto request) {
    return ResponseBase.success(refundRepository.queryRefundInfoBatch(request));
  }

  @Override
  public ResponseBase<RefundOrderDTO> queryLatestRefundInfo(RefundWithOrderQryReqDto request) {
    return ResponseBase.success(refundRepository.queryLatestRefundInfo(request));
  }

  @Override
  public ResponseBase<Void> saveRefundAuditResult(RefundAuditOptionalReq request) {
    refundRepository.saveRefundAuditResult(request);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<Void> saveRefundLog(RefundLogOptionalReq request) {
    refundRepository.saveRefundLog(request);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<List<ErpRefundInfoDTO>> queryErpRefund(ErpRefundQueryReqDto request) {
    return ResponseBase.success(refundRepository.queryErpRefund(request));
  }

  @Override
  public ResponseBase<List<RefundDetailDTO>> createRefundDetail(RefundDetailInsertReq request) {
    return ResponseBase.success(refundRepository.createRefundDetail(request));
  }
}
