package com.yxt.order.atom.job.abstractsStageOrder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月16日 17:57
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class User404FoundUser extends AbstractStageOrderHandler {


  @Override
  protected String tag() {
    return buildUser404Tag("foundUser");
  }

  @Override
  protected Boolean isStageOrder() {
    return Boolean.TRUE;
  }


}
