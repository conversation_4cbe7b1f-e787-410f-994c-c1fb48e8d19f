package com.yxt.order.atom.common.sharding;

import com.google.common.collect.Lists;
import com.yxt.order.types.utils.ShardingHelper;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月10日 17:43
 * @email: <EMAIL>
 */
@Data
public class OfflineOrderHit implements Comparable<Object> {

  private String defineNo; // 自定义的正单号或者退单号

  private QueryHit queryHit;

  @Override
  public int compareTo(@NotNull Object o) {
    return 0;
  }

  /**
   * 查询Hit
   */
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static
  class QueryHit {

    private String seq;

    private OfflineDataBaseEnum dataBaseEnum;

    public QueryHit(Date shardingDateTime, OfflineDataBaseEnum dataBaseEnum) {
      this.seq = ShardingHelper.archiveByYearMonth(shardingDateTime == null ? new Date() : shardingDateTime);;
      this.dataBaseEnum = dataBaseEnum == null ? OfflineDataBaseEnum.OFFLINE_ORDER : dataBaseEnum;
    }

    public QueryHit(Date shardingDateTime) {
      this.seq = ShardingHelper.archiveByYearMonth(shardingDateTime == null ? new Date() : shardingDateTime);;
    }
    public QueryHit(LocalDateTime shardingDateTime) {
      this.seq = ShardingHelper.archiveByYearMonth(shardingDateTime == null ? LocalDateTime.now() : shardingDateTime);;
    }

    @Getter
    public enum OfflineDataBaseEnum {
      // 线下单库
      OFFLINE_ORDER(Lists.newArrayList("order-offline-0")),
      // 线下单归档库
      OFFLINE_ORDER_ARCHIVE(Lists.newArrayList("order-offline-1")),
      // 线下单库和线下单归档库
      ALL_OFFLINE_DATABASES(Lists.newArrayList("order-offline-0","order-offline-1")),
      ;

      private final List<String> actualDataSourceList; // 和配置文件映射

      OfflineDataBaseEnum(List<String> actualDataSourceList) {
        this.actualDataSourceList = actualDataSourceList;
      }
    }
  }

}
