package com.yxt.order.atom.order.converter;

import com.yxt.order.atom.order.entity.MqMessageDO;
import com.yxt.order.atom.sdk.mqmessage.req.SaveMqMessageReqDto;
import com.yxt.order.atom.sdk.mqmessage.res.MqMessageResDto;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:35
 * @email: <EMAIL>
 */
@Mapper
public interface MqMessageConverter {

  MqMessageConverter INSTANCE = Mappers.getMapper(MqMessageConverter.class);

  MqMessageDO toDO(SaveMqMessageReqDto saveMqMessageReqDto);


  List<MqMessageResDto> toDo(List<MqMessageDO> mqMessageDOList);


}
