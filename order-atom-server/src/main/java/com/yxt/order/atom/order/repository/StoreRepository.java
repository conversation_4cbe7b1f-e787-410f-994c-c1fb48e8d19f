package com.yxt.order.atom.order.repository;


import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.atom.sdk.common.data.StoreBillConfigDTO;
import com.yxt.order.atom.sdk.online_order.store.req.GetOnlineStoreByPlatformShopIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.InnerStoreDictionaryListReq;
import com.yxt.order.atom.sdk.online_order.store.req.InnerStoreDictionaryReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigByIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStoreAccessReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStorePageReq;
import com.yxt.order.atom.sdk.online_order.store.req.QuerySysStoreInfoReq;
import com.yxt.order.atom.sdk.online_order.store.req.StoreQueryByScaleReq;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreConfigResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreResDto;
import com.yxt.order.atom.sdk.online_order.store.res.InnerStoreDictionaryResDto;
import com.yxt.order.atom.sdk.online_order.store.res.OnlineStoreInfoResDto;
import com.yxt.order.atom.sdk.online_order.store.res.StoreQueryByScaleResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsStoreResDto;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * o2o平台网店信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */

@Repository
public interface StoreRepository {

  /**
   * 获取服务商模式网店信息
   *
   * @param platformCode
   * @param onlineStoreCode
   * @return
   */
  DsOnlineClientResDto selectSupplierClient(String platformCode, String onlineStoreCode);


  OnlineStoreInfoResDto querySysStoreInfo(QuerySysStoreInfoReq req);


  The3DsStoreResDto getStoreAccess(QueryStoreAccessReq req);

  DsOnlineStoreResDto queryDsOnlineStore(QueryDsOnlineStoreReq req);

  DsOnlineStoreConfigResDto queryDsOnlineStoreConfig(QueryDsOnlineStoreConfigReq req);

  InnerStoreDictionaryResDto queryInnerStoreDictionary(InnerStoreDictionaryReq req);
  List<InnerStoreDictionaryResDto> queryInnerStoreDictionaryList(InnerStoreDictionaryListReq req);

  The3DsOnlineClientResDto getOnlineStoreByPlatformShopId(GetOnlineStoreByPlatformShopIdReq req);

  StoreBillConfigDTO getBillConfigById(QueryBillConfigByIdReq req);

  StoreBillConfigDTO getBillConfig(QueryBillConfigReq req);

  StoreQueryByScaleResDto queryStoreInfoByScale(StoreQueryByScaleReq req);
  PageDTO<DsOnlineStoreResDto> queryStorePage(QueryStorePageReq req);
}
