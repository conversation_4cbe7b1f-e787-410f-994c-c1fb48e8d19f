package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderLogDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OrderLogBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class OrderLogInsert extends AbstractInsert<List<OrderLogDO>> {

  @Resource
  private OrderLogBatchRepository orderLogBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderLogList());
  }

  @Override
  protected Integer insert(List<OrderLogDO> list) {
    return orderLogBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderLogDO> data() {
    return saveDataOptional.getOrderLogList();
  }

}
