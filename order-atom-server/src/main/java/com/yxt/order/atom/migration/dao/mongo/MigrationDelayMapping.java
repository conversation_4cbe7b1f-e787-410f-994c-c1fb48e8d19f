package com.yxt.order.atom.migration.dao.mongo;

import java.util.Date;
import lombok.Data;

/**
 * 迁移完就没用了
 * <p>
 * 迁移订单只有正单有场景
 *
 * <AUTHOR> (moatkon)
 * @date 2024年08月15日 17:40
 * @email: <EMAIL>
 */
@Data
public class MigrationDelayMapping {

  public static final String COLLECTION_NAME = "migration_delay_parent_order_mapping";


  private String parentThirdOrderNo;

  private String thirdPlatformCode;

  private String storeCode;

  private String childOrderNo;

  private Date createDateTime;

}
