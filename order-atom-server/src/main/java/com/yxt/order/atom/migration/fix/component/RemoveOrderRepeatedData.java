package com.yxt.order.atom.migration.fix.component;

import static com.yxt.order.atom.migration.constant.MigrationConstant.DELETED_MIGRATION_REPEATED;
import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildOrderExistsQuery;
import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildOrderExistsQueryOnlyForKechuna;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.fix.RemoveMigrationOrderRepeatedData;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/13 10:21
 * <p>
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class RemoveOrderRepeatedData extends
    AbstractFlash<OfflineOrderDO, OfflineOrderDO, RemoveMigrationOrderRepeatedData> {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;


  @Resource
  private OfflineOrderMapper offlineOrderMapper;


  @Override
  protected Long queryCursorStartId() {
    CustomData customData = getCustomData();
    Long startId = customData.getStartId();
    return Objects.nonNull(startId) ? startId : offlineOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    CustomData customData = getCustomData();
    Long endId = customData.getEndId();
    return Objects.nonNull(endId) ? endId : offlineOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineOrderDO> query = FlashQueryWrapper.offlineOrderFlashQuery(
        getFlashParam(), defaultLimit());
    return offlineOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineOrderDO> assembleTargetData(List<OfflineOrderDO> offlineOrderDOList) {
    return offlineOrderDOList;
  }

  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param offlineOrderList
   */
  @Override
  protected void flash(List<OfflineOrderDO> offlineOrderList) {
    CustomData customData = getCustomData();
    Boolean onlyHandleKeChuan = customData.getOnlyHandleKeChuan();

    for (OfflineOrderDO offlineOrderDO : offlineOrderList) {
      String migration = offlineOrderDO.getMigration();
      if (StringUtils.isEmpty(migration)) {
        continue;
      }
      if (!Boolean.TRUE.toString().equals(migration)) {
        continue;
      }

      if(onlyHandleKeChuan && !ThirdPlatformCodeEnum.KE_CHUAN.name().equals(offlineOrderDO.getThirdPlatformCode())){
        continue;
      }

      // 1. 检查是否有多条
      OfflineOrderExistsReqDto check1 = new OfflineOrderExistsReqDto();
      check1.setStoreCode(offlineOrderDO.getStoreCode());
      check1.setThirdOrderNo(offlineOrderDO.getThirdOrderNo());
      check1.setThirdPlatformCode(offlineOrderDO.getThirdPlatformCode());
      check1.setDefineNo(offlineOrderDO.getOrderNo());
//      reqDto.setThirdCreated();
      LambdaQueryWrapper<OfflineOrderDO> check1Count = buildOrderExistsQuery(check1);
      if (offlineOrderMapper.selectCount(check1Count) <= 1) {
        continue;
      }

      if(onlyHandleKeChuan){
        OfflineOrderExistsReqDto check2 = new OfflineOrderExistsReqDto();
        check2.setStoreCode(offlineOrderDO.getStoreCode());
        check2.setThirdOrderNo(offlineOrderDO.getThirdOrderNo());
        check2.setThirdPlatformCode(offlineOrderDO.getThirdPlatformCode());
        check2.setDefineNo(offlineOrderDO.getOrderNo());
        check2.setThirdCreated(offlineOrderDO.getBillTime());
        LambdaQueryWrapper<OfflineOrderDO> check2Count = buildOrderExistsQueryOnlyForKechuna(check2);
        if (offlineOrderMapper.selectCount(check2Count) != 2) { // 科传版本问题导致24年11月29号之前的订单时间和订单时间不一致。这查询需要有2条记录
          continue;
        }
      }else {
        // 2. 检查是否真的重复
        OfflineOrderExistsReqDto check2 = new OfflineOrderExistsReqDto();
        check2.setStoreCode(offlineOrderDO.getStoreCode());
        check2.setThirdOrderNo(offlineOrderDO.getThirdOrderNo());
        check2.setThirdPlatformCode(offlineOrderDO.getThirdPlatformCode());
        check2.setDefineNo(offlineOrderDO.getOrderNo());
        check2.setThirdCreated(offlineOrderDO.getBillTime());
        LambdaQueryWrapper<OfflineOrderDO> check2Count = buildOrderExistsQuery(check2);
        if (offlineOrderMapper.selectCount(check2Count) != 1) { // 必须为1条,如果不是,则不处理
          continue;
        }
      }

      if (Objects.isNull(offlineOrderDO.getId())) {
        continue;
      }

      offlineOrderRepository.deletedOfflineOrder(offlineOrderDO,
          DELETED_MIGRATION_REPEATED, Boolean.TRUE);
    }

  }


  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }
  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}
