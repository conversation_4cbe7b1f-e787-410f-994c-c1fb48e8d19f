package com.yxt.order.atom.order.repository.abstracts.refund.update;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundInsert;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundUpdate;
import com.yxt.order.atom.order.repository.batch.RefundBatchRepository;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RefundInfoUpdate extends AbstractRefundUpdate<List<RefundOrderDO>> {

  @Autowired
  private RefundBatchRepository refundBatchRepository;

  @Override
  protected Boolean canUpdate() {
    return CollUtil.isNotEmpty(data());
  }

  @Override
  protected Integer update(List<RefundOrderDO> refundOrderList) {
    List<RefundOrderDO> updateByIdList = refundOrderList.stream()
        .filter(refund -> ObjectUtil.isNotNull(refund.getId())).collect(Collectors.toList());
    Integer rows = 0;
    if (CollUtil.isNotEmpty(updateByIdList)) {
      refundBatchRepository.updateBatchById(updateByIdList);
      rows += updateByIdList.size();
    }
    List<RefundOrderDO> updateByRefundNoList = refundOrderList.stream()
        .filter(refund -> ObjectUtil.isNull(refund.getId()) && ObjectUtil.isNotNull(refund.getRefundNo())).collect(Collectors.toList());
    if (CollUtil.isNotEmpty(updateByRefundNoList)) {
      for (RefundOrderDO refundOrderDO : updateByRefundNoList) {
        refundBatchRepository.update(refundOrderDO, Wrappers.<RefundOrderDO>lambdaQuery().eq(RefundOrderDO::getRefundNo, refundOrderDO.getRefundNo()));
        rows += 1;
      }
    }
    return rows;
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<RefundOrderDO> data() {
    return this.saveData.getRefundOrderList();
  }

}
