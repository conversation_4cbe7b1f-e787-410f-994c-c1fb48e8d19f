package com.yxt.order.atom.repair.problem_data_repair;

import com.yxt.order.atom.migration.constant.MigrationConstant;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OrderDataRepairDO;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.repair.AbstractOrderRepair;
import com.yxt.order.atom.repair.dto.PreCheckResult;
import com.yxt.order.atom.repair.dto.RepairResult;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.repair.RepairOfflineRefundOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.common.utils.OrderJsonUtils;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.repair.RepairScene;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/1/9 10:02
 */
@Component
public class HaiDianOnlineRefundOrderAsOfflineOrderRepair extends AbstractOrderRepair<RepairOfflineRefundOrderReqDto> {

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Override
  public RepairScene scene() {
    return RepairScene.ONLINE_ORDER_AS_OFFLINE_REFUND_ORDER;
  }

  @Override
  protected RepairOfflineRefundOrderReqDto parse(OrderDataRepairDO orderDataRepair) {
    return OrderJsonUtils.toObject(
        orderDataRepair.getInput(), RepairOfflineRefundOrderReqDto.class);
  }

  @Override
  protected String shardingNo(OrderDataRepairDO orderDataRepairDO) {
    return parse(orderDataRepairDO).getOfflineRefundOrderDTO().getRefundNo();
  }

  @Override
  protected PreCheckResult repairPreCheck(OrderDataRepairDO orderDataRepairDO) {
    RepairOfflineRefundOrderReqDto onlineRefundOrderReq = parse(orderDataRepairDO);
    OfflineRefundOrderDTO onlineRefundOrder = onlineRefundOrderReq.getOfflineRefundOrderDTO();

    OfflineRefundOrderExistsReqDto reqDto = new OfflineRefundOrderExistsReqDto();
    reqDto.setStoreCode(onlineRefundOrder.getStoreCode());
    reqDto.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    reqDto.setThirdRefundNo(onlineRefundOrder.getThirdRefundNo());
    reqDto.setThirdCreated(onlineRefundOrder.getCreated());
    OfflineRefundOrderDO offlineRefundOrderDO = offlineOrderRepository.offlineRefundOrderForRepair(reqDto);
    if (Objects.isNull(offlineRefundOrderDO)) {
      return PreCheckResult.create()
          .failed(String.format("%s 查不到数据,可能是重复MQ消息,已经处理被删了,或者就是找不到", OrderJsonUtils.toJson(reqDto)));
    } else {
      return PreCheckResult.create().passAndRecordBeforeImage(OrderJsonUtils.toJson(offlineRefundOrderDO),offlineRefundOrderDO.getRefundNo());
    }
  }

  @Override
  protected RepairResult orderRepair(OrderDataRepairDO orderDataRepair) {
    // 物理删除
    OfflineRefundOrderDO offlineRefundOrderDO = OrderJsonUtils.toObject(orderDataRepair.getBeforeImage(),OfflineRefundOrderDO.class);
    Boolean result = offlineOrderRepository.deletedOfflineRefundOrder(offlineRefundOrderDO,
        MigrationConstant.DELETED_HAIDIAN_OFFLINE_REFUND_AS_OFFLINE_REFUND_ORDER,Boolean.FALSE);

    return RepairResult.builder()
        .result(result)
        .businessNo(offlineRefundOrderDO.getRefundNo())
        .build();
  }

}
