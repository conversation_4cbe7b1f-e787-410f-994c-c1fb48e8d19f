package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalPlatformOrderInfo.PlatformOrderInfo;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2024/11/9 10:55
 */
public class CanalPlatformOrderInfo extends BaseCanalData<PlatformOrderInfo> {
  @Data
  public static class PlatformOrderInfo {

    @JsonProperty("olorderno")
    private String thirdOrderNo ;

    @JsonProperty("ectype")
    private String thirdPlatformCode;

  }
}
