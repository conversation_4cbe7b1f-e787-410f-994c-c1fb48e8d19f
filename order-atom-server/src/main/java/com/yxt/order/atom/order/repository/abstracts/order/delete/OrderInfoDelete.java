package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class OrderInfoDelete extends AbstractDelete {

    @Resource
    private OrderInfoMapper orderInfoMapper;


    @Override
    protected Boolean canDelete() {
        return dto.getOrderInfo();
    }

    @Override
    protected Integer delete() {
        Assert.isTrue(!StringUtils.isEmpty(orderNo), "orderNo不能为空");
        return orderInfoMapper.deleteByOrderNo(orderNo);
    }
}
