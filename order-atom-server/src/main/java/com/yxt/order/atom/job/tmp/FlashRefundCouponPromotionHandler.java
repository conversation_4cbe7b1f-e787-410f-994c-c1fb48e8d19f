package com.yxt.order.atom.job.tmp;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.COMMON_BUSINESS_POOL;
import static com.yxt.order.common.constants.Constant.OFFLINE_SHARDING_NUM;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.event.WriteRefundPromotionCouponDataEvent;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderCouponMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderPromotionMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.order.mapper.dto.IdOrderNo;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 刷订单促销、券信息到退单临时表 只会执行一次
 *
 * @author: moatkon
 * @time: 2024/11/29 10:28
 */
@Component
@Slf4j
public class FlashRefundCouponPromotionHandler {

  @Resource
  private OfflineOrderPromotionMapper offlineOrderPromotionMapper;
  @Resource
  private OfflineOrderCouponMapper offlineOrderCouponMapper;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Value("${flashRefundToBigDataYYMM:2406,2407,2408,2409,2410,2411,2412}")
  private String flashRefundToBigDataYYMM;

  @Resource
  private ApplicationEventPublisher applicationEventPublisher;


  @XxlJob("flashRefundPromotionHandler")
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void executePromotion() {
    for (String shardingValue : shardingValueList()) {
      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setQueryHit(QueryHit.builder().seq(shardingValue).build());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

        Long startId = 0L; //每次分表从0开始
        for (; ; ) {
          List<IdOrderNo> idOrderNoList = offlineOrderPromotionMapper.selectPromotionOrderNoList(
              startId);
          if (CollectionUtils.isEmpty(idOrderNoList)) {
            break;
          }
          // 计算最大的Id
          startId = idOrderNoList.stream().map(IdOrderNo::getId).max(Comparator.naturalOrder())
              .get();

          handleBigData(idOrderNoList);
        }
      }
    }

    XxlJobHelper.handleSuccess();
  }


  @XxlJob("flashRefundCouponHandler")
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void executeCoupon() {
    for (String shardingValue : shardingValueList()) {
      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setQueryHit(QueryHit.builder().seq(shardingValue).build());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

        Long startId = 0L; //每次分表从0开始
        for (; ; ) {
          List<IdOrderNo> idOrderNoList = offlineOrderCouponMapper.selectCouponOrderNoList(
              startId);
          if (CollectionUtils.isEmpty(idOrderNoList)) {
            break;
          }
          // 计算最大的Id
          startId = idOrderNoList.stream().map(IdOrderNo::getId).max(Comparator.naturalOrder())
              .get();

          handleBigData(idOrderNoList);
        }
      }
    }

    XxlJobHelper.handleSuccess();
  }

  private void handleBigData(List<IdOrderNo> idOrderNoList) {
    List<String> orderNoList = idOrderNoList.stream().map(IdOrderNo::getOrderNo)
        .collect(Collectors.toList());
    LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<>();
    query.in(OfflineRefundOrderDO::getOrderNo, orderNoList);
    List<OfflineRefundOrderDO> refundOrderDOList = offlineRefundOrderMapper.selectList(query);
    if (!CollectionUtils.isEmpty(refundOrderDOList)) {
      for (OfflineRefundOrderDO offlineRefundOrderDO : refundOrderDOList) {
        WriteRefundPromotionCouponDataEvent event = new WriteRefundPromotionCouponDataEvent();
        event.setOfflineRefundOrderDO(offlineRefundOrderDO);
        applicationEventPublisher.publishEvent(event);
      }
    }
  }


  @Async(COMMON_BUSINESS_POOL)
  @EventListener
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void onEvent(WriteRefundPromotionCouponDataEvent event) {
    OfflineRefundOrderDO offlineRefundOrderDO = event.getOfflineRefundOrderDO();
    offlineOrderRepository.writeRefundPromotionCouponData(offlineRefundOrderDO);
  }

  public List<String> shardingValueList() {
    List<String> shardingValueList = Lists.newArrayList();
    // 会员分表
    for (int shardingValue = 0; shardingValue <= OFFLINE_SHARDING_NUM; shardingValue++) {
      shardingValueList.add(String.valueOf(shardingValue));
    }

    // 非会员分表
    shardingValueList.addAll(Arrays.asList(flashRefundToBigDataYYMM.split(",")));
    return shardingValueList;
  }


}
