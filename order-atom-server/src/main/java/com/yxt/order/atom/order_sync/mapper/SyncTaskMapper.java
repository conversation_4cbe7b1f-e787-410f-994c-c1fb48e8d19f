package com.yxt.order.atom.order_sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.order_sync.entity.OrderSyncMappingDO;
import com.yxt.order.atom.order_sync.entity.SyncTaskModelDO;
import com.yxt.order.atom.sdk.order_sync.req.SyncTaskSearchReq;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SyncTaskMapper extends BaseMapper<SyncTaskModelDO> {

  List<SyncTaskModelDO> syncTaskSearch(@Param("request") SyncTaskSearchReq request);
}
