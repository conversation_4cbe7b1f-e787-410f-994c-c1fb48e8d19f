package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("platform_order_detail")
public class PlatformOrderDetailDO {

  /**
   * 平台编码
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 平台订单明细编号
   */
  private String thirdOrderDetailNo;

  /**
   * 商品行号
   */
  private String rowNo;

  /**
   * 平台商品编码
   */
  private String platformSkuId;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 明细状态 NORMAL-正常
   */
  private String status;

  /**
   * 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品
   */
  private String giftType;

  /**
   * 商品原单价
   */
  private BigDecimal originalPrice;

  /**
   * 商品售价
   */
  private BigDecimal price;

  /**
   * 商品总额
   */
  private BigDecimal totalAmount;

  /**
   * 优惠分摊
   */
  private BigDecimal discountShare;

  /**
   * 折扣金额
   */
  private BigDecimal discountAmount;

  /**
   * 商品成本价
   */
  private BigDecimal commodityCostPrice;

  /**
   * 商品规格
   */
  private String commoditySpec;

  /**
   * 生产商
   */
  private String manufacture;

  /**
   * 商品五级分类编码
   */
  private String fiveClass;

  /**
   * 商品五级分类Name
   */
  private String fiveClassName;

  /**
   * 商品图片
   */
  private String mainPic;

  /**
   * 售货员Id
   */
  private String salerId;

  /**
   * 售货员Name
   */
  private String salerName;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

}
