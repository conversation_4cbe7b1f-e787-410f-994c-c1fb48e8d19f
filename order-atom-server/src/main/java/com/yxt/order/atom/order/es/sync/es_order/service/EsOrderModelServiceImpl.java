package com.yxt.order.atom.order.es.sync.es_order.service;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.doc.EsOrder;
import com.yxt.order.atom.order.es.mapper.EsOrderMapper;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月07日 18:05
 * @email: <EMAIL>
 */
@Service
@Slf4j
public class EsOrderModelServiceImpl implements EsOrderModelService {

  @Resource
  private EsOrderMapper esOrderMapper;


  @Override
  public Boolean createEsOrder(EsOrderIndexModel esOrderModel) {
    EsOrder esOrder = esOrderModel.create();
    //  org.dromara.easyes.core.kernel.BaseEsMapperImpl.doInsert  多线程\\id已存在,数据被更新的情况返回0
    return esOrderMapper.insert(esOrder) >= 0;
  }

  @Override
  public Boolean saveEsOrder(EsOrderIndexModel esOrderModel) {
    // 适配逻辑删除
    if(Objects.nonNull(esOrderModel.getDeleted()) && esOrderModel.getDeleted() != 0L){// 不为0,表示删除
      return deleteEsOrder(esOrderModel);
    }

    EsOrder esOrder = esOrderModel.create();

    LambdaEsQueryWrapper<EsOrder> query = new LambdaEsQueryWrapper<>();
    query.eq(EsOrder::getOrderNumber, esOrder.getOrderNumber());
    query.eq(EsOrder::getPlatformCode, esOrder.getPlatformCode());
    Long count = esOrderMapper.selectCount(query);
    if (count > 0) {
      // update
      Boolean update = esOrderMapper.updateById(esOrder) > 0;
      if (!update) {
        log.warn("更新索引数据失败,{}", JsonUtils.toJson(esOrder));
      }
      return update;
    } else {
      Boolean create = createEsOrder(esOrderModel);
      if (!create) {
        log.warn("创建索引数据失败,{}", JsonUtils.toJson(esOrder));
      }
      return create;
    }
  }

  @Override
  public Boolean deleteEsOrder(EsOrderIndexModel esOrderModel) {
    EsOrder esOrder = esOrderModel.create();
    Boolean result = esOrderMapper.deleteById(esOrder.getOrderNumber()) > 0;
    if (!result) {
      log.warn("删除索引数据失效{}", JsonUtils.toJson(esOrder));
    }
    return result;
  }
}
