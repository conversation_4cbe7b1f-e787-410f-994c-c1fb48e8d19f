package com.yxt.order.atom.order_sync.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order_sync.entity.OrderSyncInitErrorLogDO;
import com.yxt.order.atom.order_sync.mapper.OrderSyncInitErrorLogMapper;
import com.yxt.order.atom.sdk.common.order_world.OrderSyncInitErrorLogDTO;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncInitErrorLogRemoveReq;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncInitErrorLogSearchReq;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@DS(DATA_SOURCE.ORDER_OFFLINE)
public class OrderSyncInitErrorLogService {

  @Autowired
  private OrderSyncInitErrorLogMapper orderSyncInitErrorLogMapper;

  /**
   * 查询业务单号同步记录
   *
   * @param request 查询条件
   * @return 同步记录列表
   */
  public List<OrderSyncInitErrorLogDTO> orderSyncInitErrorLogSearch(OrderSyncInitErrorLogSearchReq request) {
    // 构建查询条件
    LambdaQueryWrapper<OrderSyncInitErrorLogDO> queryWrapper = new LambdaQueryWrapper<>();

    // 根据请求参数添加查询条件
    if (StrUtil.isNotBlank(request.getBusinessNo())) {
      queryWrapper.eq(OrderSyncInitErrorLogDO::getBusinessNo, request.getBusinessNo());
    }

    if (StrUtil.isNotBlank(request.getFlashType())) {
      queryWrapper.eq(OrderSyncInitErrorLogDO::getFlashType, request.getFlashType());
    }

    // 按创建时间倒序排序
    queryWrapper.orderByDesc(OrderSyncInitErrorLogDO::getCreatedTime);

    // 查询数据库
    List<OrderSyncInitErrorLogDO> mappingList = orderSyncInitErrorLogMapper.selectList(queryWrapper);
    if (CollUtil.isEmpty(mappingList)) {
      return new ArrayList<>(0);
    }

    // 转换为DTO对象
    return BeanUtil.copyToList(mappingList, OrderSyncInitErrorLogDTO.class);
  }

  public void orderSyncInitErrorLogSaveOrUpdate(OrderSyncInitErrorLogDTO request) {

    OrderSyncInitErrorLogDO orderSyncInitErrorLogDO = BeanUtil.toBean(request, OrderSyncInitErrorLogDO.class);
    if (ObjectUtil.isNull(orderSyncInitErrorLogDO.getId())) {
      orderSyncInitErrorLogMapper.insert(orderSyncInitErrorLogDO);
    } else {
      orderSyncInitErrorLogMapper.updateById(orderSyncInitErrorLogDO);
    }
  }

  public void orderSyncInitErrorLogRemove(OrderSyncInitErrorLogRemoveReq request) {
    if(CollUtil.isEmpty(request.getLogIdList()) && CollUtil.isEmpty(request.getBizNoList())){
      return;
    }
    if(CollUtil.isNotEmpty(request.getLogIdList())){
      orderSyncInitErrorLogMapper.deleteBatchIds(request.getLogIdList());
    }
    if (CollUtil.isNotEmpty(request.getBizNoList())) {
      orderSyncInitErrorLogMapper.delete(Wrappers.<OrderSyncInitErrorLogDO>lambdaQuery()
          .in(OrderSyncInitErrorLogDO::getBusinessNo, request.getBizNoList())
          .eq(StrUtil.isNotBlank(request.getFlashType()), OrderSyncInitErrorLogDO::getFlashType, request.getFlashType()));
    }
  }
}
