package com.yxt.order.atom.order;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.member.api.MemberInfoApi;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.OfflineAlarmComponent;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.manage.OfflineOrderManageService;
import com.yxt.order.atom.order.converter.OfflineOrder2DOConverter;
import com.yxt.order.atom.order.converter.OfflineRefundOrderConverter;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderPayDO;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.order.repository.StageOrderService;
import com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomApi;
import com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomManageQueryApi;
import com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomQueryApi;
import com.yxt.order.atom.sdk.offline_order.dto.ExistOfflineOrderResDto;
import com.yxt.order.atom.sdk.offline_order.dto.ExistOfflineRefundOrderResDto;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineOrderByDateResDto;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineRefundOrderByDateResDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCouponDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailPickDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailTraceDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderMedInsSettleDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPrescriptionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPromotionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderStagingReqDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderUserDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailPickDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailTraceDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderMedInsSettleDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderUserDTO;
import com.yxt.order.atom.sdk.offline_order.req.CommonOfflineRefundQueryReqDto;
import com.yxt.order.atom.sdk.offline_order.req.CompensateHdMissPromotionCouponReqDto;
import com.yxt.order.atom.sdk.offline_order.req.CompensateRefundDataReqDto;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineOrderByDateCondition;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineOrderByDateReqDto;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineRefundOrderByDateReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoQryByScaleBatchReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundAmountReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundInfoQryBatchReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundInfoQryReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.SaveOfflineOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.SaveOfflineRefundOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.ShardingReqDto;
import com.yxt.order.atom.sdk.offline_order.req.UnionOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineOrderListReq;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineRefundOrderListReq;
import com.yxt.order.atom.sdk.offline_order.res.CommonOfflineRefundResDto;
import com.yxt.order.atom.sdk.offline_order.res.ExistOrderInfo;
import com.yxt.order.atom.sdk.offline_order.res.ExistRefundOrderInfo;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderInfoResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundAmountResDTO;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.ShardingResDto;
import com.yxt.order.atom.sdk.offline_order.res.UnionOrderResDto;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineOrderRes;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineRefundOrderRes;
import com.yxt.order.common.Utils;
import com.yxt.order.common.utils.WarnUtils;
import com.yxt.order.types.utils.ShardingHelper;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月01日 14:35
 * @email: <EMAIL>
 */
@RestController
@Slf4j
public class OfflineOrderController extends AbstractController implements OfflineOrderAtomApi,
    OfflineOrderAtomQueryApi , OfflineOrderAtomManageQueryApi {

  @Resource
  private StageOrderService stageOrderService;

  @Value("${dateDifferenceTooLargeWarn:1}")
  private Long dateDifferenceTooLargeWarn;

  @Value("${dateDifferenceTooLargeWarnOnOff:true}")
  private Boolean dateDifferenceTooLargeWarnOnOff;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Resource
  private MemberInfoApi memberInfoApi;

  @Resource
  private OfflineAlarmComponent offlineAlarmComponent;

  @Resource
  private OfflineOrderManageService offlineOrderManageService;


  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<Boolean> saveOfflineOrder(SaveOfflineOrderReqDto saveOfflineOrderReqDto) {
    OfflineOrderDTO offlineOrderDTO = saveOfflineOrderReqDto.getOfflineOrderDTO();

    // DTO
    OfflineOrderOrganizationDTO offlineOrderOrganizationDTO = saveOfflineOrderReqDto.getOfflineOrderOrganizationDTO();
    OfflineOrderCashierDeskDTO offlineOrderCashierDeskDTO = saveOfflineOrderReqDto.getOfflineOrderCashierDeskDTO();
    OfflineOrderUserDTO offlineOrderUserDTO = saveOfflineOrderReqDto.getOfflineOrderUserDTO();
    OfflineOrderPrescriptionDTO offlineOrderPrescriptionDTO = saveOfflineOrderReqDto.getOfflineOrderPrescriptionDTO();
    List<OfflineOrderPayDTO> offlineOrderPayDTOList = saveOfflineOrderReqDto.getOfflineOrderPayDTOList();
    List<OfflineOrderDetailDTO> offlineOrderDetailDTOList = saveOfflineOrderReqDto.getOfflineOrderDetailDTOList();
    OfflineOrderMedInsSettleDto medInsSettleDto = saveOfflineOrderReqDto.getOfflineOrderMedInsSettleDto();
    List<OfflineOrderCouponDTO> offlineOrderCouponDTOList = saveOfflineOrderReqDto.getOfflineOrderCouponDTOList();
    List<OfflineOrderPromotionDTO> offlineOrderPromotionDTOList = saveOfflineOrderReqDto.getOfflineOrderPromotionDTOList();
    Assert.isTrue(!CollectionUtils.isEmpty(offlineOrderDetailDTOList), "正单明细不能为空");

    alarm(offlineOrderDTO);

    // 转DO
    OfflineOrder2DOConverter instance = OfflineOrder2DOConverter.INSTANCE;
    OfflineOrderDO offlineOrderDO = instance.toDO(offlineOrderDTO);
    offlineOrderDO.setOrganizationDO(instance.toDO(offlineOrderOrganizationDTO));
    offlineOrderDO.setCashierDeskDO(instance.toDO(offlineOrderCashierDeskDTO));
    if (Objects.nonNull(offlineOrderUserDTO)) {
      offlineOrderDO.setUserDO(instance.toDO(offlineOrderUserDTO));
    }
    offlineOrderDO.setPrescriptionDO(instance.toDO(offlineOrderPrescriptionDTO));
    offlineOrderDO.setPayDOList(instance.toDO(offlineOrderPayDTOList));
    offlineOrderDO.setOrderMedInsSettleDO(instance.toDO(medInsSettleDto));
    offlineOrderDO.setOfflineOrderCouponDOList(instance.toOrderCouponDO(offlineOrderCouponDTOList));
    offlineOrderDO.setOfflineOrderPromotionDOList(
        instance.toOrderPromotionDO(offlineOrderPromotionDTOList));

    List<OfflineOrderDetailDO> offlineOrderDetailDOList = offlineOrderDetailDTOList.stream()
        .map(offlineOrderDetailDTO -> {
          OfflineOrderDetailDO offlineOrderDetailDO = instance.toDO(offlineOrderDetailDTO);

          // 拣货信息
          List<OfflineOrderDetailPickDTO> offlineOrderDetailPickDTOList = offlineOrderDetailDTO.getOfflineOrderDetailPickDTOList();
          if (!CollectionUtils.isEmpty(offlineOrderDetailPickDTOList)) {
            offlineOrderDetailDO.setDetailPickDOList(
                offlineOrderDetailPickDTOList.stream().map(instance::toDO)
                    .collect(Collectors.toList()));
          }

          // 追溯码信息
          List<OfflineOrderDetailTraceDTO> offlineOrderDetailTraceDTOList = offlineOrderDetailDTO.getOfflineOrderDetailTraceDTOList();
          if (!CollectionUtils.isEmpty(offlineOrderDetailTraceDTOList)) {
            offlineOrderDetailDO.setOfflineOrderDetailTraceDOList(
                offlineOrderDetailTraceDTOList.stream().map(instance::toDO)
                    .collect(Collectors.toList()));
          }

          return offlineOrderDetailDO;
        }).collect(Collectors.toList());
    offlineOrderDO.setDetailDOList(offlineOrderDetailDOList);
    offlineOrderRepository.saveOfflineOrderDO(offlineOrderDO);

    if (String.valueOf(Boolean.TRUE).equals(offlineOrderDO.getMigration())) {
      offlineOrderRepository.identifyMainOrder(offlineOrderDO);
    } else {
      offlineOrderRepository.updateOfflineParentOrderData(offlineOrderDO);
    }

    return generateSuccess(Boolean.TRUE);
  }

  public void alarm(OfflineOrderDTO offlineOrderDTO) {
    String content = String.format(
        "正单[%s],门店[%s],实际生单和实际同步到库时间相差过大,超过%s天",
        offlineOrderDTO.getOrderNo(), offlineOrderDTO.getStoreCode(), dateDifferenceTooLargeWarn);

    try {
      if (!dateDifferenceTooLargeWarnOnOff) {
        log.warn("实际生单和实际同步到库时间过大告警现在已关闭");
        return;
      }
      // 非迁移
      boolean nonMigration = !String.valueOf(Boolean.TRUE).equals(offlineOrderDTO.getMigration());
      if (nonMigration &&
          WarnUtils.dateDifferenceTooLargeWarn(offlineOrderDTO.getCreated(),
              offlineOrderDTO.getCreatedTime(), dateDifferenceTooLargeWarn)) {

        offlineAlarmComponent.alarm(content);
      }
    } catch (Exception e) {
      log.warn("实际生单和实际同步到库时间过大告警异常,{}", content);
    }
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<Boolean> saveOfflineRefundOrder(
      SaveOfflineRefundOrderReqDto saveOfflineRefundOrderReqDto) {
    OfflineRefundOrderDTO offlineRefundOrderDTO = saveOfflineRefundOrderReqDto.getOfflineRefundOrderDTO();
    List<OfflineRefundOrderDetailDTO> offlineRefundOrderDetailDTOList = saveOfflineRefundOrderReqDto.getOfflineRefundOrderDetailDTOList();
    OfflineRefundOrderMedInsSettleDto medInsSettleDto = saveOfflineRefundOrderReqDto.getOfflineRefundOrderMedInsSettleDto();
    OfflineRefundOrderUserDTO offlineRefundOrderUserDTO = saveOfflineRefundOrderReqDto.getOfflineRefundOrderUserDTO();
    OfflineRefundOrderOrganizationDTO offlineRefundOrderOrganizationDTO = saveOfflineRefundOrderReqDto.getOfflineRefundOrderOrganizationDTO();
    OfflineRefundOrderCashierDeskDTO offlineRefundOrderCashierDeskDTO = saveOfflineRefundOrderReqDto.getOfflineRefundOrderCashierDeskDTO();
    Assert.isTrue(!CollectionUtils.isEmpty(offlineRefundOrderDetailDTOList), "退款明细不能为空");

    List<OfflineRefundOrderPayDTO> offlineRefundOrderPayDTOList = saveOfflineRefundOrderReqDto.getOfflineRefundOrderPayDTOList();

    OfflineRefundOrderConverter instance = OfflineRefundOrderConverter.INSTANCE;
    OfflineRefundOrderDO refundOrderDO = instance.toDO(offlineRefundOrderDTO);

    // 处理明细
    List<OfflineRefundOrderDetailDO> refundDetailList =offlineRefundOrderDetailDTOList.stream().map(refundDetailDto ->{
      OfflineRefundOrderDetailDO refundDetail = instance.toDO(refundDetailDto);
      // 处理退单明细追溯码信息
      List<OfflineRefundOrderDetailTraceDTO> refundDetailTraceList = refundDetailDto.getOfflineRefundOrderDetailTraceDTOList();
      if(!CollectionUtils.isEmpty(refundDetailTraceList)){
        refundDetail.setOfflineRefundOrderDetailTraceDOList(instance.toDOList(refundDetailTraceList));
      }

      // 处理退单明细拣货信息
      List<OfflineRefundOrderDetailPickDTO> offlineRefundOrderDetailPickDTOList = refundDetailDto.getOfflineRefundOrderDetailPickDTOList();
      if(!CollectionUtils.isEmpty(offlineRefundOrderDetailPickDTOList)){
        refundDetail.setOfflineRefundOrderDetailPickDOList(instance.toDetailPickList(offlineRefundOrderDetailPickDTOList));
      }

      return refundDetail;
    }).collect(Collectors.toList());

    List<OfflineRefundOrderPayDO> refundPayList = instance.toRefundPayDO(
        offlineRefundOrderPayDTOList);
    refundOrderDO.setRefundOrderDetailDOList(refundDetailList);
    refundOrderDO.setRefundOrderPayDOList(refundPayList);
    refundOrderDO.setOfflineRefundOrderMedInsSettleDO(instance.toDO(medInsSettleDto));
    refundOrderDO.setOfflineRefundOrderUserDO(instance.toDO(offlineRefundOrderUserDTO));
    refundOrderDO.setOfflineRefundOrderOrganizationDO(
        instance.toDO(offlineRefundOrderOrganizationDTO));
    refundOrderDO.setOfflineRefundOrderCashierDeskDO(
        instance.toDO(offlineRefundOrderCashierDeskDTO));
    offlineOrderRepository.saveOfflineRefundOrderDO(refundOrderDO);
    offlineOrderRepository.updateOfflineParentRefundData(refundOrderDO);
    offlineOrderRepository.writeRefundPromotionCouponData(refundOrderDO);
    return generateSuccess(Boolean.TRUE);
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<Boolean> offlineOrderExists(OfflineOrderExistsReqDto reqDto) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(reqDto.getDefineNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      Boolean result = offlineOrderRepository.offlineThirdOrderExists(reqDto);
      return generateSuccess(result);
    }
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<ExistOrderInfo> offlineOrderExistsInfo(OfflineOrderExistsReqDto reqDto) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(reqDto.getDefineNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      ExistOrderInfo existOrderInfo = offlineOrderRepository.offlineThirdOrderExistsInfo(reqDto);
      return generateSuccess(existOrderInfo);
    }
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<Boolean> offlineRefundOrderExists(OfflineRefundOrderExistsReqDto reqDto) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(reqDto.getDefineNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      Boolean result = offlineOrderRepository.offlineThirdRefundOrderExists(reqDto);
      return generateSuccess(result);
    }
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<ExistRefundOrderInfo> offlineRefundOrderExistsInfo(
      OfflineRefundOrderExistsReqDto reqDto) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(reqDto.getDefineNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      ExistRefundOrderInfo existRefundOrderInfo = offlineOrderRepository.offlineThirdRefundOrderExistsInfo(
          reqDto);
      return generateSuccess(existRefundOrderInfo);
    }
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<OfflineOrderInfoResDto> getOfflineOrderInfo(
      OfflineOrderInfoReqDto offlineOrderInfoReqDto) {
    return generateSuccess(offlineOrderRepository.get(offlineOrderInfoReqDto));
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<OfflineRefundAmountResDTO> getOrderRefundAmount(
      OfflineRefundAmountReqDto offlineRefundAmountReqDto) {
    return generateSuccess(offlineOrderRepository.getOrderRefundAmount(offlineRefundAmountReqDto));
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<OfflineOrderDetailResDto> detail(OfflineOrderDetailReqDto detailReqDto) {
    return generateSuccess(offlineOrderRepository.detail(detailReqDto));
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<UnionOrderResDto> unionOrder(
      UnionOrderReqDto unionOrderReqDto) {
    return generateSuccess(offlineOrderRepository.unionOrder(unionOrderReqDto));
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<OfflineRefundOrderDetailResDto> refundDetail(
      OfflineRefundOrderDetailReqDto refundDetailReqDto) {
    return generateSuccess(offlineOrderRepository.refundDetail(refundDetailReqDto));
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<CommonOfflineRefundResDto> commonRefundInfo(
      CommonOfflineRefundQueryReqDto commonOfflineRefundQuery) {
    return generateSuccess(offlineOrderRepository.commonRefundInfo(commonOfflineRefundQuery));
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<GetOfflineOrderByDateResDto> orderData2MqQuery(
      GetOfflineOrderByDateReqDto reqDto) {
    valid(reqDto.getCondition());
    return generateSuccess(offlineOrderRepository.orderData2MqQuery(reqDto));
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<GetOfflineRefundOrderByDateResDto> refundData2MqQuery(
      GetOfflineRefundOrderByDateReqDto reqDto) {
    valid(reqDto.getCondition());
    return generateSuccess(offlineOrderRepository.refundData2MqQuery(reqDto));
  }

  private void valid(GetOfflineOrderByDateCondition condition) {
    if (!OrderDateUtils.theSameDate(condition.getStartDate(), condition.getEndDate())) {
      throw new RuntimeException("需传入同一天的日期");
    }
  }


  @Override
  public ResponseBase<ShardingResDto> shardingValue(ShardingReqDto shardingReq) {
    ShardingResDto shardingResDto = new ShardingResDto();
    String position = "";

    String orderNo = shardingReq.getOrderNo();
    if (!StringUtils.isEmpty(orderNo)) {
      position = ShardingHelper.getTableIndexByNo(orderNo);
    }

    String refundOrderNo = shardingReq.getRefundOrderNo();
    if (!StringUtils.isEmpty(refundOrderNo)) {
      position = ShardingHelper.getTableIndexByNo(refundOrderNo);
    }
    String userId = shardingReq.getUserId();
    if (!StringUtils.isEmpty(userId)) {
      position = ShardingHelper.getTableIndexByUserId(userId);
    }

    String userCardNo = shardingReq.getUserCardNo();
    if (!StringUtils.isEmpty(userCardNo)) {
      ResponseBase<MemberInfoVo> res = memberInfoApi.getMemberByCardNo(userCardNo);
      if ("E2011".equals(res.getCode()) || "会员不存在".equals(res.getMsg())) {
        throw new RuntimeException(String.format("会员%s不存在", userCardNo));
      }
      Utils.checkRespDataNull(res);
      position = ShardingHelper.getTableIndexByUserId(String.valueOf(res.getData().getUserId()));
    }

    shardingResDto.setPosition(position);
    return generateSuccess(shardingResDto);
  }

  @Override
  public ResponseBase<ExistOfflineOrderResDto> offlineOrderQueryByThirdOrderNo(
      OfflineOrderExistsReqDto reqDto) {
    ExistOfflineOrderResDto result = offlineOrderRepository.queryOfflineOrderByDto(reqDto);
    return generateSuccess(result);
  }

  @Override
  public ResponseBase<ExistOfflineRefundOrderResDto> offlineOrderQueryByThirdRefundNo(
      OfflineRefundOrderExistsReqDto reqDto) {
    ExistOfflineRefundOrderResDto result = offlineOrderRepository.queryOfflineRefundOrderByDto(
        reqDto);
    return generateSuccess(result);
  }

  @Override
  public ResponseBase<Boolean> stageOfflineOrder(OfflineOrderStagingReqDto dto) {
    Boolean result = stageOrderService.create(dto);
    return generateSuccess(result);
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<Boolean> compensateRefundOrderData(
      CompensateRefundDataReqDto compensateRefundDataReqDto) {
    OfflineRefundOrderDTO offlineRefundOrderDTO = compensateRefundDataReqDto.getOfflineRefundOrderDTO();
    OfflineRefundOrderOrganizationDTO refundOrderOrganizationDTO = compensateRefundDataReqDto.getOfflineRefundOrderOrganizationDTO();
    OfflineRefundOrderCashierDeskDTO refundOrderCashierDeskDTO = compensateRefundDataReqDto.getOfflineRefundOrderCashierDeskDTO();

    OfflineRefundOrderConverter instance = OfflineRefundOrderConverter.INSTANCE;
    OfflineRefundOrderOrganizationDO organizationDO = instance.toDO(refundOrderOrganizationDTO);
    OfflineRefundOrderCashierDeskDO cashierDeskDO = instance.toDO(refundOrderCashierDeskDTO);

    OfflineRefundOrderDO refundOrderDO = instance.toDO(offlineRefundOrderDTO);
    refundOrderDO.setOfflineRefundOrderOrganizationDO(organizationDO);
    refundOrderDO.setOfflineRefundOrderCashierDeskDO(cashierDeskDO);
    offlineOrderRepository.compensateRefundOrderData(refundOrderDO);
    return generateSuccess(true);
  }


  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ResponseBase<Boolean> compensateHdMissPromotionCouponData(CompensateHdMissPromotionCouponReqDto req) {
    Long compensateId = req.getCompensateId();
    // DTO
    OfflineOrderDTO offlineOrderDTO = req.getOfflineOrderDTO();
    List<OfflineOrderCouponDTO> offlineOrderCouponDTOList = req.getOfflineOrderCouponDTOList();
    List<OfflineOrderPromotionDTO> offlineOrderPromotionDTOList = req.getOfflineOrderPromotionDTOList();

    // 转DO
    OfflineOrder2DOConverter instance = OfflineOrder2DOConverter.INSTANCE;
    OfflineOrderDO offlineOrderDO = instance.toDO(offlineOrderDTO);
    offlineOrderDO.setOfflineOrderCouponDOList(instance.toOrderCouponDO(offlineOrderCouponDTOList));
    offlineOrderDO.setOfflineOrderPromotionDOList(instance.toOrderPromotionDO(offlineOrderPromotionDTOList));
    offlineOrderRepository.compensateHdMissPromotionCouponData(compensateId,offlineOrderDO);
    return generateSuccess(true);
  }


  @Override
  public ResponseBase<List<OfflineOrderDetailResDto>> getOrderInfoBatchByScale(OfflineOrderInfoQryByScaleBatchReqDto request) {
    return ResponseBase.success(offlineOrderRepository.getOrderInfoBatchByScale(request));
  }

  @Override
  public ResponseBase<OfflineOrderDetailResDto> getOrderInfoByScale(OfflineOrderInfoQryByScaleReqDto request) {
    return ResponseBase.success(offlineOrderRepository.getOrderInfoByScale(request));
  }

  @Override
  public ResponseBase<List<OfflineRefundOrderDetailResDto>> getRefundInfoBatchByScale(OfflineRefundInfoQryBatchReqDto request) {
    return ResponseBase.success(offlineOrderRepository.getRefundInfoBatchByScale(request));
  }

  @Override
  public ResponseBase<OfflineRefundOrderDetailResDto> getRefundInfoByScale(OfflineRefundInfoQryReqDto request) {
    return ResponseBase.success(offlineOrderRepository.getRefundInfoByScale(request));
  }

  @Override
  public ResponseBase<PageDTO<OfflineOrderRes>> offlineOrderList(OfflineOrderListReq req) {
    return ResponseBase.success(offlineOrderManageService.offlineOrderList(req));
  }

  @Override
  public ResponseBase<PageDTO<OfflineRefundOrderRes>> offlineRefundOrderList(
      OfflineRefundOrderListReq req) {
    return ResponseBase.success(offlineOrderManageService.offlineRefundOrderList(req));
  }


  // 手动创建索引
  @Override
  public Boolean createOfflineOrderManageIndex(){
    return offlineOrderManageService.createOfflineOrderManageIndex();
  }

  @Override
  public Boolean createOfflineRefundOrderManageIndex(){
    return offlineOrderManageService.createOfflineRefundOrderManageIndex();
  }


}
