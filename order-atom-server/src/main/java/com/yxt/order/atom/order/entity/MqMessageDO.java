package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("mq_message")
public class MqMessageDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  private String msgId;

  private String msg;

  private String mqMsgStatus;

  private String remark;

  private String msgType;

  private String createdBy;

  private String updatedBy;

  private Date createdTime;

  private Date updatedTime;

  private Long version;

}