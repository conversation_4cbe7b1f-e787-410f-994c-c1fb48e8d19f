
package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("org_order_flash_to_es_job")
public class OrgOrderFlashToEsJobDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 线上单：ONLINE 线下单：OFFLINE
     */
    private String orderSource;

    /**
     * 正单：ORDER 退款单：REFUND
     */
    private String orderType;

    /**
     * 刷数开始时间
     */
    private LocalDateTime flashStartTime;

    /**
     * 刷数结束时间
     */
    private LocalDateTime flashEndTime;

    /**
     * 上下文ID
     */
    private String contextId;

    /**
     * 执行状态 1 待执行 2 执行中 3 执行成功 4 执行失败
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

}
