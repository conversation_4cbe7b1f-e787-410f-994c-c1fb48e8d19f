//package com.yxt.order.atom.migration.fix.component;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.yxt.lang.util.JsonUtils;
//import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
//import com.yxt.order.atom.migration.fix.FlushOfflineRefundOrderCreatedScene;
//import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
//import com.yxt.order.atom.order.es.sync.AbstractFlash;
//import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
//import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
//import com.yxt.order.atom.order.repository.OfflineOrderRepository;
//import java.util.List;
//import java.util.Objects;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.dao.DuplicateKeyException;
//import org.springframework.stereotype.Component;
//
///**
// * 如果billTime != created ,使用billTime给createTime赋值
// *
// * @author: moatkon
// * @time: 2024/12/13 10:21
// */
//@Component
//@Slf4j
//@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
//@Deprecated
//public class FlushOfflineRefundOrderCreatedData extends
//    AbstractFlash<OfflineRefundOrderDO, OfflineRefundOrderDO, FlushOfflineRefundOrderCreatedScene> {
//
//  @Value("${keChuanTotalAmountDataGetLimit:2000}")
//  private Integer keChuanTotalAmountDataGetLimit;
//
//  @Resource
//  private OfflineRefundOrderMapper offlineRefundOrderMapper;
//
//  @Resource
//  private OfflineOrderRepository offlineOrderRepository;
//
//  @Override
//  protected Long queryCursorStartId() {
//    CustomData customData = getCustomData();
//    Long startId = customData.getStartId();
//    return Objects.nonNull(startId) ? startId
//        : offlineRefundOrderMapper.selectMinId(getFlashParam());
//  }
//
//  @Override
//  protected Long queryCursorEndId() {
//    CustomData customData = getCustomData();
//    Long endId = customData.getEndId();
//    return Objects.nonNull(endId) ? endId : offlineRefundOrderMapper.selectMaxId(getFlashParam());
//  }
//
//  @Override
//  protected List<OfflineRefundOrderDO> getSourceList() {
//    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(
//        getFlashParam(), defaultLimit());
//    return offlineRefundOrderMapper.selectList(query);
//  }
//
//  @Override
//  protected List<OfflineRefundOrderDO> assembleTargetData(
//      List<OfflineRefundOrderDO> offlineOrderDOList) {
//    return offlineOrderDOList;
//  }
//
//  /**
//   * 因为无输入逻辑,可以直接刷数
//   *
//   * @param offlineRefundOrderDOList
//   */
//  @Override
//  protected void flash(List<OfflineRefundOrderDO> offlineRefundOrderDOList) {
//    for (OfflineRefundOrderDO offlineRefundOrderDO : offlineRefundOrderDOList) {
//      String migration = offlineRefundOrderDO.getMigration();
//      if (StringUtils.isEmpty(migration)) {
//        continue;
//      }
//      if (!Boolean.TRUE.toString().equals(migration)) {
//        continue;
//      }
//
//      if (Objects.isNull(offlineRefundOrderDO.getId())) {
//        continue;
//      }
//
//      if (offlineRefundOrderDO.getBillTime().compareTo(offlineRefundOrderDO.getCreated()) != 0) {
//        try {
//          offlineRefundOrderDO.setCreated(offlineRefundOrderDO.getBillTime()); // 删除重复的是在内存中判断的,现在迁移的单子时间要持久化
//          offlineRefundOrderMapper.updateById(offlineRefundOrderDO);
//        } catch (Exception ignore) {
//          if(ignore instanceof DuplicateKeyException){
//            log.warn("FlushOfflineRefundOrderCreatedData uk重复,{}", JsonUtils.toJson(offlineRefundOrderDO));
//            offlineOrderRepository.deletedOfflineRefundOrder(offlineRefundOrderDO,
//               DELETED_MIGRATION_REPEATED, Boolean.TRUE);
//          }
//        }
//      }
//    }
//
//  }
//
//
//  @Override
//  protected Boolean isSharding() {
//    return Boolean.TRUE;
//  }
//
//  @Override
//  protected Boolean isHandleNonVipData() {
//    return Boolean.TRUE;
//  }
//
//  @Override
//  protected Integer defaultLimit() {
//    return keChuanTotalAmountDataGetLimit;
//  }
//}