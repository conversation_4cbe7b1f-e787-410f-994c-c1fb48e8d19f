package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.OrderPayInfoDO;
import com.yxt.order.atom.order.mapper.OrderPayInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.sdk.common.data.OrderPayInfoDTO;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 16:00
 * @email: <EMAIL>
 */
@Component
public class OrderPayInfoUpdate extends AbstractUpdate<OrderPayInfoDO> {

  @Resource
  private OrderPayInfoMapper orderPayInfoMapper;

  @Override
  protected Boolean canUpdate() {
    OrderPayInfoDTO dto = req.getOrderPayInfoDto();
    if (Objects.isNull(dto)) {
      return false;
    }
    Assert.isTrue(!StringUtils.isEmpty(dto.getId()) || !StringUtils.isEmpty(dto.getOrderNo()),
        "id or orderNo can not null");
    return Boolean.TRUE;
  }

  @Override
  protected Integer update(OrderPayInfoDO orderPayInfoDO) {
    if (Objects.nonNull(orderPayInfoDO.getId())) {
      return orderPayInfoMapper.updateById(orderPayInfoDO);
    } else {
      LambdaQueryWrapper<OrderPayInfoDO> wrapper = Wrappers.lambdaQuery();
      wrapper.eq(OrderPayInfoDO::getOrderNo, orderPayInfoDO.getOrderNo());
      return orderPayInfoMapper.update(orderPayInfoDO, wrapper);
    }
  }

  @Override
  protected OrderPayInfoDO convert() {
    return BeanUtil.copyProperties(req.getOrderPayInfoDto(),OrderPayInfoDO.class);
  }
}
