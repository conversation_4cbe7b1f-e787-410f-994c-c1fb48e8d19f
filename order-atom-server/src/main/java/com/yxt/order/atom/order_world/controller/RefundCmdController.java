package com.yxt.order.atom.order_world.controller;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order_world.service.RefundService;
import com.yxt.order.atom.sdk.order_world.OrderWorldRefundAtomCmdApi;
import com.yxt.order.atom.sdk.order_world.OrderWorldRefundAtomQueryApi;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldRefundBatchQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldRefundQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.SaveOrderWorldRefundOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderWorldRefundOptionalReq;
import com.yxt.order.atom.sdk.order_world.res.RefundRelatedInfoRes;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RefundCmdController implements OrderWorldRefundAtomCmdApi, OrderWorldRefundAtomQueryApi {

  @Autowired
  private RefundService refundService;

  @Override
  public ResponseBase<Void> saveOptional(SaveOrderWorldRefundOptionalReq req) {
    refundService.saveRefundOptional(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<Void> updateOptional(UpdateOrderWorldRefundOptionalReq req) {
    refundService.updateOptional(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<RefundRelatedInfoRes> getRefundInfoByScale(OrderWorldRefundQueryByScaleReq req) {
    return ResponseBase.success(refundService.getRefundInfoByScale(req));
  }

  @Override
  public ResponseBase<List<RefundRelatedInfoRes>> getRefundInfoBatchByScale(OrderWorldRefundBatchQueryByScaleReq req) {
    return ResponseBase.success(refundService.getRefundInfoBatchByScale(req));
  }

  @Override
  public ResponseBase<List<RefundRelatedInfoRes>> queryDBByAfterSaleNo(OrderWorldRefundBatchQueryByScaleReq req) {
    return ResponseBase.success(refundService.queryDBByAfterSaleNo(req));
  }
}
