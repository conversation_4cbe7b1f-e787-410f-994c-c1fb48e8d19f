package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@TableName("deleted_data_7")
public class DeletedDataDO implements Serializable {

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  private String businessNo;

  private String serviceName;

  private String databaseName;

  private String tableName;

  private Date createdTime;

  private String createdBy;

  /**
   * 已删除的数据
   */
  private String deletedData;

  /**
   * 删除原因
   */
  private String reason;
}