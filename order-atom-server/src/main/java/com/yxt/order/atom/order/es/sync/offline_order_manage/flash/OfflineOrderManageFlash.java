package com.yxt.order.atom.order.es.sync.offline_order_manage.flash;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.OfflineOrderManageScene;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.offline_order_manage.handler.OfflineOrderManageHandler;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/4/1 10:55
 */
@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class OfflineOrderManageFlash  extends
    AbstractFlash<OfflineOrderDO, OfflineOrder, OfflineOrderManageScene> {

  @Value("${memberTransactionFlashLimit:2000}")
  private Integer memberTransactionFlashLimit;

  @Resource
  private OfflineOrderManageHandler offlineOrderManageHandler;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Override
  protected Long queryCursorStartId() {
    return offlineOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineOrderDO> query = FlashQueryWrapper.offlineOrderFlashQuery(getFlashParam(),defaultLimit());
    return offlineOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineOrder> assembleTargetData(List<OfflineOrderDO> sourceList) {
    return sourceList.stream().map(DoToCanalDtoWrapper::getOfflineOrder).collect(Collectors.toList());
  }

  @Override
  protected void flash(List<OfflineOrder> offlineOrderList) {
    CanalOfflineOrder canalOfflineOrder = new CanalOfflineOrder();
    canalOfflineOrder.setData(offlineOrderList);
    offlineOrderManageHandler.manualFlash(canalOfflineOrder);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return memberTransactionFlashLimit;
  }
}
