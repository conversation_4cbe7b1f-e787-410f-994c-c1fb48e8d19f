package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 转b2c订单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("order_business_consumer_message")
public class OrderBusinessConsumerMessageDO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 平台类型
   */
  private String platformCode;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 消息类型  1-订单消息,2-订单取消消息,3-退款消息
   */
  private Integer messageType;

  /**
   * 1-订单消息:1待接单 2待拣货 3待配送 4配送中 5已完成 6订单取消 7订单退款 8待退货 9待退款 0其他;<br> 2-订单取消消息:1申请 2拒绝 3同意 0未知;<br>
   * 3-退款消息:1待处理  2已完成  3已拒绝  5已取消(包括用户主动取消申请) 7待退货  0其他状态<br>
   */
  private Integer status;

  /**
   * 消息内容
   */
  private String messageContent;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;

  public String uniqueKey() {
    String format = "%s_%s_%s_%s";
    return String.format(format, merCode, platformCode, thirdOrderNo, messageType);
  }
}
