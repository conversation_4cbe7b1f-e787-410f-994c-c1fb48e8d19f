package com.yxt.order.atom.order;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.CommodityRepository;
import com.yxt.order.atom.sdk.online_order.commodity.CommodityAtomQryApi;
import com.yxt.order.atom.sdk.online_order.commodity.req.CommodityStockQueryReq;
import com.yxt.order.atom.sdk.online_order.commodity.res.CommodityStockRes;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月01日 14:44
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class CommodityController extends AbstractController implements CommodityAtomQryApi {

  @Resource
  private CommodityRepository commodityRepository;

  @Override
  public ResponseBase<List<CommodityStockRes>> queryCommodityStock(CommodityStockQueryReq req) {
    return generateSuccess(commodityRepository.queryCommodityStock(req));
  }
  
}
