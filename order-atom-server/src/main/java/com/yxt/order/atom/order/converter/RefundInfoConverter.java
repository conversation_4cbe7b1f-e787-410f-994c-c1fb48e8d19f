package com.yxt.order.atom.order.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.sdk.common.data.ErpRefundInfoDTO;
import com.yxt.order.atom.sdk.common.data.RefundDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class RefundInfoConverter {


  public static RefundOrderDTO toRefundMainInfo(RefundOrderDO refundOrderDO) {
    if (refundOrderDO == null) {
      return null;
    }
    RefundOrderDTO refundMainInfo = new RefundOrderDTO();
    refundMainInfo.setId(refundOrderDO.getId());
    refundMainInfo.setThirdStatus(refundOrderDO.getThirdStatus());
    refundMainInfo.setClientCode(refundOrderDO.getClientCode());
    refundMainInfo.setOrganizationCode(refundOrderDO.getOrganizationCode());
    refundMainInfo.setOnlineStoreCode(refundOrderDO.getOnlineStoreCode());
    refundMainInfo.setSourceOrganizationCode(refundOrderDO.getSourceOrganizationCode());
    refundMainInfo.setSourceOnlineStoreCode(refundOrderDO.getSourceOnlineStoreCode());
    refundMainInfo.setServiceMode(refundOrderDO.getServiceMode());
    refundMainInfo.setTotalFoodAmount(refundOrderDO.getTotalFoodAmount());
    refundMainInfo.setTotalAmount(refundOrderDO.getTotalAmount());
    refundMainInfo.setConsumerRefund(refundOrderDO.getConsumerRefund());
    refundMainInfo.setShopRefund(refundOrderDO.getShopRefund());
    refundMainInfo.setFeeRefund(refundOrderDO.getFeeRefund());
    refundMainInfo.setPlatformDiscountRefund(refundOrderDO.getPlatformDiscountRefund());
    refundMainInfo.setShopDiscountRefund(refundOrderDO.getShopDiscountRefund());
    refundMainInfo.setCheckerId(refundOrderDO.getCheckerId());
    refundMainInfo.setReason(refundOrderDO.getReason());
    refundMainInfo.setDesc(refundOrderDO.getDesc());
    refundMainInfo.setCreateTime(refundOrderDO.getCreateTime());
    refundMainInfo.setModifyTime(refundOrderDO.getModifyTime());
    refundMainInfo.setPostageAmount(refundOrderDO.getPostageAmount());
    refundMainInfo.setUserPostage(refundOrderDO.getUserPostage());
    refundMainInfo.setPlatformPostage(refundOrderDO.getPlatformPostage());
    refundMainInfo.setPlatformRefundDeliveryFee(refundOrderDO.getPlatformRefundDeliveryFee());
    refundMainInfo.setMerchantRefundPostFee(refundOrderDO.getMerchantRefundPostFee());
    refundMainInfo.setPlatformRefundPackFee(refundOrderDO.getPlatformRefundPackFee());
    refundMainInfo.setMerchantRefundPackFee(refundOrderDO.getMerchantRefundPackFee());
    refundMainInfo.setDetailDiscountAmount(refundOrderDO.getDetailDiscountAmount());
    refundMainInfo.setErpRefundNo(refundOrderDO.getErpRefundNo());
    refundMainInfo.setBillTime(refundOrderDO.getBillTime());
    refundMainInfo.setCallErpFlag(refundOrderDO.getCallErpFlag());
    refundMainInfo.setCompleteTime(refundOrderDO.getCompleteTime());
    refundMainInfo.setHealthNum(refundOrderDO.getHealthNum());
    refundMainInfo.setReCalculateOriginOrderFlag(refundOrderDO.getReCalculateOriginOrderFlag());
    refundMainInfo.setAfterSaleGoodsTimeStart(refundOrderDO.getAfterSaleGoodsTimeStart());
    refundMainInfo.setAfterSaleGoodsTimeEnd(refundOrderDO.getAfterSaleGoodsTimeEnd());
    refundMainInfo.setExpressNo(refundOrderDO.getExpressNo());
    refundMainInfo.setExtraInfo(refundOrderDO.getExtraInfo());
    refundMainInfo.setHealthValue(refundOrderDO.getHealthValue());
    refundMainInfo.setLastApplyFlag(refundOrderDO.getLastApplyFlag());
    refundMainInfo.setEbaiCommissionFlag(refundOrderDO.getEbaiCommissionFlag());
    refundMainInfo.setMigrationRefundNo(refundOrderDO.getMigrationRefundNo());
    refundMainInfo.setExtendInfo(refundOrderDO.getExtendInfo());
    refundMainInfo.setRefundApplicationTime(refundOrderDO.getRefundApplicationTime());
    refundMainInfo.setDataVersion(refundOrderDO.getDataVersion());

    // refundNo
    refundMainInfo.setRefundNo(refundOrderDO.getRefundNo());
    // thirdRefundNo
    refundMainInfo.setThirdRefundNo(refundOrderDO.getThirdRefundNo());
    // orderNo
    refundMainInfo.setOrderNo(refundOrderDO.getOrderNo());
    // thirdOrderNo
    refundMainInfo.setThirdOrderNo(refundOrderDO.getThirdOrderNo());
    // type
    refundMainInfo.setType(refundOrderDO.getType());
    // state
    refundMainInfo.setState(refundOrderDO.getState());
    // thirdPlatformCode
    refundMainInfo.setThirdPlatformCode(refundOrderDO.getThirdPlatformCode());
    // merCode
    refundMainInfo.setMerCode(refundOrderDO.getMerCode());
    // erpState
    refundMainInfo.setErpState(refundOrderDO.getErpState());
    // billType
    refundMainInfo.setBillType(refundOrderDO.getBillType());
    // afterSaleType
    refundMainInfo.setAfterSaleType(refundOrderDO.getAfterSaleType());
    // refundType
    refundMainInfo.setRefundType(refundOrderDO.getRefundType());
    // omsOrderNo
    refundMainInfo.setOmsOrderNo(refundOrderDO.getOmsOrderNo());
    // dataVersion
    refundMainInfo.setThirdRefundNo(refundOrderDO.getThirdRefundNo());
    return refundMainInfo;
  }

  public static List<RefundDetailDTO> toRefundDetailInfo(List<RefundDetailDO> refundDetailDOList) {
    if (CollUtil.isEmpty(refundDetailDOList)) {
      return new ArrayList<>(0);
    }
    return refundDetailDOList.stream().map(refundDetailDO -> {
      RefundDetailDTO refundDetailData = new RefundDetailDTO();
      refundDetailData.setId(refundDetailDO.getId());
      refundDetailData.setErpCode(refundDetailDO.getErpCode());
      refundDetailData.setBarCode(refundDetailDO.getBarCode());
      refundDetailData.setThirdSkuId(refundDetailDO.getThirdSkuId());
      refundDetailData.setCommodityName(refundDetailDO.getCommodityName());
      refundDetailData.setMainPic(refundDetailDO.getMainPic());
      refundDetailData.setRefundCount(refundDetailDO.getRefundCount());
      refundDetailData.setPlatformRefundCount(refundDetailDO.getPlatformRefundCount());
      refundDetailData.setActualNetAmount(refundDetailDO.getActualNetAmount());
      refundDetailData.setBillPrice(refundDetailDO.getBillPrice());
      refundDetailData.setBuyerAmount(refundDetailDO.getBuyerAmount());
      refundDetailData.setMerchantAmount(refundDetailDO.getMerchantAmount());
      refundDetailData.setUnitRefundPrice(refundDetailDO.getUnitRefundPrice());
      refundDetailData.setOriginDetailPrice(refundDetailDO.getOriginDetailPrice());
      refundDetailData.setRefundDiscountAmount(refundDetailDO.getRefundDiscountAmount());
      refundDetailData.setStatus(refundDetailDO.getStatus());
      refundDetailData.setCreateTime(refundDetailDO.getCreateTime());
      refundDetailData.setModifyTime(refundDetailDO.getModifyTime());
      refundDetailData.setThirdDetailId(refundDetailDO.getThirdDetailId());
      refundDetailData.setCouponAmount(refundDetailDO.getCouponAmount());
      refundDetailData.setActivityDiscountAmont(refundDetailDO.getActivityDiscountAmont());
      refundDetailData.setHealthValue(refundDetailDO.getHealthValue());
      refundDetailData.setDetailDiscount(refundDetailDO.getDetailDiscount());
      refundDetailData.setShareAmount(refundDetailDO.getShareAmount());
      refundDetailData.setOrderDetailId(refundDetailDO.getOrderDetailId());
      refundDetailData.setRefundNo(refundDetailDO.getRefundNo());
      refundDetailData.setThirdOrderNo(refundDetailDO.getThirdOrderNo());
      refundDetailData.setThirdRefundNo(refundDetailDO.getThirdRefundNo());
      return refundDetailData;
    }).collect(Collectors.toList());
  }

  public static ErpRefundInfoDTO toErpRefundInfo(ErpRefundInfoDO erpRefundInfoDO) {
    if (ObjectUtil.isNull(erpRefundInfoDO)) {
      return null;
    }
    ErpRefundInfoDTO erpRefundInfoDTO = new ErpRefundInfoDTO();
    erpRefundInfoDTO.setId(erpRefundInfoDO.getId());
    erpRefundInfoDTO.setClientConfId(erpRefundInfoDO.getClientConfId());
    erpRefundInfoDTO.setRefundMerchantTotal(erpRefundInfoDO.getRefundMerchantTotal());
    erpRefundInfoDTO.setApportionAmount(erpRefundInfoDO.getApportionAmount());
    erpRefundInfoDTO.setRefundGoodsTotal(erpRefundInfoDO.getRefundGoodsTotal());
    erpRefundInfoDTO.setBrokerageAmount(erpRefundInfoDO.getBrokerageAmount());
    erpRefundInfoDTO.setPlatformDiscount(erpRefundInfoDO.getPlatformDiscount());
    erpRefundInfoDTO.setMerchantDiscount(erpRefundInfoDO.getMerchantDiscount());
    erpRefundInfoDTO.setPlatformRefundDeliveryFee(erpRefundInfoDO.getPlatformRefundDeliveryFee());
    erpRefundInfoDTO.setRefundPostFee(erpRefundInfoDO.getRefundPostFee());
    erpRefundInfoDTO.setPlatformRefundPackFee(erpRefundInfoDO.getPlatformRefundPackFee());
    erpRefundInfoDTO.setPackageFee(erpRefundInfoDO.getPackageFee());
    erpRefundInfoDTO.setDiscountAmount(erpRefundInfoDO.getDiscountAmount());
    erpRefundInfoDTO.setCreateTime(erpRefundInfoDO.getCreateTime());
    erpRefundInfoDTO.setModifyTime(erpRefundInfoDO.getModifyTime());
    erpRefundInfoDTO.setOrderNo(erpRefundInfoDO.getOrderNo());
    erpRefundInfoDTO.setRefundNo(erpRefundInfoDO.getRefundNo());
    return erpRefundInfoDTO;
  }

  public static List<RefundOrderDTO> toRefundInfoList(List<RefundOrderDO> refundOrderList) {
    if(CollUtil.isEmpty(refundOrderList)){
      return new ArrayList<>(0);
    }
    return refundOrderList.stream().map(RefundInfoConverter::toRefundMainInfo).collect(Collectors.toList());
  }
}
