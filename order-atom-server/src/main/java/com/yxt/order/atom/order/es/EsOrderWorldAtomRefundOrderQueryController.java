package com.yxt.order.atom.order.es;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.common.utils.ESSearchUtils;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrder;
import com.yxt.order.atom.order.es.doc.EsOrderWorldRefundOrder;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldRefundOrderMapper;
import com.yxt.order.atom.order.es.wrapper.EsQueryBuilder;
import com.yxt.order.atom.sdk.order_world.EsOrderWorldAtomRefundOrderQueryApi;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldRefundOrderBaseReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldRefundOrderPageQueryReq;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldOrderInfoRes;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldRefundOrderInfoRes;
import com.yxt.order.atom.sdk.order_world.res.EsOrderWorldRefundOrderInfoRes;
import com.yxt.order.common.es.EsPageDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.biz.PageSerializable;
import org.dromara.easyes.core.biz.SAPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class EsOrderWorldAtomRefundOrderQueryController implements EsOrderWorldAtomRefundOrderQueryApi {


  @Resource
  private EsOrderWorldRefundOrderMapper esOrderWorldRefundOrderMapper;


  @Override
  public ResponseBase<EsPageDTO<EsOrderWorldRefundOrderInfoRes>> refundOrderPageQuery(EsOrderWorldRefundOrderPageQueryReq request) {
    EsPageDTO<EsOrderWorldRefundOrderInfoRes> resultPage = new EsPageDTO<>();
    resultPage.setCurrentPage(request.getCurrentPage());
    resultPage.setPageSize(request.getPageSize());
    LambdaEsQueryWrapper<EsOrderWorldRefundOrder> wrapper = EsQueryBuilder.buildEsQueryForRefundOrderPageQuery(request);
    PageSerializable<EsOrderWorldRefundOrder> page = null;
    if (StrUtil.isNotBlank(request.getSearchAfter())) {
      List<Object> searchAfter = ESSearchUtils.parseSearchAfter(request.getSearchAfter());
      SAPageInfo<EsOrderWorldRefundOrder> searchAfterPage = esOrderWorldRefundOrderMapper.searchAfterPage(wrapper, searchAfter, request.getPageSize()
          .intValue());
      resultPage.setTotalPage((long) searchAfterPage.getPages());
      resultPage.setTotalCount(searchAfterPage.getTotal());
      if (CollUtil.isNotEmpty(searchAfterPage.getNextSearchAfter()) && searchAfterPage.getList()
          .size() >= request.getPageSize()) {
        resultPage.setSearchAfter(JSON.toJSONString(searchAfterPage.getNextSearchAfter()));
      }
      page = searchAfterPage;
    } else {
      EsPageInfo<EsOrderWorldRefundOrder> esPageInfo = esOrderWorldRefundOrderMapper.pageQuery(wrapper, request.getCurrentPage()
          .intValue(), request.getPageSize().intValue());
      resultPage.setTotalPage((long) esPageInfo.getPages());
      resultPage.setTotalCount(esPageInfo.getTotal());
      page = esPageInfo;
    }

    if (page.getTotal() <= 0) {
      resultPage.setData(new ArrayList<>(0));
      return ResponseBase.success(resultPage);
    }
    List<EsOrderWorldRefundOrderInfoRes> resultList = page.getList().stream().map(data -> {
      EsOrderWorldRefundOrderInfoRes refundOrderResDTO = BeanUtil.toBean(data, EsOrderWorldRefundOrderInfoRes.class);
      if (StrUtil.isNotBlank(data.getRefundFlags())) {
        refundOrderResDTO.setRefundFlags(StrUtil.split(data.getRefundFlags(), " "));
      }
      return refundOrderResDTO;
    }).collect(Collectors.toList());
    resultPage.setData(resultList);
    return ResponseBase.success(resultPage);
  }

  @Override
  public ResponseBase<EsOrderWorldRefundOrderInfoRes> refundOrderDetailQuery(EsOrderWorldRefundOrderBaseReq request) {
    LambdaEsQueryWrapper<EsOrderWorldRefundOrder> wrapper = EsWrappers.lambdaQuery(EsOrderWorldRefundOrder.class);
    wrapper.eq(EsOrderWorldRefundOrder::getRefundNo, request.getRefundNo());
    EsOrderWorldRefundOrder esData = esOrderWorldRefundOrderMapper.selectOne(wrapper);
    if(ObjectUtil.isNull(esData)){
      return ResponseBase.success(null);
    }
    EsOrderWorldRefundOrderInfoRes result = BeanUtil.toBean(esData, EsOrderWorldRefundOrderInfoRes.class);
    if (StrUtil.isNotBlank(esData.getRefundFlags())) {
      result.setRefundFlags(StrUtil.split(esData.getRefundFlags(), " "));
    }
    return ResponseBase.success(result);
  }
}
