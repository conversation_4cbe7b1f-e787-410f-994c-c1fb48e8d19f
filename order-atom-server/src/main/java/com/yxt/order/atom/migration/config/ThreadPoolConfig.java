package com.yxt.order.atom.migration.config;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/18 11:03
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "threadpool")
public class ThreadPoolConfig {

  public final static String MIGRATION_THREAD_POOL = "migrationThreadPool";
  public final static String MIGRATION_MQ_Consumer_POOL = "migrationMqConsumerPool";
  public final static String MIGRATION_MQ_ReConsumer_POOL = "migrationMqReConsumerPool";
  public final static String ALERT_THREAD_POOL = "alertThreadPool";
  public final static String COMMON_BUSINESS_POOL = "commonBusinessPool";
  // 延迟线程池
  public final static String SCHEDULED_THREAD_POOL = "scheduledThreadPool";
  public final static String ORDER_BATCH_SEARCH_POOL = "orderBatchSearchPool";
  public final static String ORDER_BATCH_SEARCH_SUB_POOL = "orderBatchSearchSubPool";
  public final static String ORDER_REFRESH_ES_POOL = "orderRefreshEsPool";
  public final static String ORDER_SYNC_TO_ES_POOL = "orderSyncToEsPool";


  private Integer queryCoreSize = 8;
  private Integer queryMaxSize = 48;
  private Long queryKeepAliveTime = 3000L;
  private Integer queryCapacity = 128;


  private Integer migrationMqConsumerCoreSize = 8;
  private Integer migrationMqConsumerMaxSize = 48;
  private Long migrationMqConsumerKeepAliveTime = 3000L;
  private Integer migrationMqConsumerCapacity = 128;


  private Integer migrationMqReConsumerCoreSize = 8;
  private Integer migrationMqReConsumerMaxSize = 48;
  private Long migrationMqReConsumerKeepAliveTime = 3000L;
  private Integer migrationMqReConsumerCapacity = 128;


  private Integer alertThreadPoolConsumerCoreSize = 8;
  private Integer alertThreadPoolConsumerMaxSize = 48;
  private Long alertThreadPoolConsumerKeepAliveTime = 3000L;
  private Integer alertThreadPoolConsumerCapacity = 128;


  private Integer commonBusinessPoolConsumerCoreSize = 8;
  private Integer commonBusinessPoolConsumerMaxSize = 48;
  private Long commonBusinessPoolConsumerKeepAliveTime = 3000L;
  private Integer commonBusinessPoolConsumerCapacity = 128;

  private Integer orderBatchSearchCoreSize = 16;
  private Integer orderBatchSearchMaxSize = 48;
  private Long orderBatchSearchKeepAliveTime = 3000L;
  private Integer orderBatchSearchCapacity = 512;

  private Integer orderSyncToEsCoreSize = 16;
  private Integer orderSyncToEsMaxSize = 48;
  private Long orderSyncToEsKeepAliveTime = 3000L;
  private Integer orderSyncToEsCapacity = 512;

  private Integer orderRefreshEsCoreSize = 6;
  private Integer orderRefreshEsMaxSize = 32;
  private Long orderRefreshEsKeepAliveTime = 3000L;
  private Integer orderRefreshEsCapacity = 128;


  /**
   * 查询并行线程池
   *
   * @return
   */
  @Bean(MIGRATION_THREAD_POOL)
  public Executor migrationThreadPool() {
    return new ThreadPoolExecutor(
        queryCoreSize,
        queryMaxSize,
        queryKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(queryCapacity),
        new ThreadFactoryBuilder().setNamePrefix(MIGRATION_THREAD_POOL).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
  }

  @Bean(MIGRATION_MQ_Consumer_POOL)
  public Executor migrationMqConsumerPool() {
    return new ThreadPoolExecutor(
        migrationMqConsumerCoreSize,
        migrationMqConsumerMaxSize,
        migrationMqConsumerKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(migrationMqConsumerCapacity),
        new ThreadFactoryBuilder().setNamePrefix(MIGRATION_MQ_Consumer_POOL).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
  }

  @Bean(MIGRATION_MQ_ReConsumer_POOL)
  public Executor migrationMqReConsumerPool() {
    return new ThreadPoolExecutor(
        migrationMqReConsumerCoreSize,
        migrationMqReConsumerMaxSize,
        migrationMqReConsumerKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(queryCapacity),
        new ThreadFactoryBuilder().setNamePrefix(MIGRATION_MQ_ReConsumer_POOL).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
  }

  @Bean(ALERT_THREAD_POOL)
  public Executor alertThreadPool() {
    return new ThreadPoolExecutor(
        alertThreadPoolConsumerCoreSize,
        alertThreadPoolConsumerMaxSize,
        alertThreadPoolConsumerKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(queryCapacity),
        new ThreadFactoryBuilder().setNamePrefix(ALERT_THREAD_POOL).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
  }

  @Bean(COMMON_BUSINESS_POOL)
  public Executor commonBusinessPool() {
    return new ThreadPoolExecutor(
        commonBusinessPoolConsumerCoreSize,
        commonBusinessPoolConsumerMaxSize,
        commonBusinessPoolConsumerKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(queryCapacity),
        new ThreadFactoryBuilder().setNamePrefix(ALERT_THREAD_POOL).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
  }



  @Bean(SCHEDULED_THREAD_POOL)
  public ScheduledExecutorService scheduledThreadPool() {
    return new ScheduledThreadPoolExecutor(
        10,
        new ThreadFactoryBuilder().setNamePrefix(SCHEDULED_THREAD_POOL).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
  }

  @Bean(ORDER_BATCH_SEARCH_POOL)
  public Executor orderBatchSearchPool() {
    return new ThreadPoolExecutor(
        orderBatchSearchCoreSize,
        orderBatchSearchMaxSize,
        orderBatchSearchKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(orderBatchSearchCapacity),
        new ThreadFactoryBuilder().setNamePrefix(ORDER_BATCH_SEARCH_POOL).build(),
        new ThreadPoolExecutor.AbortPolicy()
    );
  }

  @Bean(ORDER_BATCH_SEARCH_SUB_POOL)
  public Executor orderBatchSearchSubPool() {
    return new ThreadPoolExecutor(
        orderBatchSearchCoreSize,
        orderBatchSearchMaxSize,
        orderBatchSearchKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(orderBatchSearchCapacity),
        new ThreadFactoryBuilder().setNamePrefix(ORDER_BATCH_SEARCH_SUB_POOL).build(),
        new ThreadPoolExecutor.AbortPolicy()
    );
  }

  @Bean(ORDER_SYNC_TO_ES_POOL)
  public Executor orderSyncToEsPool() {
    return new ThreadPoolExecutor(
        orderSyncToEsCoreSize,
        orderSyncToEsMaxSize,
        orderSyncToEsKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(orderSyncToEsCapacity),
        new ThreadFactoryBuilder().setNamePrefix(ORDER_SYNC_TO_ES_POOL).build(),
        new ThreadPoolExecutor.AbortPolicy()
    );
  }

  @Bean(ORDER_REFRESH_ES_POOL)
  public Executor orderRefreshEsPool() {
    return new ThreadPoolExecutor(
        orderRefreshEsCoreSize,
        orderRefreshEsMaxSize,
        orderRefreshEsKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(orderRefreshEsCapacity),
        new ThreadFactoryBuilder().setNamePrefix(ORDER_REFRESH_ES_POOL).build(),
        new ThreadPoolExecutor.AbortPolicy()
    );
  }
}
