package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrderWorldOrder.RefundInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CanalRefundOrderWorldOrder extends BaseCanalData<RefundInfo> {

  @Data
  public static class RefundInfo {

    @JsonProperty("refund_no")
    private String refundNo;

    @JsonProperty("third_platform_code")
    private String thirdPlatformCode;

    @JsonProperty("business_type")
    private String businessType;

    @JsonProperty("transaction_channel")
    private String transactionChannel;

    private Boolean needRoute = true;
  }

}
