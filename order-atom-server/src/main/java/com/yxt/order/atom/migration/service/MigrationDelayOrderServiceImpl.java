package com.yxt.order.atom.migration.service;

import com.yxt.order.atom.migration.dao.mongo.MigrationDelayMapping;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月15日 18:12
 * @email: <EMAIL>
 */
@Service
@Slf4j
public class MigrationDelayOrderServiceImpl implements MigrationDelayOrderService {

  @Resource
  private MongoTemplate mongoTemplate;

  @Override
  public void create(MigrationDelayMapping migrationDelayMapping) {
    mongoTemplate.insert(migrationDelayMapping, MigrationDelayMapping.COLLECTION_NAME);
  }

  @Override
  public MigrationDelayMapping isParentThirdOrderNo(String thirdOrderNo, String storeCode,
      String thirdPlatformCode) {

    Query query = new Query(Criteria.where("parentThirdOrderNo").is(thirdOrderNo)
        .and("thirdPlatformCode").is(thirdPlatformCode)
        .and("storeCode").is(storeCode));

    List<MigrationDelayMapping> migrationDelayMappings = mongoTemplate.find(query,
        MigrationDelayMapping.class, MigrationDelayMapping.COLLECTION_NAME);

    if (CollectionUtils.isEmpty(migrationDelayMappings)) {
      return null;
    }

    if (migrationDelayMappings.size() != 1) {
      log.warn("isParentThirdOrderNo数据重复,size:{},{},{},{}", migrationDelayMappings.size(),
          thirdOrderNo, storeCode, thirdPlatformCode);
      return null;
    }

    return migrationDelayMappings.get(0);
  }
}
