package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 配送状态日志
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-18
 */
@Data
@TableName("order_delivery_log")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderDeliveryLogDO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 记录id
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 系统订单号
   */
  private Long orderNo;

  /**
   * 状态，包括1待接单，2待取货，3配送中，4已签收, 5已取消，6已过期，7异常
   */
  private Integer state;

  /**
   * 详细配送方式名称
   */
  private String deliveryPlatName;

  /**
   * 配送员id
   */
  private String riderName;

  /**
   * 配送员手机
   */
  private String riderPhone;

  /**
   * 描述
   */
  private String description;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;


}
