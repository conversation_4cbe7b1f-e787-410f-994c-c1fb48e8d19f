package com.yxt.order.atom.order.es.sync.member_transaction.utils;

import com.yxt.order.types.offline.enums.StoreDirectJoinTypeEnum;
import org.springframework.util.StringUtils;

/**
 * 线上订单店铺类型工具类
 *
 * @author: moatkon
 * @time: 2024/12/10 14:54
 */
public class OnlineOrderStoreTypeUtils {

  /**
   * 6 加盟 ; 4 直营
   */
  public static StoreDirectJoinTypeEnum storeType(String storeCode) {
    if (StringUtils.isEmpty(storeCode)) {
      return StoreDirectJoinTypeEnum.UNKNOWN;
    }
    int length = storeCode.length();
    if (length == 4) {
      return StoreDirectJoinTypeEnum.DIRECT_SALES;
    } else if (length == 6) {
      return StoreDirectJoinTypeEnum.JOIN;
    } else {
      return StoreDirectJoinTypeEnum.UNKNOWN;
    }
  }

}
