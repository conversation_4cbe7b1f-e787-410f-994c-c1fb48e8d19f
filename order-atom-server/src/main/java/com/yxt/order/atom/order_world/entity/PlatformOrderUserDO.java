package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 订单用户信息
 */
@Data
@TableName("platform_order_user")
public class PlatformOrderUserDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 三方平台
   */
  private String thirdPlatformCode;

  /**
   * 会员id
   */
  private String userId;

  /**
   * 发起人id，目前仅B2B有值
   */
  private String launchUserId;

  /**
   * 会员名称
   */
  private String userName;

  /**
   * 会员标记
   */
  private String userTag;

  /**
   * 会员卡号
   */
  private String userCardNo;

  /**
   * 会员手机号
   */
  private String userMobile;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

}
