package com.yxt.order.atom.migration.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.repair.dto.StartEndId;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:40
 * @email: <EMAIL>
 */
@DS(DATA_SOURCE.ORDER_OFFLINE)
@Mapper
public interface HanaMigrationErrorDOMapper extends BaseMapper<HanaMigrationErrorDO> {

  @Select("select IFNULL(min(id),0) as startId,IFNULL(max(id),0) as endId from hana_migration_error where entry_retry_queue = 'false'")
  StartEndId selectMaxMinId();

}
