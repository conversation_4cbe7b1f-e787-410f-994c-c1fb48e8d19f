package com.yxt.order.atom.migration.fix.component.es;

import com.yxt.order.atom.migration.fix.dto.RepeatedEsDataOperate;
import com.yxt.order.atom.order.es.doc.EsMemberOrder;
import com.yxt.order.atom.order.es.doc.EsMemberRefundOrder;
import com.yxt.order.atom.order.es.doc.EsOrder;
import com.yxt.order.atom.order.es.doc.EsOrgOrder;
import com.yxt.order.atom.order.es.doc.EsOrgRefund;
import com.yxt.order.atom.order.es.mapper.EsMemberOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsMemberRefundOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsOrgOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsOrgRefundMapper;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/4/7 10:48
 */
@Component
public class EsDataOperateComponent {


  /**
   * 慢病
   */
  @Resource
  private EsOrderMapper esOrderMapper;

  /**
   * 会员消费记录
   */
  @Resource
  private EsMemberOrderMapper esMemberOrderMapper;

  @Resource
  private EsMemberRefundOrderMapper esMemberRefundOrderMapper;

  @Resource
  private EsOrgOrderMapper esOrgOrderMapper;

  @Resource
  private EsOrgRefundMapper esOrgRefundMapper;


  public void removeEs(RepeatedEsDataOperate repeatedEsDataOperate) {
    boolean hasUserId = StringUtils.isNotEmpty(repeatedEsDataOperate.getUserId());

    if (hasUserId) {
      // 删除慢病失效索引
      LambdaEsQueryWrapper<EsOrder> deleteEsOrderQuery = new LambdaEsQueryWrapper<>();
      deleteEsOrderQuery.eq(EsOrder::getOrderNumber, repeatedEsDataOperate.esOrderId());
      esOrderMapper.delete(deleteEsOrderQuery);
    }

    common(repeatedEsDataOperate, hasUserId);
  }


  public void removeEsEsUnhandled(RepeatedEsDataOperate repeatedEsDataOperate) {
    boolean hasUserId = StringUtils.isNotEmpty(repeatedEsDataOperate.getUserId());
//   慢病的不用处理,因为是根据单号作为唯一Id的,不是根据平台编码来的
//    if (hasUserId) {
//      // 删除慢病失效索引
//      LambdaEsQueryWrapper<EsOrder> deleteEsOrderQuery = new LambdaEsQueryWrapper<>();
//      deleteEsOrderQuery.eq(EsOrder::getOrderNumber, repeatedEsDataOperate.esOrderId());
//      esOrderMapper.delete(deleteEsOrderQuery);
//    }

    common(repeatedEsDataOperate, hasUserId);
  }

  private void common(RepeatedEsDataOperate repeatedEsDataOperate, boolean hasUserId) {
    if (repeatedEsDataOperate.offlineOrderTable()) {
      if (hasUserId) {
        // 删除会员消费记录-正单ES
        LambdaEsQueryWrapper<EsMemberOrder> deleteEsMemberOrderQuery = new LambdaEsQueryWrapper<>();
        deleteEsMemberOrderQuery.routing(repeatedEsDataOperate.esMemberOrderIdRouting());
        deleteEsMemberOrderQuery.eq(EsMemberOrder::getId,
            repeatedEsDataOperate.esMemberOrderId());
        esMemberOrderMapper.delete(deleteEsMemberOrderQuery);
      }

      // 删除门店订单-正单ES
      LambdaEsQueryWrapper<EsOrgOrder> deleteEsOrgOrderQuery = new LambdaEsQueryWrapper<>();
      deleteEsOrgOrderQuery.routing(repeatedEsDataOperate.orgOrderIdRouting());
      deleteEsOrgOrderQuery.eq(EsOrgOrder::getId, repeatedEsDataOperate.orgOrderId());
      esOrgOrderMapper.delete(deleteEsOrgOrderQuery);

    }

    if (repeatedEsDataOperate.offlineRefundOrderTable()) {

      if (hasUserId) {
        // 删除会员消费记录-退单ES
        LambdaEsQueryWrapper<EsMemberRefundOrder> deleteEsMemberRefundOrderQuery = new LambdaEsQueryWrapper<>();
        deleteEsMemberRefundOrderQuery.routing(
            repeatedEsDataOperate.esMemberRefundOrderIdRouting());
        deleteEsMemberRefundOrderQuery.eq(EsMemberRefundOrder::getId,
            repeatedEsDataOperate.esMemberRefundOrderId());
        esMemberRefundOrderMapper.delete(deleteEsMemberRefundOrderQuery);
      }

      // 删除门店订单-退单ES
      LambdaEsQueryWrapper<EsOrgRefund> deleteEsOrgRefundQuery = new LambdaEsQueryWrapper<>();
      deleteEsOrgRefundQuery.routing(repeatedEsDataOperate.orgRefundOrderIdRouting());
      deleteEsOrgRefundQuery.eq(EsOrgRefund::getId, repeatedEsDataOperate.orgRefundOrderId());
      esOrgRefundMapper.delete(deleteEsOrgRefundQuery);
    }
  }
}
