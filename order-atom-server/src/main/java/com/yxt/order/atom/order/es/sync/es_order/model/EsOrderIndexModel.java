package com.yxt.order.atom.order.es.sync.es_order.model;

import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsOrder;
import com.yxt.order.atom.order.es.doc.EsOrderItem;
import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * 所有操作通过模型来操作,隔绝索引对象
 *
 * <AUTHOR> (moatkon)
 * @date 2024年08月07日 17:59
 * @email: <EMAIL>
 */
@Data
public class EsOrderIndexModel extends BaseEsIndexModel {

  // pk
  private String orderNumber;  //对应的订单号或退单号

  private String orderNo;
  private String refundNo;

  private String onlineStoreCode; // 网点code,线下单没有网点
  private String organizationCode; // 网点单对应的履约门店,会员慢病用这个
  private String userId;  //会员id 可以查会员卡号
  private EsOrderType esOrderType;  //订单类型，正单/退单
  private EsServiceMode serviceMode; // O2O,B2C,POS
  private EsOrderStatus esOrderStatus;
  private Date payTime;  //支付时间
  private Date completeTime; // 完成时间
  private String thirdOrderNo;
  private String thirdRefundNo;
  private String platformCode;
  // 交易日期==订单的创建时间
  private Date createTime;

  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  private Long deleted;



  private List<EsOrderItemModel> esOrderItemModelList;

  @Data
  public static class EsOrderItemModel {

    private String commodityCode;//	商品编码
    private String commodityName;//商品名称
    private BigDecimal commodityCount;  //商品数量
    private String fiveClass; // 五级分类
  }

  /**
   * 新建索引订单数据
   *
   * @return
   */
  public EsOrder create() {
    Assert.isTrue(Objects.nonNull(this.getOrderNumber()), "orderNumber不能为空");
    Assert.isTrue(Objects.nonNull(this.getPlatformCode()), "platformCode不能为空");
//    Assert.isTrue(Objects.nonNull(this.getOnlineStoreCode()), "onlineStoreCode不能为空");
//    Assert.isTrue(Objects.nonNull(this.getThirdOrderNo()), "thirdOrderNo不能为空");// 线下单退单可能没有

    Assert.isTrue(Objects.nonNull(this.getEsOrderType()), "esOrderType不能为空");
    Assert.isTrue(Objects.nonNull(this.getServiceMode()), "serviceMode不能为空");
    Assert.isTrue(Objects.nonNull(this.getCreateTime()), "createTime不能为空");
    Assert.isTrue(Objects.nonNull(this.getEsOrderStatus()), "esOrderStatus不能为空");
    Assert.isTrue(!CollectionUtils.isEmpty(this.getEsOrderItemModelList()), "明细不能为空");

    EsOrder esOrder = new EsOrder();
    esOrder.setOnlineStoreCode(this.getOnlineStoreCode());
    esOrder.setOrganizationCode(this.getOrganizationCode());
    esOrder.setUserId(this.getUserId());
    esOrder.setEsOrderType(this.getEsOrderType());
    esOrder.setOrderNumber(this.getOrderNumber());
    if (EsOrderType.ORDER.equals(this.getEsOrderType())) {
      esOrder.setOrderNo(this.getOrderNumber());
    }
    if (EsOrderType.REFUND.equals(this.getEsOrderType())) {
      esOrder.setRefundNo(this.getOrderNumber());
      esOrder.setOrderNo(this.getOrderNo());
    }
    esOrder.setServiceMode(this.getServiceMode());
    esOrder.setEsOrderStatus(this.getEsOrderStatus());
    esOrder.setPayTime(this.getPayTime());
    esOrder.setCompleteTime(this.getCompleteTime());
    esOrder.setThirdOrderNo(this.getThirdOrderNo());
    esOrder.setThirdRefundNo(this.getThirdRefundNo());
    esOrder.setPlatformCode(this.getPlatformCode());
    esOrder.setCreateTime(this.getCreateTime());

    List<EsOrderItem> esOrderItemList = this.getEsOrderItemModelList().stream()
        .map(item -> {
          EsOrderItem esItem = new EsOrderItem();
          esItem.setCommodityCode(item.getCommodityCode());
          esItem.setCommodityName(item.getCommodityName());
          esItem.setCommodityCount(item.getCommodityCount());
          esItem.setFiveClass(item.getFiveClass());
          return esItem;
        }).collect(Collectors.toList());

    esOrder.setEsOrderItemList(esOrderItemList);
    return esOrder;
  }

}
