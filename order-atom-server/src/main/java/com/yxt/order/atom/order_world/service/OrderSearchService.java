package com.yxt.order.atom.order_world.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.sharding.YxtOrderSharding;
import com.yxt.order.atom.log.service.BizLogService;
import com.yxt.order.atom.order_world.entity.ExtendDataDO;
import com.yxt.order.atom.order_world.entity.OrderAmountDO;
import com.yxt.order.atom.order_world.entity.OrderCouponDO;
import com.yxt.order.atom.order_world.entity.OrderDeliveryAddressDO;
import com.yxt.order.atom.order_world.entity.OrderDetailCouponDO;
import com.yxt.order.atom.order_world.entity.OrderDetailDO;
import com.yxt.order.atom.order_world.entity.OrderDetailPromotionDO;
import com.yxt.order.atom.order_world.entity.OrderPayDO;
import com.yxt.order.atom.order_world.entity.OrderPrescriptionDO;
import com.yxt.order.atom.order_world.entity.OrderPromotionDO;
import com.yxt.order.atom.order_world.entity.OrderSetDetailDO;
import com.yxt.order.atom.order_world.entity.OrderUserInfoDO;
import com.yxt.order.atom.order_world.mapper.ExtendDataMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderAmountMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderCouponMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderDetailCouponMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderDetailPromotionMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderPrescriptionMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderPromotionMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderUserInfoMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderSetDetailMapper;
import com.yxt.order.atom.order_world.repository.NewOrderDetailBatchRepository;
import com.yxt.order.atom.order_world.repository.NewOrderPayBatchRepository;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogReq;
import com.yxt.order.atom.sdk.common.order_world.BizLogInfoDTO;
import com.yxt.order.common.order_world_dto.enums.ExtendDataTypeEnum;
import java.util.List;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class OrderSearchService {

  @Autowired
  private NewOrderAmountMapper orderAmountMapper;
  @Autowired
  private NewOrderDetailBatchRepository orderDetailBatchRepository;
  @Autowired
  private NewOrderDeliveryAddressMapper orderDeliveryAddressMapper;
  @Autowired
  private NewOrderPayBatchRepository orderPayBatchRepository;
  @Autowired
  private NewOrderUserInfoMapper orderUserInfoMapper;
  @Autowired
  private ExtendDataMapper extendDataMapper;
  @Autowired
  private BizLogService bizLogService;
  @Autowired
  private NewOrderSetDetailMapper orderSetDetailMapper;
  @Autowired
  private NewOrderPrescriptionMapper orderPrescriptionMapper;
  @Autowired
  private NewOrderCouponMapper orderCouponMapper;
  @Autowired
  private NewOrderPromotionMapper orderPromotionMapper;
  @Autowired
  private NewOrderDetailCouponMapper orderDetailCouponMapper;
  @Autowired
  private NewOrderDetailPromotionMapper orderDetailPromotionMapper;

  public OrderDeliveryAddressDO getOrderDeliveryAddress(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderDeliveryAddressMapper.selectOne(Wrappers.<OrderDeliveryAddressDO>lambdaQuery()
          .eq(OrderDeliveryAddressDO::getOrderNo, orderNo).last(" limit 1 "));
    }
  }

  public OrderUserInfoDO getOrderUserInfo(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderUserInfoMapper.selectOne(Wrappers.<OrderUserInfoDO>lambdaQuery()
          .eq(OrderUserInfoDO::getOrderNo, orderNo).last(" limit 1 "));
    }
  }

  public OrderAmountDO getOrderAmount(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderAmountMapper.selectOne(Wrappers.<OrderAmountDO>lambdaQuery()
          .eq(OrderAmountDO::getOrderNo, orderNo).last(" limit 1 "));
    }
  }

  public List<OrderPayDO> getOrderPayList(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderPayBatchRepository.list(Wrappers.<OrderPayDO>lambdaQuery()
          .eq(OrderPayDO::getOrderNo, orderNo));
    }
  }

  public List<OrderDetailDO> getOrderDetailList(String orderNo) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderDetailBatchRepository.list(Wrappers.<OrderDetailDO>lambdaQuery()
          .eq(OrderDetailDO::getOrderNo, orderNo));
    }
  }


  public ExtendDataDO getOrderExt(String orderNo) {

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return extendDataMapper.selectOne(Wrappers.<ExtendDataDO>lambdaQuery()
          .eq(ExtendDataDO::getBusinessNo, orderNo)
          .eq(ExtendDataDO::getDataType, ExtendDataTypeEnum.ORDER_EXTEND.getCode())
          .last(" limit 1 "));
    }
  }


  public List<BizLogInfoDTO> getOrderBizLog(String orderNo) {
    SearchBizLogReq req = new SearchBizLogReq();
    req.setBizNo(orderNo);
    return bizLogService.searchLog(req);
  }

  public List<ExtendDataDO> getOrderDetailExtList(String orderNo, List<String> orderDetailOrderNoList) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return extendDataMapper.selectList(Wrappers.<ExtendDataDO>lambdaQuery()
          .eq(ExtendDataDO::getBusinessNo, orderNo)
          .eq(ExtendDataDO::getDataType, ExtendDataTypeEnum.ORDER_DETAIL_EXTEND.getCode())
          .in(ExtendDataDO::getSubBusinessNo, orderDetailOrderNoList)
      );
    }

  }

  @YxtOrderSharding(shardingNo = "#orderNo")
  public List<OrderSetDetailDO> getOrderSetDetailList(String orderNo) {
    return orderSetDetailMapper.selectList(Wrappers.<OrderSetDetailDO>lambdaQuery()
        .eq(OrderSetDetailDO::getOrderNo, orderNo));
  }

  @YxtOrderSharding(shardingNo = "#orderNo")
  public List<OrderPrescriptionDO> getOrderPrescriptionList(String orderNo) {
    return orderPrescriptionMapper.selectList(Wrappers.<OrderPrescriptionDO>lambdaQuery()
        .eq(OrderPrescriptionDO::getOrderNo, orderNo));
  }

  @YxtOrderSharding(shardingNo = "#orderNo")
  public List<OrderCouponDO> getOrderCouponList(String orderNo) {
    return orderCouponMapper.selectList(Wrappers.<OrderCouponDO>lambdaQuery()
        .eq(OrderCouponDO::getOrderNo, orderNo));
  }

  @YxtOrderSharding(shardingNo = "#orderNo")
  public List<OrderPromotionDO> getOrderPromotionList(String orderNo) {
    return orderPromotionMapper.selectList(Wrappers.<OrderPromotionDO>lambdaQuery()
        .eq(OrderPromotionDO::getOrderNo, orderNo));
  }

  @YxtOrderSharding(shardingNo = "#orderNo")
  public List<OrderDetailCouponDO> getOrderDetailCouponList(String orderNo) {
    return orderDetailCouponMapper.selectList(Wrappers.<OrderDetailCouponDO>lambdaQuery()
        .eq(OrderDetailCouponDO::getOrderNo, orderNo));
  }

  @YxtOrderSharding(shardingNo = "#orderNo")
  public List<OrderDetailPromotionDO> getOrderDetailPromotionList(String orderNo) {
    return orderDetailPromotionMapper.selectList(Wrappers.<OrderDetailPromotionDO>lambdaQuery()
        .eq(OrderDetailPromotionDO::getOrderNo, orderNo));
  }
}
