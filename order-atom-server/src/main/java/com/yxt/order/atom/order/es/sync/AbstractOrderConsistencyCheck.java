package com.yxt.order.atom.order.es.sync;

import static com.yxt.order.atom.common.utils.OrderDateUtils.formatYYMMDD;

import com.alibaba.fastjson.JSONObject;
import com.yxt.common.logic.consistency.AbstractConsistencyCheck;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.OfflineAlarmComponent;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.utils.ShardingUtils;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;

/**
 * 抽象一致性检查
 *
 * @author: moatkon
 * @time: 2024/12/24 15:09
 */
@Slf4j
public abstract class AbstractOrderConsistencyCheck extends AbstractConsistencyCheck {
  @Resource
  private OfflineAlarmComponent offlineAlarmComponent;

  protected Date getStartDate(){
    return getParam().getStartDate();
  }
  protected Date getEndDate(){
    return getParam().getEndDate();
  }

  protected FlashParam getFlashParam(){
    return getParam().getFlashParam();
  }


  @Override
  protected Long dbTotalCount() {
    return dbDscloudCount() + dbOfflineAllShardingCount();
  }

  /**
   * dscloud库总数查找
   *
   * @return
   */
  protected Long dbDscloudCount(){
    return 0L;
  }

  /**
   * dscloud_offline库总数查找
   *
   * @return
   */
  protected Long dbDscloudOfflineCount(){
    return 0L;
  }

  /**
   * 是否处理非会员数据
   *
   * @return
   */
  protected Boolean isHandleNonVipData() {
    return Boolean.FALSE;
  }


  /**
   * dscloud_offline库总数查找
   * <p>
   * 私有
   *
   * @return
   */
  private Long dbOfflineAllShardingCount() {
    Long shardingTotal = 0L;
    List<String> shardingValueList = ShardingUtils.shardingValueList(isHandleNonVipData());
    for (String shardingValue : shardingValueList) {
      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        hit.setQueryHit(QueryHit.builder().seq(shardingValue).build());
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
        Long shardingCount = dbDscloudOfflineCount();
        log.info("dbOfflineAllShardingCount {}-{}", shardingValue, shardingCount);
        shardingTotal = shardingTotal + shardingCount;
      }
    }
    return shardingTotal;
  }

  /**
   * 一致性告警
   * @return
   */
  protected abstract ConsistencyNotify consistencyNotify();


  @Override
  protected void consistencyAlarm(Long dbTotalCount, Long esTotalCount, String tips) {
    JSONObject req = new JSONObject();
    req.put("startDate", formatYYMMDD(getStartDate()));
    req.put("endDate", formatYYMMDD(getEndDate()));
    ConsistencyNotify consistencyNotify = consistencyNotify();
    String content = String.format("[%s]-参数:%s,数据库总条数:%s,ES总条数:%s。tips:%s", consistencyNotify.domain,
        JsonUtils.toJson(req), dbTotalCount, esTotalCount, tips);
    log.info(content);
    offlineAlarmComponent.esOrderMemberTransactionRecordAlert(content,consistencyNotify.domain);
  }

  public enum ConsistencyNotify {
    MEMBER_ORDER("会员消费记录-正单"),
    MEMBER_REFUND_ORDER("会员消费记录-退单"),

    ORG_ORDER("门店订单记录-正单"),
    ORG_REFUND("门店退单记录-退单"),

    OMS_ORDER_INFO("B2C订单"),

    ORDER_WORLD_ORDER("订单新模型-正单"),

    ;
    private final String domain; // 业务域


    ConsistencyNotify(String domain) {
      this.domain = domain;
    }
  }
}
