package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName("offline_refund_order")
public class RefundOrderDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 退单对应的内部订单号(自己生成)
   */
  private String orderNo;

  /**
   * 内部退款单号,自己生成
   */
  private String refundNo;

  /**
   * 内部父退款单号
   */
  private String parentRefundNo;

  /**
   * 会员id
   */
  private String userId;

  /**
   * 平台编码,HAIDIAN,内部定义
   */
  private String thirdPlatformCode;

  /**
   * 第三方平台退款单号
   */
  private String thirdRefundNo;

  /**
   * 第三方平台退款单号(主)
   */
  private String parentThirdRefundNo;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 退款类型 PART-部分退款 ALL-全额退款
   */
  private String refundType;

  /**
   * 售后单类型 AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后
   */
  private String afterSaleType;

  /**
   * 退款原因
   */
  private String reason;

  /**
   * 退单创单时间
   */
  private Date created;

  /**
   * 退单申请时间
   */
  private Date applyTime;

  /**
   * 退单完成时间
   */
  private Date completeTime;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 售后单号
   */
  private String afterSaleNo;

  /**
   * 交易场景 online:代表线上交易 ,offline:代表线下交易
   */
  private String transactionChannel;

  /**
   * 业务类型 O2O、B2C、B2B
   */
  private String businessType;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 所属机构名称
   */
  private String organizationName;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  private String launchOrganizationCode;

  /**
   * 发起方所属机构名称,仅B2B场景有值
   */
  private String launchOrganizationName;

  /**
   * 发起人id，目前仅B2B有值
   */
  private String launchUserId;

  /**
   * 退单状态  待审核:WAIT_VERIFY  审核驳回 :REVIEW_REJECT 待退款: WAIT_REFUND 已退款: REFUNDED 
   */
  private String refundStatus;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 来源业务线 如POS,ASSIST
   */
  private String sourceBizCode;

  /**
   * 来源场景
   */
  private String sourceScene;

  /**
   * 来源渠道,待产品定义
   */
  private String sourceChannel;

  /**
   * 来源端 如 PC,APP,POS
   */
  private String sourceDevice;

  /**
   * 是否起效 1-起效 -1-未起效
   */
  private Long isValid;

  /**
   * 平台创建日
   */
  private String createdDay;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

}
