package com.yxt.order.atom.log;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.log.service.BizLogService;
import com.yxt.order.atom.sdk.biz_log.BizLogAtomApi;
import com.yxt.order.atom.sdk.biz_log.req.SaveBizLogReq;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogBatchReq;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogReq;
import com.yxt.order.atom.sdk.common.order_world.BizLogInfoDTO;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class BizLogController implements BizLogAtomApi {

  @Autowired
  private BizLogService bizLogService;

  @PostMapping(ORDER_ENDPOINT + "/log/index/create")
  public ResponseBase<Boolean> createLogIndex() {
    return ResponseBase.success(bizLogService.createLogIndex());
  }

  @Override
  public ResponseBase<Void> saveLog(@RequestBody SaveBizLogReq req) {
    bizLogService.saveLog(req);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<List<BizLogInfoDTO>> searchLog(@RequestBody SearchBizLogReq req) {
    return ResponseBase.success(bizLogService.searchLog(req));
  }

  @Override
  public ResponseBase<List<BizLogInfoDTO>> searchLogBatch(@RequestBody SearchBizLogBatchReq req) {
    return ResponseBase.success(bizLogService.searchLogBatch(req));
  }

}
