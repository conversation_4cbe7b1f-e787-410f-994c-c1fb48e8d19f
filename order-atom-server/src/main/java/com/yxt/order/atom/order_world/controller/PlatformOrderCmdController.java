package com.yxt.order.atom.order_world.controller;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order_world.service.OrderService;
import com.yxt.order.atom.order_world.service.PlatformOrderService;
import com.yxt.order.atom.sdk.order_world.OrderWorldOrderAtomCmdApi;
import com.yxt.order.atom.sdk.order_world.PlatformOrderAtomCmdApi;
import com.yxt.order.atom.sdk.order_world.req.SavePlatformOrderOptionalReq;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class PlatformOrderCmdController implements PlatformOrderAtomCmdApi {

  @Resource
  private PlatformOrderService platformOrderService;

  @Override
  public ResponseBase<Void> saveOptional(SavePlatformOrderOptionalReq request) {
    platformOrderService.saveOptional(request);
    return ResponseBase.success();
  }
}
