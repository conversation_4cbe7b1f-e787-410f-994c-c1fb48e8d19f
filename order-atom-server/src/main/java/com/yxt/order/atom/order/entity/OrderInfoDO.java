package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 基本订单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
@Data
@TableName("order_info")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class OrderInfoDO implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 订单号，雪花算法
   */
  @JsonSerialize(using = ToStringSerializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING)
  @ApiModelProperty(value = "订单号")
  private Long orderNo;

  /**
   * 订单状态
   */
  @ApiModelProperty(value = "订单状态:5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭")
  private Integer orderState;

  /**
   * erp状态
   */
  @ApiModelProperty(value = "下账状态: 20 待锁库存 30 待下帐  100 已下账  110 已取消")
  private Integer erpState;

  /**
   * 平台code
   */
  private String thirdPlatformCode;

  /**
   * 第三方平台订单号
   */
  @ApiModelProperty(value = "第三方平台订单号")
  private String thirdOrderNo;

  /**
   * 第三方平台订单号, 消息通知时使用
   */
  @ApiModelProperty(value = "第三方平台订单id，消息通知时使用")
  private String thirdOrderId;

  /**
   * 第三方平台订单状态
   */
  @ApiModelProperty(value = "第三方平台订单状态")
  private String thirdOrderState;

  /**
   * 接口中台的订单状态
   */
  @ApiModelProperty(value = "第三方平台订单状态")
  private Integer offState;

  /**
   * 运费订单编号
   */
  @ApiModelProperty(value = "运费订单号")
  @JsonSerialize(using = ToStringSerializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING)
  private Long freightOrderNo;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 网店编码
   */
  private String clientCode;

  /**
   * 下单线上门店code
   */
  @ApiModelProperty(value = "下单线上门店code")
  private String onlineStoreCode;

  /**
   * 线上门店名称
   */
  private String onlineStoreName;

  /**
   * 线下门店编码
   */
  @ApiModelProperty(value = "线下门店编码")
  private String organizationCode;

  /**
   * 线下门店名称
   */
  @ApiModelProperty(value = "线下门店名称")
  private String organizationName;

  /**
   * 送达方式，0即时或1预约
   */
  @ApiModelProperty(value = "送达方式，0即时或1预约")
  private Integer deliveryTimeType;

  /**
   * 送达时间描述
   */
  @ApiModelProperty(value = "送达时间描述")
  private String deliveryTimeDesc;

  /**
   * 买家备注
   */
  @ApiModelProperty(value = "买家备注")
  private String buyerRemark;

  /**
   * 买家留言
   */
  @ApiModelProperty(value = "买家留言")
  private String buyerMessage;

  /**
   * 卖家备注
   */
  @ApiModelProperty(value = "卖家备注")
  private String sellerRemark;

  /**
   * 锁定标志,0未锁定
   */
  @ApiModelProperty(value = "锁定标志: 0未锁定,10取消锁定,20退款锁定,31库存不足,32部分商定不存在,33金额异常,34配送异常")
  private Integer lockFlag;

  /**
   * 锁定信息
   */
  @ApiModelProperty(value = "异常信息")
  private String lockMsg;

  /**
   * 锁定者id
   */
  private String lockerId;


  /**
   * 催单标志,0未催，1已催促
   */
  @ApiModelProperty(value = "催单标志,0未催，1已催促")
  private Integer remindFlag;

  /**
   * 三方平台买家昵称
   */
  @ApiModelProperty(value = "三方平台买家昵称")
  private String buyerName;

  /**
   * 收货地址纬度
   */
  @ApiModelProperty(value = "收货地址纬度")
  private String receiverLat;


  /**
   * 收货地址经度
   */
  @ApiModelProperty(value = "收货地址经度")
  private String receiverLng;

  /**
   * 接单员id
   */
  @ApiModelProperty(value = "接单员id")
  private String acceptorId;

  @ApiModelProperty(value = "接单员名")
  private String acceptorName;
  /**
   * 接单时间
   */
  @ApiModelProperty(value = "接单时间")
  private Date acceptTime;

  /**
   * 拣货操作者id
   */
  @ApiModelProperty(value = "拣货操作者id")
  private String pickerId;

  @ApiModelProperty(value = "拣货操作者名")
  private String pickerName;

  /**
   * 拣货实施者id
   */
  @ApiModelProperty(value = "拣货实施者id")
  private String pickOperatorId;

  @ApiModelProperty(value = "拣货实施者名")
  private String pickOperatorName;

  /**
   * 拣货时间
   */
  @ApiModelProperty(value = "拣货时间")
  private Date pickTime;

  /**
   * 取消者id
   */
  @ApiModelProperty(value = "取消者id")
  private String cancellerId;


  @ApiModelProperty(value = "取消者名")
  private String cancellerName;

  @ApiModelProperty(value = "取消原因")
  private String cancelReason;

  /**
   * 取消时间
   */
  @ApiModelProperty(value = "取消时间")
  private Date cancelTime;

  @ApiModelProperty(value = "异常操作人id")
  private String exOperatorId;


  @ApiModelProperty(value = "异常操作人名")
  private String exOperatorName;

  @ApiModelProperty(value = "异常操作时间")
  private Date exOperatorTime;

  /**
   * 完成时间
   */
  @ApiModelProperty(value = "完成时间")
  private Date completeTime;

  /**
   * 订单在第三方的创建时间
   */
  @ApiModelProperty(value = "订单在第三方的创建时间")
  private Date created;

  @ApiModelProperty(value = "每日号")
  private String dayNum;
  /**
   * 订单在第三方的修改时间
   */
  @ApiModelProperty(value = "订单在第三方的修改时间")
  private Date modified;

  /**
   * erp 货位调整单号
   */
  @ApiModelProperty(value = "erp 货位调整单号")
  private String erpAdjustNo;

  /**
   * erp 零售流水
   */
  @ApiModelProperty(value = "erp 零售流水")
  private String erpSaleNo;

  @ApiModelProperty(value = "自提取货码")
  private String selfVerifyCode;

  @ApiModelProperty(value = "是否审方单 1：审方单  0：非审方单")
  private Integer prescriptionFlag;


  @ApiModelProperty("下账时间")
  private Date billTime;


  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;

  @ApiModelProperty(value = "是否调用ERP接口标识（0不调用 1调用）")
  private Integer callErpFlag;

  /**
   * 会员编号
   */
  private String memberNo;

  @ApiModelProperty(value = "转仓发货标识（0未转仓 1已转仓）")
  private Integer transferDelivery;

  @ApiModelProperty(value = "网店配置ID")
  private Long clientConfId;

  @ApiModelProperty(value = "下账操作人")
  private String billOperator;

  @ApiModelProperty(value = "0-正常订单，1-预约订单")
  private Integer appointment;

  @ApiModelProperty(value = "预约单业务处理状态 0-待处理 1-已处理")
  private Integer appointmentBusinessFlag;

  @ApiModelProperty(value = "预约单业务处理类型 0-修改门店 1-确认到货 2-供应商代发")
  private Integer appointmentBusinessType;
  @ApiModelProperty(value = "申请代发服务商处理结果,1-发货，2-拒单")
  private Integer requestDeliverGoodsResult;
  @ApiModelProperty(value = "代发拒单理由")
  private String deliverGoodsRefuseReason;


  @ApiModelProperty(value = "是否审方单  0-不是  1-是 ")
  private Integer isPrescription;

  @ApiModelProperty(value = "处方状态 0-待审  1-通过  2-不通过  3-取消")
  private Integer prescriptionStatus;

  @ApiModelProperty(value = "是否推送审方平台")
  private Integer isPushCheck;

  @ApiModelProperty(value = "新客标识，0：非新客，1：新客")
  private String newCustomerFlag;

  @ApiModelProperty(value = "积分标识，0：非积分订单，1：积分订单")
  private String integralFlag;

  @ApiModelProperty(value = "是否开票，1.开发票；2.不开发票")
  private String needInvoice;

  @ApiModelProperty(value = "发票抬头")
  private String invoiceTitle;

  @ApiModelProperty(value = "发票抬头类型 0：个人、1：企业普票、2：企业专票")
  private String invoiceType;

  @ApiModelProperty(value = "发票内容")
  private String invoiceContent;

  @ApiModelProperty(value = "纳税人识别码")
  private String taxerId;

  /**
   * 下单线上门店code-来源
   */
  @ApiModelProperty(value = "下单线上门店code")
  private String sourceOnlineStoreCode;

  /**
   * 线上门店名称-来源
   */
  private String sourceOnlineStoreName;

  /**
   * 线下门店编码-来源
   */
  @ApiModelProperty(value = "线下门店编码")
  private String sourceOrganizationCode;

  /**
   * 线下门店名称-来源
   */
  @ApiModelProperty(value = "线下门店名称")
  private String sourceOrganizationName;

  /**
   * 复杂换货标识，0未参与复杂换货，1已参与复杂换货 参与过复杂换货后，部分退款需要人工录入退款单
   */
  @ApiModelProperty(value = "复杂换货标识")
  private Integer complexModifyFlag;

  @ApiModelProperty(value = "0-非医保订单,1-医保订单")
  private Integer medicalInsurance;

  @ApiModelProperty(value = "服务模式 O2O B2C")
  private String serviceMode;

  /**
   * 已下账后取消下账次数
   */
  private Integer cancelBillTimes;

  @ApiModelProperty(value = "微商城额外信息")
  private String wscExtJson;

  @ApiModelProperty(value = "TOP拦截标识，0不拦截，1拦截 null=平台无此标识")
  private String topHold;

  /**
   * OrderTypeEnum
   */
  @ApiModelProperty(value = "订单类型: 30-机器自动拣货单")
  private Integer orderType;

  @ApiModelProperty(value = "是否新订单: 1-新订单,2老订单")
  private String orderIsNew;

  @ApiModelProperty(value = "订单实际拣货类型 1-人工拣货 2-机器自动拣货")
  private Integer orderPickType;

  @ApiModelProperty(value = "来源渠道类型 1-京东渠道")
  private Integer sourceChannelType;

  @ApiModelProperty(value = "平台迁移订单号-旧系统迁移数据的订单号")
  private String migrationOrderNo;

  @ApiModelProperty(value = "扩展信息Json See OrderInfoExtendInfo")
  private String extendInfo;

  @ApiModelProperty(value = "数据版本号")
  private Long dataVersion;

  @ApiModelProperty(value = "系统备注")
  private String remark;

  @ApiModelProperty("逻辑删除字段 默认0-未删除")
  private Long deleted;

  private Date payTime;


  /**
   * 是否修改门店
   *
   * @return
   */
  public boolean isUpdateStore() {
    if (StringUtils.isBlank(organizationCode) || StringUtils.isBlank(sourceOrganizationCode)) {
      return false;
    }
    return !this.organizationCode.equals(this.sourceOrganizationCode);
  }

}
