package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.migration.config.ThreadPoolConfig;
import com.yxt.order.atom.order.converter.OnlineOrderConverter;
import com.yxt.order.atom.order.converter.OrderDetailConverter;
import com.yxt.order.atom.order.converter.OrderInfoConverter;
import com.yxt.order.atom.order.converter.OrderPickInfo2DtoConverter;
import com.yxt.order.atom.order.entity.CommodityExceptionOrderDO;
import com.yxt.order.atom.order.entity.CommodityStockDO;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.entity.OrderAssembleCommodityRelationDO;
import com.yxt.order.atom.order.entity.OrderBusinessConsumerMessageDO;
import com.yxt.order.atom.order.entity.OrderCommodityDetailCostPriceDO;
import com.yxt.order.atom.order.entity.OrderCouponInfoDO;
import com.yxt.order.atom.order.entity.OrderDeliveryAddressDO;
import com.yxt.order.atom.order.entity.OrderDeliveryLogDO;
import com.yxt.order.atom.order.entity.OrderDeliveryRecordDO;
import com.yxt.order.atom.order.entity.OrderDetailCommodityCostPriceDO;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderGiftInfoDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.OrderMultiPayInfoDO;
import com.yxt.order.atom.order.entity.OrderPayInfoDO;
import com.yxt.order.atom.order.entity.OrderPickInfoDO;
import com.yxt.order.atom.order.entity.OrderPrescriptionDO;
import com.yxt.order.atom.order.entity.OriThirdOrderDetailDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.mapper.ErpBillInfoMapper;
import com.yxt.order.atom.order.entity.AfterSaleOrderDO;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.mapper.AfterSaleOrderMapper;
import com.yxt.order.atom.order.mapper.ErpRefundInfoMapper;
import com.yxt.order.atom.order.mapper.OrderBusinessConsumerMessageMapper;
import com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper;
import com.yxt.order.atom.order.mapper.OrderDeliveryRecordMapper;
import com.yxt.order.atom.order.mapper.OrderDetailMapper;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import com.yxt.order.atom.order.mapper.OrderPayInfoMapper;
import com.yxt.order.atom.order.mapper.OrderPickInfoMapper;
import com.yxt.order.atom.order.mapper.OrderPrescriptionMapper;
import com.yxt.order.atom.order.mapper.RefundDetailMapper;
import com.yxt.order.atom.order.mapper.OriThirdOrderDetailMapper;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import com.yxt.order.atom.order.repository.OrderRepository;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.order.repository.abstracts.order.SaveOrderOptionalDO;
import com.yxt.order.atom.sdk.common.data.ErpBillInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderBusinessConsumerMessageDTO;
import com.yxt.order.atom.sdk.common.data.OrderDeliveryAddressDTO;
import com.yxt.order.atom.sdk.common.data.OrderDeliveryRecordDTO;
import com.yxt.order.atom.sdk.common.data.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPayInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPrescriptionDTO;
import com.yxt.order.atom.sdk.common.data.OriThirdOrderDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderDetailInfo;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.DeleteBeforeSaveDTO;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleBatchReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderBusinessConsumerMessageReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderDetailByThirdReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderDetailReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QuerySimpleOrderReqByThirdDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QuerySimpleOrderReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.SaveOrderOptionalReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.UpdateOrderOptionalReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.FullOrderDtoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.SimpleOrderInfoResDto;
import com.yxt.order.common.utils.CompletableFutureUtils;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.enums.OrderQryScaleEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;


/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年02月28日 18:22
 * @email: <EMAIL>
 */
@Repository
@Slf4j
public class OrderRepositoryImpl implements OrderRepository {

  @Resource
  private AfterSaleOrderMapper afterSaleOrderMapper;

  @Resource
  private OrderBusinessConsumerMessageMapper orderBusinessConsumerMessageMapper;

  @Resource
  private OrderInfoMapper orderInfoMapper;
  @Resource
  private RefundOrderMapper refundOrderMapper;
  @Resource
  private RefundDetailMapper refundDetailMapper;

  @Resource
  private ErpRefundInfoMapper erpRefundInfoMapper;

  @Resource
  private OrderPayInfoMapper orderPayInfoMapper;

  @Resource
  private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
  @Resource
  private OrderDeliveryAddressMapper orderDeliveryAddressMapper;

  @Resource
  private List<AbstractDelete> abstractDeleteList;

  @Resource
  private List<AbstractInsert<?>> abstractInsertList;

  @Resource
  private List<AbstractUpdate<?>> abstractUpdateList;

  @Resource
  private OrderDetailMapper orderDetailMapper;

  @Resource
  private OriThirdOrderDetailMapper thirdOrderDetailMapper;

  @Resource
  private OrderPickInfoMapper orderPickInfoMapper;

  @Resource
  private OrderPrescriptionMapper orderPrescriptionMapper;

  @Resource
  private ErpBillInfoMapper erpBillInfoMapper;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_POOL)
  private Executor orderSearchPool;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_SUB_POOL)
  private Executor orderSearchSubPool;

  @Override
  public OrderInfoResDto getOrderInfoByOrderNo(OrderInfoQryReqDto orderNoDto) {
    LambdaQueryWrapper<OrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderInfoDO::getOrderNo, orderNoDto.getOrderNo());
    OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(queryWrapper);
    OrderInfoResDto orderInfo = OrderInfoConverter.toOrderInfo(orderInfoDO);
    if (orderNoDto.getQryScale().equals(OrderInfoQryReqDto.OrderQryScaleEnum.MAIN)) {
      return orderInfo;
    } else if (orderNoDto.getQryScale().equals(OrderInfoQryReqDto.OrderQryScaleEnum.DETAIL)) {
      LambdaQueryWrapper<OrderDetailDO> detailWrapper = new LambdaQueryWrapper<>();
      detailWrapper.eq(OrderDetailDO::getOrderNo, orderNoDto.getOrderNo());
      List<OrderDetailDO> orderDetailDOList = orderDetailMapper.selectList(detailWrapper);

      List<Long> orderDetailIdList =  orderDetailDOList.stream().map(OrderDetailDO::getId)
          .collect(Collectors.toList());
      LambdaQueryWrapper<OrderPickInfoDO> orderPickWrapper = new LambdaQueryWrapper<>();
      orderPickWrapper.in(OrderPickInfoDO::getOrderDetailId, orderDetailIdList)
          .eq(OrderPickInfoDO::getIsValid, 1);
      List<OrderPickInfoDO> orderPickInfoDOList = orderPickInfoMapper.selectList(orderPickWrapper);
      List<OrderPickInfoDTO> orderPickInfoDtoList = OrderPickInfo2DtoConverter.INSTANCE.toDto(orderPickInfoDOList);

      OrderDetailInfo orderDetailInfoList = OrderDetailConverter.toOrderDetailInfoList(
          orderDetailDOList,orderPickInfoDtoList);
      orderInfo.setOrderDetailInfo(orderDetailInfoList);
      return orderInfo;
    } else if (orderNoDto.getQryScale().equals(OrderInfoQryReqDto.OrderQryScaleEnum.OTHER)) {
      //todo 补充订单周边信息比如 支付 配送等

    }

    return orderInfo;
  }

  @Override
  public SimpleOrderInfoResDto querySimpleOrderInfo(QuerySimpleOrderReqByThirdDto dto) {
    String thirdOrderNo = dto.getThirdOrderNo();
    String thirdPlatformCode = dto.getThirdPlatformCode();
    OrderInfoDO orderInfoDO = getOrderInfoDOByThirdReq(thirdOrderNo, thirdPlatformCode);
    return OnlineOrderConverter.toSimpleOrderInfoResDto(orderInfoDO);
  }


  @Override
  public SimpleOrderInfoResDto querySimpleOrderInfo(QuerySimpleOrderReqDto dto) {
    OrderInfoDO orderInfoDO = getOrderInfo(dto.getOrderNo());
    return OnlineOrderConverter.toSimpleOrderInfoResDto(orderInfoDO);
  }

  @Override
  public List<OrderInfoDO> querySimpleOrderInfoList(List<Long> orderNoList) {
    if(CollectionUtils.isEmpty(orderNoList)){
      return Lists.newArrayList();
    }

    LambdaQueryWrapper<OrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.in(OrderInfoDO::getOrderNo, orderNoList);
    queryWrapper.eq(OrderInfoDO::getDeleted, DsConstants.UN_DELETE);

    return orderInfoMapper.selectList(queryWrapper);
  }

  @Override
  @Transactional
  public Boolean saveOptional(SaveOrderOptionalReq req) {

    // 如果需要再新增前删除,则执行删除
    if (Objects.nonNull(req.getDeleteBeforeSaveDTO())) {
      deleteBeforeSave(req);
    }

    SaveOrderOptionalDO saveOrderOptionalDO = convert(req);
    // 新增
    int insert = 0;
    for (AbstractInsert<?> abstractInsert : abstractInsertList) {
      insert = insert + abstractInsert.exec(saveOrderOptionalDO);
    }

    return insert > 0;
  }

  private SaveOrderOptionalDO convert(SaveOrderOptionalReq req) {
    SaveOrderOptionalDO saveOrderOptionalDO = new SaveOrderOptionalDO();
    saveOrderOptionalDO.setOriThirdOrderDetailList(BeanUtil.copyToList(req.getOriThirdOrderDetailDtoList(),
        OriThirdOrderDetailDO.class));
    saveOrderOptionalDO.setOrderInfo(BeanUtil.copyProperties(req.getOrderInfoDto(),OrderInfoDO.class));
    saveOrderOptionalDO.setOrderPayInfo(BeanUtil.copyProperties(req.getOrderPayInfoDto(),OrderPayInfoDO.class));
    saveOrderOptionalDO.setOrderMultiPayInfoList(BeanUtil.copyToList(req.getOrderMultiPayInfoDtoList(),
        OrderMultiPayInfoDO.class));
    saveOrderOptionalDO.setOrderDetailList(BeanUtil.copyToList(req.getOrderDetailDtoList(),
        OrderDetailDO.class));
    saveOrderOptionalDO.setOrderDeliveryAddress(BeanUtil.copyProperties(req.getOrderDeliveryAddressDto(),OrderDeliveryAddressDO.class));
    saveOrderOptionalDO.setOrderDeliveryRecord(BeanUtil.copyProperties(req.getOrderDeliveryRecordDto(),OrderDeliveryRecordDO.class));
    saveOrderOptionalDO.setOrderDeliveryLog(BeanUtil.copyProperties(req.getOrderDeliveryLogDto(),
        OrderDeliveryLogDO.class));
    saveOrderOptionalDO.setOrderPrescriptionList(BeanUtil.copyToList(req.getOrderPrescriptionDtoList(),
        OrderPrescriptionDO.class));
    saveOrderOptionalDO.setOrderGiftInfoList(BeanUtil.copyToList(req.getOrderGiftInfoDtoList(),
        OrderGiftInfoDO.class));
    saveOrderOptionalDO.setOrderCouponInfoList(BeanUtil.copyToList(req.getOrderCouponInfoDtoList(),
        OrderCouponInfoDO.class));
    saveOrderOptionalDO.setErpBillInfo(BeanUtil.copyProperties(req.getErpBillInfoDto(),
        ErpBillInfoDO.class));
    saveOrderOptionalDO.setOrderDetailCommodityCostPriceList(BeanUtil.copyToList(req.getOrderDetailCommodityCostPriceDtoList(),
        OrderDetailCommodityCostPriceDO.class));
    saveOrderOptionalDO.setOrderCommodityDetailCostPriceList(BeanUtil.copyToList(req.getOrderCommodityDetailCostPriceDtoList(),
        OrderCommodityDetailCostPriceDO.class));
    saveOrderOptionalDO.setCommodityStockList(BeanUtil.copyToList(req.getCommodityStockDtoList(),
        CommodityStockDO.class));
    saveOrderOptionalDO.setCommodityExceptionOrderList(BeanUtil.copyToList(req.getCommodityExceptionOrderDtoList(),
        CommodityExceptionOrderDO.class));
    saveOrderOptionalDO.setOrderBusinessConsumerMessage(BeanUtil.copyProperties(req.getOrderBusinessConsumerMessageDto(),OrderBusinessConsumerMessageDO.class));
    saveOrderOptionalDO.setOrderAssembleCommodityRelationList(BeanUtil.copyToList(req.getOrderAssembleCommodityRelationDTOList(),
        OrderAssembleCommodityRelationDO.class));
    saveOrderOptionalDO.setSaveDeliveryInfo(BeanUtil.copyProperties(req.getSaveDeliveryInfo(),OrderInfoDO.class));
    saveOrderOptionalDO.setOrderPickInfoList(BeanUtil.copyToList(req.getOrderPickInfoDTOList(),
            OrderPickInfoDO.class));
    return saveOrderOptionalDO;
  }

  private void deleteBeforeSave(SaveOrderOptionalReq req) {
    DeleteBeforeSaveDTO deleteBeforeSaveDTO = req.getDeleteBeforeSaveDTO();
    Assert.isTrue(Objects.nonNull(deleteBeforeSaveDTO.getOrderNo()), "orderNo can not null");
    int delete = 0;
    for (AbstractDelete abstractDelete : abstractDeleteList) {
      delete = delete + abstractDelete.exec(deleteBeforeSaveDTO);
    }
    log.info("DeleteBeforeSave result:{},deleteParam:{}", delete > 0, deleteBeforeSaveDTO);
  }


  @Override
  @Transactional
  public Boolean updateOptional(UpdateOrderOptionalReq req) {
    int total = 0;
    for (AbstractUpdate<?> abstractUpdate : abstractUpdateList) {
      total = total + abstractUpdate.exec(req);
    }
    return total > 0;
  }

  @Override
  public FullOrderDtoResDto queryFullOrderByThirdReq(QueryOrderDetailByThirdReqDto dto) {
    OrderInfoDO orderInfoDO = getOrderInfoDOByThirdReq(dto.getThirdOrderNo(),
        dto.getThirdPlatformCode());
    if (Objects.isNull(orderInfoDO)) {
      return new FullOrderDtoResDto();
    }
    QueryOrderDetailReqDto queryOrderDetailReqDto = new QueryOrderDetailReqDto();
    queryOrderDetailReqDto.setOrderNo(orderInfoDO.getOrderNo());
    return queryFullOrder(queryOrderDetailReqDto);
  }

  @Override
  public FullOrderDtoResDto queryFullOrder(QueryOrderDetailReqDto dto) {
    FullOrderDtoResDto fullOrderDtoResDto = new FullOrderDtoResDto();

    Long orderNo = dto.getOrderNo();
    OrderInfoDO orderInfoDO = getOrderInfo(orderNo);
    if (Objects.isNull(orderInfoDO)) {
      return fullOrderDtoResDto;
    }

    OrderPayInfoDO orderPayInfoDO = getOrderPayInfoDO(orderNo);
    List<OrderDetailDO> orderDetailDOList = getOrderDetailDOList(orderNo);
    List<OrderPrescriptionDO> orderPrescriptionDOList = getOrderPrescriptionDOList(orderNo);
    OrderDeliveryRecordDO orderDeliveryRecordDO = getOrderDeliveryRecordDO(orderNo);
    OrderDeliveryAddressDO orderDeliveryAddressDO = getOrderDeliveryAddressDO(orderInfoDO);
    List<OrderDeliveryAddressDO> orderDeliveryAddressDOList = getB2cOrderDeliveryAddressDO(
        orderInfoDO);

    fullOrderDtoResDto.setDetailDtoList(
        BeanUtil.copyToList(orderDetailDOList, OrderDetailDTO.class));
    fullOrderDtoResDto.setOrderPayInfoDto(
        BeanUtil.copyProperties(orderPayInfoDO, OrderPayInfoDTO.class));
    fullOrderDtoResDto.setOrderPrescriptionDtoList(
        BeanUtil.copyToList(orderPrescriptionDOList, OrderPrescriptionDTO.class));
    fullOrderDtoResDto.setOrderInfoDto(BeanUtil.copyProperties(orderInfoDO, OrderInfoDTO.class));
    fullOrderDtoResDto.setOrderDeliveryRecordDto(
        BeanUtil.copyProperties(orderDeliveryRecordDO, OrderDeliveryRecordDTO.class));
    fullOrderDtoResDto.setOrderDeliveryAddressDto(
        BeanUtil.copyProperties(orderDeliveryAddressDO, OrderDeliveryAddressDTO.class));
    fullOrderDtoResDto.setB2cOrderDeliveryAddressDtoList(
        BeanUtil.copyToList(orderDeliveryAddressDOList, OrderDeliveryAddressDTO.class));

    // 获取运费单相关内容
    Long freightOrderNo = orderInfoDO.getFreightOrderNo();
    if (StringUtils.isEmpty(freightOrderNo)) {
      OrderInfoDO freightOrderInfo = getOrderInfo(freightOrderNo);
      RefundOrderDO latestFreightRefundOrder = getLatestRefundOrder(freightOrderNo);

      fullOrderDtoResDto.setFreightOrderDto(
          BeanUtil.copyProperties(freightOrderInfo, OrderInfoDTO.class));
      fullOrderDtoResDto.setLatestFreightRefundOrderDto(
          BeanUtil.copyProperties(latestFreightRefundOrder, RefundOrderDTO.class));
    }

    return fullOrderDtoResDto;
  }

  private List<OrderDeliveryAddressDO> getB2cOrderDeliveryAddressDO(OrderInfoDO orderInfoDO) {
    Long orderNo = orderInfoDO.getOrderNo();
    if (DsConstants.B2C.equals(orderInfoDO.getServiceMode())) {
      LambdaQueryWrapper<OrderDeliveryAddressDO> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(OrderDeliveryAddressDO::getOrderNo, orderNo);
      return orderDeliveryAddressMapper.selectList(queryWrapper);
    }
    return Lists.newArrayList();
  }

  private OrderDeliveryAddressDO getOrderDeliveryAddressDO(OrderInfoDO orderInfoDO) {
    Long orderNo = orderInfoDO.getOrderNo();
    if (DsConstants.O2O.equals(orderInfoDO.getServiceMode())) {
      LambdaQueryWrapper<OrderDeliveryAddressDO> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(OrderDeliveryAddressDO::getOrderNo, orderNo);
      return orderDeliveryAddressMapper.selectOne(queryWrapper);
    }
    return null;
  }


  private List<OrderPrescriptionDO> getOrderPrescriptionDOList(Long orderNo) {
    LambdaQueryWrapper<OrderPrescriptionDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderPrescriptionDO::getOrderNo, orderNo);
    return orderPrescriptionMapper.selectList(queryWrapper);
  }

  private List<OrderDetailDO> getOrderDetailDOList(Long orderNo) {
    LambdaQueryWrapper<OrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderDetailDO::getOrderNo, orderNo);
    return orderDetailMapper.selectList(queryWrapper);
  }

  private OrderPayInfoDO getOrderPayInfoDO(Long orderNo) {
    LambdaQueryWrapper<OrderPayInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderPayInfoDO::getOrderNo, orderNo);
    return orderPayInfoMapper.selectOne(queryWrapper);
  }

  @Override
  public List<OrderPayInfoDO> queryPayInfoList(List<Long> orderNoList) {
    LambdaQueryWrapper<OrderPayInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.in(OrderPayInfoDO::getOrderNo, orderNoList);
    return orderPayInfoMapper.selectList(queryWrapper);
  }

  private OrderDeliveryRecordDO getOrderDeliveryRecordDO(Long orderNo) {
    LambdaQueryWrapper<OrderDeliveryRecordDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderDeliveryRecordDO::getOrderNo, orderNo);
    return orderDeliveryRecordMapper.selectOne(queryWrapper);
  }

  private List<OriThirdOrderDetailDO> getThirdDetailDOList(Long orderNo) {
    LambdaQueryWrapper<OriThirdOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OriThirdOrderDetailDO::getOrderNo, orderNo);
    return thirdOrderDetailMapper.selectList(queryWrapper);
  }

  private OrderInfoDO getOrderInfo(Long orderNo) {
    LambdaQueryWrapper<OrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderInfoDO::getOrderNo, orderNo);
    queryWrapper.eq(OrderInfoDO::getDeleted, DsConstants.UN_DELETE);
    return orderInfoMapper.selectOne(queryWrapper);
  }

  private RefundOrderDO getLatestRefundOrder(Long orderNo) {
    LambdaQueryWrapper<RefundOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(RefundOrderDO::getOrderNo, orderNo);
    // latest
    queryWrapper.orderByDesc(RefundOrderDO::getCreateTime);
    queryWrapper.last(" limit 1 ");

    return refundOrderMapper.selectOne(queryWrapper);
  }

  private OrderInfoDO getOrderInfoDOByThirdReq(String thirdOrderNo, String thirdPlatformCode) {
    LambdaQueryWrapper<OrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderInfoDO::getThirdOrderNo, thirdOrderNo);
    queryWrapper.eq(OrderInfoDO::getThirdPlatformCode, thirdPlatformCode);
    queryWrapper.eq(OrderInfoDO::getDeleted, DsConstants.UN_DELETE);
    return orderInfoMapper.selectOne(queryWrapper);
  }

  @Override
  public List<OrderBusinessConsumerMessageDTO> queryOrderBusinessConsumerMessageList(
      QueryOrderBusinessConsumerMessageReq dto) {
    QueryWrapper<OrderBusinessConsumerMessageDO> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(OrderBusinessConsumerMessageDO::getMessageType, dto.getMessageType())
        .eq(OrderBusinessConsumerMessageDO::getMerCode, dto.getMerCode())
        .eq(OrderBusinessConsumerMessageDO::getPlatformCode, dto.getPlatformCode())
        .eq(OrderBusinessConsumerMessageDO::getThirdOrderNo, dto.getThirdOrderNo());
    List<OrderBusinessConsumerMessageDO> list = orderBusinessConsumerMessageMapper.selectList(
        queryWrapper);
    return BeanUtil.copyToList(list, OrderBusinessConsumerMessageDTO.class);
  }

//  @Override
//  public List<OrderDetailSimpleResDto> queryOrderDetailList(List<Long> orderNoList) {
//    if(CollectionUtils.isEmpty(orderNoList)){
//      return Lists.newArrayList();
//    }
//
//    LambdaQueryWrapper<OrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
//    queryWrapper.in(OrderDetailDO::getOrderNo,orderNoList);
//    List<OrderDetailDO> list = orderDetailMapper.selectList(queryWrapper);
//    return BeanUtil.copyToList(list, OrderDetailSimpleResDto.class);
//  }

  @Override
  public List<OrderDetailDO> queryOrderDetailList(List<Long> orderNoList) {
    if(CollectionUtils.isEmpty(orderNoList)){
      return Lists.newArrayList();
    }

    LambdaQueryWrapper<OrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.in(OrderDetailDO::getOrderNo,orderNoList);
    return orderDetailMapper.selectList(queryWrapper);
  }

  @Override
  public Map<Long, Boolean> hasRefundOrder(List<Long> orderNoList) {
    LambdaQueryWrapper<RefundOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.in(RefundOrderDO::getOrderNo,orderNoList);
    List<RefundOrderDO> refundOrderList = refundOrderMapper.selectList(queryWrapper);
    if(CollectionUtils.isEmpty(refundOrderList)){
      return orderNoList.stream()
          .collect(Collectors.toMap(
              orderNo->orderNo,  // key
              orderNo -> false,     // value
              (v1, v2) -> v1       // in case of duplicate keys, keep the first value
          ));
    }

    Map<Long, RefundOrderDO> refundOrderMap = refundOrderList.stream().collect(Collectors.toMap(
        RefundOrderDO::getOrderNo, // key
        v->v,
        (v1, v2) -> v1
    ));
    return orderNoList.stream()
        .collect(Collectors.toMap(
            orderNo->orderNo,  // key
            orderNo -> Objects.nonNull(refundOrderMap.get(orderNo)),     // value
            (v1, v2) -> v1       // in case of duplicate keys, keep the first value
        ));
  }

  @Override
  public List<ErpBillInfoDO> queryBillInfoList(List<Long> orderNoList) {
    LambdaQueryWrapper<ErpBillInfoDO> query = new LambdaQueryWrapper<>();
    query.in(ErpBillInfoDO::getOrderNo, orderNoList);
    return erpBillInfoMapper.selectList(query);
  }

  @Override
  public List<RefundOrderDO> queryRefundOrderList(List<String> refundNoList) {
    LambdaQueryWrapper<RefundOrderDO> query = new LambdaQueryWrapper<>();
    query.in(RefundOrderDO::getRefundNo, refundNoList);
    return refundOrderMapper.selectList(query);
  }

  @Override
  public List<RefundDetailDO> queryRefundDetailList(List<String> refundNoList) {
    LambdaQueryWrapper<RefundDetailDO> query = new LambdaQueryWrapper<>();
    query.in(RefundDetailDO::getRefundNo, refundNoList);
    return refundDetailMapper.selectList(query);
  }

  @Override
  public List<ErpRefundInfoDO> queryErpRefundInfoList(List<String> refundNoList) {
    LambdaQueryWrapper<ErpRefundInfoDO> query = new LambdaQueryWrapper<>();
    query.in(ErpRefundInfoDO::getRefundNo, refundNoList);
    return erpRefundInfoMapper.selectList(query);
  }


  @Override
  @DS(DsConstants.DB_ORDER_SLAVE)
  public FullOrderDtoResDto getOrderInfoByScale(OrderInfoQryByScaleReqDto request) {
    OrderInfoDO orderInfoDO = getOrderInfo(request.getOrderNo().getOrderNo());
    if (ObjectUtil.isNull(orderInfoDO)) {
      return null;
    }
    AtomicReference<FullOrderDtoResDto> fullOrderDtoResDto = new AtomicReference<>(new FullOrderDtoResDto());
    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    for (OrderQryScaleEnum orderQryScale : request.getQryScaleList()) {
      switch (orderQryScale){
        case MAIN:
          fullOrderDtoResDto.get().setOrderInfoDto(BeanUtil.copyProperties(orderInfoDO, OrderInfoDTO.class));
          break;
        case DETAIL:
          CompletableFuture<Void> detailFuture = CompletableFuture.runAsync(() -> {
            List<OrderDetailDTO> orderDetailDTOList = getOrderDetailDOList(request.getOrderNo()
                .getOrderNo()).stream()
                .map(orderDetailDO -> BeanUtil.copyProperties(orderDetailDO, OrderDetailDTO.class))
                .collect(Collectors.toList());
            fullOrderDtoResDto.get().setDetailDtoList(orderDetailDTOList);
          } ,orderSearchSubPool);
          futureList.add(detailFuture);
          break;
        case PAY:
          CompletableFuture<Void> payFuture = CompletableFuture.runAsync(() -> {
            OrderPayInfoDO orderPayInfoDO = getOrderPayInfoDO(request.getOrderNo().getOrderNo());
            fullOrderDtoResDto.get().setOrderPayInfoDto(BeanUtil.copyProperties(orderPayInfoDO, OrderPayInfoDTO.class));
          },orderSearchSubPool);
          futureList.add(payFuture);
          break;
        case BILL:
          CompletableFuture<Void> billFuture = CompletableFuture.runAsync(() -> {
            ErpBillInfoDO erpBillInfoDO = getErpBillInfoDO(request.getOrderNo().getOrderNo());
            fullOrderDtoResDto.get().setErpBillInfoDto(BeanUtil.copyProperties(erpBillInfoDO, ErpBillInfoDTO.class));
          },orderSearchSubPool);
          futureList.add(billFuture);
          break;
        case PICK:
          CompletableFuture<Void> pickFuture = CompletableFuture.runAsync(() -> {
            List<Long> detailIdList = new ArrayList<>();
            if (CollUtil.isNotEmpty(fullOrderDtoResDto.get().getDetailDtoList())) {
              detailIdList = fullOrderDtoResDto.get().getDetailDtoList().stream().map(OrderDetailDTO::getId).collect(Collectors.toList());
            } else {
              detailIdList = getOrderDetailDOList(request.getOrderNo().getOrderNo()).stream().map(OrderDetailDO::getId).collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(detailIdList)) {
              List<OrderPickInfoDO> orderPickInfoDOS = orderPickInfoMapper.selectList(Wrappers.<OrderPickInfoDO>lambdaQuery()
                  .in(OrderPickInfoDO::getOrderDetailId, detailIdList));
              fullOrderDtoResDto.get().setOrderPickInfoDtoList(BeanUtil.copyToList(orderPickInfoDOS, OrderPickInfoDTO.class));
            }
          },orderSearchSubPool);
          futureList.add(pickFuture);
          break;
        case DELIVERY:
          CompletableFuture<Void> deliveryFuture = CompletableFuture.runAsync(() -> {
            OrderDeliveryRecordDO orderDeliveryRecordDO = getOrderDeliveryRecordDO(request.getOrderNo().getOrderNo());
            fullOrderDtoResDto.get().setOrderDeliveryRecordDto(BeanUtil.copyProperties(orderDeliveryRecordDO, OrderDeliveryRecordDTO.class));
          },orderSearchSubPool);
          futureList.add(deliveryFuture);
          break;
        case RECEIVE_ADDRESS:
          CompletableFuture<Void> receiveFuture = CompletableFuture.runAsync(() -> {
            OrderDeliveryAddressDO orderDeliveryAddressDO = getOrderDeliveryAddressDO(request);
            fullOrderDtoResDto.get().setOrderDeliveryAddressDto(BeanUtil.copyProperties(orderDeliveryAddressDO, OrderDeliveryAddressDTO.class));
          },orderSearchSubPool);
          futureList.add(receiveFuture);
          break;
        case THIRD_DETAIL:
          CompletableFuture<Void> thirdDetailFuture = CompletableFuture.runAsync(() -> {
            List<OriThirdOrderDetailDO> thirdDetailDOList = getThirdDetailDOList(request.getOrderNo().getOrderNo());
            fullOrderDtoResDto.get().setThirdOrderDetailDtoList(BeanUtil.copyToList(thirdDetailDOList, OriThirdOrderDetailDTO.class));
          },orderSearchSubPool);
          futureList.add(thirdDetailFuture);
          break;
        case OTHER:
          //todo
        default:
          break;
      }
    }
    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    return fullOrderDtoResDto.get();
  }

  private OrderDeliveryAddressDO getOrderDeliveryAddressDO(OrderInfoQryByScaleReqDto request) {
    LambdaQueryWrapper<OrderDeliveryAddressDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OrderDeliveryAddressDO::getOrderNo, request.getOrderNo().getOrderNo());
    OrderDeliveryAddressDO orderDeliveryAddressDO = orderDeliveryAddressMapper.selectOne(queryWrapper);
    return orderDeliveryAddressDO;
  }

  private ErpBillInfoDO getErpBillInfoDO(Long orderNo) {
    List<ErpBillInfoDO> erpBillInfoDOS = erpBillInfoMapper.selectList(Wrappers.<ErpBillInfoDO>lambdaQuery()
        .eq(ErpBillInfoDO::getOrderNo, orderNo));
    if(CollUtil.isEmpty(erpBillInfoDOS)){
      return null;
    }
    return erpBillInfoDOS.get(0);
  }

  @Override
  public List<FullOrderDtoResDto> getOrderInfoBatchByScale(OrderInfoQryByScaleBatchReqDto request) {
    Function<OrderNo, Supplier<FullOrderDtoResDto>> function = (orderNo) -> () -> {
      OrderInfoQryByScaleReqDto tempRequest = new OrderInfoQryByScaleReqDto(request.getQryScaleList(), orderNo);
      return SpringUtil.getBean(OrderRepository.class).getOrderInfoByScale(tempRequest);
    };
    return CompletableFutureUtils.supplyAsync(function, request.getOrderNoList(), 10, orderSearchPool).stream().filter(Objects::nonNull).collect(Collectors.toList());
  }
  /**
   * (需要排除掉 after_sale_order 手工的记录。如果还有多条，就是脏数据)
   * @param refundNoList
   * @return
   */
  @Override
  public List<AfterSaleOrderDO> selectEfficientErpRefundInfo(List<String> refundNoList) {
    LambdaQueryWrapper<AfterSaleOrderDO> afterSaleOrderQuery = new LambdaQueryWrapper<>();
    afterSaleOrderQuery.in(AfterSaleOrderDO::getRefundNo, refundNoList);
    // after_sale_source 售后来源,1、退款生成，2、手工录入; 排除掉手工记录
    afterSaleOrderQuery.notIn(AfterSaleOrderDO::getAfterSaleSource,2);
    return afterSaleOrderMapper.selectList(afterSaleOrderQuery);
  }
}
