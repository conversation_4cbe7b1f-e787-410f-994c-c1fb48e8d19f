package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.OrderCouponDO;
import com.yxt.order.atom.order_world.entity.OrderDetailCouponDO;
import com.yxt.order.atom.order_world.mapper.NewOrderCouponMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderDetailCouponMapper;
import org.springframework.stereotype.Repository;

@Repository
public class NewOrderCouponBatchRepository extends ServiceImpl<NewOrderCouponMapper, OrderCouponDO> {

}
