package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderDeliveryRecordDO;
import com.yxt.order.atom.order.mapper.OrderDeliveryRecordMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:57
 * @email: <EMAIL>
 */
@Component
public class OrderDeliveryRecordInsert extends AbstractInsert<OrderDeliveryRecordDO> {

  @Resource
  private OrderDeliveryRecordMapper orderDeliveryRecordMapper;

  @Override
  protected Boolean canInsert() {
    return Objects.nonNull(saveDataOptional.getOrderDeliveryRecord());
  }

  @Override
  protected Integer insert(OrderDeliveryRecordDO orderDeliveryRecordDO) {
    return orderDeliveryRecordMapper.insert(orderDeliveryRecordDO);
  }

  @Override
  protected OrderDeliveryRecordDO data() {
    return saveDataOptional.getOrderDeliveryRecord();
  }

}
