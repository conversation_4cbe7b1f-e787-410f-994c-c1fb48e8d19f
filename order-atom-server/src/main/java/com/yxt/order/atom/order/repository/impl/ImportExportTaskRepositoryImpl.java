package com.yxt.order.atom.order.repository.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.ImportExportTaskDO;
import com.yxt.order.atom.order.mapper.ImportExportTaskMapper;
import com.yxt.order.atom.order.repository.ImportExportTaskRepository;
import com.yxt.order.atom.sdk.imextask.ImportExportTaskRes;
import com.yxt.order.atom.sdk.imextask.QueryImportExportTaskReq;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/4/3 10:56
 */
@Repository
@DS(DATA_SOURCE.ORDER_OFFLINE)
public class ImportExportTaskRepositoryImpl implements ImportExportTaskRepository {

  @Resource
  private ImportExportTaskMapper importExportTaskMapper;

  @Override
  public ImportExportTaskDO queryTask(String taskNo) {
    LambdaQueryWrapper<ImportExportTaskDO> query = new LambdaQueryWrapper<>();
    query.eq(ImportExportTaskDO::getTaskNo, taskNo);
    return importExportTaskMapper.selectOne(query);
  }

  @Override
  public Boolean insert(ImportExportTaskDO create) {
    return importExportTaskMapper.insert(create) > 0;
  }

  @Override
  public Boolean update(ImportExportTaskDO update) {
    LambdaQueryWrapper<ImportExportTaskDO> where = new LambdaQueryWrapper<>();
    where.eq(ImportExportTaskDO::getTaskNo, update.getTaskNo());
    return importExportTaskMapper.update(update, where) > 0;
  }

  @Override
  public PageDTO<ImportExportTaskRes> list(QueryImportExportTaskReq req) {
    LambdaQueryWrapper<ImportExportTaskDO> query = new LambdaQueryWrapper<>();
    query.eq(StringUtils.isNotEmpty(req.getTaskNo()),ImportExportTaskDO::getTaskNo, req.getTaskNo());
    query.eq(StringUtils.isNotEmpty(req.getMerCode()),ImportExportTaskDO::getMerCode, req.getMerCode());
    query.eq(StringUtils.isNotEmpty(req.getTaskType()),ImportExportTaskDO::getTaskType, req.getTaskType());
    query.in(!CollectionUtils.isEmpty(req.getDataMappingClazzList()),ImportExportTaskDO::getDataMappingClazz, req.getDataMappingClazzList());
    query.eq(StringUtils.isNotEmpty(req.getState()),ImportExportTaskDO::getState, req.getState());
    query.eq(StringUtils.isNotEmpty(req.getCreatedBy()),ImportExportTaskDO::getCreatedBy, req.getCreatedBy());
    query.orderByDesc(ImportExportTaskDO::getCreatedTime);
    IPage<ImportExportTaskDO> page = new Page<>(req.getCurrentPage(),req.getPageSize());
    IPage<ImportExportTaskDO> pageData = importExportTaskMapper.selectPage(page, query);

    PageDTO<ImportExportTaskRes> resPageDTO = new PageDTO<>();
    resPageDTO.setTotalCount(pageData.getTotal());
    resPageDTO.setTotalPage(pageData.getPages());
    resPageDTO.setData(pageData.getRecords().stream().map(record->{
      ImportExportTaskRes res = new ImportExportTaskRes();
      res.setId(record.getId());
      res.setTaskNo(record.getTaskNo());
      res.setMerCode(record.getMerCode());
      res.setTaskType(record.getTaskType());
      res.setDataMappingClazz(record.getDataMappingClazz());
      res.setParamJson(record.getParamJson());
      res.setParamMappingClazz(record.getParamMappingClazz());
      res.setState(record.getState());
      res.setNote(record.getNote());
      res.setDownloadUrl(record.getDownloadUrl());
      res.setCreatedBy(record.getCreatedBy());
      res.setUpdatedBy(record.getUpdatedBy());
      res.setCreatedTime(record.getCreatedTime());
      res.setUpdatedTime(record.getUpdatedTime());
      res.setVersion(record.getVersion());
      res.setFileName(record.getFileName());
      return res;
    }).collect(Collectors.toList()));
    resPageDTO.setCurrentPage(page.getCurrent());
    resPageDTO.setPageSize(page.getSize());
    return resPageDTO;
  }
}
