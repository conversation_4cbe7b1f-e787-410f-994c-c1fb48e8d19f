package com.yxt.order.atom.repair;

import static com.yxt.order.common.constants.Constant.OFFLINE_SHARDING_NUM;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.utils.ShardingUtils;
import com.yxt.order.atom.order.entity.OrderDataRepairDO;
import com.yxt.order.atom.order.mapper.OrderDataRepairMapper;
import com.yxt.order.atom.repair.dto.PreCheckResult;
import com.yxt.order.atom.repair.dto.RepairResult;
import com.yxt.order.atom.repair.dto.StartEndId;
import com.yxt.order.types.offline.enums.PreCheckEnum;
import com.yxt.order.types.repair.RepairScene;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/1/23 14:23
 */
@Slf4j
public abstract class AbstractOrderRepair<Req> {


  @Resource
  private OrderDataRepairMapper orderDataRepairMapper;

  // 修复场景
  public abstract RepairScene scene();

  protected abstract Req parse(OrderDataRepairDO orderDataRepair);

  private final Long stepLength = 2000L;


  public void repair(Long startId,Long endId) {
    // 获取起始和结束ID 这样太慢了
//    StartEndId startEndId = orderDataRepairMapper.selectStartEndId(scene().name(),
//        PreCheckEnum.WAIT.name());
    // 直接指定,提速。【切割】
    StartEndId startEndId = new StartEndId();
    startEndId.setStartId(startId);
    startEndId.setEndId(endId);
    while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
      LambdaQueryWrapper<OrderDataRepairDO> query = new LambdaQueryWrapper<>();
      query.ge(OrderDataRepairDO::getId, startEndId.getStartId());
      query.lt(OrderDataRepairDO::getId, startEndId.getStartId() + stepLength);
      query.eq(OrderDataRepairDO::getScene, scene().name());
      query.eq(OrderDataRepairDO::getPreCheck, PreCheckEnum.WAIT.name());
      List<OrderDataRepairDO> orderDataRepairList = orderDataRepairMapper.selectList(query);
      if (CollectionUtils.isEmpty(orderDataRepairList)) {
        refreshStartId(startEndId);
        continue;
      }

      for (OrderDataRepairDO orderDataRepair : orderDataRepairList) {

        String shardingNo = shardingNo(orderDataRepair);
        String tableIndexByNo = ShardingHelper.getTableIndexByNo(shardingNo);
        if (Long.parseLong(tableIndexByNo) > OFFLINE_SHARDING_NUM) {
          List<String> nonVipShardingList = ShardingUtils.nonVipShardingValueList();
          for (String shardingIndex : nonVipShardingList) {
            if (repair(orderDataRepair, shardingIndex)) {
              break;
            }
          }
        } else {
          repair(orderDataRepair, tableIndexByNo);
        }

      }

      // 刷新起始Id
      refreshStartId(startEndId);
    }
  }

  /**
   * @param orderDataRepair
   * @return 返回是否执行修复程序。具体修复是否成功参考RepairResult对象
   */
  private Boolean repair(OrderDataRepairDO orderDataRepair, String shardingIndex) {

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setQueryHit(QueryHit.builder().seq(shardingIndex).build());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      PreCheckResult preCheckResult = repairPreCheck(orderDataRepair);
      if (!preCheckResult.checkIsPassed()) {
        preCheckFailed(orderDataRepair, preCheckResult); // 失败记录
        return Boolean.FALSE;
      }

      // 通过,记录快照
      pass(orderDataRepair, preCheckResult);

      // 修复,并记录结果
      RepairResult repairResult = null;
      try {
        repairResult = orderRepair(orderDataRepair);
      } catch (Exception e) {
        log.warn("orderRepair failed,{}", e.getMessage(), e);
        repairResult = RepairResult.builder()
            .result(Boolean.FALSE)
            .businessNo(orderDataRepair.getBusinessNo())
            .repairFailedReason(e.getMessage())
            .build();
      }
      recordRepairResult(orderDataRepair, repairResult);
      return Boolean.TRUE;
    }
  }

  private void refreshStartId(StartEndId startEndId) {
    startEndId.setStartId(startEndId.getStartId() + stepLength);
  }

  private void recordRepairResult(OrderDataRepairDO orderDataRepair, RepairResult repairResult) {
    orderDataRepair.setBusinessNo(repairResult.getBusinessNo());
    orderDataRepair.setRepairResult(String.valueOf(repairResult.getResult()));
    orderDataRepair.setRepairFailedReason(repairResult.getRepairFailedReason());
    orderDataRepair.setAfterImage(repairResult.getAfterImage());
    log.info("afterImage:{}",repairResult.getAfterImage());
    orderDataRepairMapper.updateById(orderDataRepair);
  }

  /**
   * 分表单号
   *
   * @return
   */
  protected abstract String shardingNo(OrderDataRepairDO orderDataRepairDO);

  /**
   * 修复预检
   *
   * @param orderDataRepairDO
   * @return
   */
  protected abstract PreCheckResult repairPreCheck(OrderDataRepairDO orderDataRepairDO);


  protected abstract RepairResult orderRepair(OrderDataRepairDO orderDataRepair);

  private void pass(OrderDataRepairDO orderDataRepair, PreCheckResult preCheckResult) {
    orderDataRepair.setPreCheck(PreCheckEnum.PASS.name());
    orderDataRepair.setPreCheckFailed(Strings.EMPTY);
    orderDataRepair.setBeforeImage(preCheckResult.getBeforeImage());
    log.info("beforeImage:{}",preCheckResult.getBeforeImage());
    orderDataRepair.setBusinessNo(preCheckResult.getBusinessNo());
    orderDataRepairMapper.updateById(orderDataRepair);
  }

  private void preCheckFailed(OrderDataRepairDO orderDataRepair, PreCheckResult preCheckResult) {
    orderDataRepair.setPreCheck(PreCheckEnum.FAILED.name());
    orderDataRepair.setPreCheckFailed(preCheckResult.getPreCheckFailed());
    orderDataRepairMapper.updateById(orderDataRepair);
  }

}
