package com.yxt.order.atom.migration.fix.component;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.req.FlushAmtAmountReq;
import com.yxt.order.atom.migration.service.OrderDataRelationComponent;
import com.yxt.order.atom.migration.service.dto.MigrationExtend;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.repair.dto.StartEndId;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2025/3/14 19:32
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class FlushAmtAmountData {

  @Value("${hanaMigrationServiceLogError:false}")
  private Boolean hanaMigrationServiceLogError;

  @Resource
  private OrderDataRelationComponent orderDataRelationComponent;


  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private HanaMigrationMapper hanaMigrationMapper;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;


  public void fixAmount(FlushAmtAmountReq req) {
    String targetSchema = req.getTargetSchema();

    List<Integer> migrationList = Lists.newArrayList(1,
        2); // 1	迁移成功 , 2-已存在 https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=57799101
//    String migrateSort = "ORDER"; // 只需要处理正单,退单是从明细上累加的 只一次为了把关系表维护起来,这个注释掉

    StartEndId startEndId = new StartEndId();
    startEndId.setStartId(req.getStartId());
    startEndId.setEndId(req.getEndId());

    while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
      Long startId = startEndId.getStartId();
      Long endId = startEndId.getStartId() + keChuanTotalAmountDataGetLimit;

      List<HanaOrderInfo> hanaOrderInfoList = hanaMigrationMapper.queryNeedFixedHanaOrder(
          targetSchema, startId, endId, migrationList/*, migrateSort*/);

      if (CollectionUtils.isEmpty(hanaOrderInfoList)) {
        refreshStartId(startEndId);
        continue;
      }

      for (HanaOrderInfo hanaOrderInfo : hanaOrderInfoList) {
        try {
          if (!migrationList.contains(hanaOrderInfo.getMigration())) { // 再判断一遍,如果不是迁移成功的则不处理
            continue;
          }

          String extendJson = hanaOrderInfo.getExtendJson();
          if (StringUtils.isEmpty(extendJson)) {
            continue;
          }

          // 当SellingAmount和ActualPayAmount不相等的时候才需要刷
          if (hanaOrderInfo.getSellingAmount().compareTo(hanaOrderInfo.getActualPayAmount()) == 0) {
            continue;
          }

          MigrationExtend extend = JsonUtils.toObject(extendJson,
              new TypeReference<MigrationExtend>() {
              });
          String orderNo = extend.getOrderNo();
          String refundNo = extend.getRefundNo();
          if (!StringUtils.isEmpty(orderNo)) {
            handleHanaOrder(hanaOrderInfo, orderNo, targetSchema);
          }
          // 不需要处理退款表,退款是从明细上累加的
          else if (!StringUtils.isEmpty(refundNo)) {
            handleHanaRefundOrder(hanaOrderInfo, refundNo, targetSchema);
          }
        } catch (Exception e) {
          if (hanaMigrationServiceLogError) {
            log.warn("fixAmt warn message", e);
          }
        }
      }
      // 刷新起始Id
      refreshStartId(startEndId);
    }


  }


  private void refreshStartId(StartEndId startEndId) {
    startEndId.setStartId(startEndId.getStartId() + keChuanTotalAmountDataGetLimit);
  }


  public void handleHanaOrder(HanaOrderInfo hanaOrderInfo, String orderNo, String targetSchema) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<>();
      query.eq(OfflineOrderDO::getOrderNo, orderNo);
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(query);
      if (Objects.isNull(offlineOrderDO) || Objects.isNull(offlineOrderDO.getId())) {
        return; // 查不到,就是被清除了,不用处理
      }

      // 如果不是迁移订单,则不处理
      if (!Boolean.TRUE.toString().equals(offlineOrderDO.getMigration())) {
        return;
      }

      // 记录一下order和迁移表之间的关系
      orderDataRelationComponent.orderRelation(orderNo, targetSchema, hanaOrderInfo);

      BigDecimal actualCollectAmount = hanaOrderInfo.getActualCollectAmount();
      BigDecimal actualPayAmount = hanaOrderInfo.getActualPayAmount();

      // 只需要比对一个就可以了。迁移订单上面2个字段都是一样的
      if (offlineOrderDO.getActualCollectAmount().compareTo(actualCollectAmount) == 0) {
        return;
      }

      offlineOrderDO.setActualPayAmount(actualPayAmount);
      offlineOrderDO.setActualCollectAmount(actualCollectAmount);

      offlineOrderMapper.updateById(offlineOrderDO);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void handleHanaRefundOrder(HanaOrderInfo hanaOrderInfo, String refundNo,
      String targetSchema) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<>();
      query.eq(OfflineRefundOrderDO::getRefundNo, refundNo);
      OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(query);
      if (Objects.isNull(offlineRefundOrderDO) || Objects.isNull(offlineRefundOrderDO.getId())) {
        return; // 查不到,就是被清除了,不用处理
      }

      // 如果不是迁移订单,则不处理
      if (!Boolean.TRUE.toString().equals(offlineRefundOrderDO.getMigration())) {
        return;
      }

      orderDataRelationComponent.refundRelation(refundNo, targetSchema, hanaOrderInfo);

    }
  }


}
