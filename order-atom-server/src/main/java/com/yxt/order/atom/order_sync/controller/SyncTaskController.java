package com.yxt.order.atom.order_sync.controller;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order_sync.service.SyncTaskService;
import com.yxt.order.atom.sdk.common.order_world.OrderSyncMappingDTO;
import com.yxt.order.atom.sdk.common.order_world.SyncTaskModelDTO;
import com.yxt.order.atom.sdk.order_sync.SyncTaskApi;
import com.yxt.order.atom.sdk.order_sync.req.SyncTaskSearchReq;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SyncTaskController implements SyncTaskApi {

  @Autowired
  private SyncTaskService syncTaskService;

  @Override
  public ResponseBase<List<SyncTaskModelDTO>> syncTaskSearch(SyncTaskSearchReq request) {
    return ResponseBase.success(syncTaskService.syncTaskSearch(request));
  }

  @Override
  public ResponseBase<Void> syncTaskSaveBatch(List<SyncTaskModelDTO> request) {
    syncTaskService.syncTaskSaveBatch(request);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<Void> syncTaskUpdateByIdBatch(List<SyncTaskModelDTO> request) {
    syncTaskService.syncTaskUpdateByIdBatch(request);
    return ResponseBase.success();
  }
}
