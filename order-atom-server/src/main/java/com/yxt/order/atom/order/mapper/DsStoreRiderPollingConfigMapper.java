package com.yxt.order.atom.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.order.entity.DsStoreRiderPollingConfigDO;
import com.yxt.order.atom.sdk.online_order.store.res.RiderPollingConfigResDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 10:39
 * @email: <EMAIL>
 */
@Repository
public interface DsStoreRiderPollingConfigMapper extends BaseMapper<DsStoreRiderPollingConfigDO> {

  List<RiderPollingConfigResDto> queryConfigByStoreId(@Param("onlineStoreId") Long onlineStoreId);

}
