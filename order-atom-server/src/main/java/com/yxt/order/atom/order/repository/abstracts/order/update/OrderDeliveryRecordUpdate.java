package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.OrderDeliveryRecordDO;
import com.yxt.order.atom.order.mapper.OrderDeliveryRecordMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.sdk.common.data.OrderDeliveryRecordDTO;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 16:00
 * @email: <EMAIL>
 */
@Component
public class OrderDeliveryRecordUpdate extends AbstractUpdate<OrderDeliveryRecordDO> {

  @Resource
  private OrderDeliveryRecordMapper orderDeliveryRecordMapper;

  @Override
  protected Boolean canUpdate() {
    OrderDeliveryRecordDTO dto = req.getOrderDeliveryRecordDto();
    if (Objects.isNull(dto)) {
      return false;
    }
    Assert.isTrue(!StringUtils.isEmpty(dto.getId()) || !StringUtils.isEmpty(dto.getOrderNo()),
        "id or orderNo can not null");
    return Boolean.TRUE;
  }

  @Override
  protected Integer update(OrderDeliveryRecordDO orderDeliveryRecordDO) {
    if (Objects.nonNull(orderDeliveryRecordDO.getId())) {
      return orderDeliveryRecordMapper.updateById(orderDeliveryRecordDO);
    } else {
      LambdaQueryWrapper<OrderDeliveryRecordDO> wrapper = Wrappers.lambdaQuery();
      wrapper.eq(OrderDeliveryRecordDO::getOrderNo, orderDeliveryRecordDO.getOrderNo());
      return orderDeliveryRecordMapper.update(orderDeliveryRecordDO, wrapper);
    }
  }

  @Override
  protected OrderDeliveryRecordDO convert() {
    return BeanUtil.copyProperties(req.getOrderDeliveryRecordDto(),OrderDeliveryRecordDO.class);
  }
}
