package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 声音设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ds_store_sound_config")
public class DsStoreSoundConfigDO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 记录ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 商家编码
   */
  @ApiModelProperty(value = "商家编码")
  private String merCode;

  /**
   * 线上门店id
   */
  @ApiModelProperty(value = "线上门店id")
  private Long onlineStoreId;

  /**
   * 新订单：0不提示，1提示一次，3提示三次，5循环提醒
   */
  @ApiModelProperty(value = "新订单：0不提示，1提示一次，3提示三次，5循环提醒")
  private Integer newOrder;

  /**
   * 预约单：0不提示，1提示一次，3提示三次，5循环提醒
   */
  @ApiModelProperty(value = "预约单：0不提示，1提示一次，3提示三次，5循环提醒")
  private Integer bookingOrder;

  /**
   * 退款单：0不提示，1提示一次，3提示三次，5循环提醒
   */
  @ApiModelProperty(value = "退款单：0不提示，1提示一次，3提示三次，5循环提醒")
  private Integer refundOrder;

  /**
   * 取消单：0不提示，1提示一次，3提示三次
   */
  @ApiModelProperty(value = "取消单：0不提示，1提示一次，3提示三次")
  private Integer cancelOrder;

  /**
   * 催单：0不提示，1提示一次，3提示三次，5循环提醒
   */
  @ApiModelProperty(value = "催单：0不提示，1提示一次，3提示三次，5循环提醒")
  private Integer urgeOrder;

  /**
   * 配送异常：0不提示，1提示一次，3提示三次，5循环提醒
   */
  @ApiModelProperty(value = "配送异常：0不提示，1提示一次，3提示三次，5循环提醒")
  private Integer deliveryException;

  /**
   * 打印机断开：0不提示，1提示一次，3提示三次
   */
  @ApiModelProperty(value = "打印机断开：0不提示，1提示一次，3提示三次")
  private Integer printerDisconnect;

  /**
   * 网络断开：0不提示，1提示一次
   */
  @ApiModelProperty(value = "网络断开：0不提示，1提示一次")
  private Integer netDisconnect;

  /**
   * 骑手取消订单：0不提示，1提示一次，3提示三次
   */
  @ApiModelProperty(value = "骑手取消订单：0不提示，1提示一次，3提示三次")
  private Integer riderCancelOrder;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;

  /**
   * 骑手异常：1提示一次 3提示三次 0不提示
   */
  @ApiModelProperty(value = "骑手异常：1提示一次 3提示三次 0不提示")
  private Integer riderAbnormal;

  /**
   * 待审方单：1提示一次 3提示3次 0不提示
   */
  @ApiModelProperty(value = "待审方单：1提示一次 3提示3次 0不提示")
  private Integer waitTrialParty;

  /**
   * 拣货提醒：1提示一次 3提示3次 0不提示
   */
  @ApiModelProperty(value = "拣货提醒：1提示一次 3提示3次 0不提示")
  private Integer pickNotify;

  /**
   * 发货提醒：1提示一次 3提示3次 0不提示
   */
  @ApiModelProperty(value = "发货提醒：1提示一次 3提示3次 0不提示")
  private Integer deliveryNotify;

  /**
   * 拣货时长：分钟
   */
  @ApiModelProperty(value = " 拣货时长：分钟")
  private Integer pickNotifyMins;

  /**
   * 发货时长：分钟
   */
  @ApiModelProperty(value = "发货时长：分钟")
  private Integer deliveryNotifyMins;

  @ApiModelProperty(value = "销售单待下账，0-不提示 其他-N次")
  private Integer needBillOrder;

  @ApiModelProperty(value = "退款单待下账，0-不提示 其他-N次")
  private Integer needBillRefund;

  @ApiModelProperty(value = "预约单提醒呼叫时间(分钟)")
  private Integer bookingRemindTime;


  @ApiModelProperty(value = "销售单下账失败，0-不提示 其他-N次")
  private Integer needBillOrderFail;


}
