package com.yxt.order.atom.order.repository.abstracts.refund;

import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.RefundCheckDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.entity.RefundLogDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import java.util.List;
import lombok.Data;

@Data
public class SaveRefundOptionalDO {

  /**
   * 退单
   */
  private List<RefundOrderDO> refundOrderList;

  /**
   * 退款明细
   */
  private List<RefundDetailDO> refundDetailList;

  /**
   * 退单下账信息
   */
  private List<ErpRefundInfoDO> erpRefundList;

  /**
   * 退款日志
   */
  private List<RefundLogDO> refundLogList;

  /**
   * 退款审核日志
   */
  private List<RefundCheckDO> refundCheckList;
}
