package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 16:00
 * @email: <EMAIL>
 */
@Component
public class OrderInfoUpdate extends AbstractUpdate<OrderInfoDO> {

  @Resource
  private OrderInfoMapper orderInfoMapper;

  @Override
  protected Boolean canUpdate() {
    OrderInfoDTO dto = req.getOrderInfoDto();
    if (Objects.isNull(dto)) {
      return false;
    }
    Assert.isTrue(!StringUtils.isEmpty(dto.getId()) || !StringUtils.isEmpty(dto.getOrderNo()),
        "id or orderNo can not null");
    return Boolean.TRUE;
  }

  @Override
  protected Integer update(OrderInfoDO orderInfoDO) {
    if (Objects.nonNull(orderInfoDO.getId())) {
      return orderInfoMapper.updateById(orderInfoDO);
    } else {
      LambdaQueryWrapper<OrderInfoDO> wrapper = Wrappers.lambdaQuery();
      wrapper.eq(OrderInfoDO::getOrderNo, orderInfoDO.getOrderNo());
      return orderInfoMapper.update(orderInfoDO, wrapper);
    }
  }

  @Override
  protected OrderInfoDO convert() {
    return BeanUtil.copyProperties(req.getOrderInfoDto(),OrderInfoDO.class);
  }
}
