package com.yxt.order.atom.order_sync.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.lang.exception.YxtParamException;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order_sync.entity.SyncTaskModelDO;
import com.yxt.order.atom.order_sync.mapper.SyncTaskMapper;
import com.yxt.order.atom.order_sync.repository.SyncTaskRepository;
import com.yxt.order.atom.sdk.common.order_world.SyncTaskModelDTO;
import com.yxt.order.atom.sdk.order_sync.req.SyncTaskSearchReq;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@DS(DATA_SOURCE.ORDER_OFFLINE)
public class SyncTaskService {

  @Autowired
  private SyncTaskMapper syncTaskMapper;
  
  @Autowired
  private SyncTaskRepository syncTaskRepository;


  public List<SyncTaskModelDTO> syncTaskSearch(SyncTaskSearchReq request) {
    List<SyncTaskModelDO> taskList = syncTaskMapper.syncTaskSearch(request);
    if (CollUtil.isEmpty(taskList)) {
      return new ArrayList<>(0);
    }
    return BeanUtil.copyToList(taskList, SyncTaskModelDTO.class);
  }

  public void syncTaskSaveBatch(List<SyncTaskModelDTO> request) {
    syncTaskRepository.saveBatch(BeanUtil.copyToList(request, SyncTaskModelDO.class));
  }

  public void syncTaskUpdateByIdBatch(List<SyncTaskModelDTO> request) {
    List<SyncTaskModelDTO> noIdModelList = request.stream().filter(item -> item.getId() == null)
        .collect(Collectors.toList());
    if (CollUtil.isNotEmpty(noIdModelList)) {
      throw new YxtParamException("id不能为空!");
    }
    syncTaskRepository.updateBatchById(BeanUtil.copyToList(request, SyncTaskModelDO.class));
  }
}
