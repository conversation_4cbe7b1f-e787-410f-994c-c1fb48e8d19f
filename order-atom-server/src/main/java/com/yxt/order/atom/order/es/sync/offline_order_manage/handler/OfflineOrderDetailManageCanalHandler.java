package com.yxt.order.atom.order.es.sync.offline_order_manage.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_ORDER_DETAIL_REGEX;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrderDetail;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrderDetail.OfflineOrderDetail;
import com.yxt.order.atom.order.es.sync.offline_order_manage.flash.OfflineOrderManageFlash;
import com.yxt.order.atom.order.es.sync.offline_order_manage.model.OfflineOrderManageModel;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:21
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class OfflineOrderDetailManageCanalHandler extends
    AbstractCanalHandler<CanalOfflineOrderDetail, OfflineOrderManageModel> {

  @Resource
  private OfflineOrderManageFlash offlineOrderManageFlash;

  public OfflineOrderDetailManageCanalHandler() {
    super(CanalOfflineOrderDetail.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();

    return Database.DSCLOUD_OFFLINE.equals(database) && Table.tableRegex(OFFLINE_ORDER_DETAIL_REGEX,table);
  }

  @Override
  protected List<OfflineOrderManageModel> assemble() {
    for (OfflineOrderDetail offlineOrderDetail : getData().getData()) {
      if (StringUtils.isEmpty(offlineOrderDetail.getOrderNo())) {
        continue;
      }

      FlashParam param =new FlashParam();
      param.setNoList(Lists.newArrayList(offlineOrderDetail.getOrderNo()));
      offlineOrderManageFlash.startFlush(param);
    }

    return Lists.newArrayList();
  }


}
