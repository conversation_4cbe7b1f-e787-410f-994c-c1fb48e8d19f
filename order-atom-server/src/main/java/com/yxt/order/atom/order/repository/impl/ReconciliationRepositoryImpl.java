package com.yxt.order.atom.order.repository.impl;

import com.yxt.order.atom.order.es.doc.EsOfflineOrderManage;
import com.yxt.order.atom.order.es.doc.EsOfflineRefundOrderManage;
import com.yxt.order.atom.order.es.mapper.EsOfflineOrderManageMapper;
import com.yxt.order.atom.order.es.mapper.EsOfflineRefundOrderManageMapper;
import com.yxt.order.atom.order.repository.ReconciliationRepository;
import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationListReq;
import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationReq;
import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationType;
import com.yxt.order.atom.sdk.reconciliation.res.ReconciliationListRes;
import com.yxt.order.atom.sdk.reconciliation.res.ReconciliationRes;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.toolkit.FieldUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

@Repository
public class ReconciliationRepositoryImpl implements ReconciliationRepository {


  @Resource
  private EsOfflineOrderManageMapper esOfflineOrderManageMapper;

  @Resource
  private EsOfflineRefundOrderManageMapper esOfflineRefundOrderManageMapper;


  @Override
  public ReconciliationListRes offlineUnionListReconciliation(ReconciliationListReq req) {
    ReconciliationType reconciliationType = req.getReconciliationType();
    List<String> thirdBusinessNoList = req.getThirdBusinessNoList();
    String storeCode = req.getStoreCode();
    String thirdPlatformCode = req.getThirdPlatformCode();

    ReconciliationListRes res = new ReconciliationListRes();
    res.setStoreCode(storeCode);
    res.setThirdPlatformCode(thirdPlatformCode);

    if (ReconciliationType.OFFLINE_ORDER.equals(reconciliationType)) {
      LambdaEsQueryWrapper<EsOfflineOrderManage> orderQuery = new LambdaEsQueryWrapper<>();
      orderQuery.in(!CollectionUtils.isEmpty(thirdBusinessNoList),
          EsOfflineOrderManage::getThirdOrderNo, thirdBusinessNoList);
      orderQuery.eq(StringUtils.isNotEmpty(storeCode), EsOfflineOrderManage::getStoreCode,
          storeCode);
      orderQuery.eq(StringUtils.isNotEmpty(thirdPlatformCode),
          EsOfflineOrderManage::getThirdPlatformCode, thirdPlatformCode);

      orderQuery.select(EsOfflineOrderManage::getThirdOrderNo);
      List<EsOfflineOrderManage> list = esOfflineOrderManageMapper.selectList(orderQuery);
      res.setBusinessNoList(list.stream().map(EsOfflineOrderManage::getThirdOrderNo).distinct()
          .collect(Collectors.toList()));
    }

    if (ReconciliationType.OFFLINE_REFUND_ORDER.equals(reconciliationType)) {
      LambdaEsQueryWrapper<EsOfflineRefundOrderManage> refundOrderQuery = new LambdaEsQueryWrapper<>();
      refundOrderQuery.in(!CollectionUtils.isEmpty(thirdBusinessNoList),
          EsOfflineRefundOrderManage::getThirdRefundNo, thirdBusinessNoList);
      refundOrderQuery.eq(StringUtils.isNotEmpty(storeCode),
          EsOfflineRefundOrderManage::getStoreCode, storeCode);
      refundOrderQuery.eq(StringUtils.isNotEmpty(thirdPlatformCode),
          EsOfflineRefundOrderManage::getThirdPlatformCode, thirdPlatformCode);

      refundOrderQuery.select(EsOfflineRefundOrderManage::getThirdRefundNo);
      List<EsOfflineRefundOrderManage> list = esOfflineRefundOrderManageMapper.selectList(
          refundOrderQuery);

      res.setBusinessNoList(
          list.stream().map(EsOfflineRefundOrderManage::getThirdRefundNo).distinct()
              .collect(Collectors.toList()));
    }

    return res;
  }

  @Override
  public ReconciliationRes offlineUnionReconciliation(ReconciliationReq req) {
    Assert.isTrue(Objects.nonNull(req.getReconciliationType()), "reconciliationType can not null");

    Long orderCount = 0L;
    Long refundOrderCount = 0L;
    BigDecimal orderSumValue = BigDecimal.ZERO;
    BigDecimal refundOrderSumValue = BigDecimal.ZERO;

    ReconciliationType reconciliationType = req.getReconciliationType();
    if (ReconciliationType.OFFLINE_ORDER.equals(reconciliationType)) {
      ReconciliationRes orderData = offlineOrderReconciliation(req);
      orderCount = orderData.getCount();
      orderSumValue = orderData.getAmount();
    }

    if (ReconciliationType.OFFLINE_REFUND_ORDER.equals(reconciliationType)) {
      ReconciliationRes refundData = offlineRefundReconciliation(req);
      refundOrderCount = refundData.getCount();
      refundOrderSumValue = refundData.getAmount();
    }

    ReconciliationRes unionReconciliationRes = new ReconciliationRes();
    unionReconciliationRes.setCount(orderCount + refundOrderCount);
    unionReconciliationRes.setAmount(orderSumValue.add(refundOrderSumValue).setScale(6,
        RoundingMode.DOWN));
    return unionReconciliationRes;
  }

  private LambdaEsQueryWrapper<EsOfflineOrderManage> buildOrderSumQuery(ReconciliationReq req) {
    LambdaEsQueryWrapper<EsOfflineOrderManage> orderSumQuery = buildOrderQuery(req);
    orderSumQuery.sum(EsOfflineOrderManage::getActualPayAmount);
    return orderSumQuery;
  }

  @NotNull
  private static LambdaEsQueryWrapper<EsOfflineOrderManage> buildOrderQuery(ReconciliationReq req) {
    LambdaEsQueryWrapper<EsOfflineOrderManage> orderQuery = new LambdaEsQueryWrapper<>();
    orderQuery.eq(StringUtils.isNotEmpty(req.getCompanyCode()),EsOfflineOrderManage::getCompanyCode, req.getCompanyCode());
    orderQuery.eq(StringUtils.isNotEmpty(req.getStoreCode()), EsOfflineOrderManage::getStoreCode,req.getStoreCode());
    orderQuery.ge(StringUtils.isNotEmpty(req.getCreatedStart()), EsOfflineOrderManage::getCreated,req.getCreatedStart());
    orderQuery.le(StringUtils.isNotEmpty(req.getCreatedEnd()), EsOfflineOrderManage::getCreated,req.getCreatedEnd());
    orderQuery.eq(StringUtils.isNotEmpty(req.getThirdPlatformCode()),EsOfflineOrderManage::getThirdPlatformCode, req.getThirdPlatformCode());
    return orderQuery;
  }

  @NotNull
  private static LambdaEsQueryWrapper<EsOfflineRefundOrderManage> buildRefundOrderWrapper(
      ReconciliationReq req) {
    LambdaEsQueryWrapper<EsOfflineRefundOrderManage> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.eq(StringUtils.isNotEmpty(req.getCompanyCode()),EsOfflineRefundOrderManage::getCompanyCode, req.getCompanyCode());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getStoreCode()),EsOfflineRefundOrderManage::getStoreCode, req.getStoreCode());
    queryWrapper.ge(StringUtils.isNotEmpty(req.getCreatedStart()), EsOfflineRefundOrderManage::getCreated,req.getCreatedStart());
    queryWrapper.le(StringUtils.isNotEmpty(req.getCreatedEnd()), EsOfflineRefundOrderManage::getCreated,req.getCreatedEnd());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getThirdPlatformCode()),EsOfflineRefundOrderManage::getThirdPlatformCode, req.getThirdPlatformCode());
    return queryWrapper;
  }


  @Override
  public ReconciliationRes offlineOrderReconciliation(ReconciliationReq req) {
    LambdaEsQueryWrapper<EsOfflineOrderManage> orderQuery = buildOrderQuery(req);
    Long orderCount = esOfflineOrderManageMapper.selectCount(orderQuery); // 正单总数

    LambdaEsQueryWrapper<EsOfflineOrderManage> orderSumQuery = buildOrderSumQuery(req);
    SearchResponse orderSumResponse = esOfflineOrderManageMapper.search(orderSumQuery);
    String orderSumField = FieldUtils.val(EsOfflineOrderManage::getActualPayAmount);
    Aggregation orderSumAggregate = orderSumResponse.getAggregations()
        .get(String.format("%sSum", orderSumField));
    double orderSumValue = ((ParsedSum) orderSumAggregate).getValue();

    ReconciliationRes orderData = new ReconciliationRes();
    orderData.setCount(orderCount);
    orderData.setAmount(BigDecimal.valueOf(orderSumValue));
    return orderData;
  }

  @Override
  public ReconciliationRes offlineRefundReconciliation(ReconciliationReq req) {

    // count
    LambdaEsQueryWrapper<EsOfflineRefundOrderManage> refundOrderQuery = buildRefundOrderWrapper(
        req);
    Long refundOrderCount = esOfflineRefundOrderManageMapper.selectCount(refundOrderQuery);

    // sum
    LambdaEsQueryWrapper<EsOfflineRefundOrderManage> refundOrderSumQuery = buildRefundOrderWrapper(
        req);
    refundOrderSumQuery.sum(EsOfflineRefundOrderManage::getConsumerRefund);
    SearchResponse refundSumResponse = esOfflineRefundOrderManageMapper.search(refundOrderSumQuery);

    String refundSumField = FieldUtils.val(EsOfflineRefundOrderManage::getConsumerRefund);
    Aggregation refundSumAggregate = refundSumResponse.getAggregations()
        .get(String.format("%sSum", refundSumField));
    double refundOrderSumValue = ((ParsedSum) refundSumAggregate).getValue();

    ReconciliationRes refundData = new ReconciliationRes();
    refundData.setCount(refundOrderCount);
    refundData.setAmount(BigDecimal.valueOf(refundOrderSumValue));
    return refundData;
  }
}
