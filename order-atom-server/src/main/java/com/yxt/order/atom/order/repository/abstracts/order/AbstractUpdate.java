package com.yxt.order.atom.order.repository.abstracts.order;


import com.yxt.order.atom.sdk.online_order.order_info.dto.req.UpdateOrderOptionalReq;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:46
 * @email: <EMAIL>
 */
public abstract class AbstractUpdate<T> {

  protected UpdateOrderOptionalReq req;

  protected abstract Boolean canUpdate(); // notey 暂时不校验dataVersion

  protected abstract Integer update(T t);

  /**
   * 转换成目标对象T
   *
   * @return
   */
  protected abstract T convert();


  public Integer exec(UpdateOrderOptionalReq req) {

    init(req);

    if (!canUpdate()) {
      return 0;
    }

    return update(convert());
  }

  private void init(UpdateOrderOptionalReq req) {
    this.req = req;
  }


}
