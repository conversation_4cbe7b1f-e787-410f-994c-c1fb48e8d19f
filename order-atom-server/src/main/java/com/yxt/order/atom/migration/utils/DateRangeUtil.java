package com.yxt.order.atom.migration.utils;

import com.yxt.order.atom.common.utils.OrderDateUtils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import lombok.Data;

public class DateRangeUtil {

  @Data
  public static class StartEndDate {

    private String start;
    private String end;
  }

  /**
   * 生成指定日期的开始时间和结束时间
   *
   * @param dateStr 日期时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
   * @return 包含开始时间和结束时间的数组，[0]为开始时间，[1]为结束时间
   * @throws ParseException 如果日期解析失败
   */
  public static StartEndDate generateDayRange(String dateStr)  {
    try {
      SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      Date date = formatter.parse(dateStr);

      // 创建日历实例并设置为输入日期
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(date);

      // 设置时间为当天的00:00:00
      calendar.set(Calendar.HOUR_OF_DAY, 0);
      calendar.set(Calendar.MINUTE, 0);
      calendar.set(Calendar.SECOND, 0);
      calendar.set(Calendar.MILLISECOND, 0);
      Date startOfDay = calendar.getTime();

      // 设置时间为下一天的00:00:00
      calendar.add(Calendar.DAY_OF_MONTH, 1);
      Date startOfNextDay = calendar.getTime();

      StartEndDate startEndDate = new StartEndDate();
      startEndDate.setStart(OrderDateUtils.formatYYMMDD(startOfDay));
      startEndDate.setEnd(OrderDateUtils.formatYYMMDD(startOfNextDay));
      return startEndDate;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 使用示例
   */
  public static void main(String[] args) {
    try {
      String inputDate = "2025-02-16 22:50:17";
      StartEndDate startEndDate = generateDayRange(inputDate);

      System.out.println("输入日期: " + inputDate);
      System.out.println("当天开始时间: " + startEndDate.getStart());
      System.out.println("下一天开始时间: " + startEndDate.getEnd());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}