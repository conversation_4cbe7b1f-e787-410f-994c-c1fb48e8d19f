package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderMultiPayInfoDO;
import com.yxt.order.atom.order.mapper.OrderMultiPayInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class OrderMultiPayInfoDelete extends AbstractDelete {

  @Resource
  private OrderMultiPayInfoMapper orderMultiPayInfoMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getOrderPayInfo();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<OrderMultiPayInfoDO> query = new LambdaQueryWrapper<>();
    query.eq(OrderMultiPayInfoDO::getOrderNo, orderNo);
    return orderMultiPayInfoMapper.delete(query);
  }
}
