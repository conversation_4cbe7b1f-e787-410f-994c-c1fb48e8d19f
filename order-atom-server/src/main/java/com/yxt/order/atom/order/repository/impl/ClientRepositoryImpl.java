package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.DsOnlineClientDO;
import com.yxt.order.atom.order.mapper.DsOnlineClientMapper;
import com.yxt.order.atom.order.repository.ClientRepository;
import com.yxt.order.atom.sdk.online_order.client.req.OnlineClientQueryReqDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineClientResDto;
import com.yxt.order.types.DsConstants;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

@Repository
public class ClientRepositoryImpl implements ClientRepository {

  @Resource
  private DsOnlineClientMapper dsOnlineClientMapper;

  @DS(DsConstants.DB_ORDER_SLAVE)
  @Override
  public List<DsOnlineClientResDto> queryClient(OnlineClientQueryReqDto req) {

    List<DsOnlineClientDO> dsOnlineClientDOList = dsOnlineClientMapper.selectList(Wrappers.<DsOnlineClientDO>lambdaQuery()
        .eq(DsOnlineClientDO::getMerCode, req.getMerCode().getMerCode())
        .eq(DsOnlineClientDO::getPlatformCode, req.getPlatformCode().getThirdPlatformCodeEnum().getCode())
        .eq(StrUtil.isNotBlank(req.getServiceMode()), DsOnlineClientDO::getServiceMode, req.getServiceMode())
        .in(CollUtil.isNotEmpty(req.getOnlineClientCodeList()), DsOnlineClientDO::getOnlineClientCode, req.getOnlineClientCodeList())
    );
    return BeanUtil.copyToList(dsOnlineClientDOList, DsOnlineClientResDto.class);
  }
}
