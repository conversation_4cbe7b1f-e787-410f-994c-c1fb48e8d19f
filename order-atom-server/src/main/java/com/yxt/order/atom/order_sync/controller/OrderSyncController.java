package com.yxt.order.atom.order_sync.controller;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order_sync.service.OrderSyncInitErrorLogService;
import com.yxt.order.atom.order_sync.service.OrderSyncService;
import com.yxt.order.atom.sdk.common.order_world.OrderSyncInitErrorLogDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderSyncMappingDTO;
import com.yxt.order.atom.sdk.order_sync.OrderSyncApi;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncInitErrorLogRemoveReq;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncInitErrorLogSearchReq;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncMappingSearchReq;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OrderSyncController implements OrderSyncApi {

  @Autowired
  private OrderSyncService orderSyncService;

  @Autowired
  private OrderSyncInitErrorLogService orderSyncInitErrorLogService;

  @Override
  public ResponseBase<List<OrderSyncMappingDTO>> orderSyncMappingSearch(OrderSyncMappingSearchReq request) {
    return ResponseBase.success(orderSyncService.orderSyncMappingSearch(request));
  }

  @Override
  public ResponseBase<Void> orderSyncMappingSaveOrUpdate(OrderSyncMappingDTO request) {
    orderSyncService.orderSyncMappingSaveOrUpdate(request);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<List<OrderSyncInitErrorLogDTO>> orderSyncInitErrorLogSearch(OrderSyncInitErrorLogSearchReq request) {
    return ResponseBase.success(orderSyncInitErrorLogService.orderSyncInitErrorLogSearch(request));
  }

  @Override
  public ResponseBase<Void> orderSyncInitErrorLogSaveOrUpdate(OrderSyncInitErrorLogDTO request) {
    orderSyncInitErrorLogService.orderSyncInitErrorLogSaveOrUpdate(request);
    return ResponseBase.success();
  }

  @Override
  public ResponseBase<Void> orderSyncInitErrorLogRemove(OrderSyncInitErrorLogRemoveReq request) {
    orderSyncInitErrorLogService.orderSyncInitErrorLogRemove(request);
    return ResponseBase.success();
  }
}
