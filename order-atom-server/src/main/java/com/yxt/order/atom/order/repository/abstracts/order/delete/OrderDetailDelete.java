package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.mapper.OrderDetailMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class OrderDetailDelete extends AbstractDelete {

  @Resource
  private OrderDetailMapper orderDetailMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getOrderDetail();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<OrderDetailDO> query = new LambdaQueryWrapper<>();
    query.eq(OrderDetailDO::getOrderNo, orderNo);
    return orderDetailMapper.delete(query);
  }
}
