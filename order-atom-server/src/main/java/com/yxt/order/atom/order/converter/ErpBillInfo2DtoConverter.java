package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.SimpleOmsOrderInfoDTO;
import com.yxt.order.common.base_order_dto.ErpBillInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface ErpBillInfo2DtoConverter {

  ErpBillInfo2DtoConverter INSTANCE = Mappers.getMapper(ErpBillInfo2DtoConverter.class);

  ErpBillInfo toDto(ErpBillInfoDO obj);

}
