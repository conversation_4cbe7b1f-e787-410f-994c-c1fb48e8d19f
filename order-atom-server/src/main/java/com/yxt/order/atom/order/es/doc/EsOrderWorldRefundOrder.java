package com.yxt.order.atom.order.es.doc;

import cn.hutool.core.util.StrUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

@Data
@Settings(shardsNum = 18)
@IndexName(value = "es_order_world_refund_order", keepGlobalPrefix = true)
public class EsOrderWorldRefundOrder {


  @IndexId(type = IdType.CUSTOMIZE)
  private String id;

  /**
   * 退单对应的内部订单号(自己生成)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * 内部退款单号,自己生成
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundNo;

  /**
   * 会员id
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userId;

  /**
   * 会员编码(唯一值)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userCardNo;

  /**
   * 平台编码,HAIDIAN,内部定义
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdPlatformCode;

  /**
   * 第三方平台退款单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdRefundNo;

  /**
   * 第三方平台订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * 退款类型 PART-部分退款 ALL-全额退款
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String afterSaleScope;

  /**
   * 售后单类型 AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String afterSaleType;

  /**
   * 退单状态 REFUNDED 已退款
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundStatus;

  /**
   * 退单创单时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime created;

  /**
   * 退单申请时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime applyTime;

  /**
   * 退款金额
   */
  @IndexField(fieldType = FieldType.SCALED_FLOAT)
  private BigDecimal refundAmount;

  /**
   * 订单标记，通过空格分割，使用match匹配
   *
   * @see com.yxt.order.types.order.enums.OrderFlagEnum
   */
  @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.WHITESPACE, strategy = FieldStrategy.IGNORED)
  private String refundFlags;

  /**
   * 售后单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String afterSaleNo;

  /**
   * 交易场景 online:代表线上交易 ,offline:代表线下交易
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String transactionChannel;

  /**
   * 业务类型 O2O、B2C、B2B
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String businessType;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String launchOrganizationCode;
  
  /**
   * 发起人id，目前仅B2B有值
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String launchUserId;

  /**
   * 商户编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String merCode;

  /**
   * 分公司编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String companyCode;

  /**
   * 所属机构编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String organizationCode;

  /**
   * 是否起效 1-起效 -1-未起效
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Long valid;

  /**
   * 平台创建日
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String createdDay;

  /**
   * 系统创建时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime sysCreateTime;

  /**
   * 退单明细
   */
  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOrderWorldRefundOrderDetail.class)
  private List<EsOrderWorldRefundOrderDetail> detailList;

  public void fillRefundFlags(List<String> refundFlags) {
    this.refundFlags = StrUtil.join(" ", refundFlags);
  }
}
