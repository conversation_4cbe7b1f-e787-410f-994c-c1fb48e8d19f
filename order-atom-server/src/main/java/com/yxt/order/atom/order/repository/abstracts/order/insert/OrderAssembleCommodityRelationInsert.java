package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderAssembleCommodityRelationDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OrderAssembleCommodityRelationBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:57
 * @email: <EMAIL>
 */
@Component
public class OrderAssembleCommodityRelationInsert extends
    AbstractInsert<List<OrderAssembleCommodityRelationDO>> {

  @Resource
  private OrderAssembleCommodityRelationBatchRepository orderAssembleCommodityRelationBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderAssembleCommodityRelationList());
  }

  @Override
  protected Integer insert(List<OrderAssembleCommodityRelationDO> list) {
    return orderAssembleCommodityRelationBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderAssembleCommodityRelationDO> data() {
    return saveDataOptional.getOrderAssembleCommodityRelationList();
  }


}
