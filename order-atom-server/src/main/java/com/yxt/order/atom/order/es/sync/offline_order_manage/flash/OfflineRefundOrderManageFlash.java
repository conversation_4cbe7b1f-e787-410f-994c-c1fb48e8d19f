package com.yxt.order.atom.order.es.sync.offline_order_manage.flash;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.OfflineOrderManageScene;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.offline_order_manage.handler.OfflineRefundOrderManageHandler;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/4/1 10:55
 */
@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class OfflineRefundOrderManageFlash  extends
    AbstractFlash<OfflineRefundOrderDO, OfflineRefundOrder, OfflineOrderManageScene> {

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Value("${memberTransactionFlashLimit:2000}")
  private Integer memberTransactionFlashLimit;

  @Resource
  private OfflineRefundOrderManageHandler offlineRefundOrderManageHandler;


  @Override
  protected Long queryCursorStartId() {
    return offlineRefundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineRefundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(getFlashParam(),defaultLimit());
    return offlineRefundOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrder> assembleTargetData(List<OfflineRefundOrderDO> sourceList) {
    return sourceList.stream().map(DoToCanalDtoWrapper::getOfflineRefundOrder).collect(Collectors.toList());
  }

  @Override
  protected void flash(List<OfflineRefundOrder> offlineRefundOrders) {
    CanalOfflineRefundOrder canalOfflineRefundOrder = new CanalOfflineRefundOrder();
    canalOfflineRefundOrder.setData(offlineRefundOrders);
    offlineRefundOrderManageHandler.manualFlash(canalOfflineRefundOrder);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return memberTransactionFlashLimit;
  }
}
