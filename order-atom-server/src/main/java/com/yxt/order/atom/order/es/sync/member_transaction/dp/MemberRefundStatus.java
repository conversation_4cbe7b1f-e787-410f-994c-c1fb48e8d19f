package com.yxt.order.atom.order.es.sync.member_transaction.dp;

import lombok.Getter;

/**
 * @author: moatkon
 * @time: 2024/12/9 14:24
 */
@Getter
public enum MemberRefundStatus {
  //10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消
  AWAITING_RETURN_AMOUNT(10, "待退款"),
  AWAITING_RETURN_GOODS(20, "待退货"),
  COMPLETED(100, "已完成"),
  REFUSED(102, "已拒绝"),
  CANCELED(103, "已取消"),
  ;
  private final int status;
  private final String desc;

  MemberRefundStatus(int status, String desc) {
    this.status = status;
    this.desc = desc;
  }

  /**
   *
   * @param refund_order.state 10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消
   * @return MemberRefundStatus.status
   */
  public static Integer getByState(String state) {
    for (MemberRefundStatus value : values()) {
      if(String.valueOf(value.status).equals(state)){
        return value.status;
      }
    }
    return null;
  }
}
