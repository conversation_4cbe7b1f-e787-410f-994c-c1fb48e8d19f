package com.yxt.order.atom.order.es.sync.member_transaction.flash;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.MemberTransaction;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.member_transaction.handler.MemberOfflineRefundOrderTransactionHandler;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/13 10:22
 */
@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class MemberOfflineRefundOrderFlash extends
    AbstractFlash<OfflineRefundOrderDO, OfflineRefundOrder, MemberTransaction> {

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Value("${memberTransactionFlashLimit:2000}")
  private Integer memberTransactionFlashLimit;

  @Resource
  private MemberOfflineRefundOrderTransactionHandler memberOfflineRefundOrderTransactionHandler;


  @Override
  protected Long queryCursorStartId() {
    return offlineRefundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineRefundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(getFlashParam(),defaultLimit());
    return offlineRefundOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrder> assembleTargetData(List<OfflineRefundOrderDO> sourceList) {
    return sourceList.stream().map(DoToCanalDtoWrapper::getOfflineRefundOrder).collect(Collectors.toList());
  }

  @Override
  protected void flash(List<OfflineRefundOrder> offlineRefundOrders) {
    CanalOfflineRefundOrder canalOfflineRefundOrder = new CanalOfflineRefundOrder();
    canalOfflineRefundOrder.setData(offlineRefundOrders);
    memberOfflineRefundOrderTransactionHandler.manualFlash(canalOfflineRefundOrder);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return memberTransactionFlashLimit;
  }
}
