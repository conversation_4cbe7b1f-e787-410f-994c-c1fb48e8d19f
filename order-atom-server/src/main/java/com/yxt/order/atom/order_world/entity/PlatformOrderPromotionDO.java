package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 平台订单促销活动信息
 */
@Data
@TableName("platform_order_promotion")
public class PlatformOrderPromotionDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  private String thirdOrderNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 促销活动编码
   */
  private String promotionNo;

  /**
   * 子促销编码
   */
  private String subPromotionNo;

  /**
   * 促销类型
   */
  private String promotionType;

  /**
   * 促销金额
   */
  private BigDecimal promotionAmount;

  /**
   * 拓展字段
   */
  private String extendJson;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
