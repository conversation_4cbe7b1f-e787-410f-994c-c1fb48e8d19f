package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("import_export_task")
public class ImportExportTaskDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  private String taskNo;
  private String merCode = "500001";
  private String taskType;
  private String dataMappingClazz;
  private String paramJson;
  private String paramMappingClazz;
  private String state;
  private String note;
  private String downloadUrl;
  private String createdBy;
  private String updatedBy;
  private Date createdTime;
  private Date updatedTime;
  private Long version;
  private String fileName;
}