package com.yxt.order.atom.repair.problem_data_get;

import com.yxt.order.atom.repair.AbstractRepairDataBaseGet;
import com.yxt.order.types.repair.RepairScene;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/1/9 10:02
 */
@Component
public class HaiDianOnlineOrderAsOfflineOrderGet extends AbstractRepairDataBaseGet {

  @Override
  public RepairScene scene() {
    return RepairScene.ONLINE_ORDER_AS_OFFLINE_ORDER;
  }
}