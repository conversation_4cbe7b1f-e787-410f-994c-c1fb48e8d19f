package com.yxt.order.atom.migration.service;

import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月03日 16:02
 * @email: <EMAIL>
 */
@Component
public class MigrationPlatformCodeHandler {

//  @Resource
//  private InnerStoreDictionaryMapper innerStoreDictionaryMapper;


  public ThirdPlatformCodeEnum getPlatformCodeOtherOrderId(HanaOrderInfo hanaOrderInfo) {
    String orderIdStr = String.valueOf(hanaOrderInfo.getOtherOrderId());
    if (orderIdStr.length() == 16) { // 海典的单号固定是16位
      return ThirdPlatformCodeEnum.HAIDIAN;
    }
    return ThirdPlatformCodeEnum.KE_CHUAN;
  }

  /**
   * v2
   * 查不到，默认科传
   * 兜底，科传
   * @param storeCode
   * @return
   */
//  public ThirdPlatformCodeEnum getPlatformCodeByStoreCode(String storeCode) {
//    LambdaQueryWrapper<InnerStoreDictionaryDO> queryWrapper = new LambdaQueryWrapper<>();
//    queryWrapper.eq(InnerStoreDictionaryDO::getOrganizationCode, storeCode);
//    InnerStoreDictionaryDO innerStoreDictionaryDO = innerStoreDictionaryMapper.selectOne(
//        queryWrapper);
//    if (Objects.isNull(innerStoreDictionaryDO)) {
//      return ThirdPlatformCodeEnum.KE_CHUAN;
//    }
//
//    if (innerStoreDictionaryDO.getPosMode() == 1 || innerStoreDictionaryDO.getPosMode() == 2) {
//      return ThirdPlatformCodeEnum.HAIDIAN;
//    } else if (innerStoreDictionaryDO.getPosMode() == 3) {
//      return ThirdPlatformCodeEnum.KE_CHUAN;
//    }
//
//    return ThirdPlatformCodeEnum.KE_CHUAN;
//  }

//  @Resource
//  private MigrationThirdPlatformCodeConfig config;
//
//  /**
//   * v1
//   * 3月4日之后，攀枝花和重庆市H2； 6月30日之后，94家四川的门店是H2；
//   *
//   * @param schema
//   * @param created
//   * @return
//   */
//  public ThirdPlatformCodeEnum getPlatformCode(String schema, Date created, String storeCode) {
//
//    if (Objects.isNull(config) || config.isEmpty()) {
//      return ThirdPlatformCodeEnum.KE_CHUAN;
//    }
//
//    List<String> schemaList = config.getItemList().stream().map(Item::getSchema).distinct()
//        .collect(Collectors.toList());
//
////    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
////    sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
//
//    if (schemaList.contains(schema)) {
//      Item itemConfig = config.getItemConfig(schema);
//      // 指定时间
//      Date specificDate = OrderDateUtils.convertToBeijingTime(
//          CommonDateUtils.convert2Date(itemConfig.getSpecificDate()));
//
//      // 指定店铺
//      String storeCodes = itemConfig.getStoreCodes();
//      ArrayList<String> storeCodesList = Lists.newArrayList();
//      if (!StringUtils.isEmpty(storeCodes)) {
//        storeCodesList = Lists.newArrayList(storeCodes.split(","));
//      }
//      if (!CollectionUtils.isEmpty(storeCodesList) && !storeCodesList.contains(storeCode)) {
//        return ThirdPlatformCodeEnum.KE_CHUAN;
//      }
//
//      // 创单时间在指定时间(eg.10天前)之后
//      Date createdBeijing = OrderDateUtils.convertToBeijingTime(created);
//      if (DateUtil.compare(createdBeijing, specificDate) >= 0) {
//        return ThirdPlatformCodeEnum.HAIDIAN;
//      }
//
//    }
//
//    return ThirdPlatformCodeEnum.KE_CHUAN;
//  }

}
