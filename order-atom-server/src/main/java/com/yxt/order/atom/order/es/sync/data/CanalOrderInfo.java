package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo.Order;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:06
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalOrderInfo extends BaseCanalData<Order> {

  @Data
  public static class Order {

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("online_store_code")
    private String onlineStoreCode;
    
    @JsonProperty("organization_code")
    private String organizationCode;

    @JsonProperty("member_no")
    private String memberNo;

    @JsonProperty("order_state")
    private String orderState;

    @JsonProperty("third_order_no")
    private String thirdOrderNo;

    @JsonProperty("third_platform_code")
    private String thirdPlatformCode;

    @JsonProperty("created")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date created;

    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonProperty("service_mode")
    private String serviceMode;

    @JsonProperty("pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;


    @JsonProperty("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    @JsonProperty("deleted")
    private Long deleted; // 默认0-未删除
  }
}

