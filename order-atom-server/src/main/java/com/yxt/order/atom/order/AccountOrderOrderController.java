package com.yxt.order.atom.order;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.AccountOrderRepository;
import com.yxt.order.atom.sdk.common.data.AccountOrderDTO;
import com.yxt.order.atom.sdk.online_order.account.AccountOrderAtomCmdApi;
import com.yxt.order.atom.sdk.online_order.account.AccountOrderAtomQryApi;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountOrderReqDto;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountRefundOrderReqDto;
import com.yxt.order.types.order.OrderNo;
import com.yxt.starter.controller.AbstractController;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (m<PERSON>)
 * @date 2024年03月01日 14:44
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class AccountOrderOrderController extends AbstractController implements AccountOrderAtomCmdApi,
    AccountOrderAtomQryApi {

  @Resource
  private AccountOrderRepository accountOrderRepository;


  @Override
  public ResponseBase<Boolean> applyOrderAccount(ApplyAccountOrderReqDto reqDto) {
    return generateSuccess(accountOrderRepository.insertAccountOrder(reqDto));
  }

  @Override
  public ResponseBase<Boolean> applyRefundOrderAccount(ApplyAccountRefundOrderReqDto reqDto) {
//    return generateSuccess(accountOrderRepository.insertAccountRefundOrder(reqDto));
  return null;
  }



  @Override
  public ResponseBase<AccountOrderDTO> getAccountOrder(OrderNo orderNo) {
    return generateSuccess(accountOrderRepository.getAccountOrderByOrderNo(orderNo));
  }
}
