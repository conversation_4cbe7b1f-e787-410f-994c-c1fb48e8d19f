package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.CommodityExceptionOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.mapper.CommodityExceptionOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailMapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 15:41
 * @email: <EMAIL>
 */
@Repository
public class CommodityExceptionOrderBatchRepository  extends
    ServiceImpl<CommodityExceptionOrderMapper, CommodityExceptionOrderDO> {

}

