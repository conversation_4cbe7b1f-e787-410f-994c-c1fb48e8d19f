package com.yxt.order.atom.order.es.sync.org_order.model;

import cn.hutool.core.collection.CollUtil;
import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsOrgRefund;
import com.yxt.order.atom.order.es.doc.EsOrgRefundDetail;
import com.yxt.order.types.order.enums.RefundFlagEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrgRefundModel extends BaseEsIndexModel {

  /**
   * 系统订单号
   */
  private String orderNo;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 系统退单号
   */
  private String refundNo;

  /**
   * 三方退单号
   */
  private String thirdRefundNo;

  /**
   * 下单时间
   */
  private Date created;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 订单支付时间
   */
  private Date orderCreated;

  /**
   * 线上门店编码
   */
  private String storeCode;

  /**
   * 机构编码（线下实际发货门店）
   */
  private String orgCode;

  /**
   * 下单线上门店编码
   */
  private String sourceStoreCode;

  /**
   * 下单线下机构编码
   */
  private String sourceOrgCode;

  /**
   * 0, "待退款",20, "待退货",100, "已完成",102, "已拒绝",103, "已取消"
   */
  private Integer refundStatus;

  /**
   * 下账状态：20, "待下账" 99, "下账失败"  100, "已下账" 102, "已取消"
   */
  private String erpStatus;

  /**
   * 下账时间
   */
  private Date erpTime;

  /**
   * 退款单零售流水
   */
  private String erpRefundNo;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  private String orderSource;

  /**
   * HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、
   * PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康
   * 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、
   * JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传
   */
  private String platformCode;

  /**
   * 退款类型, PART-部分退款，ALL-全额退款,UNKNOWN-未知
   */
  private String refundType;

  /**
   * 售后单类型 REFUND-退款 、RETURN-退货
   */
  private String afterSaleType;

  /**
   * 退单标记
   */
  private List<RefundFlagEnum> refundFlags;

  /**
   * 下账金额
   */
  private BigDecimal refundBillAmount;

  /**
   * 退单明细
   */
  private List<OrgRefundDetailModel> detailList;

  /**
   * 会员编码(唯一值)
   */
  private String userCardNo;

  /**
   * 会员ID (心云)
   */
  private String userId;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   */
  private String storeType;

  /**
   * 服务模式 O2O B2C B2B
   */
  private String serviceMode;


  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  private Long deleted;


  public EsOrgRefund create() {
    EsOrgRefund esOrgRefund = new EsOrgRefund();
    if(CollUtil.isNotEmpty(this.refundFlags)){
      List<String> refundFlagList = this.refundFlags.stream().map(RefundFlagEnum::name).collect(Collectors.toList());
      esOrgRefund.setRefundFlags(refundFlagList);
    }
    esOrgRefund.setId(this.defineId());
    esOrgRefund.setOrderNo(this.orderNo);
    esOrgRefund.setThirdOrderNo(this.thirdOrderNo);
    esOrgRefund.setRefundNo(this.refundNo);
    esOrgRefund.setThirdRefundNo(this.thirdRefundNo);
    esOrgRefund.setCreated(this.created);
    esOrgRefund.setCreateTime(this.createTime);
    esOrgRefund.setOrderCreated(this.orderCreated);
    esOrgRefund.setStoreCode(this.getStoreCode());
    esOrgRefund.setOrgCode(this.orgCode);
    esOrgRefund.setSourceStoreCode(this.sourceStoreCode);
    esOrgRefund.setSourceOrgCode(this.sourceOrgCode);
    esOrgRefund.setRefundStatus(this.refundStatus);
    esOrgRefund.setErpStatus(this.erpStatus);
    esOrgRefund.setErpTime(this.erpTime);
    esOrgRefund.setErpRefundNo(this.erpRefundNo);
    esOrgRefund.setOrderSource(this.orderSource);
    esOrgRefund.setPlatformCode(this.platformCode);
    esOrgRefund.setRefundType(this.refundType);
    esOrgRefund.setAfterSaleType(this.afterSaleType);
    esOrgRefund.setRefundBillAmount(this.refundBillAmount);
    if(CollUtil.isNotEmpty(this.getDetailList())){
      List<EsOrgRefundDetail> esRefundDetailList = this.getDetailList().stream().map(detail -> {
        EsOrgRefundDetail orderDetail = new EsOrgRefundDetail();
        orderDetail.setOrderDetailId(detail.getOrderDetailId());
        orderDetail.setErpCode(detail.getErpCode());
        orderDetail.setItemName(detail.getItemName());
        return orderDetail;
      }).collect(Collectors.toList());
      esOrgRefund.setDetailList(esRefundDetailList);
    }
    esOrgRefund.setUserCardNo(this.userCardNo);
    esOrgRefund.setUserId(this.userId);
    esOrgRefund.setStoreType(this.storeType);
    esOrgRefund.setServiceMode(this.serviceMode);
    esOrgRefund.setDeleted(this.deleted);
    return esOrgRefund;
  }

  public String routeKey(){
    return this.orgCode;
  }

  public String defineId(){
    return this.getPlatformCode() + "-" + this.getRefundNo();
  }

  public void addRefundFlags(RefundFlagEnum refundFlag) {
    if (CollUtil.isEmpty(this.refundFlags)) {
      this.refundFlags = new ArrayList<>();
    }
    this.refundFlags.add(refundFlag);
  }
}
