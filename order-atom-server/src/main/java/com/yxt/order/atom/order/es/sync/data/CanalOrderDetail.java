package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOrderDetail.OrderDetail;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月22日 16:22
 * @email: <EMAIL>
 */
public class CanalOrderDetail extends BaseCanalData<OrderDetail> {

  @Data
  public static class OrderDetail {

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("oms_order_no")
    private Long omsOrderNo;

  }
}