package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("offline_refund_order_detail")
public class OfflineRefundOrderDetailDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  private String orderNo;

  private String refundNo;

  private String refundDetailNo;

  private String rowNo;

  private String platformSkuId;

  private String erpCode;

  private String erpName;

  private BigDecimal refundCount;

  private String refundStatus;

  private String giftType;

  private BigDecimal originalPrice;

  private BigDecimal price;

  private BigDecimal commodityCostPrice;

  private BigDecimal totalAmount;

  private BigDecimal discountShare;

  private BigDecimal discountAmount;

  private BigDecimal billPrice;

  private BigDecimal billAmount;

  private String createdBy;

  private String updatedBy;

  private Date createdTime;

  private Date updatedTime;

  private Long version;

  // 是否参加促销的标识, true,false
  private String isOnPromotion;

  private String detachable;


  /**
   * 商品五级分类编码
   */
  private String fiveClass;

  /**
   * 商品五级分类Name
   */
  private String fiveClassName;

  /**
   * 生产商
   */
  private String manufacture;
  /**
   * 商品规格
   */
  private String commoditySpec;

  private String mainPic;

  private String salerId;

  private String salerName;

  // 过账含税成本价
  private BigDecimal postedCostWithTaxPrice;

  // 过账成本价
  private BigDecimal postedCostPrice;

  // 过账税率
  private BigDecimal postedCostTax;

  @TableField(exist = false)
  private List<OfflineRefundOrderDetailTraceDO> offlineRefundOrderDetailTraceDOList;

  @TableField(exist = false)
  private List<OfflineRefundOrderDetailPickDO>  offlineRefundOrderDetailPickDOList;

  public String businessUk(){
    return String.format("%s_%s_%s",rowNo,erpCode,refundCount.setScale(6, RoundingMode.UNNECESSARY).toPlainString());
  }
  public void markUpdateBy(String updatedBy){
    if(StringUtils.isEmpty(this.updatedBy)){
      this.updatedBy = updatedBy;
    }else {
      this.updatedBy = String.format("%s,%s",this.updatedBy,updatedBy);
    }
  }
}