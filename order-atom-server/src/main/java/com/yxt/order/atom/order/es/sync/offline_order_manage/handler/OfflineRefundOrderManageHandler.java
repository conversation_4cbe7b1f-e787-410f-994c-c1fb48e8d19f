package com.yxt.order.atom.order.es.sync.offline_order_manage.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_REFUND_ORDER_REGEX;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.offline_order_manage.model.OfflineRefundOrderManageModel;
import com.yxt.order.atom.order.es.sync.offline_order_manage.model.OfflineRefundOrderManageModel.OfflineRefundOrderManageDetailModel;
import com.yxt.order.atom.order.mapper.dto.OrganizationInfoDto;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/4/1 10:54
 */
@Component
public class OfflineRefundOrderManageHandler extends
    AbstractCanalHandler<CanalOfflineRefundOrder, OfflineRefundOrderManageModel> {


  @Resource
  protected SyncComponent syncComponent;

  public OfflineRefundOrderManageHandler() {
    super(CanalOfflineRefundOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD_OFFLINE.equals(database) && Table.tableRegex(OFFLINE_REFUND_ORDER_REGEX,
        table);
  }

  @Override
  protected List<OfflineRefundOrderManageModel> assemble() {
    List<OfflineRefundOrder> offlineRefundOrderList = getData().getData();
    if (CollectionUtils.isEmpty(offlineRefundOrderList)) {
      return Lists.newArrayList();
    }
    return offlineRefundOrderList.stream().filter(this::efficientData).map(offlineRefundOrder -> {
      OfflineRefundOrderManageModel memberRefundOrderModel = new OfflineRefundOrderManageModel();
      memberRefundOrderModel.setRefundNo(offlineRefundOrder.getRefundNo());
      memberRefundOrderModel.setOrderNo(offlineRefundOrder.getOrderNo());
      memberRefundOrderModel.setThirdPlatformCode(offlineRefundOrder.getThirdPlatformCode());
      memberRefundOrderModel.setThirdOrderNo(offlineRefundOrder.getThirdOrderNo());
      memberRefundOrderModel.setThirdRefundNo(offlineRefundOrder.getThirdRefundNo());
      memberRefundOrderModel.setRefundType(offlineRefundOrder.getRefundType());
      memberRefundOrderModel.setAfterSaleType(offlineRefundOrder.getAfterSaleType());
      OrganizationInfoDto organization = syncComponent.getOrganizationByRefundNo(
          offlineRefundOrder.getRefundNo(), offlineRefundOrder.getNeedRoute());
      if (Objects.nonNull(organization)) {
        memberRefundOrderModel.setStoreDirectJoinType(organization.getStoreType());
        memberRefundOrderModel.setCompanyCode(organization.getCompanyCode());
      }

      memberRefundOrderModel.setStoreCode(offlineRefundOrder.getStoreCode());
      memberRefundOrderModel.setCreated(offlineRefundOrder.getCreated());
      memberRefundOrderModel.setConsumerRefund(offlineRefundOrder.getConsumerRefund());

      List<OfflineRefundOrderDetailDO> refundDetailList = syncComponent.getOfflineRefundDetailByRefundNo(
          offlineRefundOrder.getRefundNo(), offlineRefundOrder.getNeedRoute());
      if (!CollectionUtils.isEmpty(refundDetailList)) {
        memberRefundOrderModel.setOfflineRefundOrderManageDetailModelList(
            refundDetailList.stream().map(item -> {
              OfflineRefundOrderManageDetailModel refundOrderDetailModel = new OfflineRefundOrderManageDetailModel();
              refundOrderDetailModel.setErpCode(item.getErpCode());
              return refundOrderDetailModel;
            }).collect(Collectors.toList()));
      }
      return memberRefundOrderModel;
    }).collect(Collectors.toList());
  }

  public Boolean efficientData(OfflineRefundOrder offlineRefundOrder) {
    return !offlineRefundOrder.migrateRefundOrder() && ThirdPlatformCodeEnum.isValid(
        offlineRefundOrder.getThirdPlatformCode());
  }
}
