package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.mapper.ErpRefundInfoMapper;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import org.springframework.stereotype.Repository;


@Repository
public class ErpRefundBatchRepository extends
    ServiceImpl<ErpRefundInfoMapper, ErpRefundInfoDO> {

}

