package com.yxt.order.atom.order;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.entity.ImportExportTaskDO;
import com.yxt.order.atom.order.repository.ImportExportTaskRepository;
import com.yxt.order.atom.sdk.imextask.ImportExportAtomApi;
import com.yxt.order.atom.sdk.imextask.ImportExportTaskRes;
import com.yxt.order.atom.sdk.imextask.QueryImportExportTaskReq;
import com.yxt.order.atom.sdk.imextask.SaveImportExportTaskReq;
import com.yxt.starter.controller.AbstractController;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: moatkon
 * @time: 2025/4/3 10:51
 */
@RestController
@Slf4j
public class ImportExportController extends AbstractController implements ImportExportAtomApi {

  @Resource
  private ImportExportTaskRepository importExportTaskRepository;


  @Override
  public ResponseBase<Boolean> save(SaveImportExportTaskReq req) {
    ImportExportTaskDO taskDO = importExportTaskRepository.queryTask(req.getTaskNo());
    if (Objects.isNull(taskDO)) {
      ImportExportTaskDO create = new ImportExportTaskDO();
      BeanUtils.copyProperties(req, create);

      create.setCreatedTime(new Date());
      return generateSuccess(importExportTaskRepository.insert(create));
    } else {
      ImportExportTaskDO update = new ImportExportTaskDO();
      BeanUtils.copyProperties(req, update);

      update.setUpdatedTime(new Date());

      return generateSuccess(importExportTaskRepository.update(update));
    }

  }

  @Override
  public ResponseBase<PageDTO<ImportExportTaskRes>> list(
      QueryImportExportTaskReq queryImportExportTaskReq) {
    return generateSuccess(importExportTaskRepository.list(queryImportExportTaskReq));
  }
}
