package com.yxt.order.atom.order;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.OmsOrderRepository;
import com.yxt.order.atom.sdk.online_order.oms_order_info.OmsOrderAtomQryApi;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.OmsOrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.SimpleOmsOrderInfoDTO;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.req.OmsOrderInfoQryReqDto;
import com.yxt.order.types.order.OrderNo;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月01日 14:44
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class OnlineOmsOrderController extends AbstractController implements
    OmsOrderAtomQryApi {

  @Resource
  private OmsOrderRepository omsOrderRepository;

  @Override
  public ResponseBase<List<SimpleOmsOrderInfoDTO>> getSimpleOmsOrderList(OrderNo orderno) {
    return generateSuccess(omsOrderRepository.querySimpleOmsOrderList(orderno));
  }


  @Override
  public ResponseBase<OmsOrderInfoResDto> getOmsOrderInfo(OmsOrderInfoQryReqDto orderNoDto) {
    return generateSuccess(omsOrderRepository.getOmsOrderInfo(orderNoDto));
  }
}
