package com.yxt.order.atom.order.es;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.COMMON_BUSINESS_POOL;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.configration.AtomExceptionUtil;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.dto.SimpleOfflineOrderDetailDto;
import com.yxt.order.atom.dto.SimpleOfflineRefundOrderDetailDto;
import com.yxt.order.atom.dto.SimpleOfflineRefundOrderDetailDto.AppendOfflineOrderInfo;
import com.yxt.order.atom.dto.SimpleOfflineRefundOrderDetailDto.OfflineOrderDetailPickEnhance;
import com.yxt.order.atom.job.compensate.detail_flash_five_class.FlashDetailFiveClass;
import com.yxt.order.atom.open.sdk.domain.es_order.EsOrderApi;
import com.yxt.order.atom.open.sdk.req.es_order.EsOrderQueryReqDTO;
import com.yxt.order.atom.open.sdk.res.es_order.EsOrderResDTO;
import com.yxt.order.atom.open.sdk.res.es_order.EsOrderUserIdResDTO;
import com.yxt.order.atom.order.entity.AfterSaleOrderDO;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.OfflineOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailTraceDO;
import com.yxt.order.atom.order.entity.OfflineOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailTraceDO;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.OrderPayInfoDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.es.doc.EsMemberOrder;
import com.yxt.order.atom.order.es.doc.EsMemberOrderDetail;
import com.yxt.order.atom.order.es.doc.EsMemberRefundOrder;
import com.yxt.order.atom.order.es.doc.EsMemberRefundOrderDetail;
import com.yxt.order.atom.order.es.doc.EsOmsOrderInfo;
import com.yxt.order.atom.order.es.doc.EsOrder;
import com.yxt.order.atom.order.es.doc.EsOrderItem;
import com.yxt.order.atom.order.es.mapper.EsMemberOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsMemberRefundOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsOmsOrderInfoMapper;
import com.yxt.order.atom.order.es.mapper.EsOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.AbstractFlash.CustomData;
import com.yxt.order.atom.order.es.sync.OmsOrderTransaction;
import com.yxt.order.atom.order.es.sync.SupportChronicDisease;
import com.yxt.order.atom.order.es.sync.es_order.flash.EsOrderFlashOrderInfo;
import com.yxt.order.atom.order.es.sync.es_order.flash.EsOrderFlashRefundOrder;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberOrderSource;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberOrderStatus;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberRefundStatus;
import com.yxt.order.atom.order.es.sync.member_transaction.flash.FlashEsMemberOrderDataController;
import com.yxt.order.atom.order.es.wrapper.EsQueryBuilder;
import com.yxt.order.atom.order.mapper.OmsOrderInfoMapper;
import com.yxt.order.atom.order.mapper.SyncEsMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.order.repository.OrderRepository;
import com.yxt.order.atom.sdk.order_info.OrderAtomEsApi;
import com.yxt.order.atom.sdk.order_info.req.FlashB2cDataToEsReq;
import com.yxt.order.atom.sdk.order_info.req.FlashDataToEsReq;
import com.yxt.order.atom.sdk.order_info.req.MemberOrderDetailReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberOrderListReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberOrderSimpleReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberRefundOrderDetailReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberRefundOrderListReqDto;
import com.yxt.order.atom.sdk.order_info.req.MemberRefundOrderSimpleReqDto;
import com.yxt.order.atom.sdk.order_info.req.OrderListQueryDto;
import com.yxt.order.atom.sdk.order_info.req.OrderPageOtherReqDto;
import com.yxt.order.atom.sdk.order_info.req.OrderPageReqDto;
import com.yxt.order.atom.sdk.order_info.res.EsOmsOrderData;
import com.yxt.order.atom.sdk.order_info.res.MemberOrderDetailDto;
import com.yxt.order.atom.sdk.order_info.res.MemberOrderResDto;
import com.yxt.order.atom.sdk.order_info.res.MemberOrderSimpleResDto;
import com.yxt.order.atom.sdk.order_info.res.MemberRefundOrderDetailDto;
import com.yxt.order.atom.sdk.order_info.res.MemberRefundOrderResDto;
import com.yxt.order.atom.sdk.order_info.res.MemberRefundOrderSimpleResDto;
import com.yxt.order.atom.sdk.order_info.res.PickInfo;
import com.yxt.order.common.DsErrorType;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import com.yxt.order.types.offline.enums.OfflineOrderStateEnum;
import com.yxt.order.types.offline.enums.RefundStateEnum;
import com.yxt.order.types.order.enums.OrderDetailStatusEnum;
import com.yxt.starter.controller.AbstractController;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.biz.OrderByParam;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.toolkit.FieldUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月07日 16:31
 * @email: <EMAIL>
 */
@RestController
@Slf4j
public class EsOrderController extends AbstractController implements OrderAtomEsApi, EsOrderApi {
  private final String esMemberOrderItemField = FieldUtils.val(EsMemberOrder::getEsMemberOrderDetailList);
  private final String esMemberRefundOrderItemField = FieldUtils.val(EsMemberRefundOrder::getEsMemberRefundOrderDetailList);


  @Value("${validateEsQueryDays:31}")
  private Integer validateEsQueryDays; // 间隔天数

  @Resource
  private EsOrderMapper esOrderMapper;

  @Resource
  private EsOmsOrderInfoMapper esOmsOrderInfoMapper;


  @Value("${excludeOnlineOrderFlashHandlerOnOff:false}")
  private Boolean excludeOnlineOrderFlashHandlerOnOff;

  @Resource
  private List<AbstractFlash<?, ?, SupportChronicDisease>> abstractEsOrderFlashDataList;
  @Resource
  private List<AbstractFlash<?, ?, OmsOrderTransaction>> omsOrderAbstractFlashList;

  @Resource
  private List<AbstractFlash<?, ?, FlashDetailFiveClass>> flashDetailFiveClassList;

  @Resource
  private EsMemberOrderMapper esMemberOrderMapper;

  @Resource
  private EsMemberRefundOrderMapper esMemberRefundOrderMapper;

  @Resource
  private OrderRepository orderRepository;

  @Resource
  private OmsOrderInfoMapper omsOrderInfoMapper;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Resource
  private SyncEsMapper syncEsMapper;

  @Qualifier(COMMON_BUSINESS_POOL)
  @Resource
  private ThreadPoolExecutor commonBusinessPool;

  @Override
  public ResponseBase<Boolean> flashDataToEs(FlashDataToEsReq flashDataToEsReq) {
    Date startDate = flashDataToEsReq.getStartDate();
    Date endDate = flashDataToEsReq.getEndDate();
    List<String> noList = flashDataToEsReq.getNoList();
    List<String> shardingValueList = flashDataToEsReq.getShardingValueList();

    for (AbstractFlash<?, ?, SupportChronicDisease> abstractEsOrderFlashData : abstractEsOrderFlashDataList) {

      // 排除线上订单刷数处理器
      Set<String> clazzNameSet = Sets.newHashSet(abstractEsOrderFlashData.getClass().getName(),
          abstractEsOrderFlashData.getClass().getSuperclass().getName());
      boolean exclude = clazzNameSet.contains(EsOrderFlashOrderInfo.class.getName())
          || clazzNameSet.contains(EsOrderFlashRefundOrder.class.getName());
      if(excludeOnlineOrderFlashHandlerOnOff && exclude){
        continue;
      }

      commonBusinessPool.submit(() -> {
        try {
          log.info("flush task running,{},req:{}", abstractEsOrderFlashData.getClass().getName(),
              JsonUtils.toJson(flashDataToEsReq));

          FlashParam flashParam = new FlashParam();
          flashParam.setStart(startDate);
          flashParam.setEnd(endDate);
          flashParam.setNoList(noList);
          flashParam.setMonitorKey(flashDataToEsReq.getMonitorKey());
          if(!CollectionUtils.isEmpty(shardingValueList)){
            CustomData customData = new CustomData();
            customData.setShardingValueList(shardingValueList);
            abstractEsOrderFlashData.customData(customData);
          }
          abstractEsOrderFlashData.startFlush(flashParam);
          log.info("flush task done,{},req:{}", abstractEsOrderFlashData.getClass().getName(),
              JsonUtils.toJson(flashParam));
        } catch (Exception e) {
          log.error("SupportChronicDisease flash error",e);
        }
      });
    }

    return generateSuccess(Boolean.TRUE);
  }

  @Override
  public ResponseBase<Boolean> omsOrderFlashDataToEs(FlashB2cDataToEsReq flashDataToEsReq) {
    Date startDate = flashDataToEsReq.getStartDate();
    Date endDate = flashDataToEsReq.getEndDate();
    List<String> noList = flashDataToEsReq.getNoList();
    for (AbstractFlash<?, ?, OmsOrderTransaction> abstractFlash : omsOrderAbstractFlashList) {
      commonBusinessPool.submit(() -> {
        log.info("flush task running,{},req:{}", abstractFlash.getClass().getName(),
                JsonUtils.toJson(flashDataToEsReq));
        FlashParam queryDto = new FlashParam();
        queryDto.setStart(startDate);
        queryDto.setEnd(endDate);
        queryDto.setNoList(noList);
        queryDto.setMonitorKey(flashDataToEsReq.getMonitorKey());
        abstractFlash.startFlush(queryDto);
        log.info("flush task done,{},req:{}", abstractFlash.getClass().getName(),
                JsonUtils.toJson(queryDto));
      });
    }

    return generateSuccess(Boolean.TRUE);
  }

  @Override
  public ResponseBase<Boolean> esOrderCreateIndex() {
    return generateSuccess(esOmsOrderInfoMapper.createIndex());
  }

  @Override
  public ResponseBase<Boolean> memberOrderFlashDataToEs(FlashDataToEsReq flashDataToEsReq) {
    throw new RuntimeException(String.format("接口已经迁移至 %s", FlashEsMemberOrderDataController.URL));
  }

  @Override
  public ResponseBase<Boolean> flashFiveClassMiss(FlashDataToEsReq flashDataToEsReq) {
    Date startDate = flashDataToEsReq.getStartDate();
    Date endDate = flashDataToEsReq.getEndDate();

    for (AbstractFlash<?, ?, FlashDetailFiveClass> abstractFlash : flashDetailFiveClassList) {
      commonBusinessPool.submit(() -> {
        try {
          log.info("FlashDetailFiveClass task running,{},req:{}", abstractFlash.getClass().getName(),
              JsonUtils.toJson(flashDataToEsReq));
          FlashParam queryDto = new FlashParam();
          queryDto.setStart(startDate);
          queryDto.setEnd(endDate);
          queryDto.setMonitorKey(flashDataToEsReq.getMonitorKey());
          abstractFlash.startFlush(queryDto);
          log.info("FlashDetailFiveClass task done,{},req:{}", abstractFlash.getClass().getName(),
              JsonUtils.toJson(queryDto));
        } catch (Exception e) {
          log.error("FlashDetailFiveClass flash error",e);
        }
      });
    }

    return generateSuccess(Boolean.TRUE);
  }

  @Override
  public ResponseBase<PageDTO<EsOrderResDTO>> pageQuery(EsOrderQueryReqDTO reqDTO) {
    LambdaEsQueryWrapper<EsOrder> query = baseEsQuery(reqDTO);

    EsPageInfo<EsOrder> esOrderEsPageInfo = esOrderMapper.pageQuery(query, reqDTO.getCurrentPage(),
        reqDTO.getPageSize());
    long total = esOrderEsPageInfo.getTotal();
    if (0 == total) {
      return generateSuccess(new PageDTO<>());
    }

    List<EsOrderResDTO> esOrderResDTOList = esOrderEsPageInfo.getList().stream().map(esOrder -> {
      EsOrderResDTO esOrderResDTO = new EsOrderResDTO();
      esOrderResDTO.setOrganizationCode(esOrder.getOrganizationCode());
      esOrderResDTO.setOnlineStoreCode(esOrder.getOnlineStoreCode());
      esOrderResDTO.setUserId(esOrder.getUserId());
      esOrderResDTO.setEsOrderType(esOrder.getEsOrderType());
      if (EsOrderType.ORDER.equals(esOrder.getEsOrderType())) {
        esOrderResDTO.setOrderNo(esOrder.getOrderNumber());
      }

      if (EsOrderType.REFUND.equals(esOrder.getEsOrderType())) {
        esOrderResDTO.setRefundNo(esOrder.getOrderNumber());
        // 退单,返回对应的正单号
        esOrderResDTO.setOrderNo(esOrder.getOrderNo());
      }
      esOrderResDTO.setServiceMode(esOrder.getServiceMode());
      esOrderResDTO.setEsOrderStatus(esOrder.getEsOrderStatus());
      esOrderResDTO.setPayTime(esOrder.getPayTime());
      esOrderResDTO.setThirdOrderNo(esOrder.getThirdOrderNo());
      esOrderResDTO.setThirdRefundNo(esOrder.getThirdRefundNo());
      esOrderResDTO.setPlatformCode(esOrder.getPlatformCode());
      esOrderResDTO.setCreateTime(esOrder.getCreateTime());
      esOrderResDTO.setCompleteTime(esOrder.getCompleteTime());

      List<EsOrderResDTO.EsOrderItem> esOrderItemList = esOrder.getEsOrderItemList().stream()
          .map(item -> {
            EsOrderResDTO.EsOrderItem esItem = new EsOrderResDTO.EsOrderItem();
            esItem.setCommodityCode(item.getCommodityCode());
            esItem.setCommodityName(item.getCommodityName());
            esItem.setCommodityCount(item.getCommodityCount());
            esItem.setFiveClass(item.getFiveClass());
            return esItem;
          }).collect(Collectors.toList());

      esOrderResDTO.setEsOrderItemList(esOrderItemList);
      return esOrderResDTO;
    }).collect(Collectors.toList());

    // 继续处理响应数据,主要是处理从索引上无法直接获取的一些值
    handleChronicDiseaseData(esOrderResDTOList);

    PageDTO<EsOrderResDTO> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(total);
    pageDTO.setTotalPage((long) esOrderEsPageInfo.getPages());
    pageDTO.setData(esOrderResDTOList);
    pageDTO.setCurrentPage((long) reqDTO.getCurrentPage());
    pageDTO.setPageSize((long) reqDTO.getPageSize());
    return generateSuccess(pageDTO);
  }

  private void handleChronicDiseaseData(List<EsOrderResDTO> esOrderResDTOList) {
    esOrderResDTOList.stream().collect(Collectors.groupingBy(EsOrderResDTO::getEsOrderType)).forEach((esOrderType, esOrderResDTOS) -> {

      // 根据服务模式划分
      Map<EsServiceMode, List<EsOrderResDTO>> serviceModeListMap = esOrderResDTOS.stream()
          .collect(Collectors.groupingBy(EsOrderResDTO::getServiceMode));

      // 区分是正单、退单
      if(EsOrderType.ORDER.equals(esOrderType)){
        handleChronicDiseaseOrderData(serviceModeListMap);
      }else if(EsOrderType.REFUND.equals(esOrderType)){
        handleChronicDiseaseRefundOrderData(serviceModeListMap);
      }
    });
  }

  private void handleChronicDiseaseRefundOrderData(Map<EsServiceMode, List<EsOrderResDTO>> refundMapByServiceCode) {
    refundMapByServiceCode.forEach((esServiceMode, esOrderResDTOS) -> {
      List<String> refundNoList = esOrderResDTOS.stream().map(EsOrderResDTO::getRefundNo)
          .distinct()
          .collect(Collectors.toList());

      if(EsServiceMode.POS.equals(esServiceMode)){
        List<OfflineRefundOrderDetailDO> offlineRefundOrderDetailList = offlineOrderRepository.queryBatchRefundDetail(refundNoList);
        Map<String, OfflineRefundOrderDetailDO> offlineRefundDetailMap = offlineRefundOrderDetailList.stream()
            .collect(
                Collectors.toMap(OfflineRefundOrderDetailDO::getErpCode, v -> v, (v1, v2) -> v1));
        esOrderResDTOS.stream()
            .map(EsOrderResDTO::getEsOrderItemList)
            .filter(esOrderItemList -> !CollectionUtils.isEmpty(esOrderItemList))
            .flatMap(Collection::stream)
            .forEach(esOrderItem -> {
              Optional.ofNullable(offlineRefundDetailMap.get(esOrderItem.getCommodityCode())).ifPresent(item->{
                esOrderItem.setMainPic(item.getMainPic());
                esOrderItem.setActualAmount(item.getTotalAmount());
              });
            });
      }else {
        List<RefundDetailDO> refundDetailList = orderRepository.queryRefundDetailList(refundNoList);
        Map<String, RefundDetailDO> refundDetailMap = refundDetailList.stream()
            .collect(Collectors.toMap(RefundDetailDO::getErpCode, v -> v, (v1, v2) -> v1));
        esOrderResDTOS.stream()
            .map(EsOrderResDTO::getEsOrderItemList)
            .filter(esOrderItemList -> !CollectionUtils.isEmpty(esOrderItemList))
            .flatMap(Collection::stream)
            .forEach(esOrderItem -> {
              Optional.ofNullable(refundDetailMap.get(esOrderItem.getCommodityCode())).ifPresent(item->{
                esOrderItem.setMainPic(item.getMainPic());
                esOrderItem.setActualAmount(item.getActualNetAmount());
              });
            });
      }
    });
  }

  private void handleChronicDiseaseOrderData(Map<EsServiceMode, List<EsOrderResDTO>> orderMapByServiceCode) {
    orderMapByServiceCode.forEach((esServiceMode, esOrderResDTOS) -> {
      List<String> orderNoList = esOrderResDTOS.stream().map(EsOrderResDTO::getOrderNo)
          .distinct()
          .collect(Collectors.toList());

      if(EsServiceMode.POS.equals(esServiceMode)){
        List<OfflineOrderDetailDO> offlineOrderDetailList = offlineOrderRepository.queryBatchOrderDetail(orderNoList);
        Map<String, OfflineOrderDetailDO> offlineDetailMap = offlineOrderDetailList.stream()
            .collect(
                Collectors.toMap(OfflineOrderDetailDO::getErpCode, v -> v, (v1, v2) -> v1));
        esOrderResDTOS.stream()
            .map(EsOrderResDTO::getEsOrderItemList)
            .filter(esOrderItemList -> !CollectionUtils.isEmpty(esOrderItemList))
            .flatMap(Collection::stream)
            .forEach(esOrderItem -> {
              Optional.ofNullable(offlineDetailMap.get(esOrderItem.getCommodityCode())).ifPresent(item->{
                esOrderItem.setMainPic(item.getMainPic());
                esOrderItem.setActualAmount(item.getTotalAmount());
              });
            });
      }else {
        List<OrderDetailDO> orderDetailList = orderRepository.queryOrderDetailList(orderNoList.stream().map(Long::valueOf).collect(
            Collectors.toList()));
        Map<String, OrderDetailDO> orderDetailMap = orderDetailList.stream()
            .collect(Collectors.toMap(OrderDetailDO::getErpCode, v -> v, (v1, v2) -> v1));
        esOrderResDTOS.stream()
            .map(EsOrderResDTO::getEsOrderItemList)
            .filter(esOrderItemList -> !CollectionUtils.isEmpty(esOrderItemList))
            .flatMap(Collection::stream)
            .forEach(esOrderItem -> {
              Optional.ofNullable(orderDetailMap.get(esOrderItem.getCommodityCode())).ifPresent(item->{
                esOrderItem.setMainPic(item.getMainPic());
                esOrderItem.setActualAmount(item.getActualNetAmount());
              });
            });
      }
    });
  }




  private LambdaEsQueryWrapper<EsOrder> baseEsQuery(
      EsOrderQueryReqDTO reqDTO) {

    validateEsQuery(reqDTO);
    String itemListEsField = FieldUtils.val(EsOrder::getEsOrderItemList);

    LambdaEsQueryWrapper<EsOrder> query = new LambdaEsQueryWrapper<>();
    query.eq(StringUtils.isNotEmpty(reqDTO.getOnlineStoreCode()), EsOrder::getOnlineStoreCode,
        reqDTO.getOnlineStoreCode());
    query.eq(StringUtils.isNotEmpty(reqDTO.getOrganizationCode()), EsOrder::getOrganizationCode,
        reqDTO.getOrganizationCode());


    List<String> commodityCodeList = reqDTO.getCommodityCodeList();
    if (!CollectionUtils.isEmpty(commodityCodeList)) {
      query.nested(itemListEsField,
          item -> item.in(
              String.format("%s.%s.keyword", itemListEsField,
                  FieldUtils.val(EsOrderItem::getCommodityCode)), commodityCodeList));
    }

    if(!StringUtils.isEmpty(reqDTO.getCommodityName())){
      query.nested(itemListEsField,
          item -> item.like(
              String.format("%s.%s.keyword", itemListEsField,
                  FieldUtils.val(EsOrderItem::getCommodityName)), reqDTO.getCommodityName()));
    }
    List<String> fiveClassList = reqDTO.getFiveClassList();
    if (!CollectionUtils.isEmpty(fiveClassList)) {
      query.nested(itemListEsField,
          item -> item.in(
              String.format("%s.%s.keyword", itemListEsField,
                  FieldUtils.val(EsOrderItem::getFiveClass)), fiveClassList));
    }

    query.ge(Objects.nonNull(reqDTO.getStartDate()), EsOrder::getCreateTime, reqDTO.getStartDate());
    query.le(Objects.nonNull(reqDTO.getEndDate()), EsOrder::getCreateTime, reqDTO.getEndDate());
    if (!CollectionUtils.isEmpty(reqDTO.getEsServiceModeList())) {
      query.in(EsOrder::getServiceMode,
          reqDTO.getEsServiceModeList().stream().map(EsServiceMode::name)
              .collect(Collectors.toList()));
    }
    query.in(!CollectionUtils.isEmpty(reqDTO.getUserIdList()), EsOrder::getUserId,
        reqDTO.getUserIdList());
    query.eq(Objects.nonNull(reqDTO.getEsOrderType()), EsOrder::getEsOrderType,
        reqDTO.getEsOrderType());

    query.eq(Objects.nonNull(reqDTO.getEsOrderStatus()), EsOrder::getEsOrderStatus,
        reqDTO.getEsOrderStatus());

    // 排序
    OrderByParam orderByParam = new OrderByParam();
    orderByParam.setSort(reqDTO.getOrder().name());
    orderByParam.setOrder(reqDTO.getOrderBy());
    query.orderBy(orderByParam);
    return query;
  }

  private void validateEsQuery(EsOrderQueryReqDTO reqDTO) {
    String startDate = reqDTO.getStartDate();
    String endDate = reqDTO.getEndDate();
    if (OrderDateUtils.isExceedingDays(startDate, endDate, validateEsQueryDays)) {
      throw AtomExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(),
          DsErrorType.PARAM_VALID_ERROR.getMsg() + "时间间隔超过" + validateEsQueryDays + "天");
    }
  }

  @Override
  public ResponseBase<PageDTO<EsOrderUserIdResDTO>> pageQueryUserId(EsOrderQueryReqDTO reqDTO) {
    LambdaEsQueryWrapper<EsOrder> queryWrapper = baseEsQuery(reqDTO);
    queryWrapper.distinct(EsOrder::getUserId);
    queryWrapper.select(EsOrder::getUserId);

    EsPageInfo<EsOrder> esOrderEsPageInfo = esOrderMapper.pageQuery(queryWrapper,
        reqDTO.getCurrentPage(), reqDTO.getPageSize());
    long total = esOrderEsPageInfo.getTotal();
    if (0 == total) {
      return generateSuccess(new PageDTO<>());
    }

    List<EsOrderUserIdResDTO> esUserIdList = esOrderEsPageInfo.getList().stream().map(esOrder -> {
      EsOrderUserIdResDTO esOrderUserIdResDTO = new EsOrderUserIdResDTO();
      esOrderUserIdResDTO.setUserId(esOrder.getUserId());
      return esOrderUserIdResDTO;
    }).collect(Collectors.toList());

    PageDTO<EsOrderUserIdResDTO> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(total);
    pageDTO.setTotalPage((long) esOrderEsPageInfo.getPages());
    pageDTO.setData(esUserIdList);
    pageDTO.setCurrentPage((long) reqDTO.getCurrentPage());
    pageDTO.setPageSize((long) reqDTO.getPageSize());
    return generateSuccess(pageDTO);
  }

  @Override
  public ResponseBase<PageDTO<EsOmsOrderData>> orderPageNormal(OrderPageReqDto reqDto) {

    LambdaEsQueryWrapper<EsOmsOrderInfo> query = EsQueryBuilder.buildEsQueryForOrderPageReqDto(
        reqDto);

    EsPageInfo<EsOmsOrderInfo> page = esOmsOrderInfoMapper.pageQuery(query,
        reqDto.getCurrentPage().intValue(), reqDto.getPageSize().intValue());

    PageDTO<EsOmsOrderData> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(page.getTotal());
    pageDTO.setTotalPage((long) page.getPages());
    pageDTO.setData(page.getList().stream().map(e -> {
      EsOmsOrderData esOmsOrderData = new EsOmsOrderData();
      esOmsOrderData.setOmsOrderNo(e.getOmsOrderNo());
      esOmsOrderData.setClientCode(e.getClientCode());
      return esOmsOrderData;
    }).collect(Collectors.toList()));
    pageDTO.setCurrentPage(reqDto.getCurrentPage());
    pageDTO.setPageSize(reqDto.getPageSize());

    return generateSuccess(pageDTO);
  }


  @Override
  public ResponseBase<Integer> orderErpAuditCount(OrderPageReqDto reqDto) {
    LambdaEsQueryWrapper<EsOmsOrderInfo> queryWrapper = EsQueryBuilder.buildEsQueryForOrderPageReqDto(
        reqDto);
    Long count = esOmsOrderInfoMapper.selectCount(queryWrapper);
    return generateSuccess(Math.toIntExact(count));
  }

  @Override
  public ResponseBase<PageDTO<EsOmsOrderData>> orderPageException(OrderPageOtherReqDto reqDto) {
    LambdaEsQueryWrapper<EsOmsOrderInfo> query = EsQueryBuilder.buildEsQueryForOrderPageOtherReqDto(
        reqDto);
    EsPageInfo<EsOmsOrderInfo> page = esOmsOrderInfoMapper.pageQuery(query,
        reqDto.getCurrentPage().intValue(), reqDto.getPageSize().intValue());

    PageDTO<EsOmsOrderData> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(page.getTotal());
    pageDTO.setTotalPage((long) page.getPages());
    pageDTO.setData(page.getList().stream().map(e -> {
      EsOmsOrderData esOmsOrderData = new EsOmsOrderData();
      esOmsOrderData.setOmsOrderNo(e.getOmsOrderNo());
      esOmsOrderData.setClientCode(e.getClientCode());
      return esOmsOrderData;
    }).collect(Collectors.toList()));
    pageDTO.setCurrentPage(reqDto.getCurrentPage());
    pageDTO.setPageSize(reqDto.getPageSize());

    return generateSuccess(pageDTO);
  }

  @Override
  public ResponseBase<PageDTO<EsOmsOrderData>> orderList(OrderListQueryDto reqDto) {
    LambdaEsQueryWrapper<EsOmsOrderInfo> query = EsQueryBuilder.buildEsQueryForOrderListQueryDto(
        reqDto);

    EsPageInfo<EsOmsOrderInfo> page = esOmsOrderInfoMapper.pageQuery(query,
        reqDto.getCurrentPage().intValue(), reqDto.getPageSize().intValue());

    PageDTO<EsOmsOrderData> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(page.getTotal());
    pageDTO.setTotalPage((long) page.getPages());
    pageDTO.setData(page.getList().stream().map(e -> {
      EsOmsOrderData esOmsOrderData = new EsOmsOrderData();
      esOmsOrderData.setOmsOrderNo(e.getOmsOrderNo());
      esOmsOrderData.setClientCode(e.getClientCode());
      return esOmsOrderData;
    }).collect(Collectors.toList()));
    pageDTO.setCurrentPage(reqDto.getCurrentPage());
    pageDTO.setPageSize(reqDto.getPageSize());

    return generateSuccess(pageDTO);
  }

  @Override
  public ResponseBase<PageDTO<MemberOrderResDto>> memberOrderTransaction(
      MemberOrderListReqDto reqDto) {

    LambdaEsQueryWrapper<EsMemberOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.routing(reqDto.getUserId());
    queryWrapper.eq(EsMemberOrder::getUserId, reqDto.getUserId());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getOrderNo()),EsMemberOrder::getOrderNo,reqDto.getOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getOrderSource()),EsMemberOrder::getOrderSource,reqDto.getOrderSource());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getThirdOrderNo()),EsMemberOrder::getThirdOrderNo,reqDto.getThirdOrderNo());
    queryWrapper.gt(EsMemberOrder::getCreated,
        OrderDateUtils.formatYYMMDD(reqDto.getCreatedStart()));
    queryWrapper.lt(EsMemberOrder::getCreated, OrderDateUtils.formatYYMMDD(reqDto.getCreatedEnd()));
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getStoreCode()), EsMemberOrder::getStoreCode,
        reqDto.getStoreCode());
    itemQuery(queryWrapper,reqDto.getErpCode(),  reqDto.getErpName());

    queryWrapper.orderByDesc(EsMemberOrder::getCreated);

    EsPageInfo<EsMemberOrder> esPageInfo = esMemberOrderMapper.pageQuery(queryWrapper,
        reqDto.getCurrentPage().intValue(), reqDto.getPageSize().intValue());
    long total = esPageInfo.getTotal();
    if (0 == total) {
      return generateSuccess(new PageDTO<>());
    }

    List<MemberOrderResDto> memberOrderResDtoList = buildMemberOrderList(esPageInfo.getList(), Boolean.TRUE);

    PageDTO<MemberOrderResDto> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(total);
    pageDTO.setTotalPage((long) esPageInfo.getPages());
    pageDTO.setData(memberOrderResDtoList);
    pageDTO.setCurrentPage((long) esPageInfo.getPageNum());
    pageDTO.setPageSize((long) esPageInfo.getPageSize());
    return generateSuccess(pageDTO);
  }

  private void itemQuery(LambdaEsQueryWrapper<EsMemberOrder> queryWrapper,String erpCode,
      String erpName) {
    if(StringUtils.isNotEmpty(erpCode)){
      queryWrapper.nested(esMemberOrderItemField,
          item -> item.eq(
              String.format("%s.%s.keyword", esMemberOrderItemField,
                  FieldUtils.val(EsMemberOrderDetail::getErpCode)), erpCode));
    }

    if(StringUtils.isNotEmpty(erpName)){
      queryWrapper.nested(esMemberOrderItemField,
          item -> item.eq(
              String.format("%s.%s.keyword", esMemberOrderItemField,
                  FieldUtils.val(EsMemberOrderDetail::getErpName)), erpName));
    }
  }

  private void refundItemQuery(LambdaEsQueryWrapper<EsMemberRefundOrder> queryWrapper,String erpCode,
      String erpName) {
    if(StringUtils.isNotEmpty(erpCode)){
      queryWrapper.nested(esMemberRefundOrderItemField,
          item -> item.eq(
              String.format("%s.%s.keyword", esMemberRefundOrderItemField,
                  FieldUtils.val(EsMemberRefundOrderDetail::getErpCode)), erpCode));
    }

    if(StringUtils.isNotEmpty(erpName)){
      queryWrapper.nested(esMemberRefundOrderItemField,
          item -> item.eq(
              String.format("%s.%s.keyword", esMemberRefundOrderItemField,
                  FieldUtils.val(EsMemberRefundOrderDetail::getErpName)), erpName));
    }
  }

  private List<MemberOrderResDto> buildMemberOrderList(List<EsMemberOrder> esMemberOrderList,
      Boolean isBuildDetail) {
    List<MemberOrderResDto> memberOrderResDtoList = new ArrayList<>();
    esMemberOrderList.stream().collect(Collectors.groupingBy(EsMemberOrder::getOrderSource))
        .forEach((orderSource, esOrderList) -> {
          if (MemberOrderSource.ONLINE.name().equals(orderSource)) {
            List<Long> esOrderNoList = esOrderList.stream()
                .map(EsMemberOrder::getOrderNo)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
            List<OrderInfoDO> orderInfoList = orderRepository.querySimpleOrderInfoList(esOrderNoList);

            List<OrderPayInfoDO> orderPayInfoList = orderRepository.queryPayInfoList(esOrderNoList);
            Map<Long, Boolean> hasRefundOrderMap = orderRepository.hasRefundOrder(esOrderNoList);
            List<ErpBillInfoDO> erpBillInfoDOList = orderRepository.queryBillInfoList(esOrderNoList);

            Map<Long, OrderPayInfoDO> orderPayInfoMap = orderPayInfoList.stream()
                .filter(v -> ignorePostOrder(v.getOmsOrderNo()))
                .collect(Collectors.toMap(OrderPayInfoDO::getOrderNo, v -> v, (v1, v2) -> v1));

            Map<Long, ErpBillInfoDO> erpBillInfoDOMap = erpBillInfoDOList.stream()
                .filter(v -> ignorePostOrder(v.getOmsOrderNo()))
                .collect(Collectors.toMap(ErpBillInfoDO::getOrderNo, v -> v, (v1, v2) -> v1));

            Map<Long, List<OrderDetailDO>> orderNoDetailListMap = Maps.newHashMap();
            if (isBuildDetail) {
              List<OrderDetailDO> orderDetailList = orderRepository.queryOrderDetailList(
                  esOrderNoList);
              orderNoDetailListMap = orderDetailList.stream()
                  .collect(Collectors.groupingBy(OrderDetailDO::getOrderNo));
            }

            for (OrderInfoDO orderInfo : orderInfoList) {
              MemberOrderResDto memberOrderResDto = new MemberOrderResDto();
              Long orderNo = orderInfo.getOrderNo();
              memberOrderResDto.setOrderNo(String.valueOf(orderNo));
              memberOrderResDto.setThirdOrderNo(orderInfo.getThirdOrderNo());
              memberOrderResDto.setOrderStatus(orderInfo.getOrderState());
              memberOrderResDto.setCreated(orderInfo.getCreated());
              memberOrderResDto.setPayTime(orderInfo.getPayTime());
              memberOrderResDto.setCashier(orderInfo.getPickOperatorId());
              memberOrderResDto.setCashierName(orderInfo.getPickOperatorName());

              OrderPayInfoDO orderPayInfoDO = orderPayInfoMap.get(orderNo);
              if (Objects.nonNull(orderPayInfoDO)) {
                memberOrderResDto.setBuyerActualAmount(orderPayInfoDO.getBuyerActualAmount());
                // 计算运费
                BigDecimal deliveryFee = orderPayInfoDO.getDeliveryFee();
                BigDecimal merchantDeliveryFeeDiscount = orderPayInfoDO.getMerchantDeliveryFeeDiscount();
                BigDecimal platformDeliveryFeeDiscount = orderPayInfoDO.getPlatformDeliveryFeeDiscount();
                BigDecimal deliveryFeeAmount = deliveryFee
                    .subtract(merchantDeliveryFeeDiscount)
                    .subtract(platformDeliveryFeeDiscount);
                memberOrderResDto.setDeliveryFeeAmount(deliveryFeeAmount);
              }
              memberOrderResDto.setOrderSource(MemberOrderSource.ONLINE.name());
              memberOrderResDto.setStoreCode(orderInfo.getOnlineStoreCode());
              memberOrderResDto.setStoreName(orderInfo.getOnlineStoreName());
              OmsOrderInfoDO omsOrderInfo = getOmsOrderInfoDO(orderInfo);
              if (isBuildDetail) {
                List<OrderDetailDO> orderDetailList = orderNoDetailListMap.get(
                    orderNo);
                if (!CollectionUtils.isEmpty(orderDetailList)) {
                  List<MemberOrderDetailDto> memberOrderDetailDtoList = buildOrderTransactionDetail(orderDetailList, omsOrderInfo);
                  memberOrderResDto.setOrderDetailDtoList(memberOrderDetailDtoList);
                }
              }
              memberOrderResDto.setHasRefundOrder(hasRefundOrderMap.get(orderNo));
              Optional.ofNullable(erpBillInfoDOMap.get(orderNo))
                  .ifPresent(erpBillInfoDO -> memberOrderResDto.setBillAmount(
                      erpBillInfoDO.getBillTotalAmount()));
              if (Objects.nonNull(omsOrderInfo)) {
//                memberOrderResDto.setOrderNo(String.valueOf(omsOrderInfo.getOmsOrderNo()));
                memberOrderResDto.setStoreCode(omsOrderInfo.getOrganizationCode());
                memberOrderResDto.setStoreName(omsOrderInfo.getOrganizationName());
                BigDecimal billAmount = omsOrderInfoMapper.sumBillAmount(
                    omsOrderInfo.getOmsOrderNo());
                Optional.ofNullable(billAmount).ifPresent(memberOrderResDto::setBillAmount);
              }
              memberOrderResDtoList.add(memberOrderResDto);
            }
          }
          if (MemberOrderSource.POS.name().equals(orderSource)) {
            List<String> offlineOrderNoList = esOrderList.stream()
                .map(EsMemberOrder::getOrderNo)
                .distinct()
                .collect(Collectors.toList());

            for (String offlineOrderNo : offlineOrderNoList) {

              SimpleOfflineOrderDetailDto simpleOfflineOrderDetailDto;
              try {
                simpleOfflineOrderDetailDto = offlineOrderRepository.querySimpleOfflineOrderDetail(
                    offlineOrderNo);
              } catch (Exception e) {
                log.warn("订单数据缺失,{}", offlineOrderNo);
                continue;
              }

              OfflineOrderDO offlineOrder = simpleOfflineOrderDetailDto.getOfflineOrder();
              // 这里不根据isBuildDetail来特殊处理,因为要计算下账金额,需要从明细汇总
              List<OfflineOrderDetailDO> offlineOrderDetailList = simpleOfflineOrderDetailDto.getOfflineOrderDetailList();
              OfflineOrderOrganizationDO offlineOrderOrganizationDO = simpleOfflineOrderDetailDto.getOfflineOrderOrganizationDO();
              OfflineOrderCashierDeskDO cashierDeskDO = simpleOfflineOrderDetailDto.getOfflineOrderCashierDeskDO();

              Boolean hasRefundOrder = simpleOfflineOrderDetailDto.getHasRefundOrder();

              MemberOrderResDto memberOrderResDto = new MemberOrderResDto();
              memberOrderResDto.setOrderNo(offlineOrder.getOrderNo());
              memberOrderResDto.setThirdOrderNo(offlineOrder.getThirdOrderNo());
              if (OfflineOrderStateEnum.DONE.name().equals(offlineOrder.getOrderState())) {
                memberOrderResDto.setOrderStatus(MemberOrderStatus.COMPLETED.getStatus());
              }
              memberOrderResDto.setCreated(offlineOrder.getCreated());
              memberOrderResDto.setPayTime(offlineOrder.getPayTime());
              memberOrderResDto.setBuyerActualAmount(offlineOrder.getActualPayAmount());
              memberOrderResDto.setDeliveryFeeAmount(BigDecimal.ZERO);//线下单没有运费
              memberOrderResDto.setOrderSource(MemberOrderSource.POS.name());
              memberOrderResDto.setStoreCode(offlineOrder.getStoreCode());
              memberOrderResDto.setStoreName(offlineOrderOrganizationDO.getStoreName());
              Optional.ofNullable(cashierDeskDO).ifPresent(
                c->{
                  memberOrderResDto.setCashier(c.getCashier());
                  memberOrderResDto.setCashierName(c.getCashierName());
                }
              );

              if (isBuildDetail) {
                List<OfflineOrderDetailTraceDO> offlineOrderDetailTraceList = simpleOfflineOrderDetailDto.getOfflineOrderDetailTraceList();
                List<OfflineOrderDetailPickDO> offlineOrderDetailPickList = simpleOfflineOrderDetailDto.getOfflineOrderDetailPickList();

                // 转成Map
                Map<String, List<OfflineOrderDetailTraceDO>> detailTraceMap = offlineOrderDetailTraceList.stream()
                    .collect(Collectors.groupingBy(OfflineOrderDetailTraceDO::getOrderDetailNo));
                Map<String, List<OfflineOrderDetailPickDO>> detailPickMap = offlineOrderDetailPickList.stream()
                    .collect(Collectors.groupingBy(OfflineOrderDetailPickDO::getOrderDetailNo));

                memberOrderResDto.setOrderDetailDtoList(
                    offlineOrderDetailList.stream().map(offlineOrderDetail -> {
                      MemberOrderDetailDto memberOrderDetailDto = new MemberOrderDetailDto();
                      memberOrderDetailDto.setErpCode(offlineOrderDetail.getErpCode());
                      memberOrderDetailDto.setErpName(offlineOrderDetail.getErpName());
                      memberOrderDetailDto.setCommodityCount(
                          offlineOrderDetail.getCommodityCount());
                      memberOrderDetailDto.setActualAmount(offlineOrderDetail.getTotalAmount());
                      memberOrderDetailDto.setBillAmount(offlineOrderDetail.getBillAmount());
                      memberOrderDetailDto.setCommoditySpec(offlineOrderDetail.getCommoditySpec());
                      memberOrderDetailDto.setManufacture(offlineOrderDetail.getManufacture());
                      memberOrderDetailDto.setFiveClass(offlineOrderDetail.getFiveClass());
                      memberOrderDetailDto.setFiveClassName(offlineOrderDetail.getFiveClassName());
                      memberOrderDetailDto.setSalerId(offlineOrderDetail.getSalerId());
                      memberOrderDetailDto.setSalerName(offlineOrderDetail.getSalerName());

                      List<OfflineOrderDetailPickDO> offlineOrderDetailPickDOList = detailPickMap.get(
                          offlineOrderDetail.getOrderDetailNo());
                      List<OfflineOrderDetailTraceDO> offlineOrderDetailTraceDOS = detailTraceMap.get(
                          offlineOrderDetail.getOrderDetailNo());
                      if(!CollectionUtils.isEmpty(offlineOrderDetailPickDOList)) {
                        memberOrderDetailDto.setPickInfoList(
                        offlineOrderDetailPickDOList.stream().map(detailPick->{
                          PickInfo pickInfo = new PickInfo();
                          pickInfo.setMarkNo(detailPick.getMakeNo());
                          pickInfo.setCount(detailPick.getCount());
                          Optional.ofNullable(offlineOrderDetailTraceDOS).ifPresent(traceList->{
                            pickInfo.setTraceCodeList(traceList.stream().map(OfflineOrderDetailTraceDO::getTraceCode).collect(
                                Collectors.toList()));
                          });
                          return pickInfo;
                        }).collect(Collectors.toList()));
                      }



                      return memberOrderDetailDto;
                    }).collect(Collectors.toList()));
              }

              memberOrderResDto.setHasRefundOrder(hasRefundOrder);
              if (!CollectionUtils.isEmpty(offlineOrderDetailList)) {
                memberOrderResDto.setBillAmount(
                    offlineOrderDetailList.stream().map(OfflineOrderDetailDO::getBillAmount)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
              }
              memberOrderResDtoList.add(memberOrderResDto);
            }
          }
        });
    // 处理完之后就乱序了,这里重新根据创建时间排序
    memberOrderResDtoList.sort(Comparator.comparing(MemberOrderResDto::getCreated).reversed());
    return memberOrderResDtoList;
  }

  private List<MemberOrderDetailDto> buildOrderTransactionDetail(List<OrderDetailDO> orderDetailList, OmsOrderInfoDO omsOrderInfo) {
    boolean b2cOrder = Objects.nonNull(omsOrderInfo);
    List<OrderDetailDO> goodsList;
    if(b2cOrder){
      // b2c订单明细处理

      // 1. 排除掉已换货的商品
      Integer b2cBeReplacedStatus = 2; // BE_REPLACED(2, "被换货的商品"),
      goodsList = orderDetailList.stream()
          .filter(detail -> !b2cBeReplacedStatus.equals(detail.getStatus()))
          .filter(orderDetail -> orderDetail.getIsJoint() != 2) // 2 组合商品父商品。 b2c排除掉(接口文档之后0,1)
          .collect(Collectors.toList());

    }else{
      // o2o订单明细处理

      // 1. 排除掉已换货的商品
      Integer o2oBeReplacedGoods = OrderDetailStatusEnum.REPLACE.getCode();
      goodsList = orderDetailList.stream()
          .filter(detail -> !o2oBeReplacedGoods.equals(detail.getStatus()))
          .collect(Collectors.toList());
    }

    return goodsList.stream()
        .map(goods -> {
          // 正常组装
          MemberOrderDetailDto memberOrderDetailDto = buildMemberOrderDetailDto(omsOrderInfo, goods, b2cOrder);

          // 如果被换货,则会查商品,如果原商品不为空,则赋值
          OrderDetailDO originDetail = findOriginDetail(goods, orderDetailList,b2cOrder);
          if(Objects.nonNull(originDetail)){
            memberOrderDetailDto.setBeReplacedOrderDetail(buildMemberOrderDetailDto(omsOrderInfo, originDetail, b2cOrder));
          }

          return memberOrderDetailDto;
        }).collect(Collectors.toList());
  }

  @NotNull
  private MemberOrderDetailDto buildMemberOrderDetailDto(OmsOrderInfoDO omsOrderInfo,
      OrderDetailDO orderDetail, boolean b2cOrder) {
    MemberOrderDetailDto memberOrderDetailDto = new MemberOrderDetailDto();
    memberOrderDetailDto.setErpCode(orderDetail.getErpCode());
    memberOrderDetailDto.setErpName(orderDetail.getCommodityName());
    memberOrderDetailDto.setCommoditySpec(orderDetail.getCommoditySpec());
    memberOrderDetailDto.setManufacture(orderDetail.getManufacture());
    memberOrderDetailDto.setCommodityCount(
        BigDecimal.valueOf(orderDetail.getCommodityCount()));
    memberOrderDetailDto.setActualAmount(orderDetail.getActualAmount());
    memberOrderDetailDto.setBillAmount(
        orderDetail.getActualNetAmount()); // 下账金额
    if (b2cOrder) {
      BigDecimal detailBillAmount = omsOrderInfoMapper.b2cGoodsBillAmount(
          omsOrderInfo.getOmsOrderNo(), orderDetail.getErpCode());
      Optional.ofNullable(detailBillAmount)
          .ifPresent(memberOrderDetailDto::setBillAmount);
    }
    memberOrderDetailDto.setFiveClass(orderDetail.getFiveClass());
    memberOrderDetailDto.setFiveClassName(orderDetail.getFiveClassName());
    return memberOrderDetailDto;
  }

  /**
   * 找到原始订单
   * @param originDetailList
   * @param b2cOrder
   * @return
   */
  private OrderDetailDO findOriginDetail(OrderDetailDO lastedDetail,List<OrderDetailDO> originDetailList, boolean b2cOrder) {
    if(b2cOrder){
      String originalThirdDetailId = findOriginalParentId(lastedDetail);
      if(!lastedDetail.getThirdDetailId().equals(originalThirdDetailId)){ // 不等提取出来的根Id,说明被换过货
        return originDetailList.stream().filter(s->s.getThirdDetailId().equals(originalThirdDetailId)).findFirst().orElse(null);
      }

    }else {
      Long swapId = lastedDetail.getSwapId();
      if(Objects.nonNull(swapId)){
        return originDetailList.stream().filter(s->swapId.equals(s.getId())).findFirst().orElse(null);
      }
    }
    return null;
  }

  public static String findOriginalParentId(OrderDetailDO orderDetail) {
    if (orderDetail == null || orderDetail.getThirdDetailId() == null) {
      throw new IllegalArgumentException("OrderDetail or orderDetailId cannot be null");
    }

    String childId = orderDetail.getThirdDetailId();
    String[] parts = childId.split("_");

    if (parts.length < 2) {
      throw new IllegalArgumentException("Invalid ID format: must contain at least one underscore");
    }

    // 如果只有两部分（已经是根ID），直接返回
    if (parts.length == 2) {
      return childId;
    }

    // 否则返回根ID（基础ID和第一个数字）
    return parts[0] + "_" + parts[1];
  }


  public static void main(String[] args) {
    OrderDetailDO orderDetailDO = new OrderDetailDO();
    orderDetailDO.setThirdDetailId("1819197131610937863_1_1_1");
    System.out.println(findOriginalParentId(orderDetailDO));
    orderDetailDO.setThirdDetailId("1819197131610937863_1");
    System.out.println(findOriginalParentId(orderDetailDO));


    orderDetailDO.setThirdDetailId("1819197131610937863_2_2_2");
    System.out.println(findOriginalParentId(orderDetailDO));
    orderDetailDO.setThirdDetailId("1819197131610937863_2");
    System.out.println(findOriginalParentId(orderDetailDO));
    orderDetailDO.setThirdDetailId("1819197131610937863_2_1_1");
    System.out.println(findOriginalParentId(orderDetailDO));
    orderDetailDO.setThirdDetailId("1819197131610937863_2");
    System.out.println(findOriginalParentId(orderDetailDO));
  }

  /**
   * 忽略邮费单
   *
   * @param omsOrderNo
   * @return
   */
  private boolean ignorePostOrder(Long omsOrderNo) {
    if (Objects.isNull(omsOrderNo)) {
      return Boolean.TRUE;
    }
    Integer postOrder = syncEsMapper.isPostOrder(omsOrderNo);
    // `is_post_fee_order` tinyint(1) DEFAULT '0' COMMENT '是否为邮费单，0否（默认），1是',
    return Objects.isNull(postOrder) || postOrder != 1;
  }

  @Nullable
  private OmsOrderInfoDO getOmsOrderInfoDO(OrderInfoDO orderInfo) {
    OmsOrderInfoDO omsOrderInfo = null;
    if (DsConstants.B2C.equals(orderInfo.getServiceMode())) {
      omsOrderInfo = omsOrderInfoMapper.selectOne(
          new LambdaUpdateWrapper<OmsOrderInfoDO>()
              .eq(OmsOrderInfoDO::getThirdPlatformCode, orderInfo.getThirdPlatformCode())
              .eq(OmsOrderInfoDO::getThirdOrderNo, orderInfo.getThirdOrderNo())
              .eq(OmsOrderInfoDO::getIsPostFeeOrder, DsConstants.INTEGER_ZERO)
              .eq(OmsOrderInfoDO::getDeleted, DsConstants.INTEGER_ZERO)
      );
    }
    return omsOrderInfo;
  }

  @Override
  public ResponseBase<PageDTO<MemberOrderSimpleResDto>> orderTransactionSimple(
      MemberOrderSimpleReqDto reqDto) {
    LambdaEsQueryWrapper<EsMemberOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.routing(reqDto.getUserId());
    queryWrapper.eq(EsMemberOrder::getUserId, reqDto.getUserId());
    queryWrapper.gt(EsMemberOrder::getCreated,
        OrderDateUtils.formatYYMMDD(reqDto.getCreatedStart()));
    queryWrapper.lt(EsMemberOrder::getCreated, OrderDateUtils.formatYYMMDD(reqDto.getCreatedEnd()));
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getStoreCode()), EsMemberOrder::getStoreCode,
        reqDto.getStoreCode());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getUserCardNo()), EsMemberOrder::getUserCardNo,
        reqDto.getUserCardNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getOrderSource()), EsMemberOrder::getOrderSource,
        reqDto.getOrderSource());
    queryWrapper.eq(Objects.nonNull(reqDto.getOrderStatus()), EsMemberOrder::getOrderStatus,
        reqDto.getOrderStatus());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getThirdOrderNo()),
        EsMemberOrder::getThirdOrderNo, reqDto.getThirdOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getOrderNo()), EsMemberOrder::getOrderNo,
        reqDto.getOrderNo());
    itemQuery(queryWrapper, reqDto.getErpCode(), reqDto.getErpName());
    queryWrapper.orderByDesc(EsMemberOrder::getCreated);
    EsPageInfo<EsMemberOrder> esPageInfo = esMemberOrderMapper.pageQuery(queryWrapper,
        reqDto.getCurrentPage().intValue(), reqDto.getPageSize().intValue());
    long total = esPageInfo.getTotal();
    if (0 == total) {
      return generateSuccess(new PageDTO<>());
    }
    List<MemberOrderResDto> memberOrderResDtoList = buildMemberOrderList(esPageInfo.getList(), Boolean.FALSE);

    PageDTO<MemberOrderSimpleResDto> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(total);
    pageDTO.setTotalPage((long) esPageInfo.getPages());
    pageDTO.setData(memberOrderResDtoList.stream().map(res -> {
      MemberOrderSimpleResDto memberOrderSimpleResDto = new MemberOrderSimpleResDto();
      memberOrderSimpleResDto.setOrderNo(res.getOrderNo());
      memberOrderSimpleResDto.setThirdOrderNo(res.getThirdOrderNo());
      memberOrderSimpleResDto.setOrderStatus(res.getOrderStatus());
      memberOrderSimpleResDto.setCreated(res.getCreated());
      memberOrderSimpleResDto.setPayTime(res.getPayTime());
      memberOrderSimpleResDto.setBuyerActualAmount(res.getBuyerActualAmount());
      memberOrderSimpleResDto.setDeliveryFeeAmount(res.getDeliveryFeeAmount());
      memberOrderSimpleResDto.setOrderSource(res.getOrderSource());
      memberOrderSimpleResDto.setStoreCode(res.getStoreCode());
      memberOrderSimpleResDto.setStoreName(res.getStoreName());
      memberOrderSimpleResDto.setHasRefundOrder(res.getHasRefundOrder());
      memberOrderSimpleResDto.setBillAmount(res.getBillAmount());
      memberOrderSimpleResDto.setCashier(res.getCashier());
      memberOrderSimpleResDto.setCashierName(res.getCashierName());
      return memberOrderSimpleResDto;
    }).collect(Collectors.toList()));
    pageDTO.setCurrentPage((long) esPageInfo.getPageNum());
    pageDTO.setPageSize((long) esPageInfo.getPageSize());
    return generateSuccess(pageDTO);

  }

  @Override
  public ResponseBase<MemberOrderResDto> orderTransactionDetail(MemberOrderDetailReqDto reqDto) {
    LambdaEsQueryWrapper<EsMemberOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.routing(reqDto.getUserId());
    queryWrapper.eq(EsMemberOrder::getOrderNo, reqDto.getOrderNo());
    queryWrapper.eq(EsMemberOrder::getOrderSource, reqDto.getOrderSource());
    EsMemberOrder esMemberOrder = esMemberOrderMapper.selectOne(queryWrapper);
    if (Objects.isNull(esMemberOrder)) {
      return generateSuccess(null);
    }

    List<MemberOrderResDto> memberOrderResDtoList = buildMemberOrderList(Lists.newArrayList(esMemberOrder), Boolean.TRUE);
    if (CollectionUtils.isEmpty(memberOrderResDtoList)) {
      return generateSuccess(null);
    }

    return generateSuccess(memberOrderResDtoList.get(0));
  }

  @Override
  public ResponseBase<PageDTO<MemberRefundOrderResDto>> memberRefundOrderTransaction(
      MemberRefundOrderListReqDto reqDto) {
    LambdaEsQueryWrapper<EsMemberRefundOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.routing(reqDto.getUserId());
    queryWrapper.eq(EsMemberRefundOrder::getUserId, reqDto.getUserId());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getOrderNo()),EsMemberRefundOrder::getOrderNo,reqDto.getOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getRefundNo()),EsMemberRefundOrder::getRefundNo,reqDto.getRefundNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getOrderSource()),EsMemberRefundOrder::getOrderSource,reqDto.getOrderSource());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getThirdOrderNo()),EsMemberRefundOrder::getThirdOrderNo,reqDto.getThirdOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getThirdRefundNo()),EsMemberRefundOrder::getThirdRefundNo,reqDto.getThirdRefundNo());
    queryWrapper.gt(EsMemberRefundOrder::getCreated,
        OrderDateUtils.formatYYMMDD(reqDto.getCreatedStart()));
    queryWrapper.lt(EsMemberRefundOrder::getCreated,
        OrderDateUtils.formatYYMMDD(reqDto.getCreatedEnd()));
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getStoreCode()), EsMemberRefundOrder::getStoreCode,
        reqDto.getStoreCode());
    refundItemQuery(queryWrapper,reqDto.getErpCode(),reqDto.getErpName());
    queryWrapper.orderByDesc(EsMemberRefundOrder::getCreated);
    EsPageInfo<EsMemberRefundOrder> esPageInfo = esMemberRefundOrderMapper.pageQuery(queryWrapper,
        reqDto.getCurrentPage().intValue(), reqDto.getPageSize().intValue());
    long total = esPageInfo.getTotal();
    if (0 == total) {
      return generateSuccess(new PageDTO<>());
    }

    List<MemberRefundOrderResDto> memberRefundOrderResDtoList = buildMemberRefundOrderList(
        esPageInfo.getList(), Boolean.TRUE);

    PageDTO<MemberRefundOrderResDto> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(total);
    pageDTO.setTotalPage((long) esPageInfo.getPages());
    pageDTO.setData(memberRefundOrderResDtoList.stream().map(refundOrderRes -> {
      MemberRefundOrderResDto memberRefundOrderResDto = new MemberRefundOrderResDto();
      memberRefundOrderResDto.setRefundNo(refundOrderRes.getRefundNo());
      memberRefundOrderResDto.setOrderNo(refundOrderRes.getOrderNo());
      memberRefundOrderResDto.setThirdOrderNo(refundOrderRes.getThirdOrderNo());
      memberRefundOrderResDto.setThirdRefundNo(refundOrderRes.getThirdRefundNo());
      memberRefundOrderResDto.setRefundStatus(refundOrderRes.getRefundStatus());
      memberRefundOrderResDto.setCreated(refundOrderRes.getCreated());
      memberRefundOrderResDto.setRefundAmount(refundOrderRes.getRefundAmount());
      memberRefundOrderResDto.setRefundDeliveryFeeAmount(
          refundOrderRes.getRefundDeliveryFeeAmount());
      memberRefundOrderResDto.setOrderSource(refundOrderRes.getOrderSource());
      memberRefundOrderResDto.setReason(refundOrderRes.getReason());
      memberRefundOrderResDto.setRefundDetailDtoList(refundOrderRes.getRefundDetailDtoList());
      memberRefundOrderResDto.setBillAmount(refundOrderRes.getBillAmount());
      memberRefundOrderResDto.setCashier(refundOrderRes.getCashier());
      memberRefundOrderResDto.setCashierName(refundOrderRes.getCashierName());
      memberRefundOrderResDto.setStoreCode(refundOrderRes.getStoreCode());
      return memberRefundOrderResDto;
    }).collect(Collectors.toList()));
    pageDTO.setCurrentPage((long) esPageInfo.getPageNum());
    pageDTO.setPageSize((long) esPageInfo.getPageSize());
    return generateSuccess(pageDTO);

  }

  private List<MemberRefundOrderResDto> buildMemberRefundOrderList(
      List<EsMemberRefundOrder> esMemberRefundOrderList, Boolean isBuildDetail) {
    List<MemberRefundOrderResDto> memberRefundOrderResDtoList = new ArrayList<>();
    esMemberRefundOrderList.stream()
        .collect(Collectors.groupingBy(EsMemberRefundOrder::getOrderSource))
        .forEach((orderSource, esRefundOrderList) -> {
          if (MemberOrderSource.ONLINE.name().equals(orderSource)) {
            List<String> esRefundNoList = esRefundOrderList.stream().map(EsMemberRefundOrder::getRefundNo)
                .distinct().collect(Collectors.toList());

            List<RefundOrderDO> refundOrderList = orderRepository.queryRefundOrderList(
                esRefundNoList);
            List<ErpRefundInfoDO> erpRefundInfoList = orderRepository.queryErpRefundInfoList(
                esRefundNoList);
            Map<Long, List<ErpRefundInfoDO>> erpRefundInfoMapByRefundNo = erpRefundInfoList.stream()
                .collect(Collectors.groupingBy(ErpRefundInfoDO::getRefundNo));

            // 这里不根据isBuildDetail来特殊处理,因为要计算下账金额,需要从明细汇总
            List<RefundDetailDO> refundDetailList = orderRepository.queryRefundDetailList(
                esRefundNoList);
            Map<Long, List<RefundDetailDO>> refundOrderDetailMap = refundDetailList.stream()
                .collect(Collectors.groupingBy(RefundDetailDO::getRefundNo));

            // 查询有效的售后单
            List<AfterSaleOrderDO> afterSaleOrderListAll = orderRepository.selectEfficientErpRefundInfo(
                esRefundNoList);
            Map<Long, List<AfterSaleOrderDO>> afterSaleOrderMap = afterSaleOrderListAll.stream()
                .collect(Collectors.groupingBy(AfterSaleOrderDO::getRefundNo));

            List<Long> orderNoList = esRefundOrderList.stream()
                .map(EsMemberRefundOrder::getOrderNo)
                .map(Long::valueOf)
                .distinct().collect(Collectors.toList());
            List<OrderDetailDO> orderDetailList = orderRepository.queryOrderDetailList(orderNoList);

            // 只用于基础信息
            Map<String, OrderDetailDO> erpCodeOrderDetailMap = orderDetailList.stream()
                .collect(Collectors.toMap(OrderDetailDO::getErpCode, v -> v, (v1, v2) -> v1));

            for (RefundOrderDO refundOrderDO : refundOrderList) {
              OmsOrderInfoDO omsOrderInfo;
              if (StringUtils.isNotEmpty(refundOrderDO.getServiceMode()) && DsConstants.B2C.equals(
                  refundOrderDO.getServiceMode())) {
                Long omsOrderNo = refundOrderDO.getOmsOrderNo();
                omsOrderInfo = omsOrderInfoMapper.selectOne(
                    new LambdaUpdateWrapper<OmsOrderInfoDO>()
                        .eq(OmsOrderInfoDO::getOmsOrderNo, omsOrderNo)
                        .eq(OmsOrderInfoDO::getIsPostFeeOrder, DsConstants.INTEGER_ZERO)
                        .eq(OmsOrderInfoDO::getDeleted, DsConstants.INTEGER_ZERO)
                );
              } else {
                omsOrderInfo = null; // idea generate
              }

              MemberRefundOrderResDto memberRefundOrderResDto = new MemberRefundOrderResDto();
              memberRefundOrderResDto.setRefundNo(String.valueOf(refundOrderDO.getRefundNo()));
              memberRefundOrderResDto.setOrderNo(String.valueOf(refundOrderDO.getOrderNo()));
              memberRefundOrderResDto.setThirdOrderNo(refundOrderDO.getThirdOrderNo());
              memberRefundOrderResDto.setThirdRefundNo(refundOrderDO.getThirdRefundNo());
              memberRefundOrderResDto.setRefundStatus(
                  MemberRefundStatus.getByState(String.valueOf(refundOrderDO.getState())));
              memberRefundOrderResDto.setCreated(refundOrderDO.getCreateTime());
              memberRefundOrderResDto.setRefundAmount(refundOrderDO.getConsumerRefund()); // 退卖家金额
              memberRefundOrderResDto.setRefundDeliveryFeeAmount(
                  refundOrderDO.getUserPostage()); // 退用户配送费金额
              memberRefundOrderResDto.setOrderSource(MemberOrderSource.ONLINE.name());
              memberRefundOrderResDto.setReason(refundOrderDO.getReason());
              memberRefundOrderResDto.setStoreCode(refundOrderDO.getOnlineStoreCode());

              // 根据refundNo获取明细
              List<RefundDetailDO> refundOrderDetailList = refundOrderDetailMap.get(
                  refundOrderDO.getRefundNo());
              if (isBuildDetail && !CollectionUtils.isEmpty(refundOrderDetailList)) {
                memberRefundOrderResDto.setRefundDetailDtoList(
                    refundOrderDetailList.stream().map(refundDetail -> {
                      MemberRefundOrderDetailDto memberRefundOrderDetailDto = new MemberRefundOrderDetailDto();
                      memberRefundOrderDetailDto.setErpCode(refundDetail.getErpCode());
                      memberRefundOrderDetailDto.setErpName(refundDetail.getCommodityName());
                      // 从正单获取
                      Optional.ofNullable(erpCodeOrderDetailMap.get(refundDetail.getErpCode()))
                          .ifPresent(
                              orderDetail -> memberRefundOrderDetailDto.setCommoditySpec(
                                  orderDetail.getCommoditySpec()));
                      Optional.ofNullable(erpCodeOrderDetailMap.get(refundDetail.getErpCode()))
                          .ifPresent(
                              orderDetail -> memberRefundOrderDetailDto.setManufacture(
                                  orderDetail.getManufacture()));
                      memberRefundOrderDetailDto.setCommodityCount(
                          BigDecimal.valueOf(refundDetail.getRefundCount()));
                      memberRefundOrderDetailDto.setActualRefundAmount(
                          refundDetail.getBuyerAmount()); // 退卖家金额
                      memberRefundOrderDetailDto.setBillAmount(refundDetail.getActualNetAmount());
                      if (Objects.nonNull(omsOrderInfo)) {
                        BigDecimal detailBillAmount = omsOrderInfoMapper.selectDetailBillAmount(
                            refundOrderDO.getRefundNo(), refundDetail.getErpCode());
                        Optional.ofNullable(detailBillAmount)
                            .ifPresent(memberRefundOrderDetailDto::setBillAmount);
                      }
                      return memberRefundOrderDetailDto;
                    }).collect(Collectors.toList()));
              }

              BigDecimal billAmount = BigDecimal.ZERO;
              // erp_refund_info.refund_merchant_total
              List<ErpRefundInfoDO> erpRefundInfobyRefundNoList = erpRefundInfoMapByRefundNo.get(
                  refundOrderDO.getRefundNo());
              if (!CollectionUtils.isEmpty(erpRefundInfobyRefundNoList)) {
                billAmount = erpRefundInfobyRefundNoList.get(0).getRefundMerchantTotal(); // 理论上只有1条
              }
              memberRefundOrderResDto.setBillAmount(billAmount);

              // B2C退单下账金额累计逻辑
              if (Objects.nonNull(omsOrderInfo)) {
                BigDecimal refundBillAmount = omsOrderInfoMapper.sumRefundBillAmount(
                    omsOrderInfo.getOmsOrderNo());
                if (Objects.nonNull(refundBillAmount)) {
                  memberRefundOrderResDto.setBillAmount(refundBillAmount);
                } else {
                  List<AfterSaleOrderDO> afterSaleOrderList = afterSaleOrderMap.get(
                      refundOrderDO.getRefundNo());
                  if (!CollectionUtils.isEmpty(afterSaleOrderList) && !CollectionUtils.isEmpty(
                      erpRefundInfobyRefundNoList)) {
                    Set<Long> afterSaleNoSet = afterSaleOrderList.stream()
                        .map(AfterSaleOrderDO::getAfterSaleNo).collect(
                            Collectors.toSet());
                    List<ErpRefundInfoDO> filtedList = erpRefundInfobyRefundNoList.stream()
                        .filter(i -> afterSaleNoSet.contains(i.getAfterSaleNo()))
                        .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filtedList)) {
                      memberRefundOrderResDto.setBillAmount(
                          filtedList.get(0).getRefundMerchantTotal());
                    }
                  }
                }
                // 设置店铺
                memberRefundOrderResDto.setStoreCode(omsOrderInfo.getOrganizationCode());
              }

              memberRefundOrderResDtoList.add(memberRefundOrderResDto);
            }

          }
          if (MemberOrderSource.POS.name().equals(orderSource)) {
            List<String> offlineRefundNoList = esRefundOrderList.stream()
                .map(EsMemberRefundOrder::getRefundNo)
                .distinct().collect(Collectors.toList());

            for (String offlineRefundNo : offlineRefundNoList) {
              SimpleOfflineRefundOrderDetailDto simpleOfflineOrderDetailDto;
              try {
                simpleOfflineOrderDetailDto = offlineOrderRepository.querySimpleOfflineRefundOrderDetail(
                    offlineRefundNo);
              } catch (Exception e) {
                log.warn("退单数据缺失,{}", offlineRefundNo);
                continue;
              }

              OfflineRefundOrderDO offlineRefundOrderDO = simpleOfflineOrderDetailDto.getOfflineRefundOrderDO();
              OfflineRefundOrderCashierDeskDO cashierDeskDO = simpleOfflineOrderDetailDto.getOfflineRefundOrderCashierDeskDO();

              List<OfflineRefundOrderDetailDO> offlineRefundOrderDetailDOList = simpleOfflineOrderDetailDto.getOfflineRefundOrderDetailDOList();
              List<OfflineRefundOrderDetailPickDO> offlineRefundOrderDetailPickDOList = simpleOfflineOrderDetailDto.getOfflineRefundOrderDetailPickDOList();
              List<OfflineRefundOrderDetailTraceDO> offlineRefundOrderDetailTraceDOList = simpleOfflineOrderDetailDto.getOfflineRefundOrderDetailTraceDOList();
              Map<String, List<OfflineRefundOrderDetailPickDO>> refundDetailPickMap = offlineRefundOrderDetailPickDOList.stream()
                  .collect(Collectors.groupingBy(OfflineRefundOrderDetailPickDO::getRefundDetailNo));
              Map<String, List<OfflineRefundOrderDetailTraceDO>> refundDetailTraceMap = offlineRefundOrderDetailTraceDOList.stream()
                  .collect(Collectors.groupingBy(OfflineRefundOrderDetailTraceDO::getRefundDetailNo));

              // 如果找不到退单明细的拣货信息,处理方式
              AppendOfflineOrderInfo appendOfflineOrderInfo = simpleOfflineOrderDetailDto.getAppendOfflineOrderInfo();
              // 根据rowNo+erpCode分组
              Map<String, List<OfflineOrderDetailPickEnhance>> rowNoErpCodeMap = appendOfflineOrderInfo.getOfflineOrderDetailPickEnhanceList().stream()
                  .collect(Collectors.groupingBy(OfflineOrderDetailPickEnhance::rowNoErpCode));

              MemberRefundOrderResDto memberRefundOrderResDto = new MemberRefundOrderResDto();
              memberRefundOrderResDto.setRefundNo(offlineRefundOrderDO.getRefundNo());
              memberRefundOrderResDto.setOrderNo(offlineRefundOrderDO.getOrderNo());
              memberRefundOrderResDto.setThirdOrderNo(offlineRefundOrderDO.getThirdOrderNo());
              memberRefundOrderResDto.setThirdRefundNo(offlineRefundOrderDO.getThirdRefundNo());
              if (RefundStateEnum.REFUNDED.name().equals(offlineRefundOrderDO.getRefundState())) {
                memberRefundOrderResDto.setRefundStatus(MemberRefundStatus.COMPLETED.getStatus());
              }
              memberRefundOrderResDto.setCreated(offlineRefundOrderDO.getCreated());
              memberRefundOrderResDto.setRefundAmount(offlineRefundOrderDO.getConsumerRefund());
              memberRefundOrderResDto.setRefundDeliveryFeeAmount(BigDecimal.ZERO);//线下单没有运费
              memberRefundOrderResDto.setOrderSource(MemberOrderSource.POS.name());
              memberRefundOrderResDto.setReason(Strings.EMPTY);
              Optional.ofNullable(cashierDeskDO).ifPresent(c->{
                memberRefundOrderResDto.setCashier(c.getCashier());
                memberRefundOrderResDto.setCashierName(c.getCashierName());
              });

              memberRefundOrderResDto.setRefundDetailDtoList(
                  offlineRefundOrderDetailDOList.stream().map(refundDetail -> {
                    MemberRefundOrderDetailDto detail = new MemberRefundOrderDetailDto();
                    detail.setErpCode(refundDetail.getErpCode());
                    detail.setErpName(refundDetail.getErpName());
                    detail.setCommoditySpec(refundDetail.getCommoditySpec());
                    detail.setManufacture(refundDetail.getManufacture());
                    detail.setCommodityCount(refundDetail.getRefundCount());
                    detail.setActualRefundAmount(refundDetail.getBillAmount());
                    detail.setBillAmount(refundDetail.getBillAmount());
                    detail.setSalerId(refundDetail.getSalerId());
                    detail.setSalerName(refundDetail.getSalerName());

                    List<OfflineRefundOrderDetailPickDO> offlineRefundOrderDetailPickDOS = refundDetailPickMap.get(
                        refundDetail.getRefundDetailNo());
                    List<OfflineRefundOrderDetailTraceDO> offlineRefundOrderDetailTraceDOS = refundDetailTraceMap.get(
                        refundDetail.getRefundDetailNo());

                    if (!CollectionUtils.isEmpty(offlineRefundOrderDetailPickDOS)) {
                      detail.setPickInfoList(offlineRefundOrderDetailPickDOS.stream().map(pick->{
                        PickInfo pickInfo = new PickInfo();
                        pickInfo.setMarkNo(pick.getMakeNo());
                        pickInfo.setCount(pick.getCount());
                        buildPickInfoTraceCode(offlineRefundOrderDetailTraceDOS, pickInfo);
                        return pickInfo;
                      }).collect(Collectors.toList()));
                    }else{

                      String rowNoErpCodeKey = String.format("%s_%s",refundDetail.getRowNo(),refundDetail.getErpCode());
                      List<OfflineOrderDetailPickEnhance> offlineOrderDetailPickEnhances = rowNoErpCodeMap.get(
                          rowNoErpCodeKey);
                      if(!CollectionUtils.isEmpty(offlineOrderDetailPickEnhances)){
                        detail.setPickInfoList(offlineOrderDetailPickEnhances.stream().map(pick->{
                          PickInfo pickInfo = new PickInfo();
                          pickInfo.setMarkNo(pick.getMakeNo());
                          pickInfo.setCount(refundDetail.getRefundCount()); // 取的是退款数量
                          buildPickInfoTraceCode(offlineRefundOrderDetailTraceDOS, pickInfo);
                          return pickInfo;
                        }).collect(Collectors.toList()));
                      }
                    }
                    return detail;
                  }).collect(Collectors.toList()));
              memberRefundOrderResDto.setBillAmount(offlineRefundOrderDetailDOList.stream()
                  .map(OfflineRefundOrderDetailDO::getBillAmount).reduce(BigDecimal::add)
                  .orElse(BigDecimal.ZERO));
              memberRefundOrderResDto.setStoreCode(offlineRefundOrderDO.getStoreCode());
              memberRefundOrderResDtoList.add(memberRefundOrderResDto);
            }
          }
        });
    // 处理完之后就乱序了,这里重新根据创建时间排序
    memberRefundOrderResDtoList.sort(Comparator.comparing(MemberRefundOrderResDto::getCreated).reversed());
    return memberRefundOrderResDtoList;
  }

  private static void buildPickInfoTraceCode(
      List<OfflineRefundOrderDetailTraceDO> offlineRefundOrderDetailTraceDOS, PickInfo pickInfo) {
    Optional.ofNullable(offlineRefundOrderDetailTraceDOS).ifPresent(traceList->{
      pickInfo.setTraceCodeList(traceList.stream().map(OfflineRefundOrderDetailTraceDO::getTraceCode).collect(
          Collectors.toList()));
    });
  }

  @Override
  public ResponseBase<PageDTO<MemberRefundOrderSimpleResDto>> refundOrderTransactionSimple(
      MemberRefundOrderSimpleReqDto reqDto) {
    LambdaEsQueryWrapper<EsMemberRefundOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.routing(reqDto.getUserId());
    queryWrapper.eq(EsMemberRefundOrder::getUserId, reqDto.getUserId());
    queryWrapper.gt(EsMemberRefundOrder::getCreated,
        OrderDateUtils.formatYYMMDD(reqDto.getCreatedStart()));
    queryWrapper.lt(EsMemberRefundOrder::getCreated,
        OrderDateUtils.formatYYMMDD(reqDto.getCreatedEnd()));
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getStoreCode()),
        EsMemberRefundOrder::getStoreCode, reqDto.getStoreCode());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getUserCardNo()),
        EsMemberRefundOrder::getUserCardNo, reqDto.getUserCardNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getOrderSource()),
        EsMemberRefundOrder::getOrderSource, reqDto.getOrderSource());
    queryWrapper.eq(Objects.nonNull(reqDto.getRefundStatus()), EsMemberRefundOrder::getRefundStatus,
        reqDto.getRefundStatus());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getThirdOrderNo()),
        EsMemberRefundOrder::getThirdOrderNo, reqDto.getThirdOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getOrderNo()), EsMemberRefundOrder::getOrderNo,
        reqDto.getOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getRefundNo()), EsMemberRefundOrder::getRefundNo,
        reqDto.getRefundNo());
    queryWrapper.eq(StringUtils.isNotEmpty(reqDto.getThirdRefundNo()),
        EsMemberRefundOrder::getThirdRefundNo, reqDto.getThirdRefundNo());
    refundItemQuery(queryWrapper,reqDto.getErpCode(),reqDto.getErpName());
    queryWrapper.orderByDesc(EsMemberRefundOrder::getCreated);
    EsPageInfo<EsMemberRefundOrder> esPageInfo = esMemberRefundOrderMapper.pageQuery(queryWrapper,
        reqDto.getCurrentPage().intValue(), reqDto.getPageSize().intValue());
    long total = esPageInfo.getTotal();
    if (0 == total) {
      return generateSuccess(new PageDTO<>());
    }

    List<MemberRefundOrderResDto> memberRefundOrderResDtoList = buildMemberRefundOrderList(
        esPageInfo.getList(), Boolean.FALSE);

    PageDTO<MemberRefundOrderSimpleResDto> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(total);
    pageDTO.setTotalPage((long) esPageInfo.getPages());
    pageDTO.setData(memberRefundOrderResDtoList.stream().map(refundOrderRes -> {
      MemberRefundOrderSimpleResDto simple = new MemberRefundOrderSimpleResDto();
      simple.setRefundNo(refundOrderRes.getRefundNo());
      simple.setOrderNo(refundOrderRes.getOrderNo());
      simple.setThirdOrderNo(refundOrderRes.getThirdOrderNo());
      simple.setThirdRefundNo(refundOrderRes.getThirdRefundNo());
      simple.setRefundStatus(refundOrderRes.getRefundStatus());
      simple.setCreated(refundOrderRes.getCreated());
      simple.setRefundAmount(refundOrderRes.getRefundAmount());
      simple.setRefundDeliveryFeeAmount(refundOrderRes.getRefundDeliveryFeeAmount());
      simple.setOrderSource(refundOrderRes.getOrderSource());
      simple.setReason(refundOrderRes.getReason());
      simple.setBillAmount(refundOrderRes.getBillAmount());
      simple.setStoreCode(refundOrderRes.getStoreCode());
      simple.setCashier(refundOrderRes.getCashier());
      simple.setCashierName(refundOrderRes.getCashierName());
      return simple;
    }).collect(Collectors.toList()));
    pageDTO.setCurrentPage((long) esPageInfo.getPageNum());
    pageDTO.setPageSize((long) esPageInfo.getPageSize());
    return generateSuccess(pageDTO);
  }

  @Override
  public ResponseBase<MemberRefundOrderResDto> refundOrderTransactionDetail(
      MemberRefundOrderDetailReqDto reqDto) {
    LambdaEsQueryWrapper<EsMemberRefundOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.routing(reqDto.getUserId());
    queryWrapper.eq(EsMemberRefundOrder::getRefundNo, reqDto.getRefundNo());
    queryWrapper.eq(EsMemberRefundOrder::getOrderSource, reqDto.getOrderSource());
    EsMemberRefundOrder esMemberRefundOrder = esMemberRefundOrderMapper.selectOne(queryWrapper);
    if(Objects.isNull(esMemberRefundOrder)){
      return generateSuccess(null);
    }

    List<MemberRefundOrderResDto> memberRefundOrderResDtoList = buildMemberRefundOrderList(
        Lists.newArrayList(esMemberRefundOrder), Boolean.TRUE);
    if (CollectionUtils.isEmpty(memberRefundOrderResDtoList)) {
      return generateSuccess(null);
    }

    return generateSuccess(memberRefundOrderResDtoList.get(0));
  }

  @Override
  public ResponseBase<Boolean> createMemberOrderIndex() {
    return generateSuccess(esMemberOrderMapper.createIndex());
  }

  @Override
  public ResponseBase<Boolean> createMemberRefundOrderIndex() {
    return generateSuccess(esMemberRefundOrderMapper.createIndex());
  }
}
