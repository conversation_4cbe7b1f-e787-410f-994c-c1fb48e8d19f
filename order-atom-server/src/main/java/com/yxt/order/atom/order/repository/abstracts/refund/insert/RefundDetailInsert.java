package com.yxt.order.atom.order.repository.abstracts.refund.insert;

import cn.hutool.core.collection.CollUtil;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundInsert;
import com.yxt.order.atom.order.repository.batch.RefundDetailBatchRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RefundDetailInsert extends AbstractRefundInsert<List<RefundDetailDO>> {

  @Autowired
  private RefundDetailBatchRepository refundDetailBatchRepository;

  @Override
  protected Boolean canInsert() {
    return CollUtil.isNotEmpty(data());
  }

  @Override
  protected Integer insert(List<RefundDetailDO> refundDetailDOS) {
    return refundDetailBatchRepository.saveBatch(refundDetailDOS) ? refundDetailDOS.size() : 0;
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<RefundDetailDO> data() {
    return this.saveData.getRefundDetailList();
  }
}
