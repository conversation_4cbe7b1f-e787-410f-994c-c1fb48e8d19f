package com.yxt.order.atom.order.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单明细成本价表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@TableName("order_detail_commodity_cost_price")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderDetailCommodityCostPriceDO implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 订单号
   */
  private Long orderNo;

  /**
   * 商品erp编码
   */
  private String erpCode;

  /**
   * 商品批号
   */
  private String makeNo;

  /**
   * 商品批次
   */
  private String batchNo;

  /**
   * 商品成本单价
   */
  private BigDecimal costPrice;

  /**
   * 商品加权成本单价
   */
  private BigDecimal averagePrice;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;

  public String uniqueKey(){
    return uniqueKeyMaybe(this.getOrderNo(),this.getErpCode());
  }

  private static String uniqueKeyMaybe(Long orderNo, String erpCode){
    String format = "%s_%s";
    return String.format(format,orderNo,erpCode);
  }

}
