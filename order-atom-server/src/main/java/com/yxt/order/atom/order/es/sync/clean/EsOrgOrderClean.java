package com.yxt.order.atom.order.es.sync.clean;

import cn.hutool.core.date.DateUtil;
import com.yxt.order.atom.order.es.doc.EsOrgOrder;
import com.yxt.order.atom.order.es.mapper.EsOrgOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractClean;
import java.util.Date;
import javax.annotation.Resource;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


@Component
public class EsOrgOrderClean extends AbstractClean {

  @Resource
  private EsOrgOrderMapper esOrgOrderMapper;

  @Override
  protected Integer expireDays() {
    return 365 * 2 + 20; // 保存2年,多冗余20天
  }

  @Override
  protected void clean(String startDate, String endDate) {
    LambdaEsQueryWrapper<EsOrgOrder> query = new LambdaEsQueryWrapper<>();
    query.gt(EsOrgOrder::getCreateTime, startDate);
    query.le(EsOrgOrder::getCreateTime, endDate);
    esOrgOrderMapper.delete(query);
  }

  @Override
  protected Boolean checkHasData(String endDate) {
    LambdaEsQueryWrapper<EsOrgOrder> query = new LambdaEsQueryWrapper<>();
    query.le(EsOrgOrder::getCreateTime, endDate);
    Long count = esOrgOrderMapper.selectCount(query);
    return count > 0;
  }

  @Override
  protected Date getLatestdEndDate(Date endDate) {
    LambdaEsQueryWrapper<EsOrgOrder> query = new LambdaEsQueryWrapper<>();
    query.le(EsOrgOrder::getCreateTime, DateUtil.formatDateTime(endDate));
    query.orderByDesc(EsOrgOrder::getCreateTime);
    EsPageInfo<EsOrgOrder> esPageInfo = esOrgOrderMapper.pageQuery(query, 1, 1);
    if(CollectionUtils.isEmpty(esPageInfo.getList())){
      return endDate;
    }
    return esPageInfo.getList().get(0).getCreateTime();
  }
}
