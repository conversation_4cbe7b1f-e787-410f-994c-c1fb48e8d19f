package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 线下单明细促销
 */
@Data
@TableName("offline_order_detail_promotion")
public class OrderDetailPromotionDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 内部订单号,自己生成
   */
  private String orderNo;

  /**
   * 内部明细编号,自己生成
   */
  private String orderDetailNo;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 促销编码
   */
  private String promotionNo;

  /**
   * 促销类型 HANDMADE_DISCOUNTS-手工折扣 PROMOTIONAL_DISCOUNTS - 促销折扣 INTEGRAL - 积分
   */
  private String promotionType;

  /**
   * 促销金额
   */
  private BigDecimal promotionAmount;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
