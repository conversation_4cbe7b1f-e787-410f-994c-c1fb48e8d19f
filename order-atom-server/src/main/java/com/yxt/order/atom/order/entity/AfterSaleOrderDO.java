package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *  退款退货售后单表
 * <AUTHOR>
 * @since 2020-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("after_sale_order")
public class AfterSaleOrderDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("售后单号")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long afterSaleNo;

    @ApiModelProperty("退款单号")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long refundNo;

    @ApiModelProperty("退货单号")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long returnGoodsNo;

    @ApiModelProperty("系统订单号")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long omsOrderNo;

    @ApiModelProperty(value = "订单状态, 10待处理（待审核）,30待配送（待发货）,40待收货（已发货）,100已完成（已收货）,102已取消,101已关闭",
            allowableValues = "10, 30, 40, 100, 101, 102")
    private Integer orderStatus;

    @ApiModelProperty("售后类型 0、仅退款，1、退货退款")
    private Integer afterSaleType;

    @ApiModelProperty("售后来源 1、退款生成，2、手工录入")
    private Integer afterSaleSource;

    @ApiModelProperty("店铺编码")
    private String shopCode;

    @ApiModelProperty("店铺名称")
    private String shopName;

    @ApiModelProperty("系统备注")
    private String remarks;

    @ApiModelProperty("售后状态 0.待处理、1.已完成、2.已关闭")
    private Integer afterSaleStatus;

    @ApiModelProperty("三方平台名称")
    @TableField(exist = false)
    private String thirdPlatformName;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    @ApiModelProperty("erp状态: 20.待退账（待锁定）,30.待下账，100.已退账（已下账）,102.退款已取消，110.已取消，120.已退款")
    private Integer erpState;

    @ApiModelProperty("退款单零售流水")
    private String erpRefundNo;

    @ApiModelProperty("下账时间")
    private Date billTime;

    @ApiModelProperty("退款商品总金额")
    private BigDecimal totalFoodAmount;

    @ApiModelProperty("退款商品退用户金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("退买家总金额")
    private BigDecimal consumerRefund;

    @ApiModelProperty("商家退款总金额")
    private BigDecimal shopRefund;

    @ApiModelProperty("退还佣金")
    private BigDecimal feeRefund;

    @ApiModelProperty("退平台优惠")
    private BigDecimal platformDiscountRefund;

    @ApiModelProperty("退还商家优惠")
    private BigDecimal shopDiscountRefund;

    @ApiModelProperty("退平台配送费")
    private BigDecimal platformRefundDeliveryFee;

    @ApiModelProperty("退商家配送费")
    private BigDecimal merchantRefundPostFee;

    @ApiModelProperty("退平台包装费")
    private BigDecimal platformRefundPackFee;

    @ApiModelProperty("退商家包装费")
    private BigDecimal merchantRefundPackFee;

    @ApiModelProperty("退商品明细优惠")
    private BigDecimal detailDiscountAmount;

    @ApiModelProperty("退款原因")
    private String reason;

    @ApiModelProperty("退款描述")
    private String description;

    /** 异常信息 */
    private String exReason;

    @ApiModelProperty("退回仓库code")
    private String warehouseCode;

    @ApiModelProperty("手工调整金额")
    private BigDecimal adjustAmount;

    @ApiModelProperty("创建人")
    private String createUserName;
}