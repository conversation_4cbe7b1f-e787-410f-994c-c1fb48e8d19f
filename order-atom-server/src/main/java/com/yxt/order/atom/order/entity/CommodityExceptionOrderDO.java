package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 商品异常订单
 *
 * <AUTHOR>
 * @date 2021/4/7 下午2:09
 */
@Data
@TableName("commodity_exception_order")
public class CommodityExceptionOrderDO implements Serializable {

  private static final long serialVersionUID = -4640487504652904093L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 订单号
   */
  private Long orderNo;

  /**
   * 状态 cn.hydee.middle.business.order.Enums.OrderDetailStatusEnum
   */
  private Integer status;

  /**
   * 商品erpCode
   */
  private String erpCode;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;

  /**
   * 业务类型 1 商品中台 2 erp 3 标记为erp库存不足
   */
  private Integer bizType;

  /**
   * 原因
   */
  private String reason;

  /**
   * 原因枚举
   */
  private Integer reasonType;

  @ApiModelProperty(value = "库存快照")
  private Integer stockTemp;

  @ApiModelProperty(value = "商品名称，历史数据为空@202307")
  private String commodityName;

  public String uniqueKey() {
    return String.format("%s_%s", orderNo, erpCode);
  }
}
