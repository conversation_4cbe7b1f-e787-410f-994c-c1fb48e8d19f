package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.sdk.online_order.delivery.dto.OrderDeliveryRecordResDto;
import com.yxt.order.atom.sdk.online_order.delivery.dto.req.UpdateOrderDeliveryRecordReqDto;
import com.yxt.order.atom.sdk.online_order.pick.dto.req.InsertPickReqDto;
import com.yxt.order.types.order.OrderNo;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:28
 * @email: <EMAIL>
 */
public interface PickRepository {




  Boolean insertPick(InsertPickReqDto req);
}
