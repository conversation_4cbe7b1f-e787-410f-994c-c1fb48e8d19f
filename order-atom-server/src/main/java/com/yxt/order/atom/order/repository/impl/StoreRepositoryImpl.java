package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtParamException;
import com.yxt.order.atom.order.converter.StoreConverter;
import com.yxt.order.atom.order.entity.DsMerchantGroupInfoDO;
import com.yxt.order.atom.order.entity.DsOnlineClientDO;
import com.yxt.order.atom.order.entity.DsOnlineStoreConfigDO;
import com.yxt.order.atom.order.entity.DsOnlineStoreDO;
import com.yxt.order.atom.order.entity.DsOnlineStoreDeliveryDO;
import com.yxt.order.atom.order.entity.DsStoreSoundConfigDO;
import com.yxt.order.atom.order.entity.InnerStoreDictionaryDO;
import com.yxt.order.atom.order.entity.StoreBillConfigDO;
import com.yxt.order.atom.order.mapper.DsMerchantGroupInfoMapper;
import com.yxt.order.atom.order.mapper.DsOnlineClientMapper;
import com.yxt.order.atom.order.mapper.DsOnlineStoreConfigMapper;
import com.yxt.order.atom.order.mapper.DsOnlineStoreDeliveryMapper;
import com.yxt.order.atom.order.mapper.DsOnlineStoreMapper;
import com.yxt.order.atom.order.mapper.DsStoreOrderConfigDO;
import com.yxt.order.atom.order.mapper.DsStoreOrderConfigMapper;
import com.yxt.order.atom.order.mapper.DsStoreRiderPollingConfigMapper;
import com.yxt.order.atom.order.mapper.DsStoreSoundConfigMapper;
import com.yxt.order.atom.order.mapper.InnerStoreDictionaryMapper;
import com.yxt.order.atom.order.mapper.StoreBillConfigMapper;
import com.yxt.order.atom.order.repository.StoreRepository;
import com.yxt.order.atom.sdk.common.data.DsOnlineStoreConfigDTO;
import com.yxt.order.atom.sdk.common.data.DsOnlineStoreDTO;
import com.yxt.order.atom.sdk.common.data.DsStoreOrderConfigDTO;
import com.yxt.order.atom.sdk.common.data.DsStoreRiderPollingConfigDTO;
import com.yxt.order.atom.sdk.common.data.DsStoreSoundConfigDTO;
import com.yxt.order.atom.sdk.common.data.StoreBillConfigDTO;
import com.yxt.order.atom.sdk.online_order.store.req.GetOnlineStoreByPlatformShopIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.InnerStoreDictionaryListReq;
import com.yxt.order.atom.sdk.online_order.store.req.InnerStoreDictionaryReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigByIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStoreAccessReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStorePageReq;
import com.yxt.order.atom.sdk.online_order.store.req.QuerySysStoreInfoReq;
import com.yxt.order.atom.sdk.online_order.store.req.StoreQueryByScaleReq;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreConfigResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreResDto;
import com.yxt.order.atom.sdk.online_order.store.res.InnerStoreDictionaryResDto;
import com.yxt.order.atom.sdk.online_order.store.res.OnlineStoreInfoResDto;
import com.yxt.order.atom.sdk.online_order.store.res.RiderPollingConfigResDto;
import com.yxt.order.atom.sdk.online_order.store.res.StoreQueryByScaleResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsStoreResDto;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.order.enums.StoreQryScaleEnum;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月04日 16:18
 * @email: <EMAIL>
 */
@Repository
public class StoreRepositoryImpl implements StoreRepository {

  @Resource
  private StoreBillConfigMapper storeBillConfigMapper;

  @Resource
  private DsOnlineClientMapper dsOnlineClientMapper;

  @Resource
  private DsOnlineStoreMapper dsOnlineStoreMapper;

  @Resource
  private DsMerchantGroupInfoMapper dsMerchantGroupInfoMapper;

  @Resource
  private DsStoreOrderConfigMapper dsStoreOrderConfigMapper;

  @Resource
  private DsStoreRiderPollingConfigMapper dsStoreRiderPollingConfigMapper;

  @Resource
  private DsOnlineStoreDeliveryMapper dsOnlineStoreDeliveryMapper;

  @Resource
  private DsOnlineStoreConfigMapper dsOnlineStoreConfigMapper;

  @Resource
  private InnerStoreDictionaryMapper innerStoreDictionaryMapper;

  @Resource
  private DsStoreSoundConfigMapper dsStoreSoundConfigMapper;

  @Resource
  private DsStoreRiderPollingConfigMapper riderPollingConfigMapper;

  @Override
  public DsOnlineClientResDto selectSupplierClient(String platformCode, String onlineStoreCode) {
    DsOnlineClientDO dsOnlineClientDO = dsOnlineClientMapper.selectSupplierClient(platformCode,
        onlineStoreCode);
    return StoreConverter.convert2Client(dsOnlineClientDO);
  }

  @Override
  public OnlineStoreInfoResDto querySysStoreInfo(QuerySysStoreInfoReq req) {
    String merCode = req.getMerCode();
    String platformCode = req.getPlatformCode();
    String clientCode = req.getClientCode();
    String onlineStoreCode = req.getOnlineStoreCode();

    OnlineStoreInfoResDto sysStoreInfoRes = new OnlineStoreInfoResDto();
    //1.查询线上门店信息根据线上门店id
    //查询唯一门店索引：mer_code, platform_code, online_client_code, online_store_code
    QueryWrapper<DsOnlineStoreDO> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(DsOnlineStoreDO::getMerCode, merCode)
        .eq(DsOnlineStoreDO::getPlatformCode, platformCode)
        .eq(DsOnlineStoreDO::getOnlineClientCode, clientCode)
        .eq(DsOnlineStoreDO::getOnlineStoreCode, onlineStoreCode);
    DsOnlineStoreDO dsOnlineStore = dsOnlineStoreMapper.selectOne(queryWrapper);
    if (dsOnlineStore == null) {
      return null;
    }
    sysStoreInfoRes.setId(dsOnlineStore.getId());
    sysStoreInfoRes.setOrganizationCode(dsOnlineStore.getOrganizationCode());
    sysStoreInfoRes.setOrganizationName(dsOnlineStore.getOrganizationName());
    sysStoreInfoRes.setPlatformCode(dsOnlineStore.getPlatformCode());
    sysStoreInfoRes.setOnlineClientCode(dsOnlineStore.getOnlineClientCode());
    sysStoreInfoRes.setOnlineStoreAddress(dsOnlineStore.getAddress());
    sysStoreInfoRes.setOnlineStorePhone(dsOnlineStore.getContactPhone());
    sysStoreInfoRes.setOnlineStoreName(dsOnlineStore.getOnlineStoreName());
    sysStoreInfoRes.setOutShopId(dsOnlineStore.getOutShopId());
    sysStoreInfoRes.setAccessType(dsOnlineStore.getAccessType());

    //3.查询线上门店对应的merCode的sessionKey
    DsMerchantGroupInfoDO dsMerchantGroupInfo = dsMerchantGroupInfoMapper.querySessionKeyByMerCode(
        dsOnlineStore.getMerCode());
    sysStoreInfoRes.setSessionKey(dsMerchantGroupInfo.getSessionKey());

    //4.查询线上门店订单配置信息（merCode、online_store_id）
    QueryWrapper<DsStoreOrderConfigDO> orderConfWrapper = new QueryWrapper<>();
    orderConfWrapper.lambda().eq(DsStoreOrderConfigDO::getMerCode, dsOnlineStore.getMerCode())
        .eq(DsStoreOrderConfigDO::getOnlineStoreId, dsOnlineStore.getId());
    DsStoreOrderConfigDO dsStoreOrderConfig = dsStoreOrderConfigMapper.selectOne(orderConfWrapper);
    if (dsStoreOrderConfig != null) {
      sysStoreInfoRes.setAutoAcceptFlag(dsStoreOrderConfig.getAutoAcceptFlag());
      sysStoreInfoRes.setAutoCallRider(dsStoreOrderConfig.getAutoCallRider());
      sysStoreInfoRes.setAutoPrintPick(dsStoreOrderConfig.getAutoPrintPick());
      sysStoreInfoRes.setAutoPrintReceipt(dsStoreOrderConfig.getAutoPrintReceipt());
      sysStoreInfoRes.setAutoToHems(dsStoreOrderConfig.getAutoToHems());
      sysStoreInfoRes.setRiderPolling(dsStoreOrderConfig.getRiderPolling());
      sysStoreInfoRes.setRiderCompare(dsStoreOrderConfig.getRiderCompare());
      sysStoreInfoRes.setRiderCompareDelay(dsStoreOrderConfig.getRiderCompareDelay());
    }
    //循环呼叫配置
    if (DsConstants.INTEGER_ONE.equals(sysStoreInfoRes.getRiderPolling())) {
      //只查询启用的循环配置
      List<RiderPollingConfigResDto> riderPollingConfigs = dsStoreRiderPollingConfigMapper.queryConfigByStoreId(
          dsOnlineStore.getId());
      List<DsOnlineStoreDeliveryDO> dsOnlineStoreDeliveryList = dsOnlineStoreDeliveryMapper.selectList(
          new QueryWrapper<DsOnlineStoreDeliveryDO>().lambda()
              .eq(DsOnlineStoreDeliveryDO::getMerCode, dsOnlineStore.getMerCode())
              .eq(DsOnlineStoreDeliveryDO::getOnlineStoreId, dsOnlineStore.getId())
              .eq(DsOnlineStoreDeliveryDO::getStatus, DsConstants.INTEGER_ONE));
      riderPollingConfigs = riderPollingConfigs.stream().filter(
              item -> dsOnlineStoreDeliveryList.stream().map(DsOnlineStoreDeliveryDO::getDeliveryType)
                  .collect(Collectors.toList()).contains(item.getPlatformName()))
          .sorted(Comparator.comparing(RiderPollingConfigResDto::getPriority))
          .collect(Collectors.toList());

      sysStoreInfoRes.setRiderPollingConfigs(riderPollingConfigs);
    }

    //5.查询对应的线上门店配置（merCode、online_store_id）
    QueryWrapper<DsOnlineStoreConfigDO> storeConfWrapper = new QueryWrapper<>();
    storeConfWrapper.lambda().eq(DsOnlineStoreConfigDO::getMerCode, dsOnlineStore.getMerCode())
        .eq(DsOnlineStoreConfigDO::getOnlineStoreId, dsOnlineStore.getId());
    DsOnlineStoreConfigDO onlineStoreConfig = dsOnlineStoreConfigMapper.selectOne(storeConfWrapper);
    if (DsConstants.INTEGER_ZERO.equals(onlineStoreConfig.getWhetherNeedPrescription())) {
      onlineStoreConfig.setCheckingType(null);
    }
    if (onlineStoreConfig != null) {
      sysStoreInfoRes.setSelfDeliveryType(onlineStoreConfig.getSelfDeliveryType());
      sysStoreInfoRes.setDeliveryFeeTo(onlineStoreConfig.getDeliveryFeeTo());
      sysStoreInfoRes.setPackageFeeTo(onlineStoreConfig.getPackageFeeTo());
      sysStoreInfoRes.setPlatformBenefitOwner(onlineStoreConfig.getPlatformBenefitOwner());
      sysStoreInfoRes.setRetailTime(onlineStoreConfig.getRetailTime());
      sysStoreInfoRes.setWhetherDiscountShare(onlineStoreConfig.getWhetherDiscountShare());
      sysStoreInfoRes.setWhetherCommissionShare(onlineStoreConfig.getWhetherCommissionShare());
      sysStoreInfoRes.setWhetherInventoryLocked(onlineStoreConfig.getWhetherInventoryLocked());
      sysStoreInfoRes.setWhetherNeedPrescription(onlineStoreConfig.getWhetherNeedPrescription());
      sysStoreInfoRes.setCheckingType(onlineStoreConfig.getCheckingType());
      sysStoreInfoRes.setPickType(onlineStoreConfig.getPickType());
    }

    //6.查询对应的线上门店声音配置（merCode、online_store_id）
    QueryWrapper<DsStoreSoundConfigDO> storeSoundConfigWrapper = new QueryWrapper<>();
    storeSoundConfigWrapper.lambda()
        .eq(DsStoreSoundConfigDO::getMerCode, dsOnlineStore.getMerCode())
        .eq(DsStoreSoundConfigDO::getOnlineStoreId, dsOnlineStore.getId());
    DsStoreSoundConfigDO storeSoundConfig = dsStoreSoundConfigMapper.selectOne(
        storeSoundConfigWrapper);
    if (storeSoundConfig != null) {
      //7.查询对应的线上门店声音配置（merCode、online_store_id）
      sysStoreInfoRes.setNewOrder(storeSoundConfig.getNewOrder() == null ? DsConstants.INTEGER_ONE
          : storeSoundConfig.getNewOrder());
      sysStoreInfoRes.setBookingOrder(
          storeSoundConfig.getBookingOrder() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getBookingOrder());
      sysStoreInfoRes.setRefundOrder(
          storeSoundConfig.getRefundOrder() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getRefundOrder());
      sysStoreInfoRes.setCancelOrder(
          storeSoundConfig.getCancelOrder() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getCancelOrder());
      sysStoreInfoRes.setUrgeOrder(storeSoundConfig.getUrgeOrder() == null ? DsConstants.INTEGER_ONE
          : storeSoundConfig.getUrgeOrder());
      sysStoreInfoRes.setDeliveryException(
          storeSoundConfig.getDeliveryException() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getDeliveryException());
      sysStoreInfoRes.setPrinterDisconnect(
          storeSoundConfig.getPrinterDisconnect() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getPrinterDisconnect());
      sysStoreInfoRes.setNetDisconnect(
          storeSoundConfig.getNetDisconnect() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getNetDisconnect());
      sysStoreInfoRes.setRiderCancelOrder(
          storeSoundConfig.getRiderCancelOrder() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getRiderCancelOrder());
      sysStoreInfoRes.setRiderAbnormal(
          storeSoundConfig.getRiderAbnormal() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getRiderAbnormal());
      sysStoreInfoRes.setWaitTrialParty(
          storeSoundConfig.getWaitTrialParty() == null ? DsConstants.INTEGER_ONE
              : storeSoundConfig.getWaitTrialParty());
      sysStoreInfoRes.setPickNotify(
          storeSoundConfig.getPickNotify() == null ? DsConstants.INTEGER_ZERO
              : storeSoundConfig.getPickNotify());
      sysStoreInfoRes.setPickNotifyMins(
          storeSoundConfig.getPickNotifyMins() == null ? DsConstants.INTEGER_ZERO
              : storeSoundConfig.getPickNotifyMins());
      sysStoreInfoRes.setDeliveryNotify(
          storeSoundConfig.getDeliveryNotify() == null ? DsConstants.INTEGER_ZERO
              : storeSoundConfig.getDeliveryNotify());
      sysStoreInfoRes.setDeliveryNotifyMins(
          storeSoundConfig.getDeliveryNotifyMins() == null ? DsConstants.INTEGER_ZERO
              : storeSoundConfig.getDeliveryNotifyMins());
      // 销售单待下账、退款单待下账
      sysStoreInfoRes.setNeedBillOrder(Optional.ofNullable(storeSoundConfig.getNeedBillOrder())
          .orElse(DsConstants.INTEGER_ZERO));
      sysStoreInfoRes.setNeedBillRefund(Optional.ofNullable(storeSoundConfig.getNeedBillRefund())
          .orElse(DsConstants.INTEGER_ZERO));
      sysStoreInfoRes.setNeedBillOrderFail(
          Optional.ofNullable(storeSoundConfig.getNeedBillOrderFail())
              .orElse(DsConstants.INTEGER_ONE));
    }
    return sysStoreInfoRes;
  }

  @Override
  public The3DsStoreResDto getStoreAccess(QueryStoreAccessReq req) {
    String merCode = req.getMerCode();
    String platformCode = req.getPlatformCode();
    String onlineStoreCode = req.getOnlineStoreCode();
    String onlineClientCode = req.getOnlineClientCode();
    return dsOnlineClientMapper.getStoreAccess(merCode, platformCode, onlineStoreCode,
        onlineClientCode);
  }

  @Override
  public DsOnlineStoreResDto queryDsOnlineStore(QueryDsOnlineStoreReq req) {
    QueryWrapper<DsOnlineStoreDO> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(DsOnlineStoreDO::getMerCode, req.getMerCode())
        .eq(DsOnlineStoreDO::getPlatformCode, req.getPlatformCode())
        .eq(DsOnlineStoreDO::getOnlineClientCode, req.getOnlineClientCode())
        .eq(DsOnlineStoreDO::getOnlineStoreCode, req.getOnlineStoreCode());
    DsOnlineStoreDO dsOnlineStore = dsOnlineStoreMapper.selectOne(queryWrapper);
    return BeanUtil.copyProperties(dsOnlineStore, DsOnlineStoreResDto.class);
  }

  @Override
  public DsOnlineStoreConfigResDto queryDsOnlineStoreConfig(QueryDsOnlineStoreConfigReq req) {
    QueryDsOnlineStoreReq queryDsOnlineStoreReq = BeanUtil.copyProperties(req,
        QueryDsOnlineStoreReq.class);
    DsOnlineStoreResDto dsOnlineStore = queryDsOnlineStore(queryDsOnlineStoreReq);

    QueryWrapper<DsOnlineStoreConfigDO> storeConfWrapper = new QueryWrapper<>();
    storeConfWrapper.lambda().eq(DsOnlineStoreConfigDO::getMerCode, dsOnlineStore.getMerCode())
        .eq(DsOnlineStoreConfigDO::getOnlineStoreId, dsOnlineStore.getId());
    DsOnlineStoreConfigDO onlineStoreConfig = dsOnlineStoreConfigMapper.selectOne(storeConfWrapper);
    return BeanUtil.copyProperties(onlineStoreConfig, DsOnlineStoreConfigResDto.class);
  }

  @Override
  public InnerStoreDictionaryResDto queryInnerStoreDictionary(InnerStoreDictionaryReq req) {


    QueryWrapper<InnerStoreDictionaryDO> storeConfWrapper = new QueryWrapper<>();
    storeConfWrapper.lambda().eq(InnerStoreDictionaryDO::getOrganizationCode, req.getOrganizationCode());
    InnerStoreDictionaryDO db = innerStoreDictionaryMapper.selectOne(storeConfWrapper);
    return BeanUtil.copyProperties(db, InnerStoreDictionaryResDto.class);
  }

  @Override
  public List<InnerStoreDictionaryResDto> queryInnerStoreDictionaryList(
      InnerStoreDictionaryListReq req) {
    QueryWrapper<InnerStoreDictionaryDO> storeConfWrapper = new QueryWrapper<>();
    storeConfWrapper.lambda().eq(InnerStoreDictionaryDO::getPosMode, req.getPosMode());
    List<InnerStoreDictionaryDO> list = innerStoreDictionaryMapper.selectList(storeConfWrapper);
    return BeanUtil.copyToList(list, InnerStoreDictionaryResDto.class);
  }

  @Override
  public The3DsOnlineClientResDto getOnlineStoreByPlatformShopId(
      GetOnlineStoreByPlatformShopIdReq req) {
    String merCode = req.getMerCode();
    String platformShopId = req.getPlatformShopId();
    String platformCode = req.getPlatformCode();
    String onlineClientCode = req.getOnlineClientCode();

    return dsOnlineClientMapper.getOnlineStoreByPlatformShopId(
        merCode, platformCode,
        platformShopId,
        onlineClientCode).stream().findFirst().orElse(null);
  }

  @Override
  public StoreBillConfigDTO getBillConfigById(QueryBillConfigByIdReq req) {
    QueryWrapper<StoreBillConfigDO> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(StoreBillConfigDO::getId, req.getClientConfId());
    StoreBillConfigDO storeBillConfigDO = storeBillConfigMapper.selectOne(queryWrapper);
    return BeanUtil.copyProperties(storeBillConfigDO, StoreBillConfigDTO.class);
  }

  @Override
  public StoreBillConfigDTO getBillConfig(QueryBillConfigReq req) {

    QueryWrapper<StoreBillConfigDO> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda()
        .eq(StoreBillConfigDO::getMerCode, req.getMerCode())
        .eq(StoreBillConfigDO::getPlatformCode, req.getPlatformCode())
        .eq(StoreBillConfigDO::getClientCode, req.getClientCode())
        .eq(StoreBillConfigDO::getStoreCode, req.getStoreCode())
        .orderByDesc(StoreBillConfigDO::getId)
        .last(" limit 0,1 ")
    ;
    StoreBillConfigDO storeBillConfigDO = storeBillConfigMapper.selectOne(queryWrapper);
    return BeanUtil.copyProperties(storeBillConfigDO, StoreBillConfigDTO.class);
  }

  @Override
  public StoreQueryByScaleResDto queryStoreInfoByScale(StoreQueryByScaleReq req) {
    StoreQueryByScaleResDto result = new StoreQueryByScaleResDto();

    DsOnlineStoreDO dsOnlineStoreDO = null;
    if (ObjectUtil.isNotNull(req.getId())) {
      dsOnlineStoreDO = dsOnlineStoreMapper.selectById(req.getId());
    } else {
      if (StrUtil.isBlank(req.getOnlineStoreCode()) && StrUtil.isBlank(req.getOnlineClientCode()) && ObjectUtil.isNull(req.getPlatformCode()) && ObjectUtil.isNull(req.getMerCode())) {
        throw new YxtParamException("参数缺失");
      }
      dsOnlineStoreDO = dsOnlineStoreMapper.selectOne(
          Wrappers.<DsOnlineStoreDO>lambdaQuery()
              .eq(DsOnlineStoreDO::getOnlineStoreCode, req.getOnlineStoreCode())
              .eq(DsOnlineStoreDO::getOnlineClientCode, req.getOnlineClientCode())
              .eq(DsOnlineStoreDO::getPlatformCode, req.getPlatformCode().getThirdPlatformCodeEnum().getCode())
              .eq(DsOnlineStoreDO::getMerCode, req.getMerCode().getMerCode())
      );
    }
    if(ObjectUtil.isNull(dsOnlineStoreDO)){
      return null;
    }

    if(CollUtil.isEmpty(req.getScaleList())){
      result.setOnlineStore(BeanUtil.copyProperties(dsOnlineStoreDO, DsOnlineStoreDTO.class));
      return result;
    }
    for (StoreQryScaleEnum scale : req.getScaleList()) {
      switch (scale){
        case STORE_MAIN:
          result.setOnlineStore(BeanUtil.copyProperties(dsOnlineStoreDO, DsOnlineStoreDTO.class));
          break;
        case STORE_CONFIG:
          DsOnlineStoreConfigDO onlineStoreConfig = dsOnlineStoreConfigMapper.selectOne(
              Wrappers.<DsOnlineStoreConfigDO>lambdaQuery()
                .eq(DsOnlineStoreConfigDO::getMerCode, dsOnlineStoreDO.getMerCode())
                .eq(DsOnlineStoreConfigDO::getOnlineStoreId, dsOnlineStoreDO.getId())
          );
          result.setStoreConfig(BeanUtil.copyProperties(onlineStoreConfig, DsOnlineStoreConfigDTO.class));
          break;
        case BILL_CONFIG:
          QueryBillConfigReq billConfigReq = new QueryBillConfigReq();
          billConfigReq.setMerCode(dsOnlineStoreDO.getMerCode());
          billConfigReq.setPlatformCode(dsOnlineStoreDO.getPlatformCode());
          billConfigReq.setClientCode(dsOnlineStoreDO.getOnlineClientCode());
          billConfigReq.setStoreCode(dsOnlineStoreDO.getOnlineStoreCode());
          StoreBillConfigDTO billConfig = getCurrentUseBillConfig(billConfigReq);
          result.setBillConfig(billConfig);
          break;
        case SOUND_CONFIG:
          DsStoreSoundConfigDO dsStoreSoundConfigDO = dsStoreSoundConfigMapper.selectOne(
              Wrappers.<DsStoreSoundConfigDO>lambdaQuery()
                  .eq(DsStoreSoundConfigDO::getMerCode, dsOnlineStoreDO.getMerCode())
                  .eq(DsStoreSoundConfigDO::getOnlineStoreId, dsOnlineStoreDO.getId())
          );
          result.setSoundConfig(BeanUtil.copyProperties(dsStoreSoundConfigDO, DsStoreSoundConfigDTO.class));
          break;
        case ORDER_CONFIG:
          DsStoreOrderConfigDO dsStoreOrderConfigDO = dsStoreOrderConfigMapper.selectOne(
              Wrappers.<DsStoreOrderConfigDO>lambdaQuery()
                  .eq(DsStoreOrderConfigDO::getMerCode, dsOnlineStoreDO.getMerCode())
                  .eq(DsStoreOrderConfigDO::getOnlineStoreId, dsOnlineStoreDO.getId())
          );
          result.setOrderConfig(BeanUtil.copyProperties(dsStoreOrderConfigDO, DsStoreOrderConfigDTO.class));
          break;
        case RIDER_POLLING_CONFIG:
          List<RiderPollingConfigResDto> riderPollingConfigList = riderPollingConfigMapper.queryConfigByStoreId(dsOnlineStoreDO.getId());
          result.setRiderPollingConfigList(BeanUtil.copyToList(riderPollingConfigList, DsStoreRiderPollingConfigDTO.class));
          break;
      }
    }
    return result;
  }

  private StoreBillConfigDTO getCurrentUseBillConfig(QueryBillConfigReq billConfigReq) {
    List<StoreBillConfigDO> storeBillConfigDOS = storeBillConfigMapper.selectList(Wrappers.<StoreBillConfigDO>lambdaQuery()
        .eq(StoreBillConfigDO::getMerCode, billConfigReq.getMerCode())
        .eq(StoreBillConfigDO::getPlatformCode, billConfigReq.getPlatformCode())
        .eq(StoreBillConfigDO::getClientCode, billConfigReq.getClientCode())
        .eq(StoreBillConfigDO::getStoreCode, billConfigReq.getStoreCode())
        .eq(StoreBillConfigDO::getConfType, 3).last(" limit 1 "));
    if(CollUtil.isNotEmpty(storeBillConfigDOS)){
      return BeanUtil.toBean(storeBillConfigDOS.get(0), StoreBillConfigDTO.class);
    }
    return null;
  }

  @DS(DsConstants.DB_ORDER_SLAVE)
  @Override
  public PageDTO<DsOnlineStoreResDto> queryStorePage(QueryStorePageReq req) {
    IPage<DsOnlineStoreDO> page = new Page<>(req.getCurrentPage(), req.getPageSize());
    IPage<DsOnlineStoreDO> onlineStoreDOPage = dsOnlineStoreMapper.selectPage(page, Wrappers.<DsOnlineStoreDO>lambdaQuery()
        .eq(DsOnlineStoreDO::getMerCode, req.getMerCode().getMerCode())
        .eq(DsOnlineStoreDO::getPlatformCode, req.getPlatformCode().getThirdPlatformCodeEnum().getCode())
        .eq(StrUtil.isNotBlank(req.getOnlineClientCode()), DsOnlineStoreDO::getOnlineClientCode, req.getOnlineClientCode())
        .eq(StrUtil.isNotBlank(req.getServiceMode()), DsOnlineStoreDO::getServiceMode, req.getServiceMode())
        .in(CollUtil.isNotEmpty(req.getStoreCodeList()), DsOnlineStoreDO::getOnlineStoreCode, req.getStoreCodeList())
        .in(CollUtil.isNotEmpty(req.getPlatformShopIdList()), DsOnlineStoreDO::getPlatformShopId, req.getPlatformShopIdList())
        .orderByAsc(DsOnlineStoreDO::getId));

    PageDTO<DsOnlineStoreResDto> resultPage = new PageDTO<>(onlineStoreDOPage.getCurrent(), req.getPageSize());
    resultPage.setTotalPage(onlineStoreDOPage.getPages());
    resultPage.setTotalCount(onlineStoreDOPage.getTotal());
    if (CollUtil.isNotEmpty(onlineStoreDOPage.getRecords())) {
      resultPage.setData(BeanUtil.copyToList(onlineStoreDOPage.getRecords(), DsOnlineStoreResDto.class));
    }
    return resultPage;
  }
}
