package com.yxt.order.atom.order_sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 订单同步日志记录表
 */
@Data
@TableName("order_sync_init_error_log")
public class OrderSyncInitErrorLogDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 同步前业务单号
   */
  private String businessNo;

  /**
   * 刷数类型
   */
  private String flashType;

  /**
   * 触发类型  JOB,MQ,API
   */
  private String triggerType;

  /**
   * 追踪id
   */
  private String traceId;

  /**
   * 备注信息
   */
  private String remark;

  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;
}
