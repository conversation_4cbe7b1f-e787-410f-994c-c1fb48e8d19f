package com.yxt.order.atom.order.es.sync.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import com.yxt.order.atom.order.es.sync.es_order.service.EsOrderModelService;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/11/5 11:17
 */
@Component
public class EsOrderIndexModelOperate extends AbstractEsOperate<EsOrderIndexModel> {

  @Resource
  private EsOrderModelService esOrderModelService;

  public EsOrderIndexModelOperate() {
    super(EsOrderIndexModel.class);
  }

  @Override
  protected Boolean exec() {

    if (Type.DELETE.equals(getType())) {
      return esOrderModelService.deleteEsOrder(getT());
    } else if (Type.SAVE.equals(getType())) {
      return esOrderModelService.saveEsOrder(getT());
    }

    return false;
  }
}
