package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月01日 14:11
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("account_order")
public class AccountInfoDO {



  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 订单号，雪花算法
   */
  @JsonSerialize(using = ToStringSerializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING)
  @ApiModelProperty(value = "订单号")
  private Long orderNo;

  /**
   * 平台code
   */
  private String thirdPlatCode;

  /**
   * 第三方平台订单号
   */
  @ApiModelProperty(value = "第三方平台订单号")
  private String thirdOrderNo;

  private Long deleted;



}
