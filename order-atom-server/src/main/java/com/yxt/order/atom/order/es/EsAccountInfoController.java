package com.yxt.order.atom.order.es;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.es.doc.EsB2cAccountInfo;
import com.yxt.order.atom.order.es.doc.EsB2cRefundAccountInfo;
import com.yxt.order.atom.order.es.mapper.EsB2cAccountInfoMapper;
import com.yxt.order.atom.order.es.mapper.EsB2cRefundAccountInfoMapper;
import com.yxt.order.atom.order.es.wrapper.EsQueryBuilder;
import com.yxt.order.atom.sdk.account_info.AccountAtomEsApi;
import com.yxt.order.atom.sdk.account_info.req.AccountInfoQueryReq;
import com.yxt.order.atom.sdk.account_info.req.RefundAccountInfoQueryReq;
import com.yxt.order.atom.sdk.account_info.res.AccountInfoQueryDto;
import com.yxt.order.atom.sdk.account_info.res.AccountItemDto;
import com.yxt.order.atom.sdk.account_info.res.RefundAccountInfoQueryDto;
import com.yxt.starter.controller.AbstractController;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> junfeng
 * @date 2024年11月13日 16:31
 */
@RestController
@Slf4j
public class EsAccountInfoController extends AbstractController implements AccountAtomEsApi {

  @Resource
  private EsB2cRefundAccountInfoMapper esB2cRefundAccountInfoMapper;
  @Resource
  private  EsB2cAccountInfoMapper esB2cAccountInfoMapper;

  @Override
  public ResponseBase<Boolean> accountCreateOrderIndex() {
    return generateSuccess(esB2cAccountInfoMapper.createIndex());
  }

  @Override
  public ResponseBase<Boolean> accountCreateRefundIndex() {
    return generateSuccess(esB2cRefundAccountInfoMapper.createIndex());
  }

  @Override
  public ResponseBase<PageDTO<AccountInfoQueryDto>> queryAccountInfo(AccountInfoQueryReq req) {

    PageDTO<AccountInfoQueryDto> pageDTO = new PageDTO<>();
    try {
      LambdaEsQueryWrapper<EsB2cAccountInfo> query = EsQueryBuilder.buildEsQueryForAccountInfoQueryDto(
          req);

      EsPageInfo<EsB2cAccountInfo> result = esB2cAccountInfoMapper.pageQuery(query,
          req.getCurrentPage().intValue(), req.getPageSize().intValue());

      pageDTO.setTotalPage(Long.parseLong(String.valueOf(result.getPages())));
      pageDTO.setTotalCount(result.getTotal());
      pageDTO.setPageSize(req.getPageSize());
      pageDTO.setCurrentPage(req.getCurrentPage());
      if (!ObjectUtil.isEmpty(result.getList())) {
        pageDTO.setData(result.getList().stream().map(e -> {
          AccountInfoQueryDto accountInfoQueryRep = new AccountInfoQueryDto();
          Optional.ofNullable(e.getAccountTime()).ifPresent(accountTime -> {
            accountInfoQueryRep.setAccountTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(e.getAccountTime()), ZoneId.systemDefault()));
          });
          accountInfoQueryRep.setAccountErrMsg(e.getAccountErrMsg());
          accountInfoQueryRep.setThirdOrderNo(e.getThirdOrderNo());
          accountInfoQueryRep.setOrderNo(e.getOrderNo());
          accountInfoQueryRep.setServiceMode(e.getServiceMode());
          accountInfoQueryRep.setOrderType(e.getOrderType());
          accountInfoQueryRep.setPosMode(e.getPosMode());
          accountInfoQueryRep.setPickType(e.getPickType());
          accountInfoQueryRep.setThirdPlatCode(e.getThirdPlatCode());
          accountInfoQueryRep.setOrganizationCode(e.getOrganizationCode());
          accountInfoQueryRep.setAccOrganizationCode(e.getAccOrganizationCode());
          accountInfoQueryRep.setAccOnlineStoreId(e.getAccOnlineStoreId());
          accountInfoQueryRep.setBuyerActualAmount(e.getBuyerActualAmount());
          accountInfoQueryRep.setMerchantActualReceive(e.getMerchantActualReceive());
          accountInfoQueryRep.setGoodsTotalAmount(e.getGoodsTotalAmount());
          accountInfoQueryRep.setBillCommodityAmount(e.getBillCommodityAmount());
          accountInfoQueryRep.setDeliveryFee(e.getDeliveryFee());
          accountInfoQueryRep.setPackageFee(e.getPackageFee());
          accountInfoQueryRep.setMerchantDiscount(e.getMerchantDiscount());
          accountInfoQueryRep.setMedicareAmount(e.getMedicareAmount());
          accountInfoQueryRep.setPayCode(e.getPayCode());
          accountInfoQueryRep.setPayChannel(e.getPayChannel());
          Optional.ofNullable(e.getOrderAcceptTime()).ifPresent(orderAcceptTime -> {
            accountInfoQueryRep.setOrderAcceptTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(e.getOrderAcceptTime()), ZoneId.systemDefault()));
          });
          accountInfoQueryRep.setOrderOperatorId(e.getOrderOperatorId());
          accountInfoQueryRep.setMemberNo(e.getMemberNo());
          accountInfoQueryRep.setCostCenterCode(e.getCostCenterCode());
          accountInfoQueryRep.setState(e.getState());
          accountInfoQueryRep.setSaleNo(e.getSaleNo());
          if (ObjectUtil.isNotNull(e.getItems())) {
            accountInfoQueryRep.setItems(e.getItems().stream().map(item -> {
              AccountItemDto accountItemDto = new AccountItemDto();
              accountItemDto.setStatus(item.getStatus());
              accountItemDto.setErpCode(item.getErpCode());
              return accountItemDto;
            }).collect(Collectors.toList()));
          }
          accountInfoQueryRep.setAccountErrMsg(e.getAccountErrMsg());
          return accountInfoQueryRep;
        }).collect(Collectors.toList()));
      }

    } catch (Exception ex) {
      log.error("queryAccountInfo request:" + JSON.toJSONString(req), ex);
    }
    return ResponseBase.success(pageDTO);
  }

  @Override
  public ResponseBase<PageDTO<RefundAccountInfoQueryDto>> queryRefundAccountInfo(
      RefundAccountInfoQueryReq req) {

    PageDTO<RefundAccountInfoQueryDto> pageDTO = new PageDTO<>();
    try {
      LambdaEsQueryWrapper<EsB2cRefundAccountInfo> query = EsQueryBuilder.buildEsQueryForRefundAccountInfoQueryDto(
          req);
      EsPageInfo<EsB2cRefundAccountInfo> result = esB2cRefundAccountInfoMapper.pageQuery(query,
          req.getCurrentPage().intValue(), req.getPageSize().intValue());
      pageDTO.setTotalPage(Long.parseLong(String.valueOf(result.getPages())));
      pageDTO.setTotalCount(result.getTotal());
      pageDTO.setPageSize(req.getPageSize());
      pageDTO.setCurrentPage(req.getCurrentPage());
      if (!ObjectUtil.isEmpty(result.getList())) {
        pageDTO.setData(result.getList().stream().map(e -> {
          RefundAccountInfoQueryDto accountInfoQueryRep = new RefundAccountInfoQueryDto();
          accountInfoQueryRep.setId(e.getId());
          accountInfoQueryRep.setThirdOrderNo(e.getThirdOrderNo());
          accountInfoQueryRep.setOrderNo(e.getOrderNo());
          accountInfoQueryRep.setRefundNo(e.getRefundNo());
          accountInfoQueryRep.setThirdRefundNo(e.getThirdRefundNo());
          accountInfoQueryRep.setRefundType(e.getRefundType());
          accountInfoQueryRep.setType(e.getType());
          accountInfoQueryRep.setRefundAmount(e.getRefundAmount());
          accountInfoQueryRep.setRefundGoodsTotal(e.getRefundGoodsTotal());
          accountInfoQueryRep.setRefundPostFee(e.getRefundPostFee());
          accountInfoQueryRep.setDiscountAmount(e.getDiscountAmount());
          accountInfoQueryRep.setPackageFee(e.getPackageFee());
          Optional.ofNullable(e.getRefundAcceptTime()).ifPresent(time -> {
            accountInfoQueryRep.setRefundAcceptTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(e.getRefundAcceptTime()), ZoneId.systemDefault()));
          });
          accountInfoQueryRep.setRefundReason(e.getRefundReason());
          accountInfoQueryRep.setState(e.getState());
          accountInfoQueryRep.setSaleNo(e.getSaleNo());
          Optional.ofNullable(e.getAccountTime()).ifPresent(time -> {
            accountInfoQueryRep.setAccountTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(e.getAccountTime()), ZoneId.systemDefault()));
          });
          accountInfoQueryRep.setAccountErrMsg(e.getAccountErrMsg());
          Optional.ofNullable(e.getCreateTime()).ifPresent(time -> {
            accountInfoQueryRep.setCreateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(e.getCreateTime()), ZoneId.systemDefault()));
          });
          Optional.ofNullable(e.getUpdateTime()).ifPresent(time -> {
            accountInfoQueryRep.setUpdateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(e.getUpdateTime()), ZoneId.systemDefault()));
          });
          accountInfoQueryRep.setServiceMode(e.getServiceMode());
          accountInfoQueryRep.setPosMode(e.getPosMode());
          accountInfoQueryRep.setPickType(e.getPickType());
          accountInfoQueryRep.setThirdPlatCode(e.getThirdPlatCode());
          accountInfoQueryRep.setOrganizationCode(e.getOrganizationCode());
          accountInfoQueryRep.setAccOrganizationCode(e.getAccOrganizationCode());
          accountInfoQueryRep.setAccOnlineStoreId(e.getAccOnlineStoreId());
          if (ObjectUtil.isNotNull(e.getItems())) {
            accountInfoQueryRep.setItems(e.getItems().stream().map(item -> {
              AccountItemDto accountItemDto = new AccountItemDto();
              accountItemDto.setStatus(item.getStatus());
              accountItemDto.setErpCode(item.getErpCode());
              return accountItemDto;
            }).collect(Collectors.toList()));
          }
          accountInfoQueryRep.setAccountErrMsg(e.getAccountErrMsg());
          return accountInfoQueryRep;
        }).collect(Collectors.toList()));
      }

    } catch (Exception ex) {
      log.error("query refund AccountInfo request:" + JSON.toJSONString(req), ex);
    }
    return ResponseBase.success(pageDTO);
  }

}
