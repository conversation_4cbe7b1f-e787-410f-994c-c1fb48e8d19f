package com.yxt.order.atom.order.es.sync.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.doc.EsB2cAccountInfo;
import com.yxt.order.atom.order.es.mapper.EsB2cAccountInfoMapper;
import com.yxt.order.atom.order.es.sync.b2c_account_info.EsB2cAccountInfoModel;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: yang jun feng
 * @time: 2024/11/12 11:18
 */
@Component
@Slf4j
public class EsB2cAccountInfoModelOperate extends AbstractEsOperate<EsB2cAccountInfoModel> {

  @Resource
  private EsB2cAccountInfoMapper  esB2cAccountInfoMapper;

  public EsB2cAccountInfoModelOperate() {
    super(EsB2cAccountInfoModel.class);
  }

  @Override
  protected Boolean exec() {

    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;

  }


  private Boolean delete(EsB2cAccountInfoModel accountInfoModel) {
    return esB2cAccountInfoMapper.deleteById(accountInfoModel.getId()) > 0;
  }

  private Boolean save(EsB2cAccountInfoModel accountInfoModel) {
    // 适配逻辑删除
    if(Objects.nonNull(accountInfoModel.getDeleted()) && accountInfoModel.getDeleted() != 0L){// 不为0,表示删除
      return delete(accountInfoModel);
    }

    EsB2cAccountInfo esB2cAccountInfo = accountInfoModel.create();

    LambdaEsQueryWrapper<EsB2cAccountInfo> query = new LambdaEsQueryWrapper<>();
    query.eq(EsB2cAccountInfo::getId, accountInfoModel.getId());
    query.eq(EsB2cAccountInfo::getDeleted, 0L);
    Long count = esB2cAccountInfoMapper.selectCount(query);
    if (count > 0) {
      boolean update = esB2cAccountInfoMapper.updateById (esB2cAccountInfo) > 0;
      if (!update) {
        log.warn("下账单更新索引数据失败,{}", JsonUtils.toJson(esB2cAccountInfo));
      }
      return update;

    } else {
      boolean create = esB2cAccountInfoMapper.insert(esB2cAccountInfo) > 0;
      if (!create) {
        log.warn("下账单创建索引数据失败,{}", JsonUtils.toJson(esB2cAccountInfo));
      }
      return create;
    }
  }
}
