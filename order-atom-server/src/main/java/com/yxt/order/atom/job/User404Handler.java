package com.yxt.order.atom.job;

import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.JsonUtils;
import com.yxt.middle.member.api.MemberInfoApi;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.atom.common.OfflineAlarmComponent;
import com.yxt.order.atom.job.abstractsStageOrder.User404DirectSave2Db;
import com.yxt.order.atom.job.abstractsStageOrder.User404FoundUser;
import com.yxt.order.atom.order.mongo.StagingOrder;
import com.yxt.order.atom.order.repository.StageOrderService;
import com.yxt.order.common.CommonDateUtils;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月16日 17:25
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class User404Handler {

  @Resource
  private StageOrderService stageOrderService;

  @Resource
  private MemberInfoApi memberInfoApi;


  @Value("${stageOrderUser404LimitDay:3}")
  private Double stageOrderUser404LimitDay;


  @Value("${alarmUser404ForOfflineOrder:true}")
  private Boolean alarmUser404ForOfflineOrder;

  @Resource
  private OfflineAlarmComponent offlineAlarmComponent;


  /**
   * 如果是退单找不到正向单，将暂不往下游推，
   * <p>
   * 3小时后，订单落库后，则再自动往下游推。
   * <p>
   * 若中途有找到正向单，则是正向单落库推送后马上进行退单的推送。
   *
   * @return
   * @throws Exception
   */
  @XxlJob("user404Handler")
  public void execute() {
    XxlJobHelper.log("user404Handler start-----");
    try {
      Long total = stageOrderService.countUser404();
      if (Objects.isNull(total) || total == 0) {
        XxlJobHelper.log("user404Handler total:{}", total);
        XxlJobHelper.handleSuccess();
        return;
      }


      List<String> allNotExistsCardNoList = Lists.newArrayList();

      Integer pageSize = 100;
      int totalPages = (int) Math.ceil((double) total / pageSize);
      for (int i = 0; i <= totalPages; i++) {
        List<StagingOrder> user404List = stageOrderService.queryUser404(i,
            pageSize);
        if (CollectionUtils.isEmpty(user404List)) {
          break;
        }

        List<String> notExistsCardNoList = handleUser404(user404List);
        if(!CollectionUtils.isEmpty(notExistsCardNoList)){
          allNotExistsCardNoList.addAll(notExistsCardNoList);
        }
      }

      alarmUser404(allNotExistsCardNoList);

    } catch (Exception e) {
      XxlJobHelper.log(e);
      XxlJobHelper.handleFail("user404Handler 执行失败-----");
      return;
    }

    XxlJobHelper.log("user404Handler end-----");
    XxlJobHelper.handleSuccess();
  }

  private void alarmUser404(List<String> allNotExistsCardNoList) {
    try {
      if(CollectionUtils.isEmpty(allNotExistsCardNoList)){
        return;
      }


      if (!alarmUser404ForOfflineOrder) {
        log.info("找不到用户预警开关已关闭");
        return;
      }

      String content = String.format(
          "[自动任务][拦截订单]截止【%s】,有【%s】条数据有会员卡号,但是通过接口查不到会员信息!卡号如下:%s",
          CommonDateUtils.formatDate(new Date()),
          allNotExistsCardNoList.size(),JsonUtils.toJson(allNotExistsCardNoList));
      offlineAlarmComponent.alarm(content);
    } catch (Exception e) {
      log.error("alarmUser404 告警异常", e);
    }


  }

  @Resource
  private User404DirectSave2Db user404DirectSave2Db;

  @Resource
  private User404FoundUser user404FoundUser;

  private List<String> handleUser404(List<StagingOrder> user404List) {
    List<String> notExistsCardNoList = Lists.newArrayList();
    for (StagingOrder stagingOrder : user404List) {
      String userCardNo = stagingOrder.getUserCardNo();
      if (StringUtils.isEmpty(userCardNo)) {
        log.warn("暂存信息无会员号,{}", JsonUtils.toJson(stagingOrder));
        continue;
      }

      Boolean exists = userExist(userCardNo);
      if (!exists) {
        log.warn("补偿任务,调用会员接口查不到会员信息,{}", JsonUtils.toJson(stagingOrder));
        notExistsCardNoList.add(userCardNo);

        // 判断是否超时指定天数,如果超过,则当做非会员
        double hours = CommonDateUtils.getHoursDifferenceWithDecimal(
            stagingOrder.getCreateTime());
        if (hours >= stageOrderUser404LimitDay * 24) {
          // 当做非会员,直接落库,即不再校验会员不存在是否暂存
          user404DirectSave2Db.handle(stagingOrder);
        }
      } else {
        // 查到用户了继续补偿
        user404FoundUser.handle(stagingOrder);
      }
    }
    return notExistsCardNoList;
  }


  private Boolean userExist(String userCardNo) {
    try {
      ResponseBase<MemberInfoVo> responseBase = memberInfoApi.getMemberByCardNo(userCardNo);
      if ("E2011".equals(responseBase.getCode()) || "会员不存在".equals(responseBase.getMsg())) {
        return false;
      }

      return true;
    } catch (Exception e) {
      log.error("调用会员接口异常,{}", e.getMessage(), e);
      return false;
    }
  }

}
