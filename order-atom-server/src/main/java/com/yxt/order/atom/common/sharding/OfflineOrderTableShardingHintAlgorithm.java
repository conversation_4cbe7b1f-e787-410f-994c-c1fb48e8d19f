package com.yxt.order.atom.common.sharding;


import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.yxt.order.atom.common.configration.ShardingJdbcLogicTableConfigGet;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.apache.shardingsphere.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.hint.HintShardingValue;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 分表
 * <AUTHOR> (moatkon)
 * @date 2024年04月09日 18:46
 * @email: <EMAIL>
 */
@Slf4j
@Component
public class OfflineOrderTableShardingHintAlgorithm implements
    HintShardingAlgorithm<OfflineOrderHit> {

  @Override
  public Collection<String> doSharding(Collection<String> collection,
      HintShardingValue<OfflineOrderHit> hintShardingValue) {
    // 分表结果
    ArrayList<String> shardingResult = Lists.newArrayList();

    String logicTableName = hintShardingValue.getLogicTableName();
    Collection<OfflineOrderHit> values = hintShardingValue.getValues();

    Optional<OfflineOrderHit> first = values.stream().findFirst();
    if (!first.isPresent()) {
      throw new RuntimeException(
          String.format("[table]强制分片无数据,请检查使用方式!!! table:%s", logicTableName));
    }

    shardingResult.add(logicTableName + "_" + commonTableRouteFunc(first.get()));
    return shardingResult;

  }

  public static String commonTableRouteFunc(OfflineOrderHit offlineOrderHit){
    // 扫描表特定的强制路由
    QueryHit queryHit = offlineOrderHit.getQueryHit();
    if (Objects.nonNull(queryHit)) {
      if (!StringUtils.isEmpty(queryHit.getSeq())) {
        return queryHit.getSeq();
      }
    }

    String defineNo = offlineOrderHit.getDefineNo();
    return ShardingHelper.getTableIndexByNo(defineNo);
  }

//  public static final List<String> SHARDING_TABLE_LIST = Lists.newArrayList(
//      "offline_order",
//      "offline_order_cashier_desk",
//      "offline_order_detail",
//      "offline_order_coupon",
//      "offline_order_detail_pick",
//      "offline_order_promotion",
//      "offline_order_organization",
//      "offline_order_pay",
//      "offline_order_prescription",
//      "offline_order_user",
//      "offline_order_med_ins_settle",
//      "offline_refund_order",
//      "offline_refund_order_detail",
//      "offline_refund_order_pay",
//      "offline_refund_order_med_ins_settle",
//      "offline_refund_order_user",
//      "offline_refund_order_cashier_desk",
//      "offline_refund_order_organization"
//  );

  public static Set<String> logicTableSet(){
    return SpringUtil.getBean(ShardingJdbcLogicTableConfigGet.class).getTables().keySet();
  }

  public static void setHintManager(HintManager hintManager, OfflineOrderHit offlineOrderHit) {
    for (String table : logicTableSet()) {
      hintManager.addTableShardingValue(table, offlineOrderHit); // 应用分表算法
      hintManager.addDatabaseShardingValue(table,offlineOrderHit);// 应用分库算法
    }
  }
}
