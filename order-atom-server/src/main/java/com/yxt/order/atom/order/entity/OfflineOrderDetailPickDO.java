package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("offline_order_detail_pick")
public class OfflineOrderDetailPickDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  private String orderDetailPickNo;

  private String orderNo;

  private String orderDetailNo;

  private String erpCode;

  private String makeNo;

  private BigDecimal count;

  private String createdBy;

  private String updatedBy;

  private Date createdTime;

  private Date updatedTime;

  private Long version;

}