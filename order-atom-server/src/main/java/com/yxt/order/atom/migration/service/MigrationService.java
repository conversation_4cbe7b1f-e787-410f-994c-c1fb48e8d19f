package com.yxt.order.atom.migration.service;

import com.yxt.order.atom.migration.MigrationHanaController.ReHandleErrorData;
import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import com.yxt.order.atom.migration.req.SpecifyMigrationBatchReq;
import com.yxt.order.atom.migration.req.SpecifyMigrationId;
import java.util.List;

/**
 * 迁移服务
 *
 * <AUTHOR> (moatkon)
 * @date 2024年06月06日 17:12
 * @email: <EMAIL>
 */
public interface MigrationService {

  void migration(HanaMigrationDO hanaMigrationDO);

  List<HanaMigrationDO> queryEnableMigrationConfigList();

  /**
   * 直接指定的到某一个id
   * @param specifyMigrationId
   * @return
   */
  Boolean specifyArchiveId(SpecifyMigrationId specifyMigrationId);

  Boolean reHandleError(ReHandleErrorData reHandleErrorData);

  Boolean specifyArchiveBatch(SpecifyMigrationBatchReq specifyMigrationBatchReq);
}
