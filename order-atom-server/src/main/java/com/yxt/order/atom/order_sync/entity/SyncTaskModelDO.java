package com.yxt.order.atom.order_sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 同步job
 */
@Getter
@Setter
@TableName("sync_task")
public class SyncTaskModelDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 上下文id
   */
  private String contextId;

  /**
   * 任务类型
   */
  private String taskType;

  /**
   * 任务状态
   */
  private String taskState;

  /**
   * 任务参数
   */
  private String taskParam;

  /**
   * 任务结果返回
   */
  private String taskResult;

  /**
   * 任务备注
   */
  private String taskNote;

  /**
   * 已执行次数
   */
  private Integer executeCount;

  /**
   * 重试间隔，单位：秒
   */
  private Long executeInterval;

  /**
   * 最大执行次数
   */
  private Integer maxExecuteCount;

  /**
   * 下次执行时间
   */
  private LocalDateTime nextExecuteTime;

  /**
   * 扩展信息
   */
  private String extendInfo;

  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;


}
