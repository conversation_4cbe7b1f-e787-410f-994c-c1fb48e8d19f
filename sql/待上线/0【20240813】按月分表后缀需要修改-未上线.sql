RENAME TABLE `offline_order_202408` to `offline_order_2408`;
RENAME TABLE `offline_order_cashier_desk_202408` to `offline_order_cashier_desk_2408`;
RENAME TABLE `offline_order_detail_202408` to `offline_order_detail_2408`;
RENAME TABLE `offline_order_detail_coupon_202408` to `offline_order_detail_coupon_2408`;
RENAME TABLE `offline_order_detail_pick_202408` to `offline_order_detail_pick_2408`;
RENAME TABLE `offline_order_detail_promotion_202408` to `offline_order_detail_promotion_2408`;
RENAME TABLE `offline_order_med_ins_settle_202408` to `offline_order_med_ins_settle_2408`;
RENAME TABLE `offline_order_organization_202408` to `offline_order_organization_2408`;
RENAME TABLE `offline_order_pay_202408` to `offline_order_pay_2408`;
RENAME TABLE `offline_order_prescription_202408` to `offline_order_prescription_2408`;
R<PERSON>AME TABLE `offline_order_user_202408` to `offline_order_user_2408`;
RENAME TABLE `offline_refund_order_202408` to `offline_refund_order_2408`;
RENAME TABLE `offline_refund_order_detail_202408` to `offline_refund_order_detail_2408`;
RENAME TABLE `offline_refund_order_med_ins_settle_202408` to `offline_refund_order_med_ins_settle_2408`;
RENAME TABLE `offline_refund_order_pay_202408` to `offline_refund_order_pay_2408`;
RENAME TABLE `offline_order_202406` to `offline_order_2406`;
RENAME TABLE `offline_order_cashier_desk_202406` to `offline_order_cashier_desk_2406`;
RENAME TABLE `offline_order_detail_202406` to `offline_order_detail_2406`;
RENAME TABLE `offline_order_detail_coupon_202406` to `offline_order_detail_coupon_2406`;
RENAME TABLE `offline_order_detail_pick_202406` to `offline_order_detail_pick_2406`;
RENAME TABLE `offline_order_detail_promotion_202406` to `offline_order_detail_promotion_2406`;
RENAME TABLE `offline_order_med_ins_settle_202406` to `offline_order_med_ins_settle_2406`;
RENAME TABLE `offline_order_organization_202406` to `offline_order_organization_2406`;
RENAME TABLE `offline_order_pay_202406` to `offline_order_pay_2406`;
RENAME TABLE `offline_order_prescription_202406` to `offline_order_prescription_2406`;
RENAME TABLE `offline_order_user_202406` to `offline_order_user_2406`;
RENAME TABLE `offline_refund_order_202406` to `offline_refund_order_2406`;
RENAME TABLE `offline_refund_order_detail_202406` to `offline_refund_order_detail_2406`;
RENAME TABLE `offline_refund_order_med_ins_settle_202406` to `offline_refund_order_med_ins_settle_2406`;
RENAME TABLE `offline_refund_order_pay_202406` to `offline_refund_order_pay_2406`;
RENAME TABLE `offline_order_202407` to `offline_order_2407`;
RENAME TABLE `offline_order_cashier_desk_202407` to `offline_order_cashier_desk_2407`;
RENAME TABLE `offline_order_detail_202407` to `offline_order_detail_2407`;
RENAME TABLE `offline_order_detail_coupon_202407` to `offline_order_detail_coupon_2407`;
RENAME TABLE `offline_order_detail_pick_202407` to `offline_order_detail_pick_2407`;
RENAME TABLE `offline_order_detail_promotion_202407` to `offline_order_detail_promotion_2407`;
RENAME TABLE `offline_order_med_ins_settle_202407` to `offline_order_med_ins_settle_2407`;
RENAME TABLE `offline_order_organization_202407` to `offline_order_organization_2407`;
RENAME TABLE `offline_order_pay_202407` to `offline_order_pay_2407`;
RENAME TABLE `offline_order_prescription_202407` to `offline_order_prescription_2407`;
RENAME TABLE `offline_order_user_202407` to `offline_order_user_2407`;
RENAME TABLE `offline_refund_order_202407` to `offline_refund_order_2407`;
RENAME TABLE `offline_refund_order_detail_202407` to `offline_refund_order_detail_2407`;
RENAME TABLE `offline_refund_order_med_ins_settle_202407` to `offline_refund_order_med_ins_settle_2407`;
RENAME TABLE `offline_refund_order_pay_202407` to `offline_refund_order_pay_2407`;