ALTER TABLE `offline_order_0` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_0` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_2406` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_2406` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_2407` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_2407` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_2408` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_2408` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_1` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_1` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_2` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_2` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_3` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_3` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_4` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_4` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_5` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_5` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_6` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_6` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_7` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_7` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_8` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_8` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_9` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_9` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_10` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_10` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_11` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_11` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_12` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_12` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_13` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_13` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_14` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_14` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_15` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_15` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_16` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_16` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_17` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_17` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_18` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_18` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_19` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_19` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_20` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_20` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_21` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_21` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_22` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_22` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_23` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_23` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_24` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_24` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_25` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_25` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_26` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_26` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_27` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_27` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_28` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_28` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_29` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_29` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_30` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_30` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_31` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_31` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_32` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_32` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_33` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_33` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_34` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_34` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_35` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_35` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_36` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_36` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_37` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_37` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_38` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_38` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_39` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_39` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_40` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_40` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_41` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_41` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_42` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_42` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_43` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_43` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_44` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_44` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_45` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_45` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_46` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_46` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_47` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_47` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_48` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_48` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_49` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_49` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_50` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_50` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_51` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_51` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_52` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_52` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_53` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_53` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_54` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_54` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_55` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_55` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_56` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_56` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_57` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_57` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_58` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_58` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_59` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_59` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_60` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_60` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_61` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_61` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_62` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_62` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_63` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_63` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_64` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_64` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_65` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_65` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_66` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_66` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_67` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_67` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_68` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_68` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_69` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_69` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_70` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_70` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_71` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_71` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_72` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_72` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_73` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_73` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_74` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_74` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_75` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_75` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_76` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_76` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_77` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_77` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_78` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_78` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_79` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_79` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_80` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_80` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_81` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_81` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_82` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_82` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_83` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_83` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_84` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_84` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_85` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_85` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_86` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_86` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_87` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_87` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_88` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_88` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_89` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_89` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_90` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_90` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_91` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_91` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_92` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_92` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_93` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_93` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_94` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_94` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_95` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_95` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_96` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_96` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_97` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_97` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_98` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_98` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_99` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_99` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_100` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_100` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_101` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_101` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_102` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_102` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_103` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_103` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_104` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_104` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_105` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_105` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_106` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_106` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_107` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_107` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_108` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_108` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_109` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_109` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_110` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_110` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_111` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_111` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_112` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_112` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_113` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_113` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_114` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_114` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_115` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_115` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_116` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_116` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_117` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_117` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_118` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_118` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_119` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_119` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_120` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_120` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_121` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_121` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_122` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_122` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_123` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_123` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_124` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_124` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_125` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_125` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_126` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_126` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_127` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_127` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_128` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_128` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_129` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_129` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_130` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_130` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_131` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_131` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_132` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_132` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_133` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_133` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_134` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_134` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_135` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_135` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_136` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_136` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_137` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_137` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_138` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_138` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_139` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_139` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_140` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_140` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_141` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_141` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_142` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_142` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_143` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_143` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_144` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_144` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_145` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_145` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_146` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_146` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_147` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_147` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_148` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_148` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_149` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_149` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_150` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_150` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_151` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_151` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_152` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_152` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_153` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_153` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_154` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_154` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_155` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_155` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_156` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_156` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_157` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_157` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_158` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_158` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_159` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_159` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_160` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_160` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_161` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_161` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_162` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_162` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_163` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_163` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_164` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_164` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_165` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_165` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_166` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_166` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_167` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_167` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_168` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_168` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_169` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_169` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_170` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_170` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_171` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_171` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_172` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_172` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_173` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_173` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_174` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_174` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_175` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_175` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_176` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_176` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_177` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_177` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_178` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_178` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_179` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_179` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_180` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_180` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_181` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_181` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_182` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_182` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_183` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_183` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_184` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_184` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_185` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_185` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_186` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_186` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_187` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_187` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_188` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_188` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_189` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_189` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_190` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_190` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_191` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_191` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_192` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_192` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_193` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_193` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_194` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_194` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_195` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_195` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_196` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_196` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_197` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_197` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_198` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_198` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_199` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_199` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_200` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_200` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_201` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_201` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_202` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_202` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_203` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_203` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_204` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_204` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_205` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_205` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_206` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_206` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_207` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_207` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_208` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_208` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_209` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_209` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_210` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_210` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_211` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_211` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_212` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_212` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_213` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_213` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_214` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_214` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_215` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_215` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_216` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_216` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_217` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_217` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_218` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_218` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_219` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_219` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_220` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_220` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_221` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_221` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_222` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_222` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_223` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_223` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_224` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_224` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_225` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_225` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_226` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_226` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_227` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_227` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_228` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_228` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_229` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_229` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_230` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_230` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_231` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_231` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_232` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_232` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_233` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_233` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_234` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_234` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_235` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_235` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_236` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_236` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_237` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_237` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_238` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_238` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_239` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_239` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_240` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_240` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_241` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_241` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_242` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_242` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_243` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_243` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_244` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_244` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_245` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_245` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_246` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_246` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_247` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_247` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_248` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_248` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_249` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_249` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_250` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_250` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_251` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_251` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_252` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_252` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_253` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_253` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_254` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_254` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_order_255` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
ALTER TABLE `offline_refund_order_255` ADD COLUMN `migration` varchar(6) NULL COMMENT '是否迁移订单 TRUE、FALSE';
