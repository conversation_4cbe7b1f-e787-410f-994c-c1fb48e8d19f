<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.yxt</groupId>
    <artifactId>yxt-xframe</artifactId>
    <version>2.16.5</version>
    <relativePath/>
  </parent>

  <groupId>com.yxt.order</groupId>
  <artifactId>order-atom-service</artifactId>
  <version>1.0.0</version>
  <packaging>pom</packaging>
  <modules>
    <!--原子服务SDK-->
    <module>order-atom-sdk</module>
    <!--原子服务SDK-->
    <module>order-atom-open-sdk</module>
    <!--订单原子服务-->
    <module>order-atom-server</module>
    <!--原子服务启动器-->
    <module>order-atom-bootstrap</module>
  </modules>

  <properties>
    <skipTests>true</skipTests> <!-- 跳过单元测试 -->
    <spring-boot.repackage.skip>false</spring-boot.repackage.skip>

    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <apollo.version>1.8.0</apollo.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <okhttp.version>4.2.2</okhttp.version> <!--$NO-MVN-MAN-VER$ -->
    <mybatis-plus.version>3.1.2</mybatis-plus.version>
    <druid.version>1.1.21</druid.version>
    <easyexcel.version>3.3.3</easyexcel.version>
    <hutool.version>5.8.16</hutool.version>
    <loop-cure.version>1.2.1-SNAPSHOT</loop-cure.version>
    <elasticsearch.version>7.14.0</elasticsearch.version>
    <poi.version>3.15</poi.version>
    <spring.cloud.sentinel.version>2.1.3.RELEASE</spring.cloud.sentinel.version>
    <sentinel.datasource.apollo.verison>1.8.0</sentinel.datasource.apollo.verison>
    <spring-amqp.version>2.2.0.RELEASE</spring-amqp.version>
    <org.mapstruct>1.3.1.Final</org.mapstruct>
    <yxt.order-types.version>orderSync-SNAPSHOT</yxt.order-types.version>
    <yxt.order-common.version>orderSync-SNAPSHOT</yxt.order-common.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-core-spring-boot-starter</artifactId>
      <version>4.6.4</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-common-alarm</artifactId>
      <version>4.10.3</version>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.hydee.starter</groupId>
      <artifactId>grey-spring-boot-lib</artifactId>
      <version>3.4.0-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>20.0</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- WebSocket -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>

    <!-- OpenFeign -->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>

    <!-- feign-okhttp -->
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-okhttp</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.squareup.okhttp3</groupId>
          <artifactId>okhttp</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
      <version>2.6.2</version>
    </dependency>

    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
      <version>2.1</version>
    </dependency>

    <!-- gson -->
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.5</version>
    </dependency>
    <!-- EasyExcel -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>${easyexcel.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-ooxml</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml-schemas</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- Hutool工具类所有模块 -->
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>${hutool.version}</version>
    </dependency>

    <!-- okhttp -->
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>${okhttp.version}</version> <!--$NO-MVN-MAN-VER$ -->
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 循环治愈工具,注释掉,里面有mybatis加载数据源的依赖..注释掉,不然会报错 -->
    <!--    <dependency>-->
    <!--      <groupId>cn.hydee</groupId>-->
    <!--      <artifactId>hydee-loop-cure</artifactId>-->
    <!--      <version>${loop-cure.version}</version>-->
    <!--    </dependency>-->
    <!-- zip -->
    <dependency>
      <groupId>net.lingala.zip4j</groupId>
      <artifactId>zip4j</artifactId>
      <version>1.3.2</version>
    </dependency>
    <!-- 引入jwt -->
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt</artifactId>
      <version>0.9.0</version>
    </dependency>

    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
      <version>2.0.27</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>5.0.0</version>
      <exclusions>
        <exclusion>
          <artifactId>bcprov-jdk15on</artifactId>
          <groupId>org.bouncycastle</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>5.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
      <version>3.3.2</version>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.yxt.order.types</groupId>
        <artifactId>order-types</artifactId>
        <version>${yxt.order-types.version}</version>
      </dependency>
      <dependency>
        <groupId>com.yxt.order.common</groupId>
        <artifactId>order-common</artifactId>
        <version>${yxt.order-common.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <repositories>
    <repository>
      <id>aliyun</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>

    <repository>
      <id>hydee</id>
      <name>hydee</name>
      <url>http://nexus.hxyxt.com/nexus/content/groups/public/</url>
    </repository>
    <repository>
      <id>local-snapshots</id>
      <name>local-snapshots</name>
      <url>https://nexus.hxyxt.com/repository/snapshots/</url>
    </repository>
    <repository>
      <id>local-releases</id>
      <name>local-releases</name>
      <url>https://nexus.hxyxt.com/repository/releases/</url>
    </repository>
  </repositories>

  <distributionManagement>
    <repository>
      <id>local-releases</id>
      <name>Nexus Release Repository</name>
      <url>https://nexus.hxyxt.com/repository/releases/</url>
    </repository>
    <snapshotRepository>
      <id>local-snapshots</id>
      <name>Nexus Snapshot Repository</name>
      <url>https://nexus.hxyxt.com/repository/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>



</project>