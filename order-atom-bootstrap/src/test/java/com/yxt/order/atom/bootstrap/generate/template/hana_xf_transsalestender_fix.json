{
  "core": {
    "transport" : {
      "channel": {
        "speed": {
          "channel": ${channel},
          "byte":  ${byte},
          "record": ${record}
        }
      }
    }
  },
  "job": {
    "setting": {
      "speed": {
        "channel": ${channel},
        "byte":  ${byte},
        "record": ${record}
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXSERIAL,XF_POSTDATE,XF_TXTIME,XF_TXBATCH,XF_DOCNO,XF_VOIDDOCNO,XF_TXTYPE,XF_TXHOUR,XF_CASHIERCODE,XF_TENDERCODE,XF_SPECIFICEDTYPE,XF_PAYAMOUNT,XF_BASEAMOUNT,XF_EXTENDPARAM,XF_CREATETIME,XF_DESTLOCATIONLIST,XF_EXCESSMONEY,CRM_EXECUTED,CRM_EXECUTED1 from ${schema}.XF_TRANSSALESTENDER  where (XF_DOCNO,XF_STORECODE) in ( ${fixtenderSql} )"
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize": ${fetchSize}
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
             "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXSERIAL",
              "XF_POSTDATE",
              "XF_TXTIME",
              "XF_TXBATCH",
              "XF_DOCNO",
              "XF_VOIDDOCNO",
              "XF_TXTYPE",
              "XF_TXHOUR",
              "XF_CASHIERCODE",
              "XF_TENDERCODE",
              "XF_SPECIFICEDTYPE",
              "XF_PAYAMOUNT",
              "XF_BASEAMOUNT",
              "XF_EXTENDPARAM",
              "XF_CREATETIME",
              "XF_DESTLOCATIONLIST",
              "XF_EXCESSMONEY",
              "CRM_EXECUTED",
              "CRM_EXECUTED1"
            ],
            "connection": [
              {
                "table": [
                  "xf_transsalestender_${schema}"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ],
            "batchSize": ${batchSize}
          }
        }
      }
    ]
  }
}
