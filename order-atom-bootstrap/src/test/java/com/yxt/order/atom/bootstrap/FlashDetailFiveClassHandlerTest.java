package com.yxt.order.atom.bootstrap;

import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.job.compensate.detail_flash_five_class.CompensateOrderDetailFiveClassHandler;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2025/2/10 14:05
 */
public class FlashDetailFiveClassHandlerTest extends BaseTest{

//  @Resource
//  private FlashDetailFiveClassHandler;

  @Resource
  private CompensateOrderDetailFiveClassHandler compensateOrderDetailFiveClassHandler;


  @Test
  public void testFlashDetailFiveClassHandlerTest(){
    try {
      FlashParam flashParam = new FlashParam();
      Date date = new Date();

      flashParam.setStart(OrderDateUtils.previousDate(date,30));
      flashParam.setEnd(date);

      compensateOrderDetailFiveClassHandler.startFlush(flashParam);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

}
