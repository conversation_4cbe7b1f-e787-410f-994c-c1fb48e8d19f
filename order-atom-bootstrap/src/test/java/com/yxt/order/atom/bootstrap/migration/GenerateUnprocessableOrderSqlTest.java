package com.yxt.order.atom.bootstrap.migration;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.bootstrap.BaseTest;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDO;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDOMapper;
import com.yxt.order.atom.migration.dao.enums.UnprocessableSceneEnum;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2025/3/14 13:34
 */

public class GenerateUnprocessableOrderSqlTest extends BaseTest {

  @Resource
  private UnprocessableOrderDOMapper unprocessableOrderDOMapper;

  @Test
  public void testGenerateSql() {

    try {
      List<UnprocessableOrderDO> unprocessableOrderDOList = unprocessableOrderDOMapper.selectList(
          new LambdaQueryWrapper<>());

      String orderFormat =
          "select third_platform_code,third_order_no,store_code,actual_pay_amount,created,migration from offline_order_%s " + "where (third_platform_code,third_order_no,store_code) "
              + "in (select third_platform_code,third_order_no,store_code from offline_order_%s where id = %s) ; ";

      String refundFormat = "select third_platform_code,third_refund_no,store_code,total_amount,created,migration from offline_refund_order_%s "
          + "where (third_platform_code,third_refund_no,store_code) "
          + "in (select third_platform_code,third_refund_no,store_code from offline_refund_order_%s where id = %s) union all ";

      for (UnprocessableOrderDO unprocessableOrderDO : unprocessableOrderDOList) {
        String shardingNo = ShardingHelper.getTableIndexByNo(unprocessableOrderDO.getBusinessNo());
        String businessId = unprocessableOrderDO.getBusinessId();
        if (UnprocessableSceneEnum.REPEATED_OFFLINE_ORDER.name()
            .equals(unprocessableOrderDO.getScene())) {
          System.out.printf((orderFormat) + "%n", shardingNo, shardingNo, businessId);
        } else if (UnprocessableSceneEnum.REPEATED_OFFLINE_REFUND_ORDER.name()
            .equals(unprocessableOrderDO.getScene())) {
          System.out.printf((refundFormat) + "%n", shardingNo, shardingNo, businessId);
        }
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }


  }

}
