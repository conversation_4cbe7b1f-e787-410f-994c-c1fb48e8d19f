package com.yxt.order.atom.bootstrap;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

public class Main {
    public static void main(String[] args) throws Exception {

        /**
         * 最终可用版本
         */
        HashMap<String, String> params = new HashMap<>();
        params.put("app_id", "20250301000001");
        params.put("charset", "utf-8");
        params.put("format", "json");
        params.put("version", "1.0");
        params.put("sign_type", "RSA2");
        params.put("timestamp", "2025-04-25 16:58:00");
        params.put("biz_content", "{\"thirdOrderNo\":\"1024120200000001\",\"companyCode\":1006}");
        String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCkwpElJdKP6sqRD4ObQLgc7tO2tSayt+rGhKv4lZiLfvdndZwSJxEc5u92gv1XaRHt3nhWkTWBG+/ZlbCAHiddeUf1lWEsCxX+N5wqxobsRF0KpNUSa5+hFLGCCfUZ+Nfn7AniRQXGPchAYpJ7dkID9amloBSD8O8Jbn0Ss0KrSFWweDC6nKZICAQIx+1yKqFz7bRc9lKzeW6XihijK+7y/4MfUSB9sTU1D+b6mqgiv24yyRrSKzPBLrjv6KdFnVv0jeVPIWYMNa965fpo9YQngEM16ok/0v+9kHsjQcA7ht9FOg5DySOZSJ2Z+npgii1vNe1T/xkj2IsKp2AvOCwFAgMBAAECggEAfnY1MD0yvHJsHyG4U28+oj6SVFgBJwZR9yQYV4qDdvbycP/t1mUUFooPXXi3eeNU9q5e0ZtNZRcLZ4gk3IHIl0+i8xZAaqzrqaAHhzGwmCL1A1l3jlb7RIl8oiKAdfnxxrr+7yUphHshfIHsi0U+8IkcONuBQ7Cn2SZsa/EaCBsWn89l12AY+iW2w2L4KqbMjqZ7aBStRFhasdfyBargiz4pnjFbLK2uNBo6QnGHLMbzzfAgJW0PnOOfuMzCaOo2C7Iz5OD/Ej77Naq31vFECwkdGwCRjaKAsAmkHYyq640qvGlcZigQxWWRlW6PeVQkrbRdySTEaDfo5ewM0CfRwQKBgQDZF03HGYGIvPSE8svPJs/UvSIACYUxHfvhg+qvMeTKeWnJFjCfIPKnZG7GVMB6OWu9AeT0Y8bG+cXwJYW6QgU8yeLNRxQO/GombmAVo45+AP1EMl6dt5ooKs4FHelvJ7p3BSVEZp2fnYcG0YH1kbJJ0ulBFa4mFqxpuMrwqgji0QKBgQDCSi6KrKSy9GSOmN020ilfaCMxHUcOrXenDPuxvbZmjpwUJHs5CkFBHgnVzFUdckpzzlw/r5fyhwPVKQBrQzIEpHdHM9Rh6RoW3DJc2LHlVDONu9EHK5lbOiKj6KsuXPHLuvLDbGwgvhGbH8eYOUPCRv6p56e6GpSpMOYbO4f69QKBgQCb32+pFVc8pu+qxeowwt1XErXm8O8BZVYFhoJraJeuCZ87EwO2PiH8rtAa9598It6Ix2NtVnbTR2QoGcj7A4xfKVX/rploaTSOg5HZ96XIM02mOcRV2g6F6LYkVmXVueDYtIkdo9BpWuIosyVs/T/WYem5Iaf5ES6aemS8iFn5AQKBgA6GxVwQ9G983lfbDg8fP4CmpJrzeXCbf6q5ycfMS2r3lqva3muxXJely0507Jg7B14JO9R3KIE1nw+89Q8QAxldwp1MPsDjUNQMuqc+fG6NER2zwTksBVdQzW474zCgOmPlImmCHcOE8oQKqD21al+IN1o0u9GqUxsjUNEQbmZRAoGBAIY1ekxi3S7jWNJRT6IjIWAyDON6z5HNlX6SohcEhoZcNADDtnGvTxh5GOf5FBH6IWjHrRWhkBOh+0p0Hw8uLwdVrU5/aQ9SdjNpTxJScBwaIKCDY2+w61iklfBIgRvWWv18fRJU5uparsqnID+YGduUmQthvf9cQdGuxu3/BY8a";
        String sign = sign(params, privateKey);
        System.out.println(sign);
    }


    /**
     * 签名
     * @param params 待签名参数
     * @param privateKey 私钥
     * @return
     * @throws Exception
     */
    public static String sign(Map<String, String> params, String privateKey) throws Exception {
        String data = mapToData(params);
        return  sign(data, privateKey);
    }



    /**
     * 将map参数排序转换字符串
     * @param params
     * @return
     */
    public static String mapToData(Map<String, String> params) {
        // 1. 按照键的 ASCII 顺序对参数进行排序
        Map<Object, Object> sortedParams = new TreeMap<>(params);
        // 2. 拼接排序后的参数
        String paramString = sortedParams.entrySet()
                .stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        return paramString;
    }

    /**
     * 通用签名方法，用于RSA和RSA2签名
     * @param data 待签名的数据
     * @param privateKey 私钥
     * @return 返回Base64编码的签名字符串
     * @throws NoSuchAlgorithmException 当指定的算法不存在时抛出
     * @throws InvalidKeySpecException 当密钥规范无效时抛出
     * @throws InvalidKeyException 当私钥无效时抛出
     * @throws UnsupportedEncodingException 当编码不被支持时抛出
     * @throws SignatureException 当签名过程中发生错误时抛出
     */
    public static String sign(String data, String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, UnsupportedEncodingException, SignatureException {
        // 清理私钥，去掉头部和尾部信息
        privateKey = replaceKey(privateKey);

        // 将Base64编码的私钥解码为字节数组
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKey);

        // 创建PKCS8EncodedKeySpec对象用于生成私钥
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);

        // 使用KeyFactory根据算法生成私钥
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey key = keyFactory.generatePrivate(keySpec);

        // 创建Signature对象并初始化为签名模式
        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(key);

        // 更新签名对象的数据
        signature.update(data.getBytes("UTF-8"));

        // 对数据进行签名
        byte[] signedData = signature.sign();

        // 返回Base64编码的签名字符串
        return Base64.getEncoder().encodeToString(signedData);
    }

    /**
     * 移除公钥/私钥中的标头、标尾及换行符
     *
     * @param key 密钥
     * @return 清理后的密钥
     */
    protected static String replaceKey(String key) {
        return key.replaceAll("-----BEGIN (.*)-----", "")
                .replaceAll("-----END (.*)-----", "")
                .replaceAll("\\s+", "");
    }
}