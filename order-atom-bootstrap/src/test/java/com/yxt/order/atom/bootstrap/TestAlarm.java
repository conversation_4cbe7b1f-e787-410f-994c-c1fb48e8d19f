package com.yxt.order.atom.bootstrap;

import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import com.yxt.order.common.CommonDateUtils;
import java.util.Date;
import org.junit.Test;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月19日 14:28
 * @email: <EMAIL>
 */
public class TestAlarm extends BaseTest {

  @Test
  public void testAlarm() {
    String content = String.format("[自动任务][拦击订单]截止【%s】,有【%s】条数据未找到用户信息",
        CommonDateUtils.formatDate(new Date()),
        100);
    WxRobotOkHttpUtils.post(
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2208c6fc-b339-4d6b-9d6f-9b636abc7a39",
        content);
  }
}
