package com.yxt.order.atom.bootstrap.migration;

import static com.yxt.order.atom.bootstrap.migration.Constant.SCHEMA_COMPANY_MAP;
import static com.yxt.order.atom.bootstrap.migration.MigrationUtils.longV;
import static com.yxt.order.atom.bootstrap.migration.URL.getFixAmount;
import static com.yxt.order.atom.bootstrap.migration.URL.getFixHaidianErrorByScene;
import static com.yxt.order.atom.bootstrap.migration.URL.getFlashEsOrderUrl;
import static com.yxt.order.types.repair.RepairScene.CHAI_LING_GOODS_AMOUNT_ERROR_ORDER;
import static com.yxt.order.types.repair.RepairScene.CHAI_LING_GOODS_AMOUNT_ERROR_REFUND_ORDER;
import static com.yxt.order.types.repair.RepairScene.ONLINE_ORDER_AS_OFFLINE_ORDER;
import static com.yxt.order.types.repair.RepairScene.ONLINE_ORDER_AS_OFFLINE_REFUND_ORDER;

import cn.hutool.core.io.FileUtil;
import com.google.common.collect.Lists;
import com.yxt.order.atom.bootstrap.migration.BatchRangeGenerator.Range;
import com.yxt.order.atom.bootstrap.migration.URL.URLParam;
import com.yxt.order.atom.common.utils.DateRangeUtils;
import com.yxt.order.atom.common.utils.DateRangeUtils.RangeDay;
import com.yxt.order.atom.common.utils.ShardingUtils;
import com.yxt.order.atom.repair.dto.StartEndId;
import com.yxt.order.types.repair.RepairScene;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2025/3/10 11:42
 */
public class ValidSQL {

  /**
   * 生成刷线下单ES的CURL
   */
  @Test
  public void testFlashManageEs() {
    // 2024-06-01 07:05:44	2025-04-24 23:57:43
    String minDateStr = "2024-06-01 07:01:44";
    String maxDateStr = "2025-04-24 15:30:43"; // 线下订单完全上线时间

//    for (List<String> shardingNoList : Lists.partition(ShardingUtils.shardingValueList(Boolean.FALSE),
//        3)) {


      for (List<String> shardingNoList : Lists.partition(ShardingUtils.nonVipShardingValueList(),
          1)) {


      URLParam urlParam = new URLParam();
      urlParam.setStartDateStr(minDateStr);
      urlParam.setEndDateStr(maxDateStr);
      urlParam.setScene("es_manage_vip"+StringUtils.join(shardingNoList, "_"));
      urlParam.setShardingNoList(Lists.newArrayList(shardingNoList));
      System.out.println(URL.flashEsOfflineOrderManageData(urlParam));
    }



    // 优先处理会员订单


  }


  /**
   select min(s.m) m ,max(s.x) x from (

   select min(create_time) m ,max(create_time) x from order_info union all
   select min(create_time) m ,max(create_time) x from refund_order

   ) as s;
   */

  @Test
  public void generateFlashOnlineEsMemberCurl(){
    String startDateStr = "2024-07-31 00:00:00";
    String endDateStr = "2025-03-31 18:41:44";

    List<RangeDay> rangeDays = DateRangeUtils.splitByDays(startDateStr, endDateStr, 15);
    for (RangeDay rangeDay : rangeDays) {
      URLParam urlParam = new URLParam();
      urlParam.setStartDateStr(rangeDay.getStart());
      urlParam.setEndDateStr(rangeDay.getEnd());
      urlParam.setScene(rangeDay.vStr("flashTypeOnline"));
      System.out.println(URL.flashEsMemberOrderOnlineData(urlParam));
    }
  }


  /**
   select min(s.m) m ,max(s.x) x from (

     select min(created_time) m ,max(created_time) x from offline_order_0 union all
     select min(created_time) m ,max(created_time) x from offline_refund_order_0

   ) as s;
   */
  @Test
  public void generateFlashOfflineEsMemberCurl(){
    String startDateStr = "2024-07-31 00:00:00";
    String endDateStr = "2025-03-31 18:44:38";

    List<String> vipShardingList = ShardingUtils.shardingValueList(Boolean.FALSE);

    for (List<String> partShardingList : Lists.partition(vipShardingList,3)) {
      specifyTargetSharing(partShardingList, startDateStr, endDateStr);
    }

  }
  @Test
  public void generateFlashOfflineOrderManageCurl(){
    String startDateStr = "2020-07-31 00:00:00";
    String endDateStr = "2026-03-31 18:44:38";

    List<String> allShardingList = ShardingUtils.shardingValueList(Boolean.TRUE);

    List<List<String>> partitionList = Lists.partition(allShardingList, 4);
    for (List<String> partition : partitionList) {
      URLParam urlParam = new URLParam();
      urlParam.setStartDateStr(startDateStr);
      urlParam.setEndDateStr(endDateStr);
      urlParam.setScene(String.format("flashOfflineManagement%s_", StringUtils.join(partition,"_")));
      urlParam.setShardingNoList(partition);
      System.out.println(URL.flashOfflineOrderManageEs(urlParam));
    }


  }

  private static void specifyTargetSharing(List<String> partShardingList, String startDateStr, String endDateStr) {
    List<RangeDay> rangeDays = DateRangeUtils.splitByDays(startDateStr, endDateStr, 250); // 设定为200,会员表数据比较少,不按照小范围时间刷数,故调大
    for (RangeDay rangeDay : rangeDays) {
      URLParam urlParam = new URLParam();
      urlParam.setStartDateStr(rangeDay.getStart());
      urlParam.setEndDateStr(rangeDay.getEnd());
      urlParam.setScene(rangeDay.vStr(String.format("flashTypeOnline_%s_", StringUtils.join(partShardingList,"_"))));
      urlParam.setShardingNoList(partShardingList);
      System.out.println(URL.flashEsMemberOrderOfflinelineData(urlParam));
    }
  }

  @Test
  public void fixOrderPlatformCode(){

    List<String> shardingList = ShardingUtils.shardingValueList(Boolean.FALSE);
    List<List<String>> partitionShardingList = Lists.partition(shardingList, 5);
    for (List<String> partList : partitionShardingList) {
      URLParam param = new URLParam();
      param.setShardingNoList(partList);
      param.setScene("offlineOrderPlatformCode"+ Strings.join(partList,"_"));
      System.out.println(URL.fixOfflineOrderPlatformCode(param));
    }

    for (String s : ShardingUtils.nonVipShardingValueList()) {
      ArrayList<String> partList = Lists.newArrayList(s);
      URLParam param = new URLParam();
      param.setShardingNoList(partList);
      param.setScene("offlineOrderPlatformCode"+ Strings.join(partList,"_"));
      System.out.println(URL.fixOfflineOrderPlatformCode(param));
    }
  }


  @Test
  public void fixRefundOrderPlatformCode(){

    List<String> shardingList = ShardingUtils.shardingValueList(Boolean.FALSE);
    List<List<String>> partitionShardingList = Lists.partition(shardingList, 5);
    for (List<String> partList : partitionShardingList) {
      URLParam param = new URLParam();
      param.setShardingNoList(partList);
      param.setScene("offlineRefundOrderPlatformCode"+ Strings.join(partList,"_"));
      System.out.println(URL.fixOfflineRefundOrderPlatformCode(param));
    }

    for (String s : ShardingUtils.nonVipShardingValueList()) {
      ArrayList<String> partList = Lists.newArrayList(s);
      URLParam param = new URLParam();
      param.setShardingNoList(partList);
      param.setScene("offlineRefundOrderPlatformCode"+ Strings.join(partList,"_"));
      System.out.println(URL.fixOfflineRefundOrderPlatformCode(param));
    }
  }


  @Test
  public void testFlushEsSupportKpiAmount() {

    String migrationStartTime = "2024-07-31 00:01:25";
    String migrationEndTime = "2025-03-17 20:46:32";

//    List<RangeDay> rangeDayList = splitByDays(migrationStartTime, migrationEndTime, 100);
    RangeDay rangeDay = new RangeDay(migrationStartTime, migrationEndTime);

    List<String> shardingValueList = ShardingUtils.shardingValueList(Boolean.FALSE);
    for (List<String> partitionShardingList : Lists.partition(shardingValueList, 3)) {

//      for (RangeDay rangeDay : rangeDayList) {
      URLParam param = new URLParam();
//        param.setScene(
//            "FlashEsMemberOrderUrlForKpi_" + String.join("_", partitionShardingList) + "_" + rangeDay.vStr());
      param.setScene(
          "FlashEsOrderUrlForKpi_" + String.join("_", partitionShardingList) + "_"
              + rangeDay.vStr(""));
      param.setStartDateStr(rangeDay.getStart());
      param.setEndDateStr(rangeDay.getEnd());
      param.setShardingNoList(partitionShardingList);

      String flashEsOrderUrl = getFlashEsOrderUrl(param);
//        String flashEsOrderUrl = getFlashEsMemberOrderUrl(param);
      System.out.println(flashEsOrderUrl);
//      }

    }


  }


  @Test
  public void schemaSql() {
    String pa =
        "S010050525,B849\n" + "S010055638,C317\n" + "S010030444,AFC4\n" + "S020000025,BC66\n"
            + "S010109682,AW57\n" + "S010100113,AU96\n" + "S010003035,ALY9\n" + "S010109683,AW57\n"
            + "S010000788,C679\n" + "S010109680,AW57\n" + "S010017618,C497\n" + "S010017619,C497\n"
            + "S020000859,BC86\n" + "S020000858,BC86\n" + "S010115928,AU23\n" + "S010115929,AU23\n"
            + "S010100111,AU96\n" + "S010003038,ALY9\n" + "S020001842,B904\n" + "S020001845,B904\n"
            + "S020001847,B904\n" + "S020001849,B904\n" + "S020001850,B904\n" + "S020004885,B504\n"
            + "S020004886,B504\n" + "S020001851,B904\n" + "S010317585,AD71\n" + "S010001799,AMH9\n"
            + "S010317586,AD71";
    String[] thirdOrderNoAndStoreCode = pa.split("\n");

    for (String s : SCHEMA_COMPANY_MAP.keySet()) {

      for (String ss : thirdOrderNoAndStoreCode) {
        String[] split1 = ss.split(",");
        String sql = String.format(
            "  select * from %s.XF_TRANSSALESTOTAL p  where p.XF_DOCNO = '%s' and p.XF_STORECODE = '%s' union all ",
            s, split1[0], split1[1]);
        System.out.println(sql);
      }


    }
  }

  public enum Migrate {
    ORDER, REFUND
  }

  @Test
  public void cleanEsData() {
    String suffixStr = "_6";

    List<Range> rangesEs = BatchRangeGenerator.generateBatchRanges(1L, 61241174L, 30 * 10000L);

    int i = 1;
    for (Range rangesE : rangesEs) {
      URLParam urlParam = new URLParam();
      urlParam.setScene("d6_" + i);
      urlParam.setSuffix(suffixStr);
      urlParam.setStartId(rangesE.getStart());
      urlParam.setEndId(rangesE.getEnd());
      System.out.println(URL.getRemoveEsOrderRepeatedDataUrl(urlParam));
      i = i + 1;
    }

  }

  @Test
  public void generateCurlFixAmount() {
    List<String> contentList = FileUtil.readLines(
        "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\migration\\actualData.log",
        StandardCharsets.UTF_8);

    for (String content : contentList) {

      String[] split = content.split(",");
      String schema = split[0];
      Long minId = Long.valueOf(split[1]);
      Long maxId = Long.valueOf(split[2]);

      if (minId == 0L && maxId == 0L) {
        continue;
      }

      List<Range> rangeList = BatchRangeGenerator.generateBatchRanges(minId, maxId, 100 * 10000L);

      for (Range range : rangeList) {
        URLParam param = new URLParam();
        param.setStartId(range.getStart());
        param.setEndId(range.getEnd());
        param.setTargetSchema(schema);
        param.setScene("修复线下单实付金额");
        System.out.println(getFixAmount(param));
      }

    }


  }

  @Test
  public void generateCurl() {
//    List<Range> rangesEs = BatchRangeGenerator.generateBatchRanges(4054001L-100L, 21231208L, 100 * 10000L);
//    int i = 1;
//    for (Range rangesE : rangesEs) {
//      URLParam urlParam2 = new URLParam();
//      urlParam2.setScene("removeEsOrderRepeatedData继续清理0314_"+i);
//      urlParam2.setSuffix("_2");
//      urlParam2.setStartId(rangesE.getStart());
//      urlParam2.setEndId(rangesE.getEnd());
//      System.out.println(URL.getRemoveEsOrderRepeatedDataUrl(urlParam2));
//      i=i+1;
//    }
//
//    if(true){
//      return;
//    }

    Boolean isHandleVip = Boolean.TRUE;
//    String scene = "fixUnprocessableOfflineOrder";
    String scene = "fixUnprocessableOfflineRefundOrder";
    List<String> shardingValueList = ShardingUtils.shardingValueList(Boolean.FALSE);
    for (List<String> partitionShardingList : Lists.partition(shardingValueList, 3)) {

      URLParam urlParam = new URLParam();
      urlParam.setScene(scene);
      urlParam.setShardingNoList(partitionShardingList);
      urlParam.setOnlyHandleKeChuan(Boolean.TRUE);
//      System.out.println(URL.getFixUnprocessableOfflineOrderUrl(urlParam));
      System.out.println(URL.getFixUnprocessableOfflineRefundOrderUrl(urlParam));

    }

    if (isHandleVip) {
      List<String> nonVipShardingNoList = ShardingUtils.nonVipShardingValueList();
      for (String sharingNo : nonVipShardingNoList) {
//        Long total = getOfflineOrderMap().get(sharingNo); // 正单
        Long total = getOfflineRefundOrderMap().get(sharingNo); // 退单
        if (total == null) {
          continue; // 不需要处理
        }
        List<Range> ranges = BatchRangeGenerator.generateBatchRanges(0L, total, 100 * 10000L);
        int i = 0;
        for (Range range : ranges) {
          URLParam urlParam = new URLParam();
          urlParam.setScene(scene + "_" + i);
          urlParam.setShardingNoList(Lists.newArrayList(sharingNo));
          urlParam.setStartId(range.getStart());
          urlParam.setEndId(range.getEnd());
          urlParam.setOnlyHandleKeChuan(Boolean.TRUE);
//          System.out.println(URL.getFixUnprocessableOfflineOrderUrl(urlParam));
          System.out.println(URL.getFixUnprocessableOfflineRefundOrderUrl(urlParam));
          i = i + 1;
        }
      }
    }

  }


  /**
   * 因为不再迁移了,所以是固定下来了
   *
   * @return
   */
  public Map<String, Long> getOfflineRefundOrderMap() {
    Map<String, Long> map = new HashMap<>();
    map.put("2406", null);
    map.put("2407", 2461L);
    map.put("2408", 64732L);
    map.put("2409", 70649L);
    map.put("2410", 83584L);
    map.put("2411", 102649L);
    map.put("2412", 114708L);
    map.put("2501", 77041L);
    map.put("2502", 53163L);
    map.put("2503", 22148L);
    return map;
  }



  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class FixHaidian {

    private Long startId;
    private Long endId;
    private RepairScene repairScene;
  }

  @Test
  public void testGetFixHaidianErrorByScene() {

    ArrayList<FixHaidian> fixHaidianList = Lists.newArrayList(
        new FixHaidian(1L, 17293L, CHAI_LING_GOODS_AMOUNT_ERROR_ORDER),
        new FixHaidian(35L, 17244L, CHAI_LING_GOODS_AMOUNT_ERROR_REFUND_ORDER),
        new FixHaidian(45332L, 16506288L, ONLINE_ORDER_AS_OFFLINE_ORDER),
        new FixHaidian(45418L, 16506273L, ONLINE_ORDER_AS_OFFLINE_REFUND_ORDER));

    for (FixHaidian fixHaidian : fixHaidianList) {
      List<Range> rangeIdList = BatchRangeGenerator.generateBatchRanges(fixHaidian.getStartId(),
          fixHaidian.getEndId(), 30 * 10000L);

      int i = 0;
      for (Range range : rangeIdList) {
        URLParam urlParam = new URLParam();
        urlParam.setStartId(range.getStart());
        urlParam.setEndId(range.getEnd());
        urlParam.setScene(fixHaidian.getRepairScene().name() + "_" + i);
        urlParam.setRepairScene(fixHaidian.getRepairScene());
        System.out.println(getFixHaidianErrorByScene(urlParam));
        i = i + 1;
      }

      i = 0;

    }


  }

  @Test
  public void length() {
    for (String schema : SCHEMA_COMPANY_MAP.keySet()) {
//      System.out.println("select count(1) from "+schema+".XF_TRANSSALESTOTAL where XF_CREATETIME>='2024-07-31 00:00:00' AND XF_CREATETIME<'2025-01-16 00:00:00' and length(XF_TXSERIAL) = 16 union all ");
//      System.out.println("select count(1) c from "+schema+".XF_TRANSSALESTOTAL where (length(XF_TXSERIAL) = 16 or length(XF_TXBATCH) = 16)  union all ");
      System.out.println("select count(1) c from " + schema
          + ".XF_TRANSSALESTOTAL where  length(XF_TXSERIAL) = 16  union all ");
    }
  }

  @Test
  public void generateDeletedHdRepeatTest() {
    List<String> contentList = FileUtil.readLines(
        "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\migration\\removeHaidianRepeated.log",
        StandardCharsets.UTF_8);

    Long total = 0L;
    for (String s : contentList) {
      if (s.startsWith("#")) {
        continue;
      }

      String[] split = s.split(",");
      String hanaId = split[0];
      String minId = split[1];
      String maxId = split[2];
      String targetSchema = split[3];

//      if(!"ynhx_data01_20241007_2025011512".equals(targetSchema)){
//        continue;
//      }

      StartEndId startEndId = new StartEndId();
      startEndId.setStartId(longV(minId));
      startEndId.setEndId(longV(maxId));
      if (startEndId.empty()) {
        continue;
      }

      long cha = startEndId.getEndId() - startEndId.getStartId();
//      System.out.println("cha:"+cha + "   "+JsonUtils.toJson(startEndId) + " " + targetSchema);
      total = total + cha;
//
//      String sql = String.format("update hana_migration set config_json = '%s' where id = %s;",
//          JsonUtils.toJson(startEndId),
//          hanaId);
//      System.out.println(sql);

      // 再根据id分批
      List<Range> rangesEs = BatchRangeGenerator.generateBatchRanges(startEndId.getStartId(),
          startEndId.getEndId(), 100 * 10000L);
      int i = 1;
      for (Range rangesE : rangesEs) {
        URLParam param = new URLParam();
        param.setStartId(rangesE.getStart());
        param.setEndId(rangesE.getEnd());
        param.setTargetSchema(targetSchema);
        param.setScene(
            String.format("RM_HD_REPEATED_V2_%s_%s_%s_seq%s", targetSchema, rangesE.getStart(),
                rangesE.getEnd(), i));
        String generateRemoveHaiDian = URL.getGenerateRemoveHaiDian(param);
        System.out.println(generateRemoveHaiDian);

//        param.setScene(String.format("platformCodeErrorCollect_%s_%s_%s_seq%s",targetSchema,rangesE.getStart(),rangesE.getEnd(),i));
//        String generateRemoveHaiDian = URL.offlineOrderPlatformCheckDataCollect(param);
//        System.out.println(generateRemoveHaiDian);

        i = i + 1;
      }


    }

//    System.out.println("===="+total);

  }


  @Test
  public void generateDeletedHdRepeatTestAllCheck() {
    List<String> contentList = FileUtil.readLines(
        "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\migration\\removeHaidianRepeated.log",
        StandardCharsets.UTF_8);

    for (String s : contentList) {
      if (s.startsWith("#")) {
        continue;
      }

      String[] split = s.split(",");
      String hanaId = split[0];
      String minId = split[1];
      String maxId = split[2];
      String targetSchema = split[3];

      StartEndId startEndId = new StartEndId();
      startEndId.setStartId(longV(minId));
      startEndId.setEndId(longV(maxId));
      if (startEndId.empty()) {
        continue;
      }

      String templete = "select c.count 迁移数,\n"
          + "        CASE\n"
          + "        WHEN migration = 0 THEN '未迁移成功(0)'\n"
          + "        WHEN migration = 1 THEN '迁移成功(1)'\n"
          + "        WHEN migration = 2 THEN '校验已存在,不迁移(2)'\n"
          + "        WHEN migration = 3 THEN '线上订单,不迁移(3)'\n"
          + "      WHEN migration = 4 THEN '明细不存在(4)'\n"
          + "      ELSE '未知状态'\n"
          + "    END AS 各状态迁移进度\n"
          + "    from (\n"
          + "select count(1) count ,migration from xf_transsalestotal_%s where  XF_CREATETIME>='2024-07-31 00:00:00' AND XF_CREATETIME<'2025-01-16 00:00:00' GROUP BY migration\n"
          + ") as c order by migration asc ; ";

      System.out.printf(templete, targetSchema);
      System.out.println();
      System.out.println();


    }


  }

}
