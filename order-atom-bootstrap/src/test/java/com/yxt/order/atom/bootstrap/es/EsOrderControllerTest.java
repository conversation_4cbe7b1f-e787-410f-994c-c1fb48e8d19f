package com.yxt.order.atom.bootstrap.es;

import com.google.common.collect.Lists;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.bootstrap.BaseTest;
import com.yxt.order.atom.open.sdk.domain.es_order.EsOrderApi;
import com.yxt.order.atom.open.sdk.req.es_order.EsOrderQueryReqDTO;
import com.yxt.order.atom.open.sdk.res.es_order.EsOrderResDTO;
import com.yxt.order.atom.order.es.dto.OrderDetailDto;
import com.yxt.order.atom.order.es.mapper.EsOrderMapper;
import com.yxt.order.atom.order.es.sync.es_order.handler.OrderInfoCanalHandler;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel.EsOrderItemModel;
import com.yxt.order.atom.order.es.sync.es_order.service.EsOrderModelService;
import com.yxt.order.atom.sdk.order_info.OrderAtomEsApi;
import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

public class EsOrderControllerTest extends BaseTest {

  @Resource
  private OrderAtomEsApi orderAtomEsApi;
  @Resource
  private EsOrderMapper esOrderMapper;
  @Resource
  private EsOrderModelService esOrderModelService;

  @Resource
  private EsOrderApi esOrderApi;


  @Test
  public void insertEsOrderData() {
    for (int i = 0; i < 10; i++) {
      EsOrderIndexModel esOrderModel = new EsOrderIndexModel();
      esOrderModel.setOrderNumber("orderNo" + i);
//      esOrderModel.setStoreCode("storeCode" + i);
      esOrderModel.setUserId("userId" + i);
      esOrderModel.setEsOrderType(EsOrderType.REFUND);
      esOrderModel.setServiceMode(EsServiceMode.B2C);
      esOrderModel.setEsOrderStatus(EsOrderStatus.DONE);
      esOrderModel.setPayTime(new Date());
      esOrderModel.setThirdOrderNo("thirdOrderNO" + i);
      esOrderModel.setPlatformCode("MT");
      esOrderModel.setCreateTime(new Date());

      List<EsOrderItemModel> esOrderItemModelList = Lists.newArrayList();
      for (int j = 0; j < 5; j++) {
        EsOrderItemModel itemModel = new EsOrderItemModel();
        itemModel.setCommodityCode("erpCode" + j);
        itemModel.setCommodityName("erpName" + j);
//        itemModel.setCommodityCount(j);
        esOrderItemModelList.add(itemModel);
      }
      esOrderModel.setEsOrderItemModelList(esOrderItemModelList);
      esOrderModelService.createEsOrder(esOrderModel);
    }
  }

  @Test
  public void pageQuery() {
    for (; ; ) {
      try {
        String startDateStr = "2024-08-08 16:31:57";
        String endDateStr = "2024-08-08 16:40:57";
//        String dateFormat = "yyyy-MM-dd HH:mm:ss";
//        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
//        Date startDate = sdf.parse(startDateStr);
//        Date endDate = sdf.parse(endDateStr);

        EsOrderQueryReqDTO reqDTO = new EsOrderQueryReqDTO();
//        reqDTO.setStoreCode("storeCode1");
        reqDTO.setCommodityCodeList(Lists.newArrayList("erpCode4"));
        reqDTO.setStartDate(startDateStr);
        reqDTO.setEndDate(endDateStr);
        reqDTO.setEsServiceModeList(Lists.newArrayList(EsServiceMode.B2C));
        reqDTO.setEsOrderStatus(EsOrderStatus.DONE);
        reqDTO.setCurrentPage(1);
        reqDTO.setPageSize(22);

        ResponseBase<PageDTO<EsOrderResDTO>> pageDTOResponseBase = esOrderApi.pageQuery(reqDTO);
        System.out.println();
      } catch (Exception e) {
        e.printStackTrace();
      }
    }


  }

  @Resource
  private OrderInfoCanalHandler orderInfoCanalHandler;

  @Test
  public void retry(){
    EsOrderIndexModel esOrderModel = new EsOrderIndexModel();
    esOrderModel.setOrderNumber("213");
    List<OrderDetailDto> orderDetailDtoList = orderInfoCanalHandler.getOrderDetailDtoList(
        esOrderModel);

    System.out.println();
  }


}