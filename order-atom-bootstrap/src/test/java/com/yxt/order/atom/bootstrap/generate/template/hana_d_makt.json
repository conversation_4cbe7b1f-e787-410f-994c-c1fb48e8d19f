{
  "core": {
    "transport" : {
      "channel": {
        "speed": {
          "channel": ${channel},
          "byte":  ${byte},
          "record": ${record}
        }
      }
    }
  },
  "job": {
    "setting": {
      "speed": {
        "channel": ${channel},
        "byte":  ${byte},
        "record": ${record}
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select MANDT,MATNR,SPRAS,MAKTX,MAKTG,REGBD,CURRN,ANAME,CONFD,EXECS,ZBCAT,ZCOMC,ZFOEM,ZGLOA,ZPRES,ZMEB,ZOTO,ZBRGEW,ZPROCESS,ZCYCLE,ZSUPCODE,ZCURING,ZINDUSTRY,ZMANAG,ZMILK,ZTTCURING,ZUNIT,ZBEHV,ZGROE,ZPLI,ZJZKP,SSBM,ZUNIT_GTAX,TAXRATE,YHZCBS,ZZSTSGLCODE,LSLBS,YHZCMC,ZBASIC_CODE,ZLIST_HOLDING,ZPFKL_DL,ZPFKL_ZDL,ZJDA_SAISO,ZJDA_AGE,ZJDA_SEX,ZJDA_PLGTP,ZJDA_PROFIT,ZJDA_HAVE,ZJDA_UNAME,ZJDA_DATE,ZJDA_TIME,ZMIN_UNIT,ZMIN_COUNT from HANA_D.MAKT"
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize": ${fetchSize}
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "MANDT",
              "MATNR",
              "SPRAS",
              "MAKTX",
              "MAKTG",
              "REGBD",
              "CURRN",
              "ANAME",
              "CONFD",
              "EXECS",
              "ZBCAT",
              "ZCOMC",
              "ZFOEM",
              "ZGLOA",
              "ZPRES",
              "ZMEB",
              "ZOTO",
              "ZBRGEW",
              "ZPROCESS",
              "ZCYCLE",
              "ZSUPCODE",
              "ZCURING",
              "ZINDUSTRY",
              "ZMANAG",
              "ZMILK",
              "ZTTCURING",
              "ZUNIT",
              "ZBEHV",
              "ZGROE",
              "ZPLI",
              "ZJZKP",
              "SSBM",
              "ZUNIT_GTAX",
              "TAXRATE",
              "YHZCBS",
              "ZZSTSGLCODE",
              "LSLBS",
              "YHZCMC",
              "ZBASIC_CODE",
              "ZLIST_HOLDING",
              "ZPFKL_DL",
              "ZPFKL_ZDL",
              "ZJDA_SAISO",
              "ZJDA_AGE",
              "ZJDA_SEX",
              "ZJDA_PLGTP",
              "ZJDA_PROFIT",
              "ZJDA_HAVE",
              "ZJDA_UNAME",
              "ZJDA_DATE",
              "ZJDA_TIME",
              "ZMIN_UNIT",
              "ZMIN_COUNT"
            ],
            "connection": [
              {
                "table": [
                  "makt"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ],
            "batchSize": ${batchSize}
          }
        }
      }
    ]
  }
}
