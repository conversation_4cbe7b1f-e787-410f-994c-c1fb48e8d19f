package com.yxt.order.atom.bootstrap.migration;

import static com.yxt.order.atom.bootstrap.migration.MigrationUtils.longV;
import static com.yxt.order.atom.bootstrap.migration.RefundConstant.REFUND_SCHEMA;

import cn.hutool.core.io.FileUtil;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.repair.dto.StartEndId;
import java.nio.charset.StandardCharsets;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2025/3/7 18:24
 */
public class MigrationRefundConfigTest {


  @Test
  public void testCheckUnSuccessRecord(){
    List<String> contentList = FileUtil.readLines(
        "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\migration\\退单未迁移成功.log",
        StandardCharsets.UTF_8);

    Integer nextBatch = 0;
    Integer otherCount = 0;
    for (String unSuccess : contentList) {
      if(unSuccess.contains("KEEP_NEXT_BATCH_TO_MIGRATION")){
        nextBatch = nextBatch + 1;
      }else{
        otherCount = otherCount + 1;
      }
    }

    System.out.println(nextBatch); // 1104
    System.out.println(otherCount); // 3
                                    // 失败的有17个


  }

  @Test
  public void testValidSql(){
    for (String migrationSchema : REFUND_SCHEMA) {
//      String migrateSort = "ORDER"; // ORDER 正单
     String migrateSort = "REFUND"; // REFUND  退单

      String startTime = "2024-07-31 00:00:00";
      String endTime = "2025-01-16 00:00:00";

      StringBuilder sb = new StringBuilder();
      sb.append("select count(1) count from xf_transsalestotal_" + migrationSchema +" where 1=1 ");

      if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime)) {
        sb.append(
            " and XF_CREATETIME>=\"" + startTime + "\" AND XF_CREATETIME<\"" + endTime + "\"");
      }
      if (migrateSort.equals("ORDER")) {
        sb.append(" and XF_SELLINGAMOUNT >= 0 ");
      } else {
        sb.append(" and XF_SELLINGAMOUNT < 0 ");
      }
      sb.append(" union all ");

      System.out.println(sb.toString());
//      break;
    }

  }

  @Test
  public void testUpdateHanaConfigJson(){

    String[] idArr = RefundConstant.REFUND_HANA_MIGRATION_ID.split("\n");
    String[] minMaxIdArr = RefundConstant.REFUND_MIN_MAX_ID.split("\n");

    System.out.println(idArr.length);
    System.out.println(minMaxIdArr.length);

    for (int i = 0; i <= idArr.length - 1; i++) {

      String[] minMaxIdLoopArr = minMaxIdArr[i].split(",");
      String minId = minMaxIdLoopArr[0];
      String maxId = minMaxIdLoopArr[1];

      StartEndId startEndId = new StartEndId();
      startEndId.setStartId(longV(minId));
      startEndId.setEndId(longV(maxId));

      String sql = String.format("update hana_migration set config_json = '%s' where id = %s;",
          JsonUtils.toJson(startEndId),
          idArr[i]);
      System.out.println(sql);


    }

  }

}
