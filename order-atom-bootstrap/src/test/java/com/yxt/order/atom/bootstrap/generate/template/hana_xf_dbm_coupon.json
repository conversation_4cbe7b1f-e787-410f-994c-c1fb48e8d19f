{
  "core": {
    "transport" : {
      "channel": {
        "speed": {
          "channel": ${channel},
          "byte":  ${byte},
          "record": ${record}
        }
      }
    }
  },
  "job": {
    "setting": {
      "speed": {
        "channel": ${channel},
        "byte":  ${byte},
        "record": ${record}
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXTIME,XF_DOCNO,XF_DBMID,XF_DESCI,XF_REBATEAMT ,XF_EXPIRETIME ,XF_STATUS,XF_USEDDATE,XF_USEDTIME,XF_USEDDOCNO from ${schema}.xf_dbm_coupon"
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize": $
      {fetchSize}
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXTIME",
              "XF_DOCNO",
              "XF_DBMID",
              "XF_DESCI",
              "XF_REBATEAMT",
              "XF_EXPIRETIME",
              "XF_STATUS",
              "XF_USEDDATE",
              "XF_USEDTIME",
              "XF_USEDDOCNO"
            ],
            "connection": [
              {
                "table": [
                  "xf_dbm_coupon_${schema}"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ],
            "batchSize": ${batchSize}
          }
        }
      }
    ]
  }
}
