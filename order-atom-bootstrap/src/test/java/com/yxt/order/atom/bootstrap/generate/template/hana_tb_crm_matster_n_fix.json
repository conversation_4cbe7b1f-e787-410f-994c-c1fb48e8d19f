{
  "core": {
    "transport" : {
      "channel": {
        "speed": {
          "channel": ${channel},
          "byte":  ${byte},
          "record": ${record}
        }
      }
    }
  },
  "job": {
    "setting": {
      "speed": {
        "channel": ${channel},
        "byte":  ${byte},
        "record": ${record}
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "splitPk": "\"会员卡ID(实物卡号)\"",
            "connection": [
              {
                "querySql": [
                  "select \"会员卡ID(实物卡号)\",\"会员卡号状态\",\"会员卡类型\",\"会员ID(会员编号)\",\"会员资格ID\",\"会员资格状态\",\"CRM会员卡ID(虚拟卡号)\",\"姓名\",\"性别\",\"出生日期\",\"身份证号\",\"固定电话\",\"移动电话\",\"电子邮件地址\",\"居住地址\",\"会员等级\",\"等级值余额\",\"积分余额\",\"上月过期积分\",\"本月即将过期积分\",\"累计交易次数\",\"累计消费金额\",\"归属门店\",\"发行店铺号\",\"发行日期\",\"职业\",\"医保类型\",\"中控台创建用户号\",\"中控台创建用户姓名\",\"微信账号\",\"负责员工工号\",\"负责员工姓名\",\"ABC分类\",\"支付宝绑定店铺\",\"微信绑定店铺\",\"支付宝绑定日期\",\"微信绑定日期 \",\"健康档案病症首次建档门店\",\"健康档案病症首次建档日期\",\"首次建档日期-母婴\",\"首次建档门店-母婴\",\"末次消费日期\",\"累计进店次数\",\"REG_CHANNEL\",\"REG_CHANNEL_NAME\",\"NEW_ACTIVE_VIP_YEAR\",\"慢卡类型\",\"慢卡病种\" from HX_CRM.TB_CRM_MATSTER_N where \"会员卡ID(实物卡号)\" in ( ${fixUserIdList} ) "
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize": ${fetchSize}
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "`会员卡ID(实物卡号)`",
              "`会员卡号状态`",
              "`会员卡类型`",
              "`会员ID(会员编号)`",
              "`会员资格ID`",
              "`会员资格状态`",
              "`CRM会员卡ID(虚拟卡号)`",
              "`姓名`",
              "`性别`",
              "`出生日期`",
              "`身份证号`",
              "`固定电话`",
              "`移动电话`",
              "`电子邮件地址`",
              "`居住地址`",
              "`会员等级`",
              "`等级值余额`",
              "`积分余额`",
              "`上月过期积分`",
              "`本月即将过期积分`",
              "`累计交易次数`",
              "`累计消费金额`",
              "`归属门店`",
              "`发行店铺号`",
              "`发行日期`",
              "`职业`",
              "`医保类型`",
              "`中控台创建用户号`",
              "`中控台创建用户姓名`",
              "`微信账号`",
              "`负责员工工号`",
              "`负责员工姓名`",
              "`ABC分类`",
              "`支付宝绑定店铺`",
              "`微信绑定店铺`",
              "`支付宝绑定日期`",
              "`微信绑定日期`",
              "`健康档案病症首次建档门店`",
              "`健康档案病症首次建档日期`",
              "`首次建档日期-母婴`",
              "`首次建档门店-母婴`",
              "`末次消费日期`",
              "`累计进店次数`",
              "`REG_CHANNEL`",
              "`REG_CHANNEL_NAME`",
              "`NEW_ACTIVE_VIP_YEAR`",
              "`慢卡类型`",
              "`慢卡病种`"
            ],
            "preSql": [
              "select max(id) from tb_crm_matster_n;"
            ],
            "connection": [
              {
                "table": [
                  "tb_crm_matster_n"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ],
            "batchSize": ${batchSize}
          }
        }
      }
    ]
  }
}
