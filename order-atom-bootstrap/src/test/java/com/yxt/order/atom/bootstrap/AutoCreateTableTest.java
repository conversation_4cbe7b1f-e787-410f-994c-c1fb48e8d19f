package com.yxt.order.atom.bootstrap;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.google.common.collect.Lists;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.DeletedDataDO;
import com.yxt.order.atom.order.mapper.AutoCreateMapper;
import com.yxt.order.atom.order.repository.DeleteDataRepository;
import com.yxt.order.common.utils.OrderJsonUtils;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月26日 13:59
 * @email: <EMAIL>
 */
public class AutoCreateTableTest extends BaseTest {

  @Resource
  private AutoCreateMapper autoCreateMapper;


  @Test
  public void showTest() {
    DynamicDataSourceContextHolder.push(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME);
    String mqMessage = autoCreateMapper.showCreateTable("mq_message");
    System.out.println();

    autoCreateMapper.createTable(mqMessage.replace("mq_message", "mq_message_2"));

    System.out.println();
  }

  @Resource
  private DeleteDataRepository deleteDataRepository;

  @Test
  public void testDeleted() {
    DynamicDataSourceContextHolder.push(DATA_SOURCE.ORDER_OFFLINE);
    while (true) {

      DeletedDataDO deletedDataDo = new DeletedDataDO();
      deletedDataDo.setBusinessNo("orderNo");
      deletedDataDo.setServiceName("applicationName");
      deletedDataDo.setDatabaseName(Database.DSCLOUD_OFFLINE);
      deletedDataDo.setTableName(Table.OFFLINE_ORDER_REGEX);
      deletedDataDo.setCreatedTime(new Date());
      deletedDataDo.setCreatedBy("applicationName");
      deletedDataDo.setDeletedData(OrderJsonUtils.toJson(Lists.newArrayList("1")));
      deletedDataDo.setReason("reason");
      deleteDataRepository.toDeleteData(deletedDataDo);

    }
  }


}
