package com.yxt.order.atom.bootstrap;

import com.yxt.order.atom.job.EsExpiredDataCleanJob;
import com.yxt.order.atom.order.es.mapper.EsMemberOrderMapper;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2024/12/15 14:14
 */
public class JobTest extends BaseTest{

  @Resource
  private EsExpiredDataCleanJob esExpiredDataCleanJob;
  @Resource
  private EsMemberOrderMapper esMemberOrderMapper;

  @Test
  public void testCleanExpiredEsJob(){
    esExpiredDataCleanJob.execute();
  }
  @Test
  public void testClean(){


  }
}
