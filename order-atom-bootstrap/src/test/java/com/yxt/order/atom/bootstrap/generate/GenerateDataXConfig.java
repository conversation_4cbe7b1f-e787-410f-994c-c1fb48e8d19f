package com.yxt.order.atom.bootstrap.generate;

import lombok.Data;

@Data
public class GenerateDataXConfig {


  private static String orderDataOrderBySql = " order by XF_CREATETIME desc";
  // where条件
  // 订单主表
  public static String salesTotalWhereSql = String.format(" %s %s",
      " where XF_CREATETIME >= '2024-06-22 00:00:00' and XF_CREATETIME <= '2024-07-18 15:12:32' ",
      orderDataOrderBySql);
  // 订单明细
  public static String salesItemWhereSql = String.format(" %s %s",
      " where XF_CREATETIME >= '2024-06-22 00:00:00' and XF_CREATETIME <= '2024-07-25 11:16:19' ",
      orderDataOrderBySql);
  // 订单支付
  public static String salesTenderWhereSql = String.format(" %s %s",
      " where XF_CREATETIME >= '2024-06-22 00:00:00' and XF_CREATETIME <= '2024-07-16 14:33:48' ",
      orderDataOrderBySql);


  @Data
  public static class Config {

    public static final String fetchSize = "1024";
    public static final String batchSize = "100";
    public static final String channel = "1";
    public static final String bytes = "548576";//500kb
    public static final String record = "500";//记录数

  }

  @Data
  public static class HanaDataSource {

    // 生产
    public static final String hanaUrl = "**************************";
    public static final String hanaUsername = "HANA_CD";
    public static final String hanaPassword = "Pas$w0rd@01";
  }


  /**
   * 同步到心云数据库的配置
   */
  @Data
  public static class SyncDataToMySQLSource {

    // 开发
//    public static final String mySqlUrl = "************************************************************************************************************************************************************************************************************************";
//    public static final String mySqlUsername = "agent";
//    public static final String mySqlPassword = "FgRdxNn8ADFC";

    // 测试
//    public static final String mySqlUrl = "*************************************************************************************************************************************************************************************************************************";
//    public static final String mySqlUsername = "agent";
//    public static final String mySqlPassword = "WrHNOhOGHR8yzMEgKvao";

    // 生产
    public static final String mySqlUrl = "***************************************************************************************************************************************************************************************************************************";
    public static final String mySqlUsername = "dscloud_offline_archive_agent";
    public static final String mySqlPassword = "MyO19kLaw44JB1b";

  }


}
