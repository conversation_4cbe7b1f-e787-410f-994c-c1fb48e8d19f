{
  "core": {
    "transport" : {
      "channel": {
        "speed": {
          "channel": ${channel},
          "byte":  ${byte},
          "record": ${record}
        }
      }
    }
  },
  "job": {
    "setting": {
      "speed": {
        "channel": ${channel},
        "byte":  ${byte},
        "record": ${record}
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXSERIAL,XF_TXTIME,XF_TXBATCH,XF_DOCNO,XF_VOIDDOCNO,XF_TXTYPE,XF_TXHOUR,XF_CASHIER,XF_SALESMAN,XF_CLIENTCODE,XF_PURCHASESTAFFCODE,XF_PURCHASEDEPENDENT,XF_DEMOGRAPHICCODE,XF_DEMOGRAPHICDATA,XF_NETQTY,XF_ORIGINALAMOUNT,XF_SELLINGAMOUNT,XF_DISCOUNTAPPROVE,XF_DISCOUNTAMOUNT,XF_TTLTAXAMOUNT1,XF_TTLTAXAMOUNT2,XF_NETAMOUNT,XF_PAIDAMOUNT,XF_CHANGEAMOUNT,XF_DEFAULTTENDER,XF_NUMOFITEM,XF_NUMOFTENDER,XF_PRICEINCLUDETAX,XF_SHOPTAXGROUP,XF_EXTENDPARAM,XF_DESTLOCATIONLIST,XF_POSTDATE,XF_CREATETIME,XF_SALESMODE,CRM_EXECUTED,CRM_EXECUTED1,XF_REFFROM,XF_REFDOCNO from ${schema}.XF_TRANSSALESTOTAL  ${salesTotalWhereSql}"
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize": ${fetchSize}
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXSERIAL",
              "XF_TXTIME",
              "XF_TXBATCH",
              "XF_DOCNO",
              "XF_VOIDDOCNO",
              "XF_TXTYPE",
              "XF_TXHOUR",
              "XF_CASHIER",
              "XF_SALESMAN",
              "XF_CLIENTCODE",
              "XF_PURCHASESTAFFCODE",
              "XF_PURCHASEDEPENDENT",
              "XF_DEMOGRAPHICCODE",
              "XF_DEMOGRAPHICDATA",
              "XF_NETQTY",
              "XF_ORIGINALAMOUNT",
              "XF_SELLINGAMOUNT",
              "XF_DISCOUNTAPPROVE",
              "XF_DISCOUNTAMOUNT",
              "XF_TTLTAXAMOUNT1",
              "XF_TTLTAXAMOUNT2",
              "XF_NETAMOUNT",
              "XF_PAIDAMOUNT",
              "XF_CHANGEAMOUNT",
              "XF_DEFAULTTENDER",
              "XF_NUMOFITEM",
              "XF_NUMOFTENDER",
              "XF_PRICEINCLUDETAX",
              "XF_SHOPTAXGROUP",
              "XF_EXTENDPARAM",
              "XF_DESTLOCATIONLIST",
              "XF_POSTDATE",
              "XF_CREATETIME",
              "XF_SALESMODE",
              "CRM_EXECUTED",
              "CRM_EXECUTED1",
              "XF_REFFROM",
              "XF_REFDOCNO"
            ],
            "connection": [
              {
                "table": [
                  "xf_transsalestotal_${schema}"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ],
            "batchSize": ${batchSize}
          }
        }
      }
    ]
  }
}
