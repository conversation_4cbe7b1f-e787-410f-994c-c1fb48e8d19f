package com.yxt.order.atom.bootstrap;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit.OfflineDataBaseEnum;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import java.util.List;
import javax.annotation.Resource;
import org.apache.shardingsphere.api.hint.HintManager;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2024/12/17 16:51
 */
public class ShardingJDBCTest extends BaseTest {

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Test
  public void testShardingDatabase() {
    DynamicDataSourceContextHolder.push(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME);

    String orderNo = "2854011868554312412";

    LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<>();
    query.eq(OfflineOrderDO::getOrderNo, orderNo);

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      List<OfflineOrderDO> offlineOrderDOS = offlineOrderMapper.selectList(query);
      System.out.println(offlineOrderDOS);
    }

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      hit.setQueryHit(QueryHit.builder().dataBaseEnum(OfflineDataBaseEnum.OFFLINE_ORDER).build());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      List<OfflineOrderDO> offlineOrderDOS = offlineOrderMapper.selectList(query);
      System.out.println(offlineOrderDOS);
    }

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      hit.setQueryHit(
          QueryHit.builder().dataBaseEnum(OfflineDataBaseEnum.OFFLINE_ORDER_ARCHIVE).build());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      List<OfflineOrderDO> offlineOrderDOS = offlineOrderMapper.selectList(query);
      System.out.println(offlineOrderDOS);
    }
  }


}
