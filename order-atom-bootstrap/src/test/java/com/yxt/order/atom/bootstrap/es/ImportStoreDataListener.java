package com.yxt.order.atom.bootstrap.es;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;


public class ImportStoreDataListener extends AnalysisEventListener<StoreData> {

  @Getter
  public int count = 0;

  @Getter
  private List<StoreData> dataList = new ArrayList<>();

  @Override
  public void onException(Exception e, AnalysisContext context) throws Exception {
    throw e;
  }

  @Override
  public void invoke(StoreData storeData, AnalysisContext analysisContext) {
    dataList.add(storeData);
    count = count + 1;
  }

  @Override
  public void doAfterAllAnalysed(AnalysisContext analysisContext) {

  }

}