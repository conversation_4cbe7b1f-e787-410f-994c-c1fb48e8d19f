package com.yxt.order.atom.bootstrap.migration;

import static com.yxt.order.atom.bootstrap.migration.Constant.ALL_SCHEMA;
import static com.yxt.order.atom.bootstrap.migration.Constant.HANA_MIGRATION_CONFIG_LIST;
import static com.yxt.order.atom.bootstrap.migration.Constant.ING;
import static com.yxt.order.atom.bootstrap.migration.Constant.SCHEMA_COMPANY_MAP;

import com.yxt.order.atom.common.utils.DateRangeUtils;
import com.yxt.order.atom.common.utils.DateRangeUtils.RangeDay;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

/**
 * 生成Yaml配置
 *
 * @author: moatkon
 * @time: 2025/2/25 16:54
 */
public class GenerateMigrationSqlTest {
  @Test
  public void testIN(){
    for (String s : ING.split("\n")) {
      System.out.println();
    }
  }
@Test
public void t1(){

  for (String s : ALL_SCHEMA.split("\n")) {
//      System.out.println("select * from "+s+".XF_TRANSSALESTOTAL where XF_STORECODE = 'F296' and XF_DOCNO = 'S900007811' ; ");
//      System.out.println("select count(1) c from "+s+".XF_TRANSSALESTOTAL where  XF_CREATETIME >= '2024-07-31 00:00:00' and XF_CREATETIME <= '2025-01-15 12:00:00' and XF_DOCNO not like  'S0%' union all ");
//    System.out.println("select count(1) c from "+s+".XF_TRANSSALESTOTAL where  XF_CREATETIME >= '2024-07-31 00:00:00' and XF_CREATETIME <= '2025-01-15 12:00:00' and length(XF_TXBATCH)>10 union all");
    // 统计erpCode
//    System.out.println("   select distinct XF_PLU as erpCode from "+s+".XF_TRANSSALESITEM union all");
    // 统计门店
//    System.out.println("select distinct XF_STORECODE as storeCode from "+s+".XF_TRANSSALESTOTAL union all");
    // 统计用户
//    System.out.println("select distinct XF_CLIENTCODE as storeCode from "+s+".XF_TRANSSALESTOTAL  union all");
    // 统计雇员
//    System.out.println("select distinct XF_SALESMAN as storeCode  from "+s+".XF_TRANSSALESITEM  union all");
  }
}

  @Test
  public void generateExtendJson() {
    for (String targetSchema : HANA_MIGRATION_CONFIG_LIST) {
      if(targetSchema.contains("cqhx_users")){
//        System.out.println("ALTER TABLE xf_transsalestotal_"+targetSchema+" ADD COLUMN `extend_json` text COLLATE utf8mb4_general_ci,ALGORITHM=INPLACE, LOCK=NONE;");
        System.out.println("select * from XF_TRANSSALESTOTAL_"+targetSchema+" where XF_STORECODE = 'F062' and XF_DOCNO = 'S500051951' union all ");
      }
    }
  }

  @Test
  public void generateDbConfigTest() {
    System.out.println(HANA_MIGRATION_CONFIG_LIST.size());
    for (String targetSchema : HANA_MIGRATION_CONFIG_LIST) {
      String companyName = fetchCompanyName(targetSchema);
      String migrateSort = "REFUND"; // REFUND  // 先生成正单 ORDER

      String migrationStartTime = "2024-07-31 00:00:00";
      String migrationEndTime = "2025-01-16 00:00:00";

      List<RangeDay> rangeDays = DateRangeUtils.splitByDays(migrationStartTime, migrationEndTime,
          30);

      for (RangeDay rangeDay : rangeDays) {
        sql(targetSchema, migrateSort, companyName, rangeDay.getStart(), rangeDay.getEnd());
      }

    }
  }

  private static void sql(String targetSchema, String migrateSort, String companyName,
      String migrationStartTime, String migrationEndTime) {
    String template =
        "INSERT INTO `dscloud_offline`.`hana_migration`"
            + "(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES "
            + "('${migrateSort}', '${companyName}', '${targetSchema}', '${migrationStartTime}', '${migrationEndTime}', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');";

    System.out.println(template
        .replace("${migrateSort}", migrateSort)
        .replace("${companyName}", companyName)
        .replace("${targetSchema}", targetSchema)
        .replace("${migrationStartTime}", migrationStartTime)
        .replace("${migrationEndTime}", migrationEndTime)
    );
  }

  @Test
  public void validateSql() {
    for (String migrationSchema : HANA_MIGRATION_CONFIG_LIST) {
      String migrateSort = "ORDER"; // ORDER 正单
//      migrateSort = "REFUND"; // REFUND  退单

      String startTime = "2024-07-29 00:00:00";
      String endTime = "2024-07-29 10:00:00";

      StringBuilder sb = new StringBuilder();
      sb.append("select c.count 迁移数,\n"
          + "        CASE\n"
          + "        WHEN migration = 0 THEN '未迁移成功(0)'\n"
          + "        WHEN migration = 1 THEN '迁移成功(1)'\n"
          + "        WHEN migration = 2 THEN '校验已存在,不迁移(2)'\n"
          + "        WHEN migration = 3 THEN '线上订单,不迁移(3)'\n"
          + "        WHEN migration = 4 THEN '明细不存在(4)'\n"
          + "        ELSE '未知状态'\n"
          + "    END AS 各状态迁移进度\n"
          + "    from (\n"
          + "select count(1) count ,migration from xf_transsalestotal_" + migrationSchema
          + " where 1=1 ");

      if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime)) {
        sb.append(
            " and XF_CREATETIME>=\"" + startTime + "\" AND XF_CREATETIME<\"" + endTime + "\"");
      }
      if (migrateSort.equals("ORDER")) {
        sb.append(" and XF_SELLINGAMOUNT >= 0 ");
      } else {
        sb.append(" and XF_SELLINGAMOUNT < 0 ");
      }
      sb.append(" GROUP BY migration\n"
          + ") as c order by migration asc; ");

      System.out.println(sb.toString());
//      break;
    }
  }

  private String fetchCompanyName(String migrationSchema) {
    for (String schema : SCHEMA_COMPANY_MAP.keySet()) {
      if (migrationSchema.contains(schema.toLowerCase())) {
        return SCHEMA_COMPANY_MAP.get(schema);
      }
    }
    throw new RuntimeException(String.format("%s未匹配到公司名", migrationSchema));
  }

}
