package com.yxt.order.atom.bootstrap.es;

import java.util.Random;

public class SnowflakeRandomGenerator {

  private static final long EPOCH = 1609459200000L; // 2021-01-01 00:00:00
  private static final int TIMESTAMP_BITS = 41;
  private static final int DATACENTER_BITS = 5;
  private static final int WORKER_BITS = 5;
  private static final int SEQUENCE_BITS = 12;

  private static final long MAX_DATACENTER_ID = (1L << DATACENTER_BITS) - 1;
  private static final long MAX_WORKER_ID = (1L << WORKER_BITS) - 1;
  private static final long MAX_SEQUENCE = (1L << SEQUENCE_BITS) - 1;

  private final Random random;

  public SnowflakeRandomGenerator() {
    this.random = new Random();
  }

  public long generateRandom19DigitNumber() {
    long timestamp = System.currentTimeMillis() - EPOCH;
    long datacenterId = random.nextLong() & MAX_DATACENTER_ID;
    long workerId = random.nextLong() & MAX_WORKER_ID;
    long sequence = random.nextLong() & MAX_SEQUENCE;

    return ((timestamp << (DATACENTER_BITS + WORKER_BITS + SEQUENCE_BITS)) |
        (datacenterId << (WORKER_BITS + SEQUENCE_BITS)) |
        (workerId << SEQUENCE_BITS) |
        sequence) & ((1L << 63) - 1); // Ensure positive number
  }

  // 使用示例
  public static void main(String[] args) {
    SnowflakeRandomGenerator generator = new SnowflakeRandomGenerator();
    for (int i = 0; i < 10; i++) {
      System.out.println(generator.generateRandom19DigitNumber());
    }
  }
}