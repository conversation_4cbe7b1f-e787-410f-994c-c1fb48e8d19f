package com.yxt.order.atom.bootstrap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.MigrationHanaErrorDataHandlerConsumer;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.service.MigrationPlatformCodeHandler;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月03日 16:55
 * @email: <EMAIL>
 */
public class MigrationPlatformCodeHandlerTest extends BaseTest {

  @Resource
  private HanaMigrationMapper hanaMigrationMapper;


  @Resource
  private MigrationPlatformCodeHandler handler;


  @Test
  @DS(DATA_SOURCE.MIGRATION_TO_MYSQL)
  public void testHanaMig(){
    List<HanaOrderInfo> ynhxData01 = hanaMigrationMapper.getOrderInfoForRefund("23423", "234",
        "ynhx_data01");
    System.out.println(ynhxData01);
  }

  @Test
  public void testConfig() {
//    System.out.println(handler.getPlatformCode("SHHX_DATA01", new Date(), "HX17")); //kc
//    System.out.println(handler.getPlatformCode("SCHX_USERS", new Date(), "HX17")); //HAIDIAN
//    System.out.println(handler.getPlatformCode("SCHX_USERS", lastYear(), "HX17")); //kc
//    System.out.println(handler.getPlatformCode("CDHX_USERS", new Date(), "HX17RRR")); //kc
//    System.out.println(handler.getPlatformCode("CDHX_USERS", new Date(), "HX17")); //HAIDIAN
  }

  private Date lastYear() {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(new Date());
    calendar.add(Calendar.YEAR, -1);
    return new Date(calendar.getTimeInMillis());
  }

  @Resource
  private MigrationHanaErrorDataHandlerConsumer reHandleConsumer;


}
