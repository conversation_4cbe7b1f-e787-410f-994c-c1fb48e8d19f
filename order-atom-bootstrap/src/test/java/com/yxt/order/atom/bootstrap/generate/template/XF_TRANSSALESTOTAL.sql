CREATE TABLE XF_TRANSSALESTOTAL_${schema} (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    XF_STORECODE VARCHAR(6),
                                    XF_TILLID VARCHAR(3),
                                    XF_TXDATE DATE,
                                    XF_<PERSON>SERIAL bigint,
                                    XF_TXTIME VARCHAR(6),
                                    XF_TXBATCH bigint,
                                    XF_DOCNO VARCHAR(10),
                                    XF_VOIDDOCNO VARCHAR(10),
                                    XF_TXTYPE bigint,
                                    XF_TXHOUR bigint,
                                    XF_CASHIER VARCHAR(10),
                                    XF_SALESMAN VARCHAR(10),
                                    XF_<PERSON><PERSON>IENTCODE VARCHAR(24),
                                    XF_<PERSON>URCHASESTAFFCODE VARCHAR(10),
                                    XF_PURCHASEDEPENDENT bigint,
                                    XF_DEMOGRAPHICCODE VARCHAR(4),
                                    XF_DEMOGRAPHICDATA VARCHAR(4),
                                    XF_NETQTY DECIMAL(16, 4),
                                    <PERSON>F_ORIGINALAMOUNT DECIMAL(16, 4),
                                    XF_SELLINGAMOUNT DECIMAL(16, 4),
                                    <PERSON><PERSON>_<PERSON>ISCOUNTAPPROVE VARCHAR(10),
                                    <PERSON><PERSON>_<PERSON>ISCOUNTAMOUNT DECIMAL(16, 4),
                                    XF_TTLTAXAMOUNT1 DECIMAL(16, 4),
                                    XF_TTLTAXAMOUNT2 DECIMAL(16, 4),
                                    XF_NETAMOUNT DECIMAL(16, 4),
                                    XF_PAIDAMOUNT DECIMAL(16, 4),
                                    XF_CHANGEAMOUNT DECIMAL(16, 4),
                                    XF_DEFAULTTENDER VARCHAR(2),
                                    XF_NUMOFITEM bigint,
                                    XF_NUMOFTENDER bigint,
                                    XF_PRICEINCLUDETAX VARCHAR(2),
                                    XF_SHOPTAXGROUP VARCHAR(40),
                                    XF_EXTENDPARAM VARCHAR(128),
                                    XF_DESTLOCATIONLIST VARCHAR(250),
                                    XF_POSTDATE VARCHAR(21),
                                    XF_CREATETIME VARCHAR(21),
                                    XF_SALESMODE VARCHAR(2),
                                    CRM_EXECUTED VARCHAR(1),
                                    CRM_EXECUTED1 VARCHAR(1),
                                    XF_REFFROM VARCHAR(20),
                                    XF_REFDOCNO VARCHAR(50),
                                    migration tinyint DEFAULT '0' COMMENT '迁移 0-否  1-是 2-已存在',
                                    `extend_json` text COLLATE utf8mb4_general_ci,
                                    `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',
                                    UNIQUE (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL),
                                    KEY `idx_order` (`XF_DOCNO`,`XF_STORECODE`),
                                    KEY `idx_create_time` (`XF_CREATETIME`) USING BTREE,
                                    KEY `idx_create_time_migration` (`XF_CREATETIME`, `migration`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;