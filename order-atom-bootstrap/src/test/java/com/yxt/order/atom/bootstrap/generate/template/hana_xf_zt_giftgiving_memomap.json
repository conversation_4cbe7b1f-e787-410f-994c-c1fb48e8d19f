{
  "core": {
    "transport" : {
      "channel": {
        "speed": {
          "channel": ${channel},
          "byte":  ${byte},
          "record": ${record}
        }
      }
    }
  },
  "job": {
    "setting": {
      "speed": {
        "channel": ${channel},
        "byte":  ${byte},
        "record": ${record}
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXTIME,XF_DOCNO,XF_GEDOCNO,XF_GIFTEVENTID,XF_GIFTEVENTAMT,XF_TTLGIFTAMT from ${schema}.XF_ZT_GIFTGIVING_MEMOMAP"
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize": ${fetchSize}
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXTIME",
              "XF_DOCNO",
              "XF_GEDOCNO",
              "XF_GIFTEVENTID",
              "XF_GIFTEVENTAMT",
              "XF_TTLGIFTAMT"
            ],
            "connection": [
              {
                "table": [
                  "xf_zt_giftgiving_memomap_${schema}"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ],
            "batchSize": ${batchSize}
          }
        }
      }
    ]
  }
}
