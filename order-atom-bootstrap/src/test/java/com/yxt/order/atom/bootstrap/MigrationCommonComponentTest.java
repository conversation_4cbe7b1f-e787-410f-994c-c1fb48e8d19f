package com.yxt.order.atom.bootstrap;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.member.api.MemberInfoApi;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.service.MigrationCommonComponent;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl;
import javax.annotation.Resource;
import org.apache.shardingsphere.api.hint.HintManager;
import org.junit.Test;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月26日 15:14
 * @email: <EMAIL>
 */
public class MigrationCommonComponentTest extends BaseTest {

  @Resource
  private MigrationCommonComponent migrationCommonComponent;

  @Resource
  private OfflineOrderRepositoryImpl offlineOrderRepository;


  @Test
  public void testFetchOutParentOrder() {
    // e: S010575923
//    String orderNo = migrationCommonComponent.fetchOutParentThirdOrderNo("S010575922",
//        "YNHX_DATA01");

    // e: S010639931
//    String orderNo2 = migrationCommonComponent.fetchOutParentThirdOrderNo("S010639932",
//        "YNHX_DATA01");
//
//    System.out.println(orderNo);
//    System.out.println(orderNo2);

  }

  @Test
  public void testIdentifyMainOrder() {
    DynamicDataSourceContextHolder.push(DATA_SOURCE.ORDER_OFFLINE);
    try (HintManager hintManager = HintManager.getInstance()) {
      hintManager.setDatabaseShardingValue("202407");

      OfflineOrderDO offlineOrderDO = new OfflineOrderDO();
      offlineOrderDO.setStoreCode("storeCode");
      offlineOrderDO.setThirdOrderNo("thirdOrderNo2");
      offlineOrderDO.setThirdPlatformCode("KE_CHUAN");

      offlineOrderRepository.identifyMainOrder(offlineOrderDO);
    }

  }

//  @Resource
//  private MiddleMemberClient middleMemberClient;

  @Test
  public void testInvokeMemberId() {
    String crmId = "";
//    ResponseBase<MemberData> memberData = middleMemberClient.getMemberData(crmId, MER_CODE_YXT);
//    System.out.println();
  }

  @Resource
  private MemberInfoApi memberInfoApi;

  @Test
  public void testInvokeMemberId2() {
    ResponseBase<MemberInfoVo> memberByCardNo = memberInfoApi.getMemberByCardNo("532500758939");
    System.out.println();
  }


}
