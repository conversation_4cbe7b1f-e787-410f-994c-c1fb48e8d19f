package com.yxt.order.atom.bootstrap;

import org.junit.Test;

/**
 * 生成线下单所有分表的建表语句
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月10日 10:31
 * @email: <EMAIL>
 */
public class GenerateTable {


  @Test
  public void generateTable() {
    String rollback = "CREATE TABLE `offline_refund_order_detail_pick_${seq}` (\n"
        + "  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',\n"
        + "  `pick_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下订单退单明细拣货明细编号,内部生成',\n"
        + "  `refund_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款单号',\n"
        + "  `refund_detail_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款单明细唯一号',\n"
        + "  `erp_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编码',\n"
        + "  `make_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品批号',\n"
        + "  `count` decimal(16,6) DEFAULT NULL COMMENT '数量',\n"
        + "  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',\n"
        + "  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',\n"
        + "  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n"
        + "  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n"
        + "  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',\n"
        + "  `created` datetime DEFAULT NULL COMMENT '平台创建时间',\n"
        + "  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',\n"
        + "  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',\n"
        + "  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',\n"
        + "  PRIMARY KEY (`id`) USING BTREE,\n"
        + "  KEY `idx_order_no_detail_no` (`refund_no`,`refund_detail_no`) USING BTREE,\n"
        + "  KEY `idx_pick_no` (`pick_no`) USING BTREE,\n"
        + "  KEY `idx_created_time` (`created_time`) USING BTREE,\n"
        + "  KEY `idx_updated_time` (`updated_time`) USING BTREE\n"
        + ") ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='线下退单明细拣货信息';";
    for (int i = 0; i < 256; i++) {
      generate(rollback, i);
    }

  }

  private static void generate(String storeCode, int i) {
    // 初始化表
    System.out.println(storeCode.replace("${seq}", String.valueOf(i)));
    if (i == 0) {
      // 初始化按照年月归档的表
      System.out.println(storeCode.replace("${seq}", "2406"));
      System.out.println(storeCode.replace("${seq}", "2407"));
      System.out.println(storeCode.replace("${seq}", "2408"));
      System.out.println(storeCode.replace("${seq}", "2409"));
      System.out.println(storeCode.replace("${seq}", "2410"));
      System.out.println(storeCode.replace("${seq}", "2411"));
      System.out.println(storeCode.replace("${seq}", "2412"));
      System.out.println(storeCode.replace("${seq}", "2501"));
      System.out.println(storeCode.replace("${seq}", "2502"));
      System.out.println(storeCode.replace("${seq}", "2503"));
      System.out.println(storeCode.replace("${seq}", "2504"));
      System.out.println(storeCode.replace("${seq}", "2505"));
    }
  }



}
