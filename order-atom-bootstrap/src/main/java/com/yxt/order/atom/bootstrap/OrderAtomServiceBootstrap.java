package com.yxt.order.atom.bootstrap;

import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.starter.register.EsMapperScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@MapperScan(value = {"com.yxt.order.atom.order.mapper",
    "com.yxt.order.atom.order_world.mapper", "com.yxt.order.atom.migration.dao","com.yxt.order.atom.order_sync.mapper"})
@SpringBootApplication
@EnableTransactionManagement
@EnableFeignClients(basePackages = {"com.yxt.middle", "com.yxt.order.atom","com.yxt.org.read.opensdk"})
@ComponentScan(basePackages = {"com.yxt.order", "cn.hydee.starter.grey"})
@Slf4j
@EnableScheduling
@EsMapperScan("com.yxt.order.atom.order.es.mapper")
@EnableRetry
@EnableAsync
public class OrderAtomServiceBootstrap {

  public static void main(String[] args) {
    SpringApplication.run(OrderAtomServiceBootstrap.class, args);
    log.info("OrderAtomService服务启动成功");
  }

}