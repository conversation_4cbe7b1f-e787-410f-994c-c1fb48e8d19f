server:
  port: 8080
api:
  version: 1.0
compensator:
  enabled: true
spring:
  kafka:
    bootstrap-servers: 10.4.3.239:9092,10.4.3.240:9092,10.4.3.241:9092
    producer:
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      ack-mode: manual
      log-container-config: false
  data:
    mongodb:
      uri: **********************************************************
  profiles:
    active: test
    robot-send: true
  application:
    name: order-atom-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: http://10.4.3.210:8848; # 测试
        namespace: 63b6732e-80fc-49c2-ac83-4b09e119d48c # 测试
        metadata:
          department: NR
        register-enabled: true
  shardingsphere:
    datasource:
      names: order-offline-0,order-offline-1
      order-offline-0:
        url: ******************************************************************************************************************************************************  #开发
        username: agent
        password: WrHNOhOGHR8yzMEgKvao
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        initial-size: 40
        min-idle: 40
        max-active: 100
        max-wait: 10000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        wall:
          multi-statement-allow: true
      order-offline-1:
        url: **************************************************************************************************************************************************************  #开发
        username: agent
        password: WrHNOhOGHR8yzMEgKvao
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        druid:
          initial-size: 40
          min-idle: 40
          max-active: 100
          max-wait: 10000
          wall:
            multi-statement-allow: true
    sharding:
      default-data-source-name: order-offline-0
      tables:
        offline_order:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_amount:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_amount_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_cashier_desk:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_cashier_desk_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_coupon:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_coupon_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_delivery_address:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_delivery_address_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail_coupon:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_coupon_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail_pick:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_pick_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail_promotion:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_promotion_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail_trace:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_trace_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_med_ins_settle:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_med_ins_settle_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_organization:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_organization_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_pay:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_pay_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_prescription:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_prescription_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_promotion:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_promotion_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_set_detail:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_set_detail_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_user:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_user_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_amount:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_amount_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_detail:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_detail_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_detail_pick:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_detail_pick_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_detail_trace:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_detail_trace_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_pay:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_pay_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_med_ins_settle:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_med_ins_settle_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_user:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_user_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_cashier_desk:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_cashier_desk_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_organization:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_organization_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        extend_data:
          actual-data-nodes: order-offline-$->{0..1}.extend_data_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm

        platform_order_info:
          actual-data-nodes: order-offline-$->{0..1}.platform_order_info_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        platform_order_detail:
          actual-data-nodes: order-offline-$->{0..1}.platform_order_detail_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        platform_order_amount:
          actual-data-nodes: order-offline-$->{0..1}.platform_order_amount_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        platform_order_user:
          actual-data-nodes: order-offline-$->{0..1}.platform_order_user_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
    props:
      sql.show: false #是否开启SQL显示，默认值: false
      # executor.size: #工作线程数量，默认值: CPU核数
      max.connections.size.per.query: 10 # 每个查询可以打开的最大连接数量,默认为1
      check.table.metadata.enabled: false #是否在启动时检查分表元数据一致性，默认值: false

  datasource:
    dynamic:
      primary: ordermaster
      strict: false
      datasource:
        order_offline:
          url: ******************************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        order_offline_archive:
          url: **************************************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 20
            min-idle: 10
            max-active: 80
            max-wait: 10000
            wall:
              multi-statement-allow: true
        ordermaster:
          url: **********************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        orderslave:
          url: **********************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        orderslave2:
          url: **********************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        # orderdoris:
        #   url: ******************************,10.100.6.224:9030,10.100.6.194:9030/doris_hydee_nr_test?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false  #开发
        #   username: agent_nr
        #   password: hydee_nr123
        #   driver-class-name: com.mysql.cj.jdbc.Driver
        #   type: com.alibaba.druid.pool.DruidDataSource
        #   druid:
        #     initial-size: 40
        #     min-idle: 40
        #     max-active: 100
        #     max-wait: 10000
        #     wall:
        #       multi-statement-allow: true
        # orderynoms:
        #   url: *****************************************************************************************************  #开发
        #   username: zhangkg
        #   password: <EMAIL>
        #   # url: *****************************************************************************************************  #生产
        #   # username: zhangkg
        #   #password: <EMAIL>
        #   driver-class-name: com.mysql.cj.jdbc.Driver
        #   type: com.alibaba.druid.pool.DruidDataSource
        #   druid:
        #     initial-size: 40
        #     min-idle: 40
        #     max-active: 100
        #     max-wait: 10000
        #     wall:
        #       multi-statement-allow: true
        hana:
          url: **************************
          username: HANA_CD
          password: Abcd%1234
          driver-class-name: com.sap.db.jdbc.Driver
    druid:
      initial-size: 40
      min-idle: 40
      max-active: 100
      max-wait: 10000
      wall:
        multi-statement-allow: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      # 启用监控统计
      filters: stat,wall  # 必须包含 stat 才能开启监控
      aop-patterns: "com.yxt.order.atom.order_world.mapper.*"
      web-stat-filter:
        enabled: true                   # 启动 StatFilter
        url-pattern: /*                 # 过滤所有url
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" # 排除一些不必要的url
      # 配置监控页面
      stat-view-servlet:
        enabled: true          # 启用监控页面
        url-pattern: /druid/*  # 访问路径（默认即为/druid/*）
        login-username: admin  # 登录用户名
        login-password: 123456 # 登录密
        reset-enable: true     # 允许清空监控数据
        allow:                 # 允许访问的 IP（可选，留空则允许所有）
        deny:                  # 禁止访问的 IP（优先于 allow）
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  redis:
    password: yxt_redis123
    lettuce:
      pool:
        max-idle: 200
        min-idle: 50
        max-active: 5000
        max-wait: 1000
    timeout: 2000
    cluster:                                                                                # 此处新增
      nodes: **********:9000,**********:9002,**********:9003,**********:9004,**********:9005     # 此处新增，集群地址（此处为测试环境地址）
      max-redirects: 3
  redisson:
    config: |
      clusterServersConfig:
        idleConnectionTimeout: 10000
        connectTimeout: 10000
        timeout: 3000
        retryAttempts: 3
        retryInterval: 1500
        failedSlaveReconnectionInterval: 3000
        failedSlaveCheckInterval: 60000
        password: yxt_redis123
        subscriptionsPerConnection: 5
        clientName: null
        loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}
        subscriptionConnectionMinimumIdleSize: 1
        subscriptionConnectionPoolSize: 50
        slaveConnectionMinimumIdleSize: 24
        slaveConnectionPoolSize: 64
        masterConnectionMinimumIdleSize: 24
        masterConnectionPoolSize: 64
        readMode: "SLAVE"
        subscriptionMode: "SLAVE"
        nodeAddresses:
          - "redis://**********:9000"
          - "redis://**********:9002"
          - "redis://**********:9003"
          - "redis://**********:9004"
          - "redis://**********:9005"
        scanInterval: 1000
        pingConnectionInterval: 30000
        keepAlive: false
        tcpNoDelay: true
      threads: 16
      nettyThreads: 32
      codec: !<org.redisson.codec.Kryo5Codec> {}
      transportMode: "NIO"


mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-value: 0
      logic-not-delete-value: 1
      update-strategy: not_null

management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
  endpoints:
    web:
      exposure:
        include: ["*"]
  health:
    elasticsearch:
      enabled: false
    mongo:
      enabled: false
  metrics:
    distribution:
      percentiles-histogram[http.server.requests]: true
      maximum-expected-value[http.server.requests]: 10000 #预期最大值
      minimum-expected-value[http.server.requests]: 1 #预期最小值

swagger:
  enable: true

feign:
  hystrix:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      customer-config:
        connectTimeout: 6000
        readTimeout: 6000
        loggerLevel: full
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            strategy: SEMAPHORE
            timeoutInMilliseconds: 60000



alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: 17710036783,17302856015

xxl:
  job:
    admin:
      addresses: http://xxl-job-admin:8080/xxl-job-admin/
    executor:
      appname:
      port:
      ip:
      address:
      logretentiondays: 7
    accessToken: sk_token

logging:
  level:
    root: info
    com.yxt.order.atom.order.mapper: debug

migration-config:
  schema-list:
    - schema: YNHX_DATA01
      enable: true
      start-time: '2024-07-29 00:00:00'
      end-time: '2024-07-29 10:00:00'
    - schema: GXHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: GZHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: SCHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: SXHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: CQHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: CDHX_USERS
      enable: false
      start-time: '2024-06-22 00:00:00'
      end-time: '2024-06-24 00:00:00'
    - schema: SHHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: TJHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: HNHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: HENHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: SXGSHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: TJQCHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: HENNYHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: ZYHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'

migration-third-platform-config:
  item-list:
    # 攀枝花 3月4日之后
    - schema: SCHX_USERS
      specific-date: '2024-03-05 00:00:00'
    # 重庆 3月4日之后
    - schema: CQHX_USERS
      specific-date: '2024-03-05 00:00:00'
    # 四川 6月30日之后
    - schema: CDHX_USERS
      specific-date: '2024-07-01 00:00:00'
      store-codes: 'H382,H383,H384,H385,H386,H387,H388,H389,H390,H391,H392,H393,H394,H395,H696,H720,H724,H732,H735,H802,H871,H999,HA00,HA30,HA31,HA32,HA33,HA34,HA35,HA36,HA37,HA38,HA39,HA40,HA41,HA42,HA43,HA44,HA45,HA46,HA47,HA48,HA49,HB04,HB05,HC36,HC59,HF16,HF19,HF74,HF82,HG21,HG74,HG75,HH78,HH86,HH96,HI11,HI25,HI36,HI47,HI51,HI55,HI68,HI79,HX01,HX02,HX03,HX04,HX05,HX06,HX07,HX08,HX09,HX10,HX11,HX12,HX13,HX14,HX15,HX16,HX17,HX18,HX19,HX20,HX21,HX22,HX23,HX24,HX25,HX26,HX27,HX28,HX29,HX30,HX31,HX32,HX33'

# 拆单的区域公司(schema),多个,以逗号分隔
migration-split-company: YNHX_DATA01

rocketmq:
  producer:
    group: order-atom-group
  name-server: 10.4.3.242:9876;10.4.3.243:9876

mq:
  topic:
    producer:
      migrationHanaData: TP_ORDER_ATOM_MIGRATION-HANA
      migrationHanaDataReHandle: TP_ORDER_ATOM_MIGRATION-RE-HANDLE-HANA
      hdOfflineOrder: TP_ORDER_OFFLINE_SYNC-HD
      offlineOrderSyncKc: TP_ORDER_OFFLINE_SYNC-KC


canal:
  # 支持慢病
  support-chronic-diseases: support_chronic_diseases
  # b2c-订单
  b2c-order: b2c_order_info_canal_to_kafka_topic_test3
  account-order: account_order_canal_to_kafka_topic_test
  account-refund-order: account_refund_canal_to_kafka_topic_test
  member-transaction: order_member_transaction_record_topic_test
  org-order: org_order_topic_test
  org-refund: org_refund_topic_test
  # hana数据迁移
  migration-hana: order_migration_hana_to_offline
  order-world-order: order_world_order_canal_to_kafka_topic_test
  order-world-refund-order: order_world_refund_order_canal_to_kafka_topic_test
  offline-order-management: offline_order_management_topic_test

# 检查是否配置了订单数据源

easy-es:
  enable: true
  address: **********:9200,**********:9200,**********:9200
  schema: http
  username: elastic
  password: ${myEncrypt.des(5ea6b5c12c5c770a08613b039664d784)}
  keep-alive-millis: 18000
  global-config:
    process-index-mode: manual
    # 异步处理索引是否阻塞主线程 默认阻塞 数据量过大时调整为非阻塞异步进行 项目启动更快
    # https://www.easy-es.cn/pages/cc15ba/#%E9%85%8D%E7%BD%AE%E5%90%AF%E7%94%A8%E6%A8%A1%E5%BC%8F
    async-process-index-blocking: false
    print-dsl: true
    db-config:
      # 添加前缀,区分环境
      index-prefix: ${spring.profiles.active}_

sensitive:
  scope:
    addUpdates[0]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.updateRecord
    addUpdates[1]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.insert
    addUpdates[2]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.insertBatch
    addUpdates[3]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.updateByOrderNo
    addUpdates[4]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.updateByBatch
    addUpdates[5]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.update
    addUpdates[6]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.insert
    addUpdates[7]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.update
    addUpdates[8]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.updateById
    selects[0]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.selectByOrderNo
    selects[1]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.selectList
    selects[2]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.selectListByOrderNo
    selects[3]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.queryNeedDealPrescriptionList
    selects[4]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.selectList
    selects[5]: com.yxt.order.atom.order.mapper.OrderInfoExportMapper.listOverall
    selects[6]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.selectByOrderNoList
    selects[7]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.selectListByOrderNoList
    selects[8]: com.yxt.order.atom.order.mapper.OrderInfoExportMapper.listOverallByOrderNos
    selects[9]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.selectOne
    selects[10]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.selectList
    selects[11]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.selectOne
    selects[12]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.selectById

schema-meta-data-load-logic: true
yyMMDatabaseStartValue: 2306
esOrderMemberTransactionRecordAlertWebhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5a46a096-03b8-4590-a41d-684263021025

validateEsQueryDays: 190


# 迁移正单配置ORDER,迁移退单配置REFUND. 不在依赖于DB
migrateSort: ORDER

# 配置这个,主要是筛有效数据,因为是基于游标的
migrateStartTime: '2024-07-29 00:00:00'
migrateEndTime: '2024-07-29 01:00:00'
