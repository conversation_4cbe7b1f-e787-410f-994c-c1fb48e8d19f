package com.yxt.order.atom.open.sdk.req.es_order;

import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月07日 16:45
 * @email: <EMAIL>
 */
@Data
public class EsOrderQueryReqDTO {

  @ApiModelProperty("用户Id集合")
  private List<String> userIdList;

  @ApiModelProperty("订单类型，ORDER-正单 REFUND-退单")
  private EsOrderType esOrderType;

  @ApiModelProperty("线上门店 可以获取storeId和StoreName")
  private String onlineStoreCode; // 网点code,线下单没有网点

  @ApiModelProperty("门店编码 可以获取storeId和StoreName")
  private String organizationCode; // 网点单对应的履约门店,会员慢病用这个

  @ApiModelProperty("erp编码集合")
  private List<String> commodityCodeList;

  @ApiModelProperty("开始时间 格式:yyyy-MM-dd HH:mm:ss")
  @NotNull(message = "startDate can not empty")
  private String startDate;

  @ApiModelProperty("结束时间 格式:yyyy-MM-dd HH:mm:ss")
  @NotNull(message = "endDate can not empty")
  private String endDate;

  @ApiModelProperty("服务模式 O2O,B2C,POS")
  private List<EsServiceMode> esServiceModeList;

  @ApiModelProperty("订单状态 DONE 已完成")
  private EsOrderStatus esOrderStatus;

  @ApiModelProperty("当前页")
  private Integer currentPage;

  @ApiModelProperty("页大小")
  private Integer pageSize;

  @ApiModelProperty("排序字段 payTime")
  @NotEmpty(message = "orderBy can not empty")
  private String orderBy;

  @ApiModelProperty("顺序 asc,desc")
  @NotNull(message = "order can not null")
  private OrderEnum order;

  @ApiModelProperty("商品名称,支持模糊搜索")
  private String commodityName;

  @ApiModelProperty("五级分类")
  private List<String> fiveClassList;

  public enum OrderEnum {
    ASC, DESC
  }

}
