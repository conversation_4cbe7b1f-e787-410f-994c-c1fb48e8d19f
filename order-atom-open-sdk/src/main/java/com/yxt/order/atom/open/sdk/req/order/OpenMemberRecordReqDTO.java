package com.yxt.order.atom.open.sdk.req.order;


import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class OpenMemberRecordReqDTO extends PageBase {

  /**
   * 会员ID
   */
  private Long userId;

  /**
   * 单据号
   */
  private String docNo;

  /**
   * 商品名称
   */
  private String itemName;

  /**
   * 会员卡号
   */
  private String memberCard;

  @ApiModelProperty("查询起始日期")
  private String startDate;

  @ApiModelProperty("查询截至日期")
  private String endDate;


  /**
   * 单据号or商品名称
   */
  private String docNoOrName;


  /**
   * 会员卡信息
   */
  List<String> memberCardNoList;
}
